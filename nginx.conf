underscores_in_headers on;

server {
  listen 80;
  server_name localhost;
  proxy_set_header Host $http_host;
  proxy_set_header X-Real-IP $remote_addr;
  proxy_buffer_size  64k;
  proxy_buffers   32 64k;
  proxy_busy_buffers_size 128k;
  client_max_body_size 500M;
  root /usr/local/openresty/nginx/html;
  index index.html;


  location /heper-api/ {

    proxy_pass http://hepar-med-ai/;
  }

    location /ms-wechat/ {

    proxy_pass http://ms-wechat/;
  }

  location / {
    try_files $uri $uri/ /index.html;
  }



  error_page 500 502 503 504 /50x.html;
  location = /50x.html {
    root /usr/local/openresty/nginx/html;
  }
}
