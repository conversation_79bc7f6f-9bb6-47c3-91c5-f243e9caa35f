import { env } from 'node:process'
import { defineConfig } from 'vite'
import UnoCSS from 'unocss/vite'
import vue from '@vitejs/plugin-vue'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'

import AutoImport from 'unplugin-auto-import/vite'
import BuildInfo from 'unplugin-info/vite'
import TurboConsole from 'unplugin-turbo-console/vite'
import { getRootPath, getSrcPath } from './build'

// 排除规则函数
function excludePngFromBase64(id) {
  const regex = /full_map_tip/
  return regex.test(id)
}

// https://vitejs.dev/config/
export default defineConfig(() => {
  const rootPath = getRootPath()
  const srcPath = getSrcPath()
  return {
    plugins: [
      vue({
        script: {
          defineModel: true,
        },
      }),
      Components({
        resolvers: [
          (componentName) => {
            if (componentName.startsWith('N'))
              return { name: componentName, from: 'wowjoy-vui' }
          },
          ElementPlusResolver(),
        ],
        dirs: ['src/components/**'],
        dts: 'src/typings/components.d.ts',
      }),
      AutoImport({
        imports: ['vue', 'vue-router'],
        dirs: ['src/stores/**'],
        dts: 'src/typings/auto-imports.d.ts',
        resolvers: [ElementPlusResolver()],

      }),
      UnoCSS(),
      // viteMockServe(),
      BuildInfo({
        meta: {
          buildNumber: JSON.stringify(env.BUILD_NUMBER),
          gitBranch: JSON.stringify(env.GIT_BRANCH),
        },
      }),
      TurboConsole(),
    ],
    resolve: {
      alias: {
        '~': rootPath,
        '@': srcPath,

      },
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: '@use "./src/styles/scss/global.scss" as *;',
        },
      },
    },
    server: {
      proxy: {
        '/heper-api': {
          // target: 'https://dev-mng.cminfo.net/', // 开发环境
          // target: 'http://192.168.0.5:9060/', // 刘坤本地
          // target: 'http://192.168.0.11:31429/', // 南京二院
          // target: 'https://test-mng.cminfo.net/', // 测试环境
          // target: 'https://show-mng.cminfo.net/', // 演示环境
          // target: 'http://10.20.32.21:31429/', // slmc生产1
          // target: 'http://10.20.32.21:31332/', // slmc生产
          target: 'https://rs.cminfo.net/', // 科研生产
          // target: 'https://dev-rs.cminfo.net/', // 科研生产
          changeOrigin: true,
          secure: false,
          // rewrite: path => path.replace(/^\/heper-api/, ''),
        },
        '/ms-wechat/': {
          target: 'https://dev-mng.cminfo.net/', // 开发环境
          // target: 'https://rs.cminfo.net/', // 生产
          changeOrigin: true,
        },
      },
    },
    define: {
      __IS_DEV__: env.NODE_ENV === 'development',
    },
  }
})
