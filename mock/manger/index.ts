import { faker } from '@faker-js/faker'

export default [
  {
    url: '/mock/heper-api/api/slmc/follow/v1/tag/list',
    timeout: '100',
    method: 'get',
    response: () => (
      {
        state: 200,
        results: [
          {
            createId: faker.string.uuid(),
            createName: '<PERSON>',
            createOrganId: '456',
            createTime: '2022-05-01T10:00:00Z',
            incrementId: 1,
            isDeleted: false,
            isEnable: '1',
            tagCategory: 'Category A',
            tagId: faker.database.mongodbObjectId(),
            tagName: faker.commerce.productMaterial(),
            updateId: faker.string.nanoid(),
            updateName: faker.person.fullName(),
            updateTime: '2022-05-02T08:30:00Z',
          },
        ],
      }
    ),
  },

]
