export default [
  {
    url: '/mock/heper-api/api/slmc/follow/v1/task/phases',
    timeout: 2000,
    method: 'post',
    response: () => (
      {
        state: 200,
        results: [
          {
            scaleId: 'SelfMonitoring',
            scaleName: '自我监测问卷',
            isCountFinishedRate: false,
            taskFinishedRate: '100%',
          },
          {
            scaleId: 'Cardiovascular',
            scaleName: '心血管疾病及糖尿病风险筛查问卷',
            isCountFinishedRate: true,
            taskFinishedRate: '100%',
          },
          {
            scaleId: 'Medication',
            scaleName: '药物依从性调查问卷',
            isCountFinishedRate: true,
            taskFinishedRate: '100%',
          },
          {
            scaleId: 'Dietary',
            scaleName: '膳食营养调查问卷',
            isCountFinishedRate: true,
            taskFinishedRate: '100%',
          },
          {
            scaleId: 'LifeStyle',
            scaleName: '生活方式调查问卷',
            isCountFinishedRate: true,
            taskFinishedRate: '100%',
          },
          {
            scaleId: 'VitalSigns',
            scaleName: '生命体征和人体测量学',
            isCountFinishedRate: true,
            taskFinishedRate: '100%',
          },
          {
            scaleId: 'BodyComposition',
            scaleName: '人体成分分析',
            isCountFinishedRate: true,
            taskFinishedRate: '100%',
          },
          {
            scaleId: 'MedicalHistory',
            scaleName: '病史采集问卷',
            isCountFinishedRate: true,
            taskFinishedRate: '100%',
          },
        ],
      }
    ),
  },
]
