export default [
  {
    url: '/mock/heper-api/api/slmc/follow/v1/task/report/iconography',
    timeout: 1000,
    method: 'get',
    response: () => (
      {
        state: 200,
        results: {
          examItem: '这是检查项目',
          imageSight: '这是检查描述',
          imageDiagnosis: '这是检查结论',
          performer: '张医生',
        },
      }
    ),
  },
  {
    url: '/mock/heper-api/api/slmc/follow/v1/task/report/examination',
    timeout: 1000,
    method: 'get',
    response: () => (
      {
        state: 200,
        results: [
          {
            chineseName: '乙肝表面抗原 (HBsAg)',
            unit: 'IU/mL',
            result: '1.00',
            refValue: '阴性＜0.05；阳性≥0.05',
            hint: '↑',
          },
          {
            chineseName: '乙肝表面抗体 (Anti-HBs)',
            unit: 'IU/mL',
            result: '1.00',
            refValue: '阴性＜10.00；阳性≥10.00',
            hint: null,
          },
        ],
      }
    ),
  },
]
