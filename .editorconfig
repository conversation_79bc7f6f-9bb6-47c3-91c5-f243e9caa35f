# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]  # 表示所有文件适用
end_of_line = lf   # 控制换行类型(lf | cr |crlf)
insert_final_newline = true # 始终在文件末尾插入一个新行
charset = utf-8   # 设置文件字符集为utf-8
indent_style = space   # 控制缩进风格(tab | space)
indent_size = 4    # 缩进大小
trim_trailing_whitespace = true #去除行首的任意空白字符串

[*.md]
trim_trailing_whitespace = false
