<script setup lang="ts">
import dayjs from 'dayjs'
import { Breadcrumb } from '@/layouts/common'
import { getPlanListApi2 } from '@/api/intelligentFollow/manage'
import { useAuthStore } from '~/src/store'
import {
  getFollowMarks,
  getFollowRecords,
  getFollowSearchAnysis,
} from '~/src/api/intelligentFollow/plan'
import femaleImg from '@/assets/images/intelligentFollow/patient_female.png'
import maleImg from '@/assets/images/intelligentFollow/patient_male.png'
import { useRouterPush } from '~/src/hooks/userRouterPush'
import { addFollowRecordInfo } from '@/api/intelligentFollow/record'
import { getEduListAPI, getSurveyListAPI } from '@/api/toolsFollow'

const { userInfo } = useAuthStore()
const { routerPush } = useRouterPush()

const searchParams = reactive({
  searchName: '',
  followDateStart: '',
  followDateEnd: '',
  planTemplateId: null,
  followTypeList: [],
  markIds: [],
  recordStatus: 'ALL',
  queryType: '2',
  operatorId: userInfo.id,
})

const pageLoadOptions = reactive({
  page: 1,
  loading: false,
  size: 30,
  finished: false,
})

const dateRange = ref([])
const typeTitle = {
  SCALE: '问卷调查',
  EDU: '健康宣教',
  REMIND_REVIEW: '复查提醒',
  REMIND_FOLLOW: '随访提醒',
  RT_HOSPITAL: '到院复查',
  NEXT_FOLLOW: '下次随访安排',
  INTRO: '项目介绍',
}
const followTypes = [
  {
    label: '问卷调查',
    value: 'SCALE',
  },
  {
    label: '健康宣教',
    value: 'EDU',
  },
  {
    label: '复查提醒',
    value: 'REMIND_REVIEW',
  },
  {
    label: '随访提醒',
    value: 'REMIND_FOLLOW',
  },
  {
    label: '到院复查',
    value: 'RT_HOSPITAL',
  },
  {
    label: '下次随访安排',
    value: 'NEXT_FOLLOW',
  },
  {
    label: '项目介绍',
    value: 'INTRO',
  },
]

const statusTypes = [
  {
    label: '全部',
    value: 'ALL',
  },
  {
    label: '已完成',
    value: 'FINISH',
  },
  {
    label: '逾期',
    value: 'OVER',
  },
  {
    label: '未开始',
    value: 'NO_START',
  },
  {
    label: '进行中',
    value: 'NO_FINISH',
  },
]

const recordStatusColor = {
  NO_START: 'color-#ccc',
  NO_FINISH: 'color-red',
  FINISH: 'color-green',
}
const sfstatus = [
  {
    label: '今日随访任务',
    key: '2',
  },
  {
    label: '全部',
    key: '1',
  },
  {
    label: '未完成随访任务',
    key: '3',
  },
]

const showStagings = ref([
  {
    key: 'patient',
    label: '随访患者',
  },
  {
    key: 'scale',
    label: '问卷调查',
  },
  {
    key: 'edu',
    label: '健康宣教',
  },
  {
    key: 'return',
    label: '复查提醒',
  },
  {
    key: 'remind',
    label: '随访提醒',
  },
  {
    key: 'next',
    label: '下次随访安排',
  },
  {
    key: 'review',
    label: '到院复查',
  },
  {
    key: 'intro',
    label: '项目介绍',
  },
])

const showOtherCounts = ref([
  {
    key: 'allRecordNum',
    label: '全部任务数',
  },
  {
    key: 'startNum',
    label: '进行中数',
  },
  {
    key: 'finishNum',
    label: '已完成数',
  },
  {
    key: 'noFinishNum',
    label: '逾期数',
  },
  {
    key: 'noStartNum',
    label: '未开始数',
  },
])
const editReocrds = ref(false) // 是否是随访选中状态
const editPatientList = ref([])
/// 新增随访记录
const showAdd = ref(false)
const addSubject = reactive({
  recordType: undefined,
  scaleEduId: undefined,
  scaleEduName: undefined,
  recordContent: undefined,
  syncPatient: 1, // 是否同步患者:1是，2否
  syncType: undefined,
  followDate: dayjs().format('YYYY-MM-DD'),
  memo: undefined,
})
const followTypeOptions = [
  {
    label: '问卷调查',
    value: 'SCALE',
  },
  {
    label: '健康宣教',
    value: 'EDU',
  }, {
    label: '随访提醒',
    value: 'REMIND_FOLLOW',
  }, {
    label: '到院复查',
    value: 'REMIND_REVIEW',
  }, {
    label: '下次随访安排',
    value: 'NEXT_FOLLOW',
  }, {
    label: '项目介绍',
    value: 'INTRO',
  },
]
const sendTypeOption = [
  { label: '肝愈小程序', value: 'XCX' }, { label: '电话', value: 'DH' }, { label: '短信', value: 'DX' },
]
const edutList = ref()
const surveyList = ref()

const planTemplates: any = ref([])
const patientMarks: any = ref([])
const ansyisData: any = ref()
const recordsList: any = ref([])

function getPlanTemplates() {
  getPlanListApi2({
    search: '',
    operateId: userInfo.id,
  }).then((res) => {
    planTemplates.value = res?.data || []
  })
}

function disabledDate(time: Date) {
  return time.getTime() > Date.now()
}

function dateRangeChange() {
  if (dateRange.value?.length > 0) {
    searchParams.followDateStart = dateRange.value[0]
    searchParams.followDateEnd = dateRange.value[1]
  }
  else {
    searchParams.followDateStart = ''
    searchParams.followDateEnd = ''
  }
  console.log(dateRange.value)
}

function getPatientMarks() {
  getFollowMarks().then((res) => {
    patientMarks.value = res?.data || []
  })
}

function getStagingData() {
  getFollowSearchAnysis(searchParams).then((res) => {
    ansyisData.value = res?.data
  })

  getFollowRecords({ ...searchParams, page: 1, size: 30 }).then((res) => {
    recordsList.value = res?.data?.records || []
  })
}

/// 搜索
function handleSearchClick() {
  getStagingData()
}

/// 重置
function handleResetClick() {
  searchParams.searchName = ''
  searchParams.followTypeList = []
  searchParams.markIds = []
  searchParams.planTemplateId = null
  searchParams.queryType = '2'
  searchParams.followDateStart = ''
  searchParams.followDateEnd = ''
  editReocrds.value = false
  editPatientList.value = []
  getStagingData()
  pageLoadOptions.page = 1
  pageLoadOptions.finished = false
  pageLoadOptions.loading = false
}

// 处理批量新增
function handleEidtRecordClick() {
  if (!editReocrds.value) {
    editReocrds.value = true
  }
  else {
    if (editPatientList.value?.length > 0) {
      // 开始批量新增
      showAdd.value = true
    }
    else {
      window.$message.warning('请选择患者')
    }
  }
}

function selectRecord(item?) {
  console.log('点击了 item=====', item)
  event?.stopPropagation()
  const index = editPatientList.value.indexOf(item.patientId)
  if (index !== -1)
    editPatientList.value.splice(index, 1)
  else
    editPatientList.value.push(item.patientId)
}

function handleBodyConfirm() {
  if (!addSubject.recordType) {
    window.$message.warning('请选择随访类型!')
    return
  }
  if (addSubject.recordType === 'SCALE' || addSubject.recordType === 'EDU') {
    if (addSubject.recordType === 'SCALE')
      addSubject.scaleEduName = surveyList.value.find((item: any) => item.scaleId === addSubject.scaleEduId)?.scaleName

    else
      addSubject.scaleEduName = edutList.value.find((item: any) => item.educationId === addSubject.scaleEduId)?.title

    if (!addSubject.scaleEduId) {
      window.$message.warning(`请选择${addSubject.recordType === 'SCALE' ? '随访问卷' : '健康宣教'}!`)
      return
    }
  }
  else if (addSubject.recordType !== 'INTRO') {
    if (!addSubject.recordContent) {
      window.$message.warning('请输入随访内容!')
      return
    }
  }

  showAdd.value = false
  const params = {
    patientIdList: editPatientList.value,
    creator: userInfo?.userName,
    ...addSubject,
  }
  console.log('请求参数======', addSubject)
  // 新增记录
  addFollowRecordInfo(params).then((res: any) => {
    console.log('新增成功了======', res)
    if (res) {
      window.$message.success('新增成功')
      addSubject.recordType = undefined
      addSubject.scaleEduId = undefined
      addSubject.recordContent = undefined
      addSubject.syncPatient = 1
      addSubject.syncType = undefined
      addSubject.followDate = dayjs().format('YYYY-MM-DD')
      addSubject.memo = undefined
      editReocrds.value = false
      editPatientList.value = []
      getStagingData()
    }
  })
}

function updateTypeChange() {
  addSubject.scaleEduId = undefined
  addSubject.scaleEduName = undefined
}

async function getOptionsData() {
  getEduListAPI().then((res) => {
    edutList.value = res.data
  })

  getSurveyListAPI('').then((res) => {
    surveyList.value = res.data?.slice(0, 6)?.map((item: any) => {
      return {
        ...item,
        isActive: false,
      }
    })
  })
}

/// 跳转到随访管理详情
function jumpToDetail(item) {
  // manage/detail?patientId=1726064491349680129&planId=yg2
  console.log(item)
  routerPush({
    name: 'intelligentFollow_manage_detail',
    query: {
      patientId: item.patientId,
      planId: item?.planTemplateId,
    },
  })
}

onMounted(() => {
  getPlanTemplates()
  getPatientMarks()
  getStagingData()
  getOptionsData()
  handleScrollBinding()
})

function handleScrollBinding() {
  const scrollContainer = document.getElementsByClassName('pageCard-content')?.[0]
  scrollContainer.addEventListener('scroll', () => {
    console.log('Scrolling...')
    const offset = 20 // 调整这个偏移量以适应不同的屏幕和分辨率
    if ((scrollContainer.scrollTop + scrollContainer.clientHeight >= scrollContainer.scrollHeight - offset) && !pageLoadOptions.loading && !pageLoadOptions.finished) {
      console.log('Scrolled to bottom!')
      // 分页加载
      if (recordsList.value.length === 0)
        return
      pageLoadOptions.loading = true
      pageLoadOptions.page += 1
      getFollowRecords({ ...searchParams, page: pageLoadOptions.page, size: 30 }).then((res) => {
        recordsList.value = recordsList.value.concat(res?.data?.records || [])
        if (pageLoadOptions.page === Number(res?.data?.pages))
          pageLoadOptions.finished = true
        pageLoadOptions.loading = false
      })
    }
  })
}

// function handleScroll() {
//   console.log('xxxxxxx')
//   const bottomOfWindow = window.innerHeight + window.scrollY >= document.documentElement.offsetHeight
//   if (bottomOfWindow && !pageLoadOptions.loading && !pageLoadOptions.finished) {
//     // getFollowRecords({ ...searchParams, page: pageLoadOptions.page, size: pageLoadOptions.size }).then((res) => {
//     //   recordsList.value.concat(res?.data || [])
//     // })

//     /// 需要加载了
//     console.log('底部需要加载了')
//   }
// }
</script>

<template>
  <div>
    <Breadcrumb
      :bread-list="[
        {
          title: '智能随访',
          link: null,
          key: 'intelligentFollow',
        },
        {
          title: '我的工作台',
          link: null,
          key: 'intelligentFollow_workbench',
        },
      ]"
    />
    <PageCard breadcrumb @scroll="handleScroll">
      <PageTitle class="mb-14px">
        随访工作台
      </PageTitle>
      <div flex flex-wrap items-center style="gap: 20px">
        <div flex items-center>
          <span color="#666" mr-10px w-85px text-14px>姓名 / 手机号</span>
          <el-input v-model="searchParams.searchName" placeholder="请输入姓名或手机号" style="width: 230px" />
        </div>
        <div flex items-center>
          <span color="#666" mr-10px w-85px text-14px>随访记录时间</span>
          <el-date-picker
            v-model="dateRange" style="width: 200px" type="daterange" clearable
            :disabled-date="disabledDate" unlink-panels :default-value="[dayjs().subtract(1, 'month'), dayjs()]"
            value-format="YYYY-MM-DD" start-placeholder="开始日期" end-placeholder="结束日期" @change="dateRangeChange"
          />
        </div>
        <div flex items-center>
          <span color="#666" mr-10px text-14px>随访方案</span>
          <el-select-v2
            v-model="searchParams.planTemplateId" clearable :options="planTemplates" :props="{
              label: 'planTemplateName',
              value: 'planTemplateId',
            }" filterable placeholder="请选择" style="width: 200px"
          >
            <template #default="{ item }">
              <el-tooltip placement="right" :content="item.planTemplateName">
                <div overflow-hidden style="text-overflow: ellipsis">
                  {{ item.planTemplateName }}
                </div>
              </el-tooltip>
            </template>
          </el-select-v2>
        </div>
        <div flex items-center>
          <span color="#666" mr-10px text-14px>随访类型</span>
          <el-select
            v-model="searchParams.followTypeList" clearable collapse-tags collapse-tags-tooltip
            :max-collapse-tags="1" multiple filterable placeholder="请选择" style="width: 200px"
          >
            <el-option v-for="(item, index) in followTypes" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div flex items-center>
          <span color="#666" mr-10px text-14px>任务状态</span>
          <el-select
            v-model="searchParams.recordStatus" clearable
            filterable placeholder="请选择" style="width: 200px"
          >
            <el-option v-for="(item, index) in statusTypes" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div flex items-center>
          <span color="#666" mr-10px text-14px>标签类型</span>
          <el-select-v2
            v-model="searchParams.markIds" clearable :options="patientMarks"
            :props="{ label: 'markName', value: 'markId' }" filterable placeholder="请选择" style="width: 200px"
          />
        </div>
        <div flex items-center>
          <span color="#666" mr-10px text-14px>随访任务</span>
          <el-select v-model="searchParams.queryType" placeholder="请选择" style="width: 200px">
            <el-option v-for="(item, index) in sfstatus" :key="index" :label="item.label" :value="item.key" />
          </el-select>
        </div>
      </div>
      <div mt-20px flex items-center>
        <n-button type="primary" class="mr-10px" @click="handleSearchClick">
          查询
        </n-button>
        <n-button class="mr-10px" @click="handleResetClick">
          重置
        </n-button>
        <n-button type="primary" class="mr-10px" @click="handleEidtRecordClick">
          {{ editReocrds ? "确认为选择患者新增随访记录" : "批量新增随访记录" }}
        </n-button>
      </div>
      <div mt-20px flex flex-wrap style="gap: 20px 40px">
        <div v-for="(item, index) in showStagings" :key="index" flex items-center>
          <div color="#666">
            {{ `${item.label}数:` }}
          </div>
          <div ml-8px>
            {{ ansyisData?.[`${item.key}Num`] || "-" }}
          </div>
          <!-- <div v-if="ansyisData" ml-10px>
            <span>(</span>
            <span style="color: red">{{
              ansyisData?.[`${item.key}UnFinishNum`] || 0
            }}</span>
            <span mx-5px>+</span>
            <span style="color: green">{{
              ansyisData?.[`${item.key}FinishNum`] || 0
            }}</span>
            <span>)</span>
          </div> -->
        </div>
      </div>
      <div mt-20px flex flex-wrap style="gap: 20px 40px">
        <div v-for="(item, index) in showOtherCounts" :key="index" flex items-center>
          <div color="#666">
            {{ `${item.label}:` }}
          </div>
          <div ml-8px>
            {{ ansyisData?.[`${item.key}`] || "-" }}
          </div>
          <!-- <div v-if="ansyisData" ml-10px>
            <span>(</span>
            <span style="color: red">{{
              ansyisData?.[`${item.key}UnFinishNum`] || 0
            }}</span>
            <span mx-5px>+</span>
            <span style="color: green">{{
              ansyisData?.[`${item.key}FinishNum`] || 0
            }}</span>
            <span>)</span>
          </div> -->
        </div>
      </div>
      <div v-if="recordsList?.length > 0" v-loading="pageLoadOptions.loading" mt-20px flex flex-wrap style="gap: 20px 14px">
        <div v-for="(item, index) in recordsList" :key="index" class="patient-card" @click="jumpToDetail(item)">
          <div flex items-center pl-10px>
            <img mt-10px h-40px w-40px :src="item?.sex === '女' ? femaleImg : maleImg" alt="">
            <div ml-20px mt-10px flex-1>
              <div>{{ item?.patientName || "-" }}</div>
              <div mt-8px>
                {{ `${item?.sex} | ${item?.age}岁` }}
              </div>
            </div>
            <div v-if="editReocrds" mt--10px h-40px w-100px flex justify-end @click="selectRecord(item)">
              <SvgIcon
                v-if="editPatientList.indexOf(item.patientId) !== -1" class="cursor-pointer"
                local-icon="slmc-icon-duoxuanxuanzhong" size="16"
              />
              <SvgIcon v-else class="cursor-pointer" local-icon="slmc-icon-fuxuankuang1" size="16" />
            </div>
          </div>
          <div mt-15px px-10px>
            {{ item?.planTemplateName || "-" }}
          </div>
          <div mt-10px flex flex-wrap px-10px style="gap: 10px 10px">
            <el-tag v-for="(tag, tindex) in item?.markList" :key="tindex" round>
              {{ tag }}
            </el-tag>
          </div>
          <div mt-8px px-10px style="border-top: 1px solid #ccc">
            <div v-for="(follow, findex) in item?.stagingRecords?.slice(0, 1)" :key="findex" mt-10px>
              <div>{{ follow.followDt || '-' }}</div>
              <div
                v-for="(record, rindex) in follow?.recordDtlList?.slice(0, 2)" :key="rindex" ml-10px mt-8px flex
                items-center :class="recordStatusColor[record.recordStatus]"
              >
                <div
                  overflow-hidden style="
                                        white-space: nowrap;
                                        text-overflow: ellipsis;
                                    "
                >
                  {{ record?.recordName || '-' }}
                </div>
                <div ml-5px style="min-width: fit-content">
                  {{ `--- ${typeTitle[record.recordType]}` }}
                </div>
              </div>
            </div>
            <el-tooltip v-if="item?.stagingRecords" effect="dark" placement="right">
              <div mt-10px text-center color="#409eff">
                查看更多记录
              </div>
              <template #content>
                <div>
                  <div v-for="(follow, findex) in item?.stagingRecords" :key="findex">
                    <div>{{ follow.followDt || '-' }}</div>
                    <div
                      v-for="(record, rindex) in follow?.recordDtlList" :key="rindex" mb-5px ml-10px flex
                      items-center :class="recordStatusColor[record.recordStatus]"
                    >
                      <div style="max-width: 180px;">
                        {{ record?.recordName || '-' }}
                      </div>
                      <div ml-5px style="min-width: fit-content">
                        {{ `--- ${typeTitle[record.recordType]}` }}
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </el-tooltip>
          </div>
          <div mt-12px flex items-center px-10px>
            <div>上次就医:</div>
            <div ml-8px>
              <span>{{
                item?.visitDate ? dayjs(item?.visitDate).format("YYYY-MM-DD")
                : "-"
              }}</span>
              <span v-if="item?.visitDate" ml-10px>(</span>
              <span v-if="item?.visitDate" color="red">{{
                `距今 ${item.visitDay} 天`
              }}</span>
              <span v-if="item?.visitDate">)</span>
            </div>
          </div>
          <div mt-12px flex items-center px-10px pb-10px>
            <div>{{ `${item?.visitDepartment || '-'} ---` }}</div>
            <div ml-4px>
              {{ item?.visitDoctor || "-" }}
            </div>
          </div>
        </div>
        <div v-if="pageLoadOptions.loading" class="loading">
          正在加载中...
        </div>
      </div>
      <DataEmpty v-else show-text="暂无数据" h-400px flex-col items-center justify-center />
    </PageCard>

    <BasicModal
      :visible="showAdd" title="新增随访记录" width="560" :footer-offset="150" @ok="handleBodyConfirm"
      @cancel="showAdd = false"
    >
      <div p-20px>
        <div flex>
          <div mt-8px w-90px>
            随访类型：
          </div>
          <n-select
            v-model:value="addSubject.recordType" :options="followTypeOptions" filterable
            @update:value="updateTypeChange"
          />
        </div>
        <div v-if="addSubject.recordType === 'SCALE' || addSubject.recordType === 'EDU'" mt-10px flex>
          <div mt-8px w-90px>
            {{ addSubject.recordType === 'SCALE' ? '随访问卷：' : '健康宣教：' }}
          </div>
          <n-select
            v-if="addSubject.recordType === 'EDU'" v-model:value="addSubject.scaleEduId" :options="edutList"
            value-field="educationId" label-field="title" filterable
          />
          <n-select
            v-if="addSubject.recordType === 'SCALE'" v-model:value="addSubject.scaleEduId" :options="surveyList"
            value-field="scaleId" label-field="scaleName" filterable
          />
        </div>
        <div
          v-if="addSubject.recordType === 'REMIND_REVIEW' || addSubject.recordType === 'REMIND_FOLLOW' || addSubject.recordType === 'NEXT_FOLLOW' || addSubject.recordType === 'INTRO'"
          mt-10px flex
        >
          <div mt-8px w-90px>
            {{ addSubject.recordType === 'REMIND_REVIEW' ? '提醒内容：' : '随访内容：' }}
          </div>
          <el-input v-model="addSubject.recordContent" flex-1 :rows="3" type="textarea" placeholder="请输入" />
        </div>
        <div v-if="addSubject.recordType !== 'NEXT_FOLLOW'" mt-12px>
          <n-radio :checked="addSubject.syncPatient === 1" :value="1" name="是否发送到患者">
            是否发送到患者
          </n-radio>

          <div mt-12px flex>
            <div mt-3px w-90px>
              发送方式：
            </div>
            <n-radio-group v-model:value="addSubject.syncType" name="radiogroup">
              <n-space item-style="display: flex;" :size="[40, 0]">
                <n-radio v-for="item in sendTypeOption" :key="item.value" :value="item.value">
                  {{ item.label }}
                </n-radio>
              </n-space>
            </n-radio-group>
          </div>
        </div>
        <div mt-10px flex>
          <div mt-8px w-90px>
            随访时间：
          </div>
          <n-date-picker
            v-model:formatted-value="addSubject.followDate" value-format="yyyy-MM-dd" type="date" clearable
            w-full
          />
        </div>
        <div mt-10px flex>
          <div mt-8px w-90px>
            备注说明：
          </div>
          <el-input v-model="addSubject.memo" flex-1 :rows="3" type="textarea" placeholder="请输入" />
        </div>
      </div>
    </BasicModal>
  </div>
</template>

<style lang="scss" scoped>
.patient-card {
  width: 250px;
  background: #ffffff;
  border: 1px solid #d1d1d1;
  border-radius: 3px;
}

.loading {
  text-align: center;
  padding: 20px;
}
</style>
