<script setup lang="ts">
import dayjs from 'dayjs'
import { Breadcrumb } from '@/layouts/common'
import { getFollowPatientData, getFollowWorkbenchData, getPlanListApi2 } from '@/api/intelligentFollow/manage'
import { useAuthStore } from '~/src/store'
import patientIcon from '@/assets/images/antibacterial/patients.png'
import drugIcon from '@/assets/images/antibacterial/drug.png'
import kangJunIcon from '@/assets/images/antibacterial/kangjun.png'
import taceIcon from '@/assets/images/antibacterial/tace.png'
import type { ECOption } from '@/hooks'
import { useEcharts } from '@/hooks'
import { useRouterPush } from '~/src/hooks/userRouterPush'

const { routerPush } = useRouterPush()
const timesRange = ref([dayjs().subtract(1, 'month').format('YYYY-MM-DD'), dayjs().format('YYYY-MM-DD')])
const searchParams = reactive({
  planTemplateId: null,
  followDateStart: '',
  followDateEnd: '',
})
const planTemplates: any = ref([])
const { userInfo } = useAuthStore()
const loadingStatus = ref(false)
const overData = reactive({
  patient: {
    title: '患者人数',
    count: '',
    icon: patientIcon,
    background: '#5B96FD1A',
  },
  waitCheck: {
    title: '待复查',
    count: '',
    icon: drugIcon,
    background: '#F5F4FEFF',
  },
  waitFollow: {
    title: '待随访',
    count: '',
    icon: taceIcon,
    background: '#F5F9F2FF',
  },
  checked: {
    title: '已复查',
    count: '',
    icon: kangJunIcon,
    background: '#EBF9F6FF',
  },
  overData: {
    title: '已逾期',
    count: '',
    icon: drugIcon,
    background: '#F5F4FEFF',
  },
})
const waitFollowList = ref()
// 分页器
const waitPaginationReactive = reactive({
  page: 1,
  size: 10,
  total: '',
  pageSizes: [10, 20, 50, 100],
})
const overPaginationReactive = reactive({
  page: 1,
  size: 10,
  total: '',
  pageSizes: [10, 20, 50, 100],
})
const overFollowList = ref()

/// 趋势图
const xAixTrueList = ref()
const yAixTrueList = ref([{
  name: '待复查人次',
  type: 'line',
  data: [],
}, {
  name: '待随访人次',
  type: 'line',
  data: [],
}, {
  name: '已复查人次',
  type: 'line',
  data: [],
}, {
  name: '已逾期人次',
  type: 'line',
  data: [],
}])
const yAixTaskTrueList = ref([{
  name: '未完成任务数',
  type: 'line',
  data: [],
}, {
  name: '已完成任务数',
  type: 'line',
  data: [],
}, {
  name: '已逾期任务数',
  type: 'line',
  data: [],
}])
const lineOptions = ref<any>({
  tooltip: {
    trigger: 'axis',
  },
  legend: {
    data: ['待复查人次', '待随访人次', '已复查人次', '已逾期人次'],
  },
  grid: {
    left: '1%',
    right: '3%',
    bottom: '5%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    axisLine: {
      lineStyle: {
        color: '#ccc',
      },
    },
    axisLabel: {
      color: '#999',
      showMinLabel: true, // 显示最小值标签
      showMaxLabel: true, // 显示最大值标签
    },
    data: xAixTrueList.value,
  },
  yAxis: {
    type: 'value',
  },
  series: [],
}) as Ref<ECOption>
const taskOptions = ref<any>({
  tooltip: {
    trigger: 'axis',
  },
  legend: {
    data: ['未完成任务数', '已完成任务数', '已逾期任务数'],
  },
  grid: {
    left: '1%',
    right: '3%',
    bottom: '5%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    axisLine: {
      lineStyle: {
        color: '#ccc',
      },
    },
    axisLabel: {
      color: '#999',
      showMinLabel: true, // 显示最小值标签
      showMaxLabel: true, // 显示最大值标签
    },
    data: xAixTrueList.value,
  },
  yAxis: {
    type: 'value',
    axisLine: {
      lineStyle: {
        color: '#ccc',
      },
    },
    axisLabel: {
      color: '#999',
    },
  },
  series: [],
}) as Ref<ECOption>
const { domRef: lineRef } = useEcharts(lineOptions)
const { domRef: taskRef } = useEcharts(taskOptions)

function getPlanTemplates() {
  getPlanListApi2({
    search: '',
    operateId: userInfo.id,
  }).then((res) => {
    planTemplates.value = res?.data || []
  })
}

function customTimeSel() {
  waitPaginationReactive.page = 1
  overPaginationReactive.page = 1
  getTableData()
  getWorkbenchData()
}

function getTableData() {
  const params = {
    userId: userInfo.id,
    followType: 'WAIT',
    page: waitPaginationReactive.page,
    size: waitPaginationReactive.size,
  }
  if (searchParams.planTemplateId)
    params.planTemplateIds = [searchParams.planTemplateId]
  getFollowPatientData(params).then((res: any) => {
    waitFollowList.value = res.data?.records
    waitPaginationReactive.total = res?.data.total
  })

  const params1 = {
    userId: userInfo.id,
    followType: 'OVERTIME',
    page: overPaginationReactive.page,
    size: overPaginationReactive.size,
  }
  if (searchParams.planTemplateId)
    params1.planTemplateIds = [searchParams.planTemplateId]
  getFollowPatientData(params1).then((res: any) => {
    overFollowList.value = res.data?.records
    overPaginationReactive.total = res?.data.total
  })
}

function getTheDataFromNet() {
  getTableData()
}

function jumpToFollowDetail(item) {
  /// 跳转到病人详情
  console.log(item)
  routerPush({
    name: 'intelligentFollow_manage_detail',
    query: {
      patientId: item.patientId,
      planId: item?.planTemplateId,
    },
  })
}

function getWorkbenchData() {
  const params = {
    userId: userInfo.id,
    startTime: timesRange.value[0],
    endTime: timesRange.value[1],
  }
  if (searchParams.planTemplateId)
    params.planTemplateIds = [searchParams.planTemplateId]

  getFollowWorkbenchData(params).then((res: any) => {
    overData.patient.count = res.data.patientNum
    overData.waitCheck.count = res.data.reviewCount
    overData.waitFollow.count = res.data.followCount
    overData.checked.count = res.data.recheckedCount
    overData.overData.count = res.data.overNum

    const data = res.data.patientCharts || []
    const list = []
    const waitCheck = []
    const waitFollow = []
    const checked = []
    const overed = []
    const unfinish = []
    const finished = []
    const overTask = []
    data.forEach((element: any) => {
      list.push(element.date || '')
      waitCheck.push(element.reviewCount.toString() || '')
      waitFollow.push(element.followCount.toString() || '')
      checked.push(element.recheckedCount.toString() || '')
      overed.push(element.overTaskNum.toString() || '')
      unfinish.push(element.noFinishTaskNum.toString() || '')
      finished.push(element.finishTaskNum.toString() || '')
      overTask.push(element.overTaskNum.toString() || '')
    })
    yAixTrueList.value.forEach((item: any) => {
      // ['待复查人次', '待随访人次', '已复查人次', '已逾期人次', '未完成任务数', '已完成任务数', '已逾期任务数'],
      switch (item.name) {
        case '待复查人次':
          item.data = waitCheck
          break
        case '待随访人次':
          item.data = waitFollow
          break
        case '已复查人次':
          item.data = checked
          break
        case '已逾期人次':
          item.data = overed
          break
        default:
          break
      }
    })
    yAixTaskTrueList.value.forEach((item: any) => {
      // ['待复查人次', '待随访人次', '已复查人次', '已逾期人次', '未完成任务数', '已完成任务数', '已逾期任务数'],
      switch (item.name) {
        case '未完成任务数':
          item.data = unfinish
          break
        case '已完成任务数':
          item.data = finished
          break
        case '已逾期任务数':
          item.data = overTask
          break
        default:
          break
      }
    })
    xAixTrueList.value = list
    lineOptions.value.series = yAixTrueList.value
    lineOptions.value.xAxis.data = xAixTrueList.value

    taskOptions.value.series = yAixTaskTrueList.value
    taskOptions.value.xAxis.data = xAixTrueList.value

    console.log('shuju======', lineOptions.value)
    // lineOptions.value && myChart.setOption(lineOptions.value)
  })
}

onMounted(() => {
  getPlanTemplates()
  getTableData()
  getWorkbenchData()
})
</script>

<template>
  <div>
    <Breadcrumb
      :bread-list="[
        {
          title: '智能随访',
          link: null,
          key: 'intelligentFollow',
        },
        {
          title: '我的工作台',
          link: null,
          key: 'intelligentFollow_workbench',
        },
      ]"
    />
    <PageCard breadcrumb>
      <div flex items-center>
        <PageTitle class="mb-14px" flex-1>
          随访工作看板
        </PageTitle>

        <span color="#666" mr-10px text-14px>随访方案</span>
        <el-select-v2
          v-model="searchParams.planTemplateId"
          clearable :options="planTemplates" :props="{
            label: 'planTemplateName',
            value: 'planTemplateId',
          }" filterable placeholder="请选择" style="width: 200px"
        >
          <template #default="{ item }">
            <el-tooltip placement="right" :content="item.planTemplateName">
              <div overflow-hidden style="text-overflow: ellipsis">
                {{ item.planTemplateName }}
              </div>
            </el-tooltip>
          </template>
        </el-select-v2>
        <div ml-10px>
          <el-date-picker
            v-model="timesRange" style="width: 280px;" unlink-panels
            type="daterange" value-format="YYYY-MM-DD" range-separator="～" start-placeholder="开始日期"
            end-placeholder="结束日期"
          />
        </div>
        <el-button ml-10px type="primary" w-100px @click="customTimeSel()">
          查询
        </el-button>
      </div>

      <div v-loading="loadingStatus" mt-14px style="gap: 0 14px;display: flex;align-items: center;">
        <div
          v-for="(item, index) in overData" :key="index" flex flex-1 items-center
          class="count-content" :style="{ background: item.background }"
        >
          <img h-46px w-46px :src="item.icon">
          <div ml-18px flex-col>
            <div class="title">
              {{ item.title }}
            </div>
            <div text-22px class="value">
              {{ `${item.count}` }}
              <span text-12px>{{ item.title === '患者人数' ? '' : '人/次' }}</span>
            </div>
          </div>
        </div>
      </div>

      <div my-20px>
        <SectionTitle mb-15px>
          <div w-full flex flex-justify-between>
            <span text="#333">随访患者</span>
          </div>
        </SectionTitle>
        <div ref="lineRef" class="h-300px" />

        <SectionTitle mb-15px mt-20px>
          <div w-full flex flex-justify-between>
            <span text="#333">随访任务</span>
          </div>
        </SectionTitle>
        <div ref="taskRef" class="h-300px" />
      </div>

      <div mt-20px w-full flex>
        <div mr-10px flex-1 style="width: 50%;">
          <SectionTitle mb-15px>
            <div w-full flex flex-justify-between>
              <span text="#333">待随访患者</span>
            </div>
          </SectionTitle>

          <el-table
            :data="waitFollowList" border
            style="border: 0.5px solid #d1d1d1;border-top: none;width: 99%;"
            @row-dblclick="jumpToFollowDetail"
          >
            <el-table-column prop="patientName" label="基本信息" />
            <el-table-column prop="patientRecordNo" label="病案号" />
            <el-table-column prop="phone" label="联系电话" />
            <el-table-column prop="followDate" label="随访计划">
              <template #default="{ row }">
                <el-popover
                  v-if="row.taskListInfoList"
                  placement="left"
                  :width="230"
                  trigger="hover"
                >
                  <template #reference>
                    <div flex items-center>
                      <div>
                        {{ row.followDate ? dayjs(row.followDate).format('YYYY-MM-DD') : '' }}
                      </div>
                    </div>
                  </template>
                  <div>
                    <div v-for="(item, index) in row.taskListInfoList" :key="index" flex items-center>
                      <div>{{ item.taskName }}</div>
                      <div>{{ item.followDate ? dayjs(item.followDate).format('YYYY-MM-DD') : '' }}</div>
                    </div>
                  </div>
                </el-popover>
                <div v-else>
                  -
                </div>
              </template>
            </el-table-column>

            <!-- <el-table-column
              prop="treatDate" label="随访计划" :formatter="({ treatDate }) => {
                return treatDate ? dayjs(treatDate).format('YYYY-MM-DD') : '-'
              }"
            /> -->
          </el-table>
          <div flex justify-end>
            <n-pagination
              v-model:page="waitPaginationReactive.page" v-model:page-size="waitPaginationReactive.size"
              :item-count="waitPaginationReactive.total" :page-sizes="waitPaginationReactive.pageSizes" show-size-picker show-quick-jumper mt-14px
              @update:page-size="getTheDataFromNet"
              @update:page="getTheDataFromNet"
            />
          </div>
        </div>
        <div flex-1 style="width: 50%;">
          <SectionTitle mb-15px>
            <div w-full flex flex-justify-between>
              <span text="#333">已逾期患者</span>
            </div>
          </SectionTitle>

          <el-table
            :data="overFollowList" border
            style="border: 0.5px solid #d1d1d1;border-top: none;width: 100%;"
            @row-dblclick="jumpToFollowDetail"
          >
            <el-table-column prop="patientName" label="基本信息" />
            <el-table-column prop="patientRecordNo" label="病案号" />
            <el-table-column prop="phone" label="联系电话" />
            <el-table-column prop="followDate" label="随访计划">
              <template #default="{ row }">
                <el-popover
                  v-if="row.taskListInfoList"
                  placement="left"
                  :width="230"
                  trigger="hover"
                >
                  <template #reference>
                    <div flex items-center>
                      <div>
                        {{ row.followDate ? dayjs(row.followDate).format('YYYY-MM-DD') : '' }}
                      </div>
                    </div>
                  </template>
                  <div>
                    <div v-for="(item, index) in row.taskListInfoList" :key="index" flex items-center>
                      <div>{{ item.taskName }}</div>
                      <div>{{ item.followDate ? dayjs(item.followDate).format('YYYY-MM-DD') : '' }}</div>
                    </div>
                  </div>
                </el-popover>
                <div v-else>
                  -
                </div>
              </template>
            </el-table-column>
            <!-- <el-table-column
              prop="treatDate" label="随访计划" :formatter="({ treatDate }) => {
                return treatDate ? dayjs(treatDate).format('YYYY-MM-DD') : '-'
              }"
            /> -->
          </el-table>
          <div flex justify-end>
            <n-pagination
              v-model:page="overPaginationReactive.page" v-model:page-size="overPaginationReactive.size"
              :item-count="overPaginationReactive.total" :page-sizes="overPaginationReactive.pageSizes" show-size-picker show-quick-jumper mt-14px
              @update:page-size="getTheDataFromNet"
              @update:page="getTheDataFromNet"
            />
          </div>
        </div>
      </div>
    </PageCard>
  </div>
</template>

<style lang="scss" scoped>
.count-content {
    border-radius: 3px;
    padding: 20px;
    height: 80px;
  }

  .title {
    font-size: 14px;
    color: #666666;
    line-height: 14px;
  }
</style>
