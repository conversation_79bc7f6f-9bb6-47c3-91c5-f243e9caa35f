import PlanSetting from './components/PlanSetting.vue'
import Patient from './components/Patient.vue'
import BaseSetting from './components/BaseSetting.vue'

type Tabs = 'Patient' | 'PlanSetting' | 'BaseSetting'

export function useTabs() {
  const route = useRoute()
  const tabFromRoute = route.query.tab as Tabs
  const currentTab = ref<Tabs>(tabFromRoute || 'PlanSetting')
  const componentMap: Record<Tabs, any> = {
    Patient,
    PlanSetting,
    BaseSetting,
  }

  const PatientTabType = ref<'LIST' | 'DETAIL'>('LIST')
  provide('PatientTabTypeKey', PatientTabType)

  return {
    currentTab,
    componentMap,
  }
}
