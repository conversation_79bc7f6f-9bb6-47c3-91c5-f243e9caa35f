import { useRouter } from 'vue-router'

export function usePatientClick() {
  const router = useRouter()
  const route = useRoute()
  function toPatientDetail(id: string, name: string | undefined) {
    router.push({
      name: 'intelligentFollow_manage_detail',
      query: {
        patientId: id,
        planId: route.query.id,
        pName: name,
      },
    })
  }

  return {
    toPatientDetail,
  }
}
