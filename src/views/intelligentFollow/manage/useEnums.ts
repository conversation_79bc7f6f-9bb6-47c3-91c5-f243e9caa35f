import { provide } from 'vue'
import { getEduListAPI, getEduTreeAPI, getSurveyListAPI, getWarningListAPI } from '@/api/toolsFollow'
import { getMedicalProject, getMedicalProjectItemAPI } from '@/api/intelligentFollow/manage'

interface Label {
  labelName: string
  labelId: string
  isActive: boolean
}

export function useEnums() {
  const eduList = ref<any[]>([])
  const eduLabelTree = ref<Label[]>([])
  const surveyList = ref<any[]>([])

  const inspectList = ref<any[]>([])
  const imageCheckList = ref<any[]>([])
  const quotas = ref<any[]>([])

  const warningEvents = ref<any[]>([])

  async function getEduList() {
    try {
      const { data } = await getEduListAPI()
      eduList.value = data
    }
    catch (error) {

    }
  }

  async function getEduTree() {
    try {
      const { data } = await getEduTreeAPI()
      eduLabelTree.value = data
    }
    catch (error) {
      console.log(error)
    }
  }

  async function getInsepctList() {
    try {
      const { data } = await getMedicalProject({
        type: 'INSPECT',
        projectName: '',
      })

      inspectList.value = data || []
    }
    catch (error) {

    }
  }

  async function getImageCheckList() {
    try {
      const { data } = await getMedicalProject({
        type: 'IMAGE',
        projectName: '',
      })

      imageCheckList.value = data || []
    }
    catch (error) {

    }
  }

  async function getMedicalProjectItem() {
    try {
      const { data } = await getMedicalProjectItemAPI('')

      quotas.value = data || []
    }
    catch (error) {

    }
  }

  async function getSurvey() {
    try {
      const { data } = await getSurveyListAPI('')
      surveyList.value = data?.slice(0, 6)?.map((item: any) => {
        return {
          ...item,
          isActive: false,
        }
      })
    }
    catch (error) {

    }
  }

  async function getWarningList() {
    try {
      const { data } = await getWarningListAPI('')
      warningEvents.value = data || []
    }
    catch (error) {

    }
  }

  onMounted(() => {
    getEduList()
    getEduTree()
    getSurvey()
    getWarningList()
    getInsepctList()
    getImageCheckList()
    getMedicalProjectItem()
  })

  provide('eduList', eduList)
  provide('eduLabelTree', eduLabelTree)
  provide('surveyList', surveyList)
  provide('inspectList', inspectList)
  provide('quotas', quotas)
  provide('warningEvents', warningEvents)
  provide('imageCheckList', imageCheckList)
  provide('quotas', quotas)
}
