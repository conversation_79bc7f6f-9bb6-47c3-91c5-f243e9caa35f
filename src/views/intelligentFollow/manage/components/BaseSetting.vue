<script setup lang="ts">
import { FollowPlanKey } from '../../type'
import StatusBadge from './StatusBadge.vue'
import { userAllUser } from '@/hooks/useAllUser'

const followPlan = inject(FollowPlanKey)!

const { userList } = userAllUser()

function handleRelationChange(_val: any, options: {
  value: string
  label: string
}[]) {
  followPlan.value.relations = options?.map((item) => {
    return {
      userId: item.value,
      userName: item.label,
    }
  })
}

function handleMethodUpdate(val: unknown) {
  followPlan.value.followMethod = (val as string[]).join(',')
}

const route = useRoute()
const isDisabled = inject<ComputedRef<boolean>>('isDisabled')
const mode = route.query.mode as string | undefined
</script>

<template>
  <div flex="~ col" gap-20px px-15px>
    <div flex items-center>
      <div w-110px flex items-center justify-end>
        <SvgIcon local-icon="slmc-icon-app_required1" flex-shrink-0 size="14" class="text-#F36969" />
        <div mr-10px color="#666" flex-shrink-0>
          随访计划名称
        </div>
      </div>
      <n-input v-model:value="followPlan.newPlanTemplateName" :disabled="isDisabled" :maxlength="20" show-count class="!w-370px" />
    </div>

    <div h-32px flex items-center>
      <div w-110px flex flex-shrink-0 items-center justify-end>
        <SvgIcon local-icon="slmc-icon-app_required1" flex-shrink-0 size="14" class="text-#F36969" />
        <div mr-10px color="#666" flex-shrink-0>
          随访周期
        </div>
      </div>
      <n-radio-group v-model:value="followPlan.planCycle" :disabled="isDisabled" flex-shrink-0>
        <n-radio mr-40px value="LONG_TREM">
          长期
        </n-radio>
        <n-radio value="CUSTOM">
          自定义
        </n-radio>
      </n-radio-group>
      <n-input-group v-show="followPlan.planCycle === 'CUSTOM'" :disabled="isDisabled" ml-10px>
        <n-input-number
          v-model:value="followPlan.cycleValue"
          class="!w-90px" :min="1"
          :show-button="false"
          @blur="() => {
            followPlan.cycleValue = Math.max(1, followPlan.cycleValue || 0)
          }"
        />
        <n-select
          v-model:value="followPlan.cycleType"
          w-56px
          :options="[{ value: 'YEAR', label: '年' }, { value: 'MONTH', label: '月' }, { value: 'DAY', label: '日' }]"
        />
      </n-input-group>
    </div>

    <div flex items-center>
      <div w-110px flex flex-shrink-0 items-center justify-end>
        <SvgIcon local-icon="slmc-icon-app_required1" flex-shrink-0 size="14" class="text-#F36969" />
        <div mr-10px color="#666" flex-shrink-0>
          触达患者方式
        </div>
      </div>

      <n-checkbox-group v-model:value="followPlan.followMethods" :disabled="isDisabled" @update:value="handleMethodUpdate">
        <div flex gap-40px>
          <n-checkbox disabled value="XCX">
            <span ml-10px>肝愈小程序</span>
          </n-checkbox>
          <n-checkbox value="DUX">
            <span ml-10px>短信</span>
          </n-checkbox>
          <n-checkbox value="AIH">
            <span ml-10px>AI电话</span>
          </n-checkbox>
        </div>
      </n-checkbox-group>
    </div>

    <div h-26px flex items-center>
      <div w-110px flex items-center justify-end>
        <div mr-10px color="#666" flex-shrink-0>
          患者未完成提醒
        </div>
      </div>
      <div flex items-center gap-10px>
        <n-switch
          v-model:value="followPlan.remindSwitch" :disabled="isDisabled"
          @update:value="(val) => {
            followPlan.noFinishRemind = val ? 3 : 0
          }"
        />
        <div v-if="followPlan.remindSwitch" flex items-center>
          <SvgIcon local-icon="slmc-icon-app_required1" flex-shrink-0 size="14" class="text-#F36969" />
          <div mr-10px text="#666">
            连续
          </div>
          <n-input-number
            v-model:value="followPlan.noFinishRemind"
            :disabled="isDisabled"
            :min="0" w-100px :show-button="false"
            @blur="() => {
              followPlan.noFinishRemind = Math.max(1, followPlan.noFinishRemind || 1)
            }"
          >
            <template #suffix>
              <span text="#999">次</span>
            </template>
          </n-input-number>
          <div ml-10px text="#666">
            未提交数据，将向管理医生/护士发送提醒
          </div>
        </div>
      </div>
    </div>

    <div h-26px flex items-center>
      <div w-110px flex items-center justify-end>
        <div mr-10px color="#666" flex-shrink-0>
          患者未完成锁定
        </div>
      </div>
      <div flex items-center gap-10px>
        <n-switch
          v-model:value="followPlan.lockSwitch" :disabled="isDisabled"
          @update:value="(val) => {
            followPlan.noFinishLock = val ? 3 : 0
          }"
        />
        <div v-if="followPlan.lockSwitch" flex items-center>
          <SvgIcon local-icon="slmc-icon-app_required1" flex-shrink-0 size="14" class="text-#F36969" />
          <div mr-10px text="#666">
            任务到期
          </div>
          <n-input-number
            v-model:value="followPlan.noFinishLock"
            :min="0" :disabled="isDisabled" w-100px :show-button="false"
            @blur="() => {
              followPlan.noFinishLock = Math.max(1, followPlan.noFinishLock || 3)
            }"
          >
            <template #suffix>
              <span text="#999">天</span>
            </template>
          </n-input-number>
          <div ml-10px text="#666">
            后患者无法填写，管理端不受限制
          </div>
        </div>
      </div>
    </div>

    <div h-26px flex items-center>
      <div w-110px flex items-center justify-end>
        <div mr-10px color="#666" flex-shrink-0>
          任务提醒设置
        </div>
      </div>
      <div flex items-center gap-10px>
        <n-switch
          v-model:value="followPlan.beforeOpenSwitch"
          :disabled="isDisabled"
          @update:value="(val) => {
            followPlan.beforeOpenDays = val ? 3 : 0
          }"
        />
        <div v-if="followPlan.beforeOpenSwitch" flex items-center>
          <SvgIcon local-icon="slmc-icon-app_required1" flex-shrink-0 size="14" class="text-#F36969" />
          <div mr-10px text="#666">
            任务开始前
          </div>
          <n-input-number
            v-model:value="followPlan.beforeOpenDays" :min="0"
            :disabled="isDisabled" w-100px :show-button="false"
            @blur="() => {
              followPlan.beforeOpenDays = Math.max(1, followPlan.beforeOpenDays || 1)
            }"
          >
            <template #suffix>
              <span text="#999">天</span>
            </template>
          </n-input-number>
          <div ml-10px text="#666">
            提醒并允许患者填写
          </div>
        </div>
      </div>
    </div>

    <template v-if="followPlan.planTemplateType !== 'SBM' && mode !== 'view'">
      <div flex items-center>
        <div w-110px flex items-center justify-end>
          <div mr-10px color="#666" flex-shrink-0>
            操作
          </div>
        </div>
        <n-radio-group v-model:value="followPlan.newTempState" :disabled="isDisabled" flex-shrink-0>
          <n-radio mr-40px :value="3">
            启用计划
          </n-radio>
          <n-radio :value="1">
            暂停计划
          </n-radio>
        </n-radio-group>
      </div>

      <div flex items-center>
        <div w-110px flex items-center justify-end>
          <div mr-10px color="#666" flex-shrink-0>
            计划状态
          </div>
        </div>
        <StatusBadge :state="followPlan.newTempState" />
      </div>

      <div flex items-center>
        <div w-110px flex items-center justify-end>
          <div mr-10px color="#666" flex-shrink-0>
            计划创建人
          </div>
        </div>
        {{ followPlan.createName }}
      </div>

      <div v-if="followPlan.planTemplateType !== 'SBF'" flex items-center>
        <div w-110px flex items-center justify-end>
          <div mr-10px color="#666" flex-shrink-0>
            计划协作者
          </div>
        </div>
        <n-select
          v-model:value="followPlan.relationIds"
          :disabled="isDisabled"
          :options="userList"
          class="!w-370px"
          multiple
          @update:value="handleRelationChange"
        />
      </div>
    </template>
  </div>
</template>
