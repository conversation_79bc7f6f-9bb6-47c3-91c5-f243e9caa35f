<script setup lang="ts">
import { NEllipsis, NPopover, NTooltip } from 'wowjoy-vui'
import { useDebounceFn } from '@vueuse/core'
import { useRouteQuery } from '@vueuse/router'
import dayjs from 'dayjs'
import { usePatientClick } from '../usePatientClick.ts'
import { FollowPlanKey } from '../../type'
import { SvgIcon } from '~/src/components/Icon'
import BasicTable, { RowAction } from '~/src/components/Table'
import { formartPhoneNumberMiss } from '~/src/utils/common/format'
import { aiccAPI, batchImportPatients, getMangeDoctSelects, getPatientSelects, queryFollowPlanPatients, queryImportDoctorsNames, queryImportPatients, queryImportPatientsNames, removeFollowPlanPatient } from '@/api/intelligentFollow/manage'
import { useAuthStore } from '~/src/store/modules/auth'
import HidePhone from '@/components/business/HidePhone.vue'
import { sexIcon } from '@/utils/common'
import { useLoading } from '~/src/hooks'
import iconReport from '@/assets/images/use_medical_report.png'
import { useRouterPush } from '@/hooks'

const { routerPush } = useRouterPush()
const { startLoading, endLoading } = useLoading()

const { toPatientDetail } = usePatientClick()

const templateId = unref(useRouteQuery('id'))
const countLabel = ref()
const importTable = ref()
const auth = useAuthStore()
const tableRef = ref<InstanceType<typeof BasicTable> | null>(null)
const columns = createColumns()
const actionColumns = createActionColumns()
const followPlan = inject(FollowPlanKey)!

const aiccModal = ref(false)
const aiPhone = ref('')
const selectedPatientId = ''

/// 导入患者loading
const loadingImport = ref(false)

// 分页器
const paginationPatient = reactive({
  page: 1,
  pageSize: 10,
  pageSizes: [10, 20, 50, 100],
})
const paginationImport = reactive({
  page: 1,
  pageSize: 10,
  pageSizes: [10, 20, 50, 100],
})

const searchData = reactive({
  alarmGive: 0,
  joinType: '',
  manageDoctorList: [],
  sexName: '',
  planState: [],
  patientNmList: [],
})

const patientStates: any = {
  FOLLOW: {
    label: '随访中',
    color: '#45A8E6',
    value: 'FOLLOW',
    actions: (params) => {
      return [{
        label: '移除',
        onClick: () => handleRemovePatient(params),
        type: 'primary',
        text: true,
      }]
    },
  },
  // IN_AUDIT: {
  //   label: '待审核',
  //   color: '#FF9B54',
  //   value: 'IN_AUDIT',
  // },
  // REFUSE: {
  //   label: '已拒绝',
  //   color: '#F36969',
  //   value: 'REFUSE',
  // },
  REMOVE: {
    label: '已移除',
    color: '#A5B8D1',
    value: 'REMOVE',
    actions: (params) => {
      return [{
        label: '再次随访',
        onClick: () => handleFollowPatientAgain(params),
        type: 'primary',
        text: true,
      }]
    },
  },
  // FINISH: {
  //   label: '已完成',
  //   color: '#06AEA6',
  //   value: 'FINISH',
  // },
  OVERTIME: {
    label: '已到期',
    color: '#CCCCCC',
    value: 'OVERTIME',
  },
  STOP: {
    label: '已暂停',
    color: '#F36969',
    value: 'STOP',
  },
}

const showImport = ref(false)

const selectOpions = reactive({
  patient: {
    opions: [],
    loading: false,
  },
  doctor: {
    opions: [],
    loading: false,
  },
})

/**
 * 慢乙肝数据相关
 */
const mygStatusOptions = {
  0: {
    title: '待建档',
    color: '#45A8E6',
    showEdit: true,
  },
  1: {
    title: '完善中',
    color: '#FF9B54',
    showEdit: true,
  },
  2: {
    title: '已建档',
    color: '#4ACFB1',
    showEdit: false,
  },
  3: {
    title: '拒绝建档',
    color: '#FF0000',
    showEdit: true,
  },
  4: {
    title: '申请建档',
    color: '#4ACFB1',
    showEdit: true,
  },
}

/// 查询参数
const patientColumns = createPatientColumns()
/// 姓名多选数据
const getPatientName = useDebounceFn(async (search = '') => {
  const { data } = await getPatientSelects({
    page: 0,
    size: 10000,
    patientNm: search,
    planTemplateId: templateId,
  })

  if (data?.records) {
    const array = data.records.map((item) => {
      return {
        label: item,
        value: item,
      }
    })
    selectOpions.patient.loading = false
    selectOpions.patient.opions = array
  }
}, 200)

/// 获取管理医生多选数据
/// 姓名多选数据
const getDoctorName = useDebounceFn(async (search = '') => {
  const { data } = await getMangeDoctSelects({
    page: 0,
    size: 10000,
    patientNm: search,
    planTemplateId: templateId,
  })

  if (data?.records) {
    const array = data.records.map((item) => {
      return {
        label: item,
        value: item,
      }
    })
    selectOpions.doctor.loading = false
    selectOpions.doctor.opions = array
  }
}, 200)

/// 管理医生多选数据
function createColumns() {
  return [
    {
      width: 20,
      render(_: any) {
        return ''
      },
    },
    {
      title: '序号',
      key: 'index',
      width: 40,
      render(_: any, index: number) {
        return index + 1
      },
    },
    {
      title: '状态',
      key: 'status',
      width: 50,
      render(row: any) {
        return h('div', { class: 'flex items-center' }, [
          h('div', {
            class: 'rd-3px w-6px h-6px',
            style: {
              backgroundColor: patientStates[row.planState]?.color,
            },
          }),
          h('div', { class: 'ml-4px' }, patientStates[row.planState]?.label ?? '-'),
        ])
      },
    },
    {
      title: '病历号',
      key: 'patientRecordNo',
      width: 60,
    },
    {
      title: '患者信息',
      key: 'info',
      width: 85,
      render(row: any) {
        return h('div', {
          class: 'flex items-center cursor-pointer',
          onClick: () => {
          ///  跳转 页面
            let pName: string | undefined
            if (['REMOVE', 'STOP'].includes(row.planState))
              pName = followPlan.value.planTemplateName

            toPatientDetail(row.patientId, pName)
          },
        }, [
          h('div', { class: 'text-#3B8FD9' }, ` ${row.patientNm}`),
          h(SvgIcon, {
            class: 'mx-3px',
            localIcon: sexIcon(row.sexName),
            size: 16,
          }),
          h('div', null, ` ${row.age}`),
        ])
      },
    },
    {
      title: '加入随访时间',
      key: 'joinTime',
      width: 80,
      render(row: any) {
        return dayjs(row.joinTime).format('YYYY-MM-DD')
      },
    },
    {
      title: '档案状态',
      key: 'diagnoseRecordStatus',
      width: 130,
      render(row: any) {
        const opt = mygStatusOptions[row?.diagnoseRecordStatus] ?? {}
        return h('div', { class: 'flex items-center' }, [
          h('div', {
            style: {
              background: opt?.color,
              width: '6px',
              height: '6px',
              marginRight: '5px',
              borderRadius: '3px',
            },
          }, ''),
          h('div', { class: 'text-14px text-#333 mr-9px' }, opt?.title),
          h('img', {
            class: 'w-15px',
            style: {
              visibility: opt.showEdit ? 'visible' : 'hidden',
            },
            src: iconReport,
            onClick: () => {
              // console.log('')
              handleLook(row, true)
            },
          }),
        ])
      },

    },
    {
      title: '患者标签',
      key: 'patientTag',
      width: 110,
      render(row: any) {
        let strs = ''
        row?.patientTag?.forEach((item) => {
          strs += `${item} | `
        })
        return h(NEllipsis, {},
          strs?.length > 1 ? strs.substring(0, strs.length - 2) : '-',
        )
      },
    },
    {
      title: '手机号',
      key: 'phone',
      width: 68,
      render(row: any) {
        return h('div', {
          class: 'flex items-center gap-8px cursor-pointer',
        }, [
          h(HidePhone, {
            phone: row.phone,
          }),
          // row.phone
          //   ? h(NPopover, {
          //     trigger: 'hover',
          //   },
          //   {
          //     trigger: () => (h(SvgIcon,
          //       {
          //         localIcon: 'slmc-icon-AIdianhua1',
          //         onClick: async () => {
          //           try {
          //             selectedPatientId = row.patientId
          //             aiPhone.value = row.phone
          //             aiccModal.value = true
          //           }
          //           catch (error) {

          //           }
          //         },
          //       },
          //     )),
          //     default: () => ('点击给用户拨打AI电话'),
          //   },
          //   )
          //   : '',
        ],
        )
      },
    },
    {
      title: '当前随访任务',
      key: 'taskList',
      width: 110,
      render(row: any) {
        if (row?.taskList && row.taskList?.length > 1) {
          const st = row?.taskList[0]
          return h('div', { class: 'flex items-center' }, [
            h(
              'div',
              {
                class: 'mr-6px',
                style: {
                  'text-overflow': 'ellipsis',
                  'overflow': 'hidden',
                },
              },
              st || '-',
            ),
            h(
              NTooltip,
              {
                trigger: 'hover',
              },
              {
                trigger: () => [
                  h(SvgIcon, {
                    localIcon: 'slmc-icon-gengduo1',
                    size: 16,
                    minWidth: 16,
                  }),
                ],
                default: () => {
                  const temps: any = []
                  row.taskList?.forEach((element) => {
                    temps.push(
                      h(
                        'div',
                        { class: 'h-17px' },
                        element,
                      ),
                    )
                  })
                  return temps
                },
              },
            ),
          ])
        }
        else {
          let sbb = '-'
          if (row.taskList && row.taskList?.length === 1)
            sbb = row.taskList[0]
          return h('div', null, sbb)
        }
      },
    },
    // {
    //   title: '异常重点指标数',
    //   key: 'alarmGive',
    //   width: 75,
    //   render(row: any) {
    //     return h(
    //       'div',
    //       {
    //         class:
    //                         row?.alarmGive > 0 ? 'text-#F36969' : 'text-#666',
    //       },
    //       row?.alarmGive || '-',
    //     )
    //   },
    // },
    // {
    //   title: '随访问卷',
    //   key: 'questionnaire',
    //   width: 70,
    // },
    // {
    //   title: '检查检验',
    //   key: 'inspection',
    //   width: 70,
    // },
    // {
    //   title: '随访完成次数',
    //   key: 'followCount',
    //   width: 100,
    //   render(row: any) {
    //     return h('div', {
    //       class: 'flex items-center',
    //     }, [h(NProgress, {
    //       style: 'width:60px;height:6px',
    //       type: 'line',
    //       color: '#06AEA6',
    //       showIndicator: false,
    //       percentage: row?.followCount * 100 / row?.followTotal,
    //     }), h('span', {
    //       style: {
    //         'color': '#06AEA6',
    //         'margin-left': '10px',
    //       },
    //     }, row.followCount), h('span', null, `/${row.followTotal}`)])
    //   },
    // },
    {
      title: '已逾期次数',
      key: 'overdue',
      width: 60,
    },
    {
      title: '管理医生',
      key: 'manageDoctorName',
      width: 60,
    },
  ]
}

function handleLook(row: any, goEdit?: boolean) {
  const { patientId, patientNm } = row
  const param = {
    patientId,
    patientName: patientNm,
    isEdit: false,
  }
  // if (row.diagnoseType === 1)
  //   param.type = '慢乙肝'

  routerPush({
    name: 'specificDisease_detail',
    query: param,
  })
}

function createActionColumns() {
  return {
    title: '操作',
    key: 'cz',
    width: 90,
    fixed: 'right',
    render(row: any) {
      return h(RowAction, {
        actions: patientStates[row.planState]?.actions
          ? patientStates[row.planState]?.actions(row)
          : [
              {
                label: '-',
                text: true,
                textColor: '#999',
              },
            ],
      })
    },
  }
}

async function loadDataTable(res: { size: number; start: number }) {
  const response = await queryFollowPlanPatients({
    ...{
      size: res.size,
      page: res.start,
    },
    ...searchData,
    planTemplateId: templateId,
  })
  return response
}

/// 导入患者
function createPatientColumns() {
  return [
    {
      type: 'selection',
      key: 'patientId',
      width: 30,
    },
    {
      title: '病历号',
      key: 'patientRecordNo',
      width: 80,
    },
    {
      title: '患者信息',
      key: 'index',
      width: 115,
      render(row: any) {
        return h('div', { class: 'flex items-center' }, [
          h('div', null, `${row.patientNm} `),
          h(SvgIcon, {
            class: 'mx-3px',
            localIcon: sexIcon(row.sexName),
            size: 16,
            minWidth: 16,
          }),
          h('div', null, ` ${row.age ?? '-'}`),
        ])
      },
    },
    {
      title: '手机号',
      key: 'phone',
      width: 80,
      render(row: any) {
        if (!row.phone)
          return h('div', {}, '-')
        return h(
          NPopover,
          {
            trigger: 'hover',
          },
          {
            trigger: () => [
              h(
                'div',
                {
                  style: {
                    cursor: 'pointer',
                  },
                  onClick: () => {
                    row.checkPhone = !row.checkPhone
                  },
                },
                row.checkPhone
                  ? row.phone
                  : formartPhoneNumberMiss(row.phone) ?? '-',
              ),
            ],
            default: () => [
              h(
                'div',
                null,
                row.checkPhone
                  ? '点击隐藏手机号'
                  : '点击显示完整手机号',
              ),
            ],
          },
        )
      },
    },
    {
      title: '已加入随访计划',
      key: 'taskList',
      width: 100,
      render(row: any) {
        if (row.taskList && row.taskList.length >= 1) {
          return h(
            NTooltip,
            {
              trigger: 'hover',
            },
            {
              trigger: () => [
                h(
                  'div',
                  {
                    style: {
                      overflow: 'hidden',
                      textOverflow: 'ellipsis',
                    },
                  },
                  row.taskList[0],
                ),
              ],
              default: () => {
                const temps: any = []
                row.taskList?.forEach((element) => {
                  temps.push(
                    h('div', { class: 'h-17px' }, element),
                  )
                })
                return temps
              },
            },
          )
        }
        else {
          let sbb = '-'
          if (row.taskList && row.taskList?.length === 1)
            sbb = row.taskList[0]
          return h('div', null, sbb)
        }
      },
    },
    {
      title: '就诊科室',
      key: 'department',
      width: 90,
      render(row: any) {
        return h('div', {}, row.department ?? '-')
      },
    },
    {
      title: '最近一次就诊',
      key: 'visitDate',
      width: 75,
      render(row: any) {
        return h('div', {}, row.visitDate ?? '-')
      },
    },
    {
      title: '小程序用户',
      key: 'appUser',
      width: 70,
      render(row: any) {
        return h(
          'div',
          {
            style: {
              fontSize: '12px',
              color: '#fff',
              background: row.isWxMini ? '#4ACFB1' : '#B3B3B3',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              borderRadius: '3px',
              width: '16px',
              height: '16px',
            },
          },
          row.appUser ? '是' : '否',
        )
      },
    },
    {
      title: '患者标签',
      key: 'patientTag',
      width: 100,
      render(row: any) {
        let strs = '-'
        row?.patientTag?.forEach((item) => {
          strs += `${item} | `
        })
        return h(NEllipsis, {},
          strs?.length > 1 ? strs.substring(0, strs.length - 2) : strs,
        )
      },
    },
  ]
}

/// 姓名搜索
function handleUpdateShowPatientName(show: boolean) {
  if (show) {
    selectOpions.patient.loading = true
    getPatientName('')
  }
}

function handleSearchPatientName(query: string) {
  getPatientName(query)
}
/// 管理医生搜索
function handleUpdateShowDoctorName(show: boolean) {
  if (show) {
    selectOpions.doctor.loading = true
    getDoctorName('')
  }
}

function handleSearchDoctorName(query: string) {
  getDoctorName(query)
}

/// 移除患者
function handleRemovePatient(params: any) {
  window.$dialog?.warning({
    title: '确定移除该患者吗？',
    positiveText: '确定',
    negativeText: '取消',
    positiveButtonProps: {
      type: 'primary',
      secondary: true,
    },
    negativeButtonProps: {
      type: 'primary',
      secondary: true,
    },
    onPositiveClick: async () => {
      try {
        const { data } = await removeFollowPlanPatient({
          planId: params.planId,
        })
        if (data) {
          window.$message.success('删除成功')
          tableRef.value?.reload()
        }
      }
      catch {

      }
    },
  })
}
function handleFollowPatientAgain(params) {
  window.$dialog?.warning({
    title: '要再次随访该患者吗？',
    positiveText: '确定',
    negativeText: '取消',
    positiveButtonProps: {
      type: 'primary',
      secondary: true,
    },
    negativeButtonProps: {
      type: 'primary',
      secondary: true,
    },
    onPositiveClick: async () => {
      try {
        const { data } = await batchImportPatients({
          planTmpId: templateId,
          patientIdList: [params.patientId],
          createId: auth.userInfo.id,
          createName: auth.userInfo.userName,
        })
        if (data) {
          window.$message.success('操作成功')
          tableRef.value?.reload()
        }
      }
      catch {

      }
    },
  })
}

/// 查询
function handleSearch() {
  tableRef?.value?.fetch({ page: 1, size: 10, ...searchData, start: 1 })
}
/// 重置查询
function resetSearch() {
  // searchData.alarmGive
  searchData.alarmGive = 0
  searchData.joinType = ''
  searchData.manageDoctorList = []
  searchData.sexName = ''
  searchData.planState = []
  searchData.patientNmList = []
  handleSearch()
}

/***
 * 导入患者相关
 */
const importSearch = reactive({
  patientNmList: [],
  manageDoctorList: [],
  departmentList: [],
})

const importPatientKeys = ref([])

const sbDepartMents = [
  { label: '感染科门诊' }, { label: '重症医学科(感染)' }, { label: '感染科' }, { label: '感染科病房' }, { label: '人工肝治疗中心' }, { label: '肝病暨感染科门诊' }, { label: '脂肪肝专科' }, { label: '脂肪肝门诊' }, { label: '疑难感染性疾病联合门诊' }, { label: '肝硬化门静脉高压门诊' }, { label: '肝病门诊' }, { label: '肝肿瘤门诊' },
]

const importSelectOptions = reactive({
  patient: {
    options: [],
    loading: false,
  },
  doctor: {
    options: [],
    loading: false,
  },
  department: {
    options: [],
    loading: false,
  },
})

/// 患者名
const getImportPatientsName = useDebounceFn(async (search = '') => {
  const { data } = await queryImportPatientsNames(search)

  if (data?.records) {
    const array = data.records?.map((item) => {
      return {
        label: item,
        value: item,
      }
    })
    importSelectOptions.patient.options = array
  }
  importSelectOptions.patient.loading = false
}, 200)
/// 管理医生
const getImportDoctorsName = useDebounceFn(async (search = '') => {
  const { data } = await queryImportDoctorsNames({
    name: search,
    organId: auth.userInfo.organId,
  })

  if (data) {
    const array = data?.map((item) => {
      return {
        label: item.doctorName,
        value: item.doctorName,
      }
    })
    importSelectOptions.doctor.options = array
  }
  importSelectOptions.doctor.loading = false
}, 200)

/// select输入
function handleImportPatientSearch(search) {
  getImportPatientsName(search)
}
function handleUpdateImportPatient(show) {
  if (show) {
    importSelectOptions.patient.loading = true
    getImportPatientsName('')
  }
}
function handleImportDoctorSearch(search) {
  getImportDoctorsName(search)
}
function handleUpdateImportDoctor(show) {
  if (show) {
    importSelectOptions.doctor.loading = true
    getImportDoctorsName('')
  }
}

const patientTotal = ref(0)

/// 查询导入患者列表
async function loadPatients(res: { size: number; start: number }) {
  //  queryFollowPlanPatients().then({
  const response = await queryImportPatients({
    ...importSearch,
    ...{
      size: res.size,
      page: res.start,
    },
  })

  patientTotal.value = Number(response.data.total)

  return response
}

/// 导入搜索触发
function searchChange() {
  /// 导入
  importTable.value.fetch({ page: 1, size: 10, ...importSearch, start: 1 })
}

/// modal 变换
function visibleChange(v) {
  if (!v) {
    showImport.value = false
    /// 清空 内容
    importSearch.patientNmList = []
    importSearch.manageDoctorList = []
    importSearch.departmentList = []
    importPatientKeys.value = []
  }
}
///  确定导入
async function handleConfirmImport() {
  /// 导入患者到随访计划
  /// 菊花旋转
  loadingImport.value = true
  const res = await batchImportPatients({
    planTmpId: templateId,
    patientIdList: importPatientKeys.value,
    createId: auth.userInfo.id,
    createName: auth.userInfo.userName,
  })
  loadingImport.value = false
  if (res.data) {
    window.$message.success('导入成功')
    tableRef.value?.reload()
    showImport.value = false
  }
}

const aiccLoading = ref(false)
async function aicc() {
  try {
    aiccLoading.value = true
    const { data } = await aiccAPI({
      patientId: selectedPatientId,
      planPhaseId: aiPhone.value,
    })

    if (data) {
      window.$message.success('AI电话拨打成功')
      aiccModal.value = false
    }
  }
  catch (error) {
    console.log(error)
  }
  finally {
    aiccLoading.value = false
  }
}

onMounted(() => {
  //
})
</script>

<template>
  <div px-14px>
    <div flex flex-wrap items-center gap-y-14px>
      <div flex items-center style="display: -webkit-inline-box">
        <div mr-20px flex items-center>
          <span mr-10px style="color: #666">状态</span>
          <n-select
            v-model:value="searchData.planState"
            placeholder="请选择(可多选)"
            :options="Object.values(patientStates)"
            multiple
            max-tag-count="responsive"
            clearable
            w-190px
            :disabled="followPlan.tempState === 2"
          />
        </div>
        <div mr-20px flex items-center>
          <span mr-10px style="color: #666">性别</span>
          <n-select
            v-model:value="searchData.sexName"
            placeholder="请选择"
            max-tag-count="responsive"
            :disabled="followPlan.tempState === 2"
            :options="[
              {
                label: '全部',
                value: '',
              },
              {
                label: '男',
                value: '男',
              },
              {
                label: '女',
                value: '女',
              },
            ]"
            w-190px
          />
        </div>
        <!-- <div mr-20px flex items-center>
          <span mr-10px style="color: #666">异常值警告</span>
          <n-select
            v-model:value="searchData.alarmGive"
            placeholder="请选择"
            :options="[{
              label: '全部',
              value: 0,
            }, {
              label: '有异常',
              value: 1,
            }, {
              label: '无异常',
              value: 2,
            }]"
            w-190px
          />
        </div> -->
        <!-- <div mr-20px flex items-center>
          <span mr-10px style="color: #666">加入方式</span>
          <n-select
            v-model:value="searchData.joinType"
            placeholder="请选择"
            :options="[{
              label: '全部',
              value: '',
            }, {
              label: '自动加入',
              value: 'AUTO',
            }, {
              label: '手动加入',
              value: 'HANDLE',
            }]"
            w-190px
          />
        </div> -->
      </div>
      <div mr-20px flex items-center>
        <span mr-10px style="color: #666">姓名</span>
        <n-select
          v-model:value="searchData.patientNmList"
          placeholder="请选择(可多选)"
          :options="selectOpions.patient.opions"
          multiple
          max-tag-count="responsive"
          :loading="selectOpions.patient.loading"
          filterable
          clearable
          :disabled="followPlan.tempState === 2"
          remote
          w-190px
          @search="handleSearchPatientName"
          @update:show="handleUpdateShowPatientName"
        />
      </div>
      <div mr-20px flex items-center>
        <span mr-10px style="color: #666">管理医生</span>
        <n-select
          v-model:value="searchData.manageDoctorList"
          placeholder="请选择(可多选)"
          :options="selectOpions.doctor.opions"
          :loading="selectOpions.doctor.loading"
          multiple
          remote
          max-tag-count="responsive"
          clearable
          :disabled="followPlan.tempState === 2"
          w-190px
          @search="handleSearchDoctorName"
          @update:show="handleUpdateShowDoctorName"
        />
      </div>
      <div>
        <n-button v-if="followPlan.tempState !== 2" type="primary" @click="handleSearch">
          查询
        </n-button>
        <n-button v-if="followPlan.tempState !== 2" ghost ml-10px @click="resetSearch">
          重置
        </n-button>
      </div>
    </div>
    <div my-14px flex items-center>
      <n-button v-if="followPlan.tempState === 3" type="primary" @click="showImport = true">
        导入患者
      </n-button>
      <!-- <n-button type="primary" ml-10px>
        自动入组
      </n-button> -->
    </div>
    <div w-full>
      <BasicTable
        ref="tableRef"
        class="table"
        :columns="columns"
        :request="loadDataTable"
        :row-key="(row:any) => row.id"
        :action-column="actionColumns"
        :pagination="paginationPatient"
        :scroll-x="1430"
        striped
      />
    </div>
    <BasicModal
      :visible="showImport"
      title="导入患者"
      :height="480"
      :loading="loadingImport"
      width="1000"
      @ok="handleConfirmImport"
      @visible-change="visibleChange"
    >
      <div mb-14px mt-24px flex flex-col px-20px>
        <div flex items-center>
          <div mr-20px flex items-center>
            <span mr-10px style="color: #666">患者姓名</span>
            <n-select
              v-model:value="importSearch.patientNmList"
              placeholder="请选择(可多选)"
              :options="importSelectOptions.patient.options"
              :loading="importSelectOptions.patient.loading"
              remote
              max-tag-count="responsive"
              filterable
              multiple
              clearable
              w-240px
              @search="handleImportPatientSearch"
              @update:show="handleUpdateImportPatient"
              @update:value="searchChange"
            />
          </div>
          <div mr-20px flex items-center>
            <span mr-10px style="color: #666">医生</span>
            <n-select
              v-model:value="importSearch.manageDoctorList"
              placeholder="请选择(可多选)"
              :options="importSelectOptions.doctor.options"
              :loading="importSelectOptions.doctor.loading"
              multiple
              max-tag-count="responsive"
              remote
              filterable
              clearable
              w-240px
              @search="handleImportDoctorSearch"
              @update:show="handleUpdateImportDoctor"
              @update:value="searchChange"
            />
          </div>
          <div mr-20px flex items-center>
            <span mr-10px style="color: #666">科室</span>
            <n-select
              v-model:value="importSearch.departmentList"
              placeholder="请选择(可多选)"
              max-tag-count="responsive"
              :options="sbDepartMents"
              value-field="label"
              multiple
              clearable
              w-240px
              @update:value="searchChange"
            />
          </div>
        </div>
        <BasicTable
          ref="importTable"
          v-model:checked-row-keys="importPatientKeys"
          class="table"
          max-height="330"
          :columns="patientColumns"
          :request="loadPatients"
          :row-key="(row:any) => row.patientId"
          :pagination="paginationImport"
          striped
          mt-14px
        />
        <div ref="countLabel" class="import-count" :class="patientTotal > 0 ? 'bottom-20px' : ' top-10px'" relative w-100px>
          已选 {{ importPatientKeys.length }} 人
        </div>
      </div>
    </BasicModal>

    <n-modal
      v-model:show="aiccModal"
      preset="card"
      :style="{ width: '540px' }"
      head-style="divide"
      title="拨打AI电话"
    >
      <div px-10px pt-20px>
        <div mb-20px flex items-center>
          <div flex-shrink-0>
            手机号：
          </div>
          <n-input v-model:value="aiPhone" class="!w-300px" />
        </div>
        <n-button :disabled="aiPhone.length !== 11 || aiccLoading" class="!w-100px" ml-58px type="primary" @click="aicc">
          拨打
        </n-button>
        <n-button class="!w-100px" ml-10px @click="aiccModal = false">
          取消
        </n-button>
      </div>
    </n-modal>
  </div>
</template>

<style lang="scss" scoped>
:deep(.n-data-table-th) {
    padding-left: 10px;
    padding-right: 10px;
}
:deep(.n-data-table-td) {
    padding-left: 10px;
    padding-right: 10px;
}

.import-count{
  font-size: 14px;
  line-height: 14px;
  color: #666;
}
</style>
