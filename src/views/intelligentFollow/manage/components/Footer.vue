<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useAppStore } from '@/store'

const { siderCollapse } = storeToRefs(useAppStore())
</script>

<template>
  <div :class="siderCollapse ? 'w-[calc(100vw-28px)]' : 'w-[calc(100vw-248px)]' " class="fixed-footer z-100">
    <n-button w-100px type="primary">
      保 存
    </n-button>
    <n-button secondary w-112px type="primary">
      另存为新模板
    </n-button>
    <n-button secondary w-100px>
      取 消
    </n-button>
  </div>
</template>

<style lang="scss" scoped>
.fixed-footer {
  height: 60px;
  position: fixed;
  box-shadow: 0px -1px 0px 0px #e8e8e8 inset, 0px 1px 0px 0px #e8e8e8 inset;
  background: #f9f9f9;
  bottom: 14px;
  right: 14px;
  justify-content: center;
  align-items: center;
  display: flex;
  gap: 20px;
}
</style>
