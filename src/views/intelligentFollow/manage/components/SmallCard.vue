<script setup lang="ts">
defineProps<{
  isActive: boolean
  name: string
}>()
</script>

<template>
  <div :class="isActive ? 'small-t-card-active' : 'small-t-card'">
    <SvgIcon
      :class="isActive ? 'small-t-card-icon-active' : 'small-t-card-icon'" local-icon="slmc-icon-wenjian"
      size="16" flex-shrink-0
    />
    <n-ellipsis :line-clamp="2">
      {{ name || '-' }}
    </n-ellipsis>
  </div>
</template>

<style lang="scss" scoped>
.small-t-card-active {
  border: 1px solid #06AEA6;
  --uno: bg-#E7F9F7 h-56px w-250px flex cursor-not-allowed items-center gap-10px b-rd-3px px-14px
}

.small-t-card-icon-active {
  color: #06AEA6;
}

.small-t-card {
  border: 1px solid #F5F5F5;
  --uno: bg-#F5F5F5 h-56px w-250px flex cursor-pointer items-center gap-10px b-rd-3px px-14px
}

.small-t-card:hover {
  border: 1px solid #06AEA6;
  --uno: bg-#fff;

  .small-t-card-icon {
    color: #06AEA6;
  }
}
</style>
