<script setup lang="ts">
import { vOnClickOutside } from '@vueuse/components'
import type { ChildTemplateVO } from '../../type'
import { FollowPlanKey } from '../../type'
import { useEnums } from '../useEnums'

import { childTemplateMeta } from '../metaData'
import Basic from './subPlan/Basic.vue'
import Survey from './subPlan/Survey.vue'
import Education from './subPlan/Education.vue'
import Inspection from './subPlan/Inspection.vue'

const currentEditPlan = ref(-1)

useEnums()

const followPlan = inject(FollowPlanKey)!
const currentPlanIndex = inject<Ref<number>>('currentPlanIndex')!

function handleAddChildPlan() {
  const childTepLength = followPlan.value.childTemplateVos.length
  const allPlanNames = followPlan.value.childTemplateVos.map(i => i.planChildName)

  function generateName(index: number) {
    const name = `子计划${index + 1}`
    if (allPlanNames.includes(name))
      return generateName(index + 1)
    else
      return name
  }

  followPlan.value.childTemplateVos.push(
    childTemplateMeta(generateName(childTepLength)),
  )

  currentPlanIndex.value = followPlan.value.childTemplateVos.length - 1
}

let oldName = ''

function handleEditName(item: ChildTemplateVO, index: number) {
  oldName = item.planChildName
  currentEditPlan.value = index
}

function handleConfirmRename() {
  if (!followPlan.value.childTemplateVos[currentEditPlan.value].planChildName) {
    window.$message.warning('请输入计划名称')
  }
  else {
    const allPlanNames = followPlan.value.childTemplateVos.map(i => i.planChildName)
    const noRepatPlanNames = new Set(allPlanNames)
    if (noRepatPlanNames.size !== allPlanNames.length)
      window.$message.warning('计划名称不能重复')

    else
      currentEditPlan.value = -1
  }
}

function handleCancelRename() {
  followPlan.value.childTemplateVos[currentEditPlan.value].planChildName = oldName
  currentEditPlan.value = -1
}

function handleDeletePlan(index: number) {
  window.$dialog.warning({
    title: '确定删除该子计划吗？',
    negativeText: '取消',
    positiveText: '确定',
    btnGhost: true,
    onPositiveClick: () => {
      followPlan.value.childTemplateVos.splice(index, 1)
      if (followPlan.value.childTemplateVos.length === 0)
        currentPlanIndex.value = -1
      else if (currentPlanIndex.value === index)
        currentPlanIndex.value = Math.max(0, index - 1)
    },
  })
}

const isDisabled = inject<ComputedRef<boolean>>('isDisabled')

defineExpose({
  handleAddChildPlan,
})
</script>

<template>
  <div :class=" isDisabled ? 'h-[calc(100%-68px)]' : 'h-[calc(100%-120px)]'" flex>
    <div relative h-full w-229px flex-shrink-0 overflow-auto pb-80px>
      <div mb-10px px-14px>
        <n-button :ghost="!isDisabled" block :disabled="isDisabled" @click="handleAddChildPlan">
          <template #icon>
            <SvgIcon local-icon="slmc-icon-add2" :class="`${isDisabled ? 'text-#999' : 'text-#666'}`" :size="16" />
          </template>
          新增子计划
        </n-button>
      </div>
      <div
        v-on-click-outside="() => {
          if (currentEditPlan > -1)
            handleConfirmRename()
        }" flex="~ col"
      >
        <div
          v-for="(item, index) in followPlan?.childTemplateVos"
          :key="index" class="child-plan"
          w-full flex items-center justify-between px-16px py-10px text-14px
          hover:bg="#FFFBE0"
          :class="currentPlanIndex === index ? 'bg-#FFFBE0 cursor-not-allowed' : 'cursor-pointer'"
          @click="currentPlanIndex = index"
        >
          <n-ellipsis v-if="index !== currentEditPlan" text="#333 12px" class="h-16px !max-w-150px">
            {{ item.planChildName }}
          </n-ellipsis>
          <n-input v-else v-model:value="followPlan.childTemplateVos[index].planChildName" :maxlength="10" class="relative -left-10px !w-154px" size="small" />

          <div v-if="index !== currentEditPlan" :class="isDisabled ? 'hidden' : 'action' ">
            <div flex gap-10px>
              <SvgIcon cursor-pointer local-icon="slmc-icon-edit1" size="16" @click="handleEditName(item, index)" />
              <SvgIcon cursor-pointer local-icon="slmc-icon-delete2" size="16" @click="handleDeletePlan(index)" />
            </div>
          </div>
          <div v-else flex gap-10px>
            <SvgIcon cursor-pointer local-icon="slmc-icon-done_line" size="16" @click="handleConfirmRename" />
            <SvgIcon cursor-pointer local-icon="slmc-icon-wrong_line" size="16" @click="handleCancelRename" />
          </div>
        </div>
      </div>
    </div>
    <div h-full bg="#ccc" w-1px />
    <div v-if="currentPlanIndex > -1" ml-14px h-full w-full overflow-auto pb-80px>
      <!-- 项目设置 -->
      <Basic />
      <div my-15px h-1px w-full bg="#ccc" />

      <Survey />
      <div my-15px h-1px w-full bg="#ccc" />

      <Education />
      <div my-15px h-1px w-full bg="#ccc" />

      <Inspection />

      <!-- <Quota /> -->
    </div>
  </div>
</template>

<style lang="scss" scoped>
.child-plan {
  .action {
    display: none;
  }
}

.child-plan:hover {
  .action {
    display: block;
  }
}
</style>
