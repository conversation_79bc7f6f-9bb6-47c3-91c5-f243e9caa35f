<script setup lang="ts">
import { cloneDeep } from 'lodash-es'
import { FollowPlanKey } from '../../../type'
import RichEditor from '@/components/RichEditor/index.vue'
import AuthRange from '@/views/toolsFollow/components/AuthRange.vue'
import { getEduBySeriesAPI, getEduDetailAPI, saveEduAPI, updateEduAPI } from '@/api/toolsFollow/education'
import { useAuthStore } from '@/store'
import TemplateCard from '@/views/toolsFollow/components/TemplateCard.vue'

const isAddModalShow = ref(false)
const isAddTemplateModalShow = ref(false)
const richEditorRef = ref<InstanceType<typeof RichEditor>>()

const edutList = inject<Ref<any[]>>('eduList')
const eduLabelTree = inject<Ref<any[]>>('eduLabelTree')
const followPlan = inject(FollowPlanKey)!
const currentPlanIndex = inject<Ref<number>>('currentPlanIndex')!
const isDisabled = inject<ComputedRef<boolean>>('isDisabled')!

const { userInfo } = useAuthStore()

const originalEduForm = {
  title: '',
  content: '',
  introduce: '',
  createId: userInfo.id,
  createName: userInfo.userName,
}

const eduForm = ref<Record<string, any>>(cloneDeep(originalEduForm))

const templateForm = ref<Record<string, any>>({
  title: '',
  labelId: null,
  visibleRange: 'ALL_USER',
  relations: [],
  introduce: '',
})

async function handleUpdate(id: string) {
  try {
    const { data } = await getEduDetailAPI(id)
    eduForm.value.title = data.title
    eduForm.value.content = data.content
    eduForm.value.introduce = data.introduce
  }
  catch (error) {

  }
}

function handleSaveTemplateClick() {
  isAddTemplateModalShow.value = true
}

async function handleTemplateSaveClick() {
  try {
    const { data } = await saveEduAPI({
      createId: eduForm.value.createId,
      createName: eduForm.value.createName,
      content: eduForm.value.content,
      isTemplate: true,
      ...templateForm.value,
    })

    if (data) {
      window.$message.success('模板保存成功')

      isAddTemplateModalShow.value = false
      templateForm.value.title = ''
      templateForm.value.labelId = null
      templateForm.value.visibleRange = 'ALL_USER'
      templateForm.value.introduce = ''
    }
  }
  catch (error) {

  }
}

function handleDeleteClick(index: number) {
  if (isDisabled.value)
    return false
  window.$dialog.warning({
    title: '确定删除该宣教吗？',
    btnGhost: true,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      followPlan.value.childTemplateVos[currentPlanIndex.value].taskTemplateEdu.splice(index, 1)
    },
  })
}

const currentEditEduIndex = ref(-1)

async function handleEduEdit(item: any, index: number) {
  try {
    if (isDisabled.value)
      return false

    const { data } = await getEduBySeriesAPI(item.relationId)
    eduForm.value = data
    eduForm.value.updateId = userInfo.id
    eduForm.value.updateName = userInfo.userName

    currentEditEduIndex.value = index
    isAddModalShow.value = true
  }
  catch (error) {
    console.log(error)
  }
}

async function handleSaveClick() {
  try {
    if (!eduForm.value.title) {
      window.$message.warning('宣教标题不能为空')
      return false
    }

    if (followPlan.value.childTemplateVos[currentPlanIndex.value].taskTemplateEdu.map(item => item.relationName).includes(eduForm.value.title)) {
      window.$message.warning('宣教标题不能重复')
      return false
    }

    if (currentEditEduIndex.value > -1) {
      const { data } = await updateEduAPI(eduForm.value)
      if (data) {
        followPlan.value.childTemplateVos[currentPlanIndex.value].taskTemplateEdu[currentEditEduIndex.value].relationName = eduForm.value.title
        eduForm.value = cloneDeep(originalEduForm)
        isAddModalShow.value = false
        currentEditEduIndex.value = -1
        window.$message.success('修改成功')
      }
      else {
        window.$message.error('修改失败')
      }
    }
    else {
      const { data } = await saveEduAPI({
        isTemplate: false,
        ...eduForm.value,
      })

      if (data) {
        followPlan.value.childTemplateVos[currentPlanIndex.value].taskTemplateEdu.push({
          relationName: eduForm.value.title,
          relationId: data.seriesId,
        })

        eduForm.value = cloneDeep(originalEduForm)

        isAddModalShow.value = false
        window.$message.success('保存成功')
      }
      else {
        window.$message.error('保存失败')
      }
    }
  }
  catch (error) {

  }
}
</script>

<template>
  <div>
    <div flex items-center gap-10px>
      <div sub-title />
      <div>患者宣教</div>
    </div>

    <div ml-14px>
      <n-button
        secondary my-14px type="primary" :disabled="isDisabled"
        @click="() => {
          eduForm = cloneDeep(originalEduForm)
          isAddModalShow = true
        }"
      >
        新增宣教
      </n-button>

      <div flex="~ wrap" gap-13px>
        <TemplateCard
          v-for="(item, index) in followPlan.childTemplateVos[currentPlanIndex].taskTemplateEdu" :key="item.relationId!"
          h-93px width="250px"
          :class="isDisabled ? '!cursor-not-allowed' : ''"
        >
          <div text="#333 16px" font-500 leading-20px>
            {{ item.relationName }}
          </div>
          <template #footer>
            <div mx-14px h-full flex items-center :class="isDisabled ? 'op-30' : ''">
              <div mx-30px flex items-center gap-10px @click="handleEduEdit(item, index)">
                <SvgIcon local-icon="slmc-icon-edit1" size="14" />
                <span text="14px #3B8FD9">编辑</span>
              </div>

              <div ml-7px mr-14px h-14px w-1px bg="#3B8FD9" />
              <div mx-15px flex items-center gap-10px @click="handleDeleteClick(index)">
                <SvgIcon local-icon="slmc-icon-delete2" size="14" />
                <span text="14px #3B8FD9">删除</span>
              </div>
            </div>
          </template>
        </TemplateCard>
      </div>
    </div>

    <n-modal
      v-model:show="isAddModalShow"
      preset="card" :style="{ width: '1000px' }"
      :title="`${currentEditEduIndex > -1 ? '修改' : '新增'}患教模板`" head-style="divide"
    >
      <div h-542px flex items-start gap-10px py-20px>
        <div flex="~ col" gap-14px>
          <div flex items-center gap-10px>
            <div w-70px text-right>
              选择模板
            </div>
            <n-select
              :options="edutList"
              value-field="educationId" label-field="title"
              class="!w-540px"
              @update:value="handleUpdate"
            />
          </div>

          <div flex items-center gap-10px>
            <div w-70px flex items-center text-right>
              <SvgIcon local-icon="slmc-icon-app_required1" size="14" class="text-#F36969" />
              <div text="#666">
                宣教标题
              </div>
            </div>
            <n-input v-model:value="eduForm.title" class="!w-540px" show-count :maxlength="10" />
          </div>

          <div flex items-start gap-10px>
            <div w-70px flex items-center text-right>
              <SvgIcon local-icon="slmc-icon-app_required1" size="14" class="text-#F36969" />
              <div text="#666">
                宣教内容
              </div>
            </div>
            <RichEditor ref="richEditorRef" v-model:html="eduForm.content" height="250px" width="540px" />
          </div>

          <div flex items-center gap-10px>
            <div w-70px text="#666" text-right>
              备注
            </div>
            <n-input v-model:value="eduForm.introduce" class="!w-540px" show-count :maxlength="10" />
          </div>
        </div>
        <div mx-10px h-full w-1px bg="#ccc" />
        <div h-full flex-1>
          <div flex items-center gap-10px>
            <div sub-title />
            <div text="#333">
              患教内容预览
            </div>
          </div>
          <div v-if="richEditorRef?.isEmpty" h-full flex items-center justify-center>
            <n-empty description="无数据" />
          </div>
          <div v-else h-500px overflow-auto class="editor-content-view" v-html="eduForm.content" />
        </div>
      </div>
      <template #footer>
        <div absolute bottom-0 left-0 h-60px w-full flex items-center justify-center style="border-radius: 0 0 3px 3px;" border-t="1px solid #e8e8e8" bg="#f9f9f9">
          <n-button type="primary" secondary mr-16px w-100px @click="handleSaveTemplateClick">
            存为新模板
          </n-button>
          <n-button mr-16px w-100px type="primary" @click="handleSaveClick">
            保 存
          </n-button>
          <n-button w-100px @click="isAddModalShow = false">
            取 消
          </n-button>
        </div>
      </template>
    </n-modal>

    <n-modal
      v-model:show="isAddTemplateModalShow" preset="card" :style="{ width: '720px' }"
      title="新增患教模板" head-style="divide"
    >
      <div flex="~ col" gap-14px py-20px>
        <div flex items-center gap-10px>
          <div w-70px flex items-center text-right>
            <SvgIcon local-icon="slmc-icon-app_required1" size="14" class="text-#F36969" />
            <div text="#666">
              模板名称
            </div>
          </div>
          <n-input v-model:value="templateForm.title" class="!w-540px" show-count :maxlength="10" />
        </div>

        <div flex items-center gap-10px>
          <div w-70px text="#666" text-right>
            可见范围
          </div>

          <AuthRange v-model:visibleRange="templateForm.visibleRange" v-model:relations="templateForm.relations" />
        </div>

        <div flex items-center gap-10px>
          <div w-70px flex items-center text-right>
            <SvgIcon local-icon="slmc-icon-app_required1" size="14" class="text-#F36969" />
            <div text="#666">
              模板分类
            </div>
          </div>

          <n-select
            v-model:value="templateForm.labelId"
            :options="eduLabelTree"
            label-field="labelName" value-field="labelId" class="!w-540px"
          />
        </div>

        <div flex items-center gap-10px>
          <div w-70px text="#666" text-right>
            备注
          </div>
          <n-input v-model:value="templateForm.introduce" class="!w-540px" show-count :maxlength="10" />
        </div>
        <div ml-80px>
          <n-button class="w-100px" mr-20px type="primary" @click="handleTemplateSaveClick">
            保 存
          </n-button>
          <n-button class="w-100px" @click="isAddTemplateModalShow = false">
            取 消
          </n-button>
        </div>
      </div>
    </n-modal>
  </div>
</template>
