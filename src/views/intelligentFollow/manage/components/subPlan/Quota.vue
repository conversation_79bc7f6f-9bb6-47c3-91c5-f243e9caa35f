<script setup lang="ts">
import { cloneDeep } from 'lodash'
import { FollowPlanKey } from '../../../type'
import { quotaDetailMeta, taskTemplateIndexMeta } from '../../metaData'

const expressionOptions = ref([
  {
    label: '大于',
    value: 'GT',
  },
  {
    label: '等于',
    value: 'ED',
  },
  {
    label: '小于',
    value: 'LT',
  },
  {
    label: '区间',
    value: 'IN',
  },
])

const followPlan = inject(FollowPlanKey)!
const currentPlanIndex = inject<Ref<number>>('currentPlanIndex')!
const quotas = inject<Ref<any[]>>('quotas')
const warningEvents = inject<Ref<any[]>>('warningEvents')
const isDisabled = inject<ComputedRef<boolean>>('isDisabled')!

function handleAddQuata() {
  followPlan.value.childTemplateVos[currentPlanIndex.value].taskTemplateIndex.push(cloneDeep(taskTemplateIndexMeta))
}

function hanldeDeleteQuata(index: number) {
  if (isDisabled.value)
    return false
  followPlan.value.childTemplateVos[currentPlanIndex.value].taskTemplateIndex.splice(index, 1)
}

function handleAddQuataItem(index: number) {
  if (isDisabled.value)
    return false

  followPlan.value.childTemplateVos[currentPlanIndex.value].taskTemplateIndex[index].planQuatas!.push(cloneDeep(quotaDetailMeta))
}

function handleDeleteQuataItem(index: number) {
  if (isDisabled.value)
    return false

  followPlan.value.childTemplateVos[currentPlanIndex.value].taskTemplateIndex[currentPlanIndex.value].planQuatas!.splice(index, 1)
}

function handleQuotaUpdate(_val: any, option: any, index: number) {
  followPlan.value.childTemplateVos[currentPlanIndex.value].taskTemplateIndex[index].relationName = option.itemName
}

function handleCondValueUpdate(ranges: [string, string], $index: number) {
  if (Number.isInteger(Number(ranges[0])))
    followPlan.value.childTemplateVos[currentPlanIndex.value].taskTemplateIndex[currentPlanIndex.value].planQuatas![$index].condValueOne = ranges[0]

  if (Number.isInteger(Number(ranges[1])))
    followPlan.value.childTemplateVos[currentPlanIndex.value].taskTemplateIndex[currentPlanIndex.value].planQuatas![$index].condValueTwo = ranges[1]
}

function handleUpdateWarningEvent(val: string[], index: number, $index: number) {
  if (val.length >= 11) {
    window.$message.warning('最多选择10项')
    return false
  }
  followPlan.value.childTemplateVos[currentPlanIndex.value].taskTemplateIndex[index].planQuatas![$index].warningIdList = val
}
</script>

<template>
  <div class="!w-[calc(100%-20px)] 2xl:!w-[calc(100%-110px)]">
    <div mb-15px flex items-center gap-10px>
      <div sub-title />
      <div>重点指标</div>
    </div>
    <div ml-15px>
      <n-button :disabled="isDisabled" type="primary" secondary @click="handleAddQuata">
        添加重点监测指标
      </n-button>
      <div v-for="(_item, index) in followPlan.childTemplateVos[currentPlanIndex].taskTemplateIndex" :key="index">
        <div my-13px w-full border="1px dashed #ccc" />
        <div mb-14px flex justify-between>
          <div flex gap-40px>
            <div flex items-center gap-10px>
              <div text="#666">
                指标 {{ index + 1 }}
              </div>
              <n-select
                v-model:value="followPlan.childTemplateVos[currentPlanIndex].taskTemplateIndex[index].relationId"
                :disabled="isDisabled"
                :options="quotas"
                label-field="itemName"
                value-field="itemCode"
                class="!w-200px"
                @update:value="(val, option) => handleQuotaUpdate(val, option, index)"
              />
            </div>
            <div flex items-center gap-10px>
              <div text="#666">
                是否必填
              </div>
              <n-checkbox
                v-model:checked="followPlan.childTemplateVos[currentPlanIndex].taskTemplateIndex[index].isRequired"
                :disabled="isDisabled"
                :checked-value="1"
                :unchecked-value="0"
              >
                <span ml-10px>必填</span>
              </n-checkbox>
            </div>
          </div>

          <div :class="isDisabled ? 'op-30 !cursor-not-allowed' : ''" flex cursor-pointer items-center gap-10px @click="hanldeDeleteQuata(index)">
            <SvgIcon local-icon="slmc-icon-delete2" size="16" />
            <div text="#3B8FD9">
              删除指标
            </div>
          </div>
        </div>
        <el-table :data="followPlan.childTemplateVos[currentPlanIndex].taskTemplateIndex[index].planQuatas">
          <el-table-column label="序号" width="80" type="index" />
          <el-table-column label="预警条件">
            <template #default="{ $index, row }">
              <n-input-group>
                <n-select
                  v-model:value="followPlan.childTemplateVos[currentPlanIndex].taskTemplateIndex[index].planQuatas![$index].compareType"
                  :disabled="isDisabled"
                  :options="expressionOptions"

                  class="custom-bg w-110px 2xl:w-140px"
                />
                <n-input
                  v-if="row.compareType !== 'IN'"
                  v-model:value="followPlan.childTemplateVos[currentPlanIndex].taskTemplateIndex[index].planQuatas![$index].condValueOne"
                  :disabled="isDisabled"
                  class="!w-171px 2xl:w-220px" placeholder="请输入预警值"
                />
                <n-input
                  v-else
                  :value="[followPlan.childTemplateVos[currentPlanIndex].taskTemplateIndex[index].planQuatas![$index].condValueOne, followPlan.childTemplateVos[currentPlanIndex].taskTemplateIndex[index].planQuatas![$index].condValueTwo]"
                  pair
                  separator="~"
                  @update:value="(rangs) => handleCondValueUpdate(rangs, $index)"
                />
              </n-input-group>
            </template>
          </el-table-column>
          <el-table-column label="预警事件">
            <template #default="{ $index }">
              <n-select
                :value="followPlan.childTemplateVos[currentPlanIndex].taskTemplateIndex[index].planQuatas![$index].warningIdList"
                :disabled="isDisabled"
                multiple
                :options="warningEvents"
                label-field="warningName"
                value-field="warningId"
                class="!w-280px 2xl:!w-360px"
                max-tag-count="responsive"
                placeholder="点击选择事件"
                @update:value="(val) => handleUpdateWarningEvent(val, index, $index)"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template #default="{ $index }">
              <div flex gap-10px>
                <SvgIcon :class="isDisabled ? 'op-30 !cursor-not-allowed' : ''" cursor-pointer local-icon="slmc-icon-add1" size="16" @click="handleAddQuataItem(index)" />
                <SvgIcon
                  v-if="followPlan.childTemplateVos[currentPlanIndex].taskTemplateIndex[index].planQuatas!.length > 1"
                  cursor-pointer local-icon="slmc-icon-delete1" size="16"
                  :class="isDisabled ? 'op-30 !cursor-not-allowed' : ''" @click="handleDeleteQuataItem($index)"
                />
              </div>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.custom-bg {
  .n-base-selection .n-base-selection-label,
  .n-base-selection:not(.n-base-selection--disabled).n-base-selection--active .n-base-selection-label {
    background: #FAFAFA;
  }
}
</style>
