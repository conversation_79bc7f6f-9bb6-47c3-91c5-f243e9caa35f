<script setup lang="ts">
import dayjs from 'dayjs'
import SmallCard from '../SmallCard.vue'
import type { taskTemplateVo } from '../../../type'
import { FollowPlanKey } from '../../../type'
import TemplateCard from '@/views/toolsFollow/components/TemplateCard.vue'
import { getSurveyDetailBySeriesAPI, updateSurveyAPI } from '@/api/toolsFollow'
import { useAuthStore } from '@/store'

const router = useRouter()
const route = useRoute()

const { userInfo } = useAuthStore()

const isAddModalShow = ref(false)
const followPlan = inject(FollowPlanKey)!
const currentPlanIndex = inject<Ref<number>>('currentPlanIndex')!
const surveyList = inject<Ref<any[]>>('surveyList')!
const isDisabled = inject<ComputedRef<boolean>>('isDisabled')!

const createType = ref('ai')

function handleAddClick() {
  isAddModalShow.value = true
}

function handleAddSurvey() {
  if (createType.value === 'template') {
    if (followPlan.value.childTemplateVos[currentPlanIndex.value].taskTemplateSales?.map(item => item.relationName).includes(surveyList.value.find(i => i.isActive).scaleName)) {
      window.$message.warning('问卷名称不能重复')
      return false
    }
    followPlan.value.childTemplateVos[currentPlanIndex.value].taskTemplateSales.push({
      relationId: surveyList.value.find(i => i.isActive).seriesId,
      relationName: surveyList.value.find(i => i.isActive).scaleName,
      recoveryNum: 0,
      createTime: surveyList.value.find(i => i.isActive).createTime,
      isDefaultScale: surveyList.value.find(i => i.isActive).type === 'SYSTEM',
    })

    isAddModalShow.value = false
  }
  else if (createType.value === 'ai') {
    storeTemplate()
    router.push({
      name: 'toolsFollow_survey_create',
      query: {
        followPlanId: route.query.id,
        ai: '1',
      },
    })
  }
  else if (createType.value === 'empty') {
    storeTemplate()
    router.push({
      name: 'toolsFollow_survey_create',
      query: {
        followPlanId: route.query.id,
      },
    })
  }
}

function handleDelete(index: number) {
  if (isDisabled.value)
    return false
  window.$dialog.warning({
    title: '确定删除该问卷吗？',
    btnGhost: true,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      followPlan.value.childTemplateVos[currentPlanIndex.value].taskTemplateSales.splice(index, 1)
    },
  })
}

function storeTemplate(surveyIndex?: number) {
  const storeInfo: Record<string, any> = {
    currentPlanIndex: currentPlanIndex.value,
    followPlan: followPlan.value,
  }

  if (Number.isInteger(surveyIndex))
    storeInfo.surveyIndex = surveyIndex

  window.localStorage.setItem('followPlanInfo', JSON.stringify(storeInfo))
}

function toSurveyTemplatePage() {
  storeTemplate()

  router.push({
    name: 'toolsFollow_survey',
    query: {
      followPlanId: route.query.id,
    },
  })
}

const surveyForm = ref<any>()

const isRenameModalShow = ref(false)
let renameIndex = -1

async function handleRenameClick(item: taskTemplateVo, index: number) {
  try {
    if (isDisabled.value)
      return false

    const { data } = await getSurveyDetailBySeriesAPI(item.relationId!)
    surveyForm.value = data
    surveyForm.value.updateId = userInfo.id
    surveyForm.value.updateName = userInfo.userName
    renameIndex = index

    isRenameModalShow.value = true
  }
  catch (error) {

  }
}

async function handleRenameSave() {
  try {
    const content = JSON.parse(surveyForm.value.content)
    content.title = surveyForm.value.scaleName

    surveyForm.value.content = JSON.stringify(content)

    const { data } = await updateSurveyAPI(surveyForm.value)

    if (data) {
      window.$message.success('修改成功')
      // renameIndex
      followPlan.value.childTemplateVos[currentPlanIndex.value].taskTemplateSales[renameIndex].relationName = surveyForm.value.scaleName
      isRenameModalShow.value = false
    }
  }
  catch (error) {

  }
}

async function handleEditClick(item: taskTemplateVo, index: number) {
  try {
    if (isDisabled.value)
      return false

    const { data } = await getSurveyDetailBySeriesAPI(item.relationId!)

    storeTemplate(index)

    if (data) {
      router.push({
        path: '/toolsFollow/survey/edit',
        query: {
          id: data.scaleId,
          followPlanId: route.query.id,
        },
      })
    }
  }
  catch (error) {

  }
}
</script>

<template>
  <div>
    <div flex items-center gap-10px>
      <div sub-title />
      <div>随访问卷</div>
    </div>
    <div ml-14px>
      <n-button secondary my-14px type="primary" :disabled="isDisabled" @click="handleAddClick">
        新增问卷
      </n-button>

      <div flex="~ wrap" gap-13px>
        <TemplateCard
          v-for="(i, index) in followPlan.childTemplateVos[currentPlanIndex].taskTemplateSales"
          :key="i.relationId!" :is-default="i.isDefaultScale" h-157px width="250px"
          :class="isDisabled ? '!cursor-not-allowed' : ''"
        >
          <n-ellipsis text="#333 16px" font-500 leading-20px>
            {{ i.relationName }}
          </n-ellipsis>
          <div mb-10px mt-12px flex>
            <span text="#666 12px" leading-none class="flex-shrink-0">已发布天数：</span>
            <span class="counter">
              {{ dayjs().diff(dayjs(i.createTime || new Date()), 'day') }}
            </span>
          </div>
          <div flex>
            <span text="#666 12px" leading-none class="flex-shrink-0">数据份数：</span>
            <span class="counter">
              {{ i.recoveryNum }}
            </span>
          </div>
          <template #footer>
            <div :class="isDisabled ? 'op-30' : ''" mx-14px h-full flex items-center justify-center>
              <template v-if="!i.isDefaultScale">
                <div flex items-center gap-10px @click="handleEditClick(i, index)">
                  <SvgIcon local-icon="slmc-icon-edit1" size="14" />
                  <span text="14px #3B8FD9">编辑</span>
                </div>
                <div ml-14px mr-7px h-14px w-1px bg="#3B8FD9" />
                <div flex items-center gap-10px @click="handleRenameClick(i, index)">
                  <SvgIcon color="#3B8FD9" local-icon="slmc-icon-bi" size="14" />
                  <span text="14px #3B8FD9">重命名</span>
                </div>
                <div ml-7px mr-14px h-14px w-1px bg="#3B8FD9" />
              </template>
              <div flex items-center gap-10px @click="handleDelete(index)">
                <SvgIcon local-icon="slmc-icon-delete2" size="14" />
                <span text="14px #3B8FD9">删除</span>
              </div>
            </div>
          </template>
        </TemplateCard>
      </div>
    </div>

    <n-modal v-model:show="isAddModalShow" preset="card" :style="{ width: '720px' }" title="新增问卷" head-style="divide">
      <div mb-14px ml-10px mt-24px>
        <div flex items-center gap-10px>
          <div text="#666" text-right>
            创建方式
          </div>
          <n-radio-group v-model:value="createType">
            <n-radio mr-40px value="ai">
              AI问卷
            </n-radio>
            <n-radio mr-40px value="empty">
              创建空白问卷
            </n-radio>
            <n-radio value="template">
              从模板引用
            </n-radio>
          </n-radio-group>
        </div>
        <div v-show="createType === 'template'">
          <div ml-68px mt-10px flex="~ wrap" gap-14px>
            <SmallCard
              v-for="i in surveyList"
              :key="i.seriesId" :name="i.scaleName"
              :is-active="i.isActive"
              @click="() => {
                surveyList.forEach((item) => {
                  item.isActive = false
                })
                i.isActive = true
              }"
            />
          </div>
          <div ml-68px mt-14px flex cursor-pointer items-center gap-5px text="#3B8FD9" @click="toSurveyTemplatePage">
            更多模板
            <SvgIcon local-icon="slmc-icon-go" text="#3B8FD9" />
          </div>
        </div>
        <div ml-68px mt-24px>
          <n-button mr-16px w-100px type="primary" @click="handleAddSurvey">
            保 存
          </n-button>
          <n-button w-100px @click="isAddModalShow = false">
            取 消
          </n-button>
        </div>
      </div>
    </n-modal>

    <n-modal v-model:show="isRenameModalShow" preset="card" :style="{ width: '560px' }" title="问卷名称修改" head-style="divide">
      <div py-20px>
        <div flex items-center gap-10px>
          <div flex items-center>
            <SvgIcon local-icon="slmc-icon-app_required1" size="14" class="text-#F36969" />
            <div text="#666">
              问卷名称
            </div>
          </div>
          <n-input v-model:value="surveyForm.scaleName" show-count :maxlength="10" class="!w-370px" />
        </div>

        <div ml-80px mt-24px>
          <n-button mr-16px w-100px type="primary" @click="handleRenameSave">
            保 存
          </n-button>
          <n-button w-100px @click="isRenameModalShow = false">
            取 消
          </n-button>
        </div>
      </div>
    </n-modal>
  </div>
</template>

<style lang="scss" scoped>
.counter {
  --uno: text-#FF9B54 text-20px font-700 relative bottom-3px
}
</style>
