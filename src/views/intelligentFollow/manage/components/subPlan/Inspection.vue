<script setup lang="ts">
import { FollowPlanKey } from '../../../type'
import { taskTemplateCheckMeta } from '../../metaData'

const followPlan = inject(FollowPlanKey)!
const currentPlanIndex = inject<Ref<number>>('currentPlanIndex')!
const inspectList = inject<Ref<any[]>>('inspectList')
const imageCheckList = inject<Ref<any[]>>('imageCheckList')
const isDisabled = inject<ComputedRef<boolean>>('isDisabled')!

function handleAddInspect() {
  if (isDisabled.value)
    return false
  followPlan.value.childTemplateVos[currentPlanIndex.value].taskTemplateCheck!.push({ ...taskTemplateCheckMeta })
}

function handleDeleteInspect(index: number) {
  if (isDisabled.value)
    return false
  followPlan.value.childTemplateVos[currentPlanIndex.value].taskTemplateCheck!.splice(index, 1)
}

function handleAddTableClick() {
  followPlan.value.childTemplateVos[currentPlanIndex.value].taskTemplateCheck!.push({ ...taskTemplateCheckMeta })
}

function handleInspectUpdate(_val: any, option: any, index: number) {
  followPlan.value.childTemplateVos[currentPlanIndex.value].taskTemplateCheck![index].relationName = option.projectName
}
</script>

<template>
  <div mb-15px flex items-center gap-10px>
    <div sub-title />
    <div>检查检验</div>
  </div>
  <div ml-14px>
    <n-button v-if="!followPlan.childTemplateVos[currentPlanIndex].taskTemplateCheck?.length" :disabled="isDisabled" secondary type="primary" @click="handleAddTableClick">
      新增检验检查
    </n-button>
    <el-table
      v-if="followPlan.childTemplateVos[currentPlanIndex].taskTemplateCheck?.length" stripe
      :max-height="236"
      class="!w-[calc(100%-20px)] 2xl:!w-[calc(100%-110px)]" mt-10px
      :data="followPlan.childTemplateVos[currentPlanIndex].taskTemplateCheck"
    >
      <el-table-column label="序号" width="80" type="index" />
      <el-table-column label="类别">
        <template #default="{ $index }">
          <n-select
            v-model:value="followPlan.childTemplateVos[currentPlanIndex].taskTemplateCheck[$index].relationType"
            class="!w-170px 2xl:!w-280px"
            :disabled="isDisabled"
            :options="[{ label: '检查', value: 'JC' }, { label: '检验', value: 'CHECK' }]"
          />
        </template>
      </el-table-column>
      <el-table-column label="项目名称">
        <template #default="{ $index, row }">
          <n-select
            v-model:value="followPlan.childTemplateVos[currentPlanIndex].taskTemplateCheck[$index].relationId"
            :options=" row.relationType ? (row.relationType === 'CHECK' ? inspectList : imageCheckList) : []"
            :disabled="isDisabled"
            class="!w-170px 2xl:!w-280px"
            label-field="projectName"
            value-field="projectCode"
            :consistent-menu-width="false"
            @update:value="(val, option) => handleInspectUpdate(val, option, $index)"
          />
        </template>
      </el-table-column>
      <el-table-column label="自定义内容">
        <template #default="{ $index }">
          <n-input
            v-model:value="followPlan.childTemplateVos[currentPlanIndex].taskTemplateCheck[$index].showName"
            :disabled="isDisabled"
            class="!w-170px 2xl:!w-280px"
          />
        </template>
      </el-table-column>
      <el-table-column label="是否必填" width="100">
        <template #default="{ $index }">
          <n-checkbox
            v-model:checked="followPlan.childTemplateVos[currentPlanIndex].taskTemplateCheck[$index].isRequired"
            :checked-value="true"
            :unchecked-value="false"
            :disabled="isDisabled"
            relative top-5px
          >
            <span ml-10px>必填</span>
          </n-checkbox>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="100">
        <template #default="{ $index }">
          <div flex gap-10px>
            <SvgIcon :class="isDisabled ? 'op-30 cursor-not-allowed' : ''" cursor-pointer local-icon="slmc-icon-add1" size="16" @click="handleAddInspect" />
            <SvgIcon
              v-if="followPlan.childTemplateVos[currentPlanIndex].taskTemplateCheck!.length > 1"
              cursor-pointer local-icon="slmc-icon-delete1" size="16"
              :class="isDisabled ? 'op-30 cursor-not-allowed' : ''" @click="handleDeleteInspect($index)"
            />
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>
