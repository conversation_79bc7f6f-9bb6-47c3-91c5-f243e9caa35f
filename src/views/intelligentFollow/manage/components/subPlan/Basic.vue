<script setup lang="ts">
import type { FormValidationStatus } from 'wowjoy-vui/lib/form/src/interface'
import { FollowPlanKey } from '../../../type'
import { pointTimeVoMeta } from '../../metaData'

const followPlan = inject(FollowPlanKey)!
const currentPlanIndex = inject<Ref<number>>('currentPlanIndex')!
const basicInputState = inject<Ref<FormValidationStatus>>('basicInputState')

function handleAddCycle() {
  followPlan.value.childTemplateVos[currentPlanIndex.value].pointTimeVos!.push({
    ...pointTimeVoMeta,
  })
}

function handleDeleteCycle(index: number) {
  followPlan.value.childTemplateVos[currentPlanIndex.value].pointTimeVos!.splice(index, 1)
}

function handleExecuteUpdate(rangs: [string, string]) {
  if (!Number.isInteger(Number(rangs[0])))
    window.$message.warning('请输入正确的开始时间')

  else
    followPlan.value.childTemplateVos[currentPlanIndex.value].executeStart = rangs[0]

  if (!Number.isInteger(Number(rangs[1])))
    window.$message.warning('请输入正确的结束时间')
  else
    followPlan.value.childTemplateVos[currentPlanIndex.value].executeEnd = rangs[1]
}

function handleIsCycleUpdate() {
  followPlan.value.childTemplateVos[currentPlanIndex.value].pointTimeVos = [
    {
      ...pointTimeVoMeta,
    },
  ]
}

const isDisabled = inject<ComputedRef<boolean>>('isDisabled')

const description = computed(() => {
  if (followPlan.value.childTemplateVos[currentPlanIndex.value].planChildName === '基线')
    return '基线问卷与V1随访任务同时发送给患者'

  return followPlan.value.childTemplateVos[currentPlanIndex.value].fixSbDesign
})

const timeUnitOptions = [
  {
    label: '天',
    value: 'DAY',
  },
  {
    label: '周',
    value: 'WEEK',
  },
  {
    label: '月',
    value: 'MONTH',
  },
  {
    label: '年',
    value: 'YEAR',
  },
]
</script>

<template>
  <div mb-15px flex items-center gap-10px>
    <div sub-title />
    <div>{{ description ? '随访频率' : '项目设置' }}</div>
  </div>
  <div v-if="description" ml-14px>
    {{ description }}
  </div>
  <div v-else ml-14px flex="~ col gap-20px">
    <div flex gap-10px>
      <div relative top-4px flex flex-shrink-0>
        <SvgIcon
          local-icon="slmc-icon-app_required1"
          size="14"
          class="text-#F36969"
        />
        <div text="#666">
          任务类型
        </div>
      </div>
      <div w-full>
        <n-radio-group v-model:value="followPlan.childTemplateVos[currentPlanIndex].isCyclic" :disabled="isDisabled" @update:value="handleIsCycleUpdate">
          <n-radio :value="true" mr-40px>
            周期循环
          </n-radio>
          <n-radio :value="false">
            自定义
          </n-radio>
        </n-radio-group>
        <el-table :max-height="236" stripe class="!w-[calc(100%-20px)] 2xl:!w-[calc(100%-110px)]" mt-10px :data="followPlan.childTemplateVos[currentPlanIndex].pointTimeVos">
          <el-table-column label="序号" width="80" type="index" />

          <el-table-column v-if="followPlan.childTemplateVos[currentPlanIndex].isCyclic === true" label="周期天数">
            <template #default="{ $index }">
              <div flex items-center gap-10px>
                <n-input-group flex items-center>
                  <span mr-10px>每</span>
                  <n-input-number
                    v-model:value="followPlan.childTemplateVos[currentPlanIndex].pointTimeVos[$index].intervalNum"
                    :show-button="false"
                    :disabled="isDisabled"
                    :min="1"
                    class="!w-120px 2xl:!w-220px"
                    @blur="() => {
                      followPlan.childTemplateVos[currentPlanIndex].pointTimeVos[$index].intervalNum = Math.max(1, followPlan.childTemplateVos[currentPlanIndex].pointTimeVos[$index].intervalNum || 0)
                    }"
                  />
                  <n-select
                    v-model:value="followPlan.childTemplateVos[currentPlanIndex].pointTimeVos[$index].intervalUnit"
                    :options="timeUnitOptions"
                    :disabled="isDisabled"
                    class="mr-10px !w-80px"
                  />
                  <span>执行一次</span>
                </n-input-group>
              </div>
            </template>
          </el-table-column>
          <el-table-column v-if="followPlan.childTemplateVos[currentPlanIndex].isCyclic === false" label="加入计划第几天">
            <template #default="{ $index }">
              <div flex items-center gap-10px>
                <n-input-group flex items-center>
                  <span mr-10px>第</span>
                  <n-input-number
                    v-model:value="followPlan.childTemplateVos[currentPlanIndex].pointTimeVos[$index].intervalNum"
                    :show-button="false"
                    :disabled="isDisabled"
                    :min="0"
                    class="!w-120px 2xl:!w-220px"
                    @blur="() => {
                      followPlan.childTemplateVos[currentPlanIndex].pointTimeVos[$index].intervalNum = Math.max(0, followPlan.childTemplateVos[currentPlanIndex].pointTimeVos[$index].intervalNum || 0)
                    }"
                  />
                  <n-select
                    v-model:value="followPlan.childTemplateVos[currentPlanIndex].pointTimeVos[$index].intervalUnit"
                    :options="timeUnitOptions"
                    :disabled="isDisabled"
                    class="mr-10px !w-80px"
                  />
                  <span>执行</span>
                </n-input-group>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template #default="{ $index }">
              <div flex gap-10px :class="isDisabled ? 'op30' : ''">
                <SvgIcon
                  :class="isDisabled ? 'cursor-not-allowed' : 'cursor-pointer'" local-icon="slmc-icon-add1" size="16" @click="() => {
                    if (isDisabled)
                      return false
                    handleAddCycle()
                  }"
                />
                <SvgIcon
                  v-if="followPlan.childTemplateVos[currentPlanIndex].pointTimeVos!.length > 1"
                  :class="isDisabled ? 'cursor-not-allowed' : 'cursor-pointer'" local-icon="slmc-icon-delete1" size="16"
                  @click="() => {
                    if (isDisabled)
                      return false

                    handleDeleteCycle($index)
                  }"
                />
              </div>
            </template>
          </el-table-column>
          <template #empty>
            <div flex justify-center>
              <DataEmpty />
            </div>
          </template>
        </el-table>
      </div>
    </div>

    <div h-32px flex items-center gap-10px>
      <div relative flex flex-shrink-0>
        <SvgIcon
          local-icon="slmc-icon-app_required1"
          size="14"
          class="text-#F36969"
        />
        <div text="#666">
          执行时间
        </div>
      </div>
      <n-radio-group v-model:value="followPlan.childTemplateVos[currentPlanIndex].executeType" disabled>
        <n-radio value="LONG_TERM" mr-40px>
          全部时段
        </n-radio>
        <n-radio value="CUSTOM">
          自定义
        </n-radio>
      </n-radio-group>
      <div v-if="followPlan.childTemplateVos[currentPlanIndex].executeType === 'CUSTOM'" flex>
        <div flex items-center>
          <SvgIcon
            local-icon="slmc-icon-app_required1"
            size="14"
            class="text-#F36969"
          />
          <div text="#666">
            加入计划后第
          </div>
        </div>
        <n-input
          :value="[followPlan.childTemplateVos[currentPlanIndex].executeStart, followPlan.childTemplateVos[currentPlanIndex].executeEnd]"
          class="ml-10px !w-180px"
          pair :disabled="isDisabled"
          :status="basicInputState"
          separator="~"
          placeholder="请输入"
          @update:value="handleExecuteUpdate"
        >
          <template #suffix>
            <span text="#999">
              天
            </span>
          </template>
        </n-input>
      </div>
    </div>

    <div flex items-center gap-10px>
      <div text="#666" ml-14px flex-shrink-0>
        提醒时间
      </div>
      <n-time-picker v-model:formatted-value="followPlan.childTemplateVos[currentPlanIndex].remindTime" :disabled="isDisabled" w-200px />
    </div>
  </div>
</template>
