<script setup lang="ts">
withDefaults(defineProps<{
  state: number
}>(), {
  state: 2,
})

const statusBg: Record<string, string> = {
  3: 'bg-#4ACFB1',
  2: 'bg-#FF9B54',
  1: 'bg-#F36969',
}

const statusText: Record<string, string> = {
  3: '启动中',
  2: '待开始',
  1: '已暂停',
}
</script>

<template>
  <div :class="statusBg[state || 2]" text="center white 12px" h-20px w-56px flex-center b-rd-10px>
    {{ statusText[state || 2] }}
  </div>
</template>
