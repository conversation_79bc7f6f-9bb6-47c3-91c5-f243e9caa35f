<script setup lang="ts">
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import { BasicModal } from '@/components/Modal'
import EDU from '@/views/specificDisease/patientDetail/components/followManage/components/Education.vue'
import SCALE from '@/views/specificDisease/patientDetail/components/followManage/components/Scale.vue'
import {
  addFollowRecordInfo, deleteFollowRecord, finishFollowRecord, getFollowRecordDetail,
  getFollowRecordList, updateFollowRecordInfo,
} from '@/api/intelligentFollow/record'
import { getEduListAPI, getSurveyListAPI } from '@/api/toolsFollow'
import { useAuthStore } from '@/store'
import { getPlanPhaseAPI } from '@/api/intelligentFollow/plan'

const planTemplateName = ref('')

defineExpose({
  planTemplateName,
})
const userInfo = useAuthStore().userInfo
dayjs.extend(utc)
dayjs.extend(timezone)
const editRemark = ref(false)
const route = useRoute()

const patientId = route.query.patientId as string

const edutList = ref()
const surveyList = ref()
const currentFollowIndex = ref(0) // 选中的一级
const currentRecordIndex = ref(0) // 选中的二级
const currentFollow = ref()
const dataList = ref()
const folllowList = ref()
const isLoading = ref(false)

provide('isLoading', isLoading)
const currentTask = reactive({
  projectType: undefined,
  taskId: undefined,
  projectId: undefined,
  checkInspect: undefined,
  status: undefined,
})

const taskDetailComponentMap = {
  EDU,
  SCALE,
}
/// 新增随访记录
const showAdd = ref(false)
const followTypeOptions = [
  {
    label: '问卷调查',
    value: 'SCALE',
  },
  {
    label: '健康宣教',
    value: 'EDU',
  }, {
    label: '随访提醒',
    value: 'REMIND_FOLLOW',
  }, {
    label: '到院复查',
    value: 'REMIND_REVIEW',
  }, {
    label: '下次随访安排',
    value: 'NEXT_FOLLOW',
  }, {
    label: '项目介绍',
    value: 'INTRO',
  },
]
const statusOption = [
  {
    label: '未开始',
    value: 'NO_START',
  },
  {
    label: '未完成',
    value: 'NO_FINISH',
  }, {
    label: '已完成',
    value: 'FINISH',
  },
]
const sendTypeOption = [
  { label: '肝愈小程序', value: 'XCX' }, { label: '电话', value: 'DH' }, { label: '短信', value: 'DX' },
]
const addSubject = reactive({
  recordType: undefined,
  scaleEduId: undefined,
  scaleEduName: undefined,
  recordContent: undefined,
  syncPatient: 1, // 是否同步患者:1是，2否
  syncType: undefined,
  followDate: dayjs().format('YYYY-MM-DD'),
  memo: undefined,
})

function handleBodyConfirm() {
  if (!addSubject.recordType) {
    window.$message.warning('请选择随访类型!')
    return
  }
  if (addSubject.recordType === 'SCALE' || addSubject.recordType === 'EDU') {
    if (addSubject.recordType === 'SCALE')
      addSubject.scaleEduName = surveyList.value.find((item: any) => item.scaleId === addSubject.scaleEduId)?.scaleName

    else
      addSubject.scaleEduName = edutList.value.find((item: any) => item.educationId === addSubject.scaleEduId)?.title

    if (!addSubject.scaleEduId) {
      window.$message.warning(`请选择${addSubject.recordType === 'SCALE' ? '随访问卷' : '健康宣教'}!`)
      return
    }
  }
  else if (addSubject.recordType !== 'INTRO') {
    if (!addSubject.recordContent) {
      window.$message.warning('请输入随访内容!')
      return
    }
  }

  showAdd.value = false
  const params = {
    patientId,
    planId: route.query.planId,
    creator: userInfo?.userName,
    ...addSubject,
  }
  console.log('请求参数======', addSubject)
  // 新增记录
  addFollowRecordInfo(params).then((res: any) => {
    if (res.data) {
      window.$message.success('新增成功')
      addSubject.recordType = undefined
      addSubject.scaleEduId = undefined
      addSubject.recordContent = undefined
      addSubject.syncPatient = 1
      addSubject.syncType = undefined
      addSubject.followDate = dayjs().format('YYYY-MM-DD')
      addSubject.memo = undefined
      getFollowRecordDataList(true)
    }
  })
}

function getFollowRecordDataList(isInit) {
  getFollowRecordList({ patientId }).then((res: any) => {
    console.log('数据=====', res)
    dataList.value = res.data
    if (dataList.value.length > 0) {
      folllowList.value = [{ recordName: '随访日记' }, ...dataList.value[currentFollowIndex.value].dtlList]
      if (isInit && currentFollow.value) {
        /// 设置一下默认的第一个选中数据,找 currentFollow对应的 index 就行
        dataList.value.forEach((item, index) => {
          if (item.recordId === currentFollow.value.recordId)
            currentFollowIndex.value = index
        })
      }
    }
  })
}

function changeFollowDate(index) {
  currentFollowIndex.value = index
  currentRecordIndex.value = 0
  folllowList.value = [{ recordName: '随访日记' }, ...dataList.value[index].dtlList]
}

// 提交问卷成功
function commitSuverySuccess() {
  console.log('提交问卷成功')
  getFollowRecordDataList(false)
  changeRecordDetail(currentRecordIndex.value)
}

function changeRecordDetail(index) {
  if (currentRecordIndex.value !== index)
    currentRecordIndex.value = index

  if (index !== 0) {
    const item = folllowList.value[index]
    getFollowRecordDetail({ recordId: item.recordId }).then((res: any) => {
      currentFollow.value = res.data.recordInfo
      if (item.recordType === 'EDU') {
        // 处理宣教和问卷的逻辑
        currentTask.projectType = currentFollow.value.recordType
        currentTask.taskId = currentFollow.value.scaleEduId
        currentTask.projectId = currentFollow.value.scaleEduId
        currentTask.status = currentFollow.value.recordStatus
      }
      else if (item.recordType === 'SCALE') {
        currentTask.projectType = currentFollow.value.recordType
        currentTask.taskId = currentFollow.value.recordId
        currentTask.projectId = res.data.scale.scaleEduId
        currentTask.status = currentFollow.value.recordStatus
      }
    })
  }
}

function saveEditData() {
  updateFollowRecordInfo(currentFollow.value).then((res: any) => {
    console.log('res====', res)
    window.$message.success('更新备注成功')
  })
  editRemark.value = false
}

function finishFollowRecordData() {
  finishFollowRecord({ recordId: currentFollow.value.recordId }).then((res) => {
    console.log('已变更随访记录为已完成!')
    window.$message.success('已变更随访记录为已完成!')
    getFollowRecordDataList(false)
    getFollowRecordDetail({ recordId: currentFollow.value.recordId }).then((res: any) => {
      currentFollow.value = res.data.recordInfo
    })
  })
}

function deleteFollowRecordData() {
  // 删除该任务
  deleteFollowRecord({ recordId: currentFollow.value.recordId }).then((res) => {
    console.log('删除更随访记录为已完成!')
    window.$message.success('删除随访记录成功!')
    currentFollow.value = null
    currentRecordIndex.value = 0
    getFollowRecordDataList(false)
  })
}

async function getOptionsData() {
  getEduListAPI().then((res) => {
    edutList.value = res.data
  })

  getSurveyListAPI('').then((res) => {
    surveyList.value = res.data?.slice(0, 6)?.map((item: any) => {
      return {
        ...item,
        isActive: false,
      }
    })
  })
}

function updateTypeChange() {
  addSubject.scaleEduId = undefined
  addSubject.scaleEduName = undefined
}

onMounted(async () => {
  getFollowRecordDataList(false)
  getOptionsData()
  getPlanPhaseAPI(patientId).then((res) => {
    if (res.data)
      planTemplateName.value = res.data[0].planTemplateName
  })
})
</script>

<template>
  <n-spin type="uni" :show="isLoading">
    <div h-full flex>
      <div flex-shrink-0 overflow-auto pb-14px class="h-[calc(100vh-255px)]">
        <div mb-10px w-220px flex justify-between pr-14px>
          <div flex gap-10px>
            <div sub-title />
            随访记录
          </div>
          <div text="#3B8FD9" cursor-pointer @click="showAdd = true">
            新增
          </div>
        </div>
        <n-timeline :icon-size="10">
          <n-timeline-item v-for="(item, index) in dataList" :key="index">
            <template #header>
              <div flex justify-between pr-14px>
                <span text="#666 12px">
                  {{ item.followDt }}
                </span>
                <div text="#999 12px">
                  {{ item.recordStatus || '-' }}
                </div>
              </div>
            </template>
            <div flex="~ col" gap-14px>
              <div class="card-content" :class="{ 'i-card-active': currentFollowIndex === index }" @click="changeFollowDate(index)">
                <div>合&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;计 {{ item.allNum }}（<span text="#06AEA6">{{ item.allFinishNum }}</span>+<span text="#F36969">{{ item.allNoFinishNum }}</span>）</div>
                <div>问卷调查 {{ item.scaleNum }}（<span text="#06AEA6">{{ item.scaleFinishNum }}</span>+<span text="#F36969">{{ item.scaleNoFinishNum }}</span>）</div>
                <div>健康宣教 {{ item.eduNum }}（<span text="#06AEA6">{{ item.eduFinishNum }}</span>+<span text="#F36969">{{ item.eduNoFinishNum }}</span>）</div>
                <div>随访提醒 {{ item.remindNum }}（<span text="#06AEA6">{{ item.remindFinishNum }}</span>+<span text="#F36969">{{ item.remindNoFinishNum }}</span>）</div>
                <div>其&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;他 {{ item.otherNum }}（<span text="#06AEA6">{{ item.otherFinishNum }}</span>+<span text="#F36969">{{ item.otherNoFinishNum }}</span>）</div>
              </div>
            </div>
            <template #icon>
              <SvgIcon local-icon="slmc-icon-dian" />
            </template>
          </n-timeline-item>
        </n-timeline>
      </div>
      <div class="h-[calc(100vh-255px)]" mr-14px w-1px flex-shrink-0 bg="#ccc" />
      <template v-if="folllowList && folllowList.length > 0">
        <div w-full flex>
          <div w-205px flex-shrink-0 overflow-auto pb-14px class="h-[calc(100vh-255px)]">
            <div flex="~ col" gap-14px overflow-auto>
              <div v-for="(item1, i) in folllowList" :key="i" class="card-content" :class="{ 'i-card-active': currentRecordIndex === i }" @click="changeRecordDetail(i)">
                <div>{{ item1.recordName }}</div>
                <div v-if="i > 0" flex justify-between>
                  <div mt-10px text-12px color="#999">
                    {{ followTypeOptions.find(item => item.value === item1.recordType)?.label }}
                  </div>
                  <span bg="#F1FCFB" color="#06AEA6" mt-5px px-5px py-5px>{{ statusOption.find(item => item.value === item1.recordStatus)?.label || '-' }}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="h-[calc(100vh-255px)]" mr-14px w-1px flex-shrink-0 bg="#ccc" />
          <div v-if="currentRecordIndex === 0" w-full overflow-auto class="h-[calc(100vh-255px)]">
            随访日记
            <div v-for="(item2, index) in dataList[currentFollowIndex].diaryList" :key="index">
              <div bg="#e5e5e5" mr-14px mt-14px w-full p-14px>
                <div flex>
                  <div mr-10px>
                    {{ followTypeOptions.find(item => item.value === item2.recordType)?.label || '-' }}
                  </div>
                  <div flex-1>
                    {{ item2.recordName }}
                  </div>
                  <div>{{ statusOption.find(item => item.value === item2.recordStatus)?.label || '-' }}</div>
                </div>
                <div my-10px>
                  {{ item2.memo || '-' }}
                </div>
                <div flex>
                  <div mr-30px>
                    {{ `记录人: ${item2.creator || '-'}` }}
                  </div>
                  <div>{{ `创建时间: ${item2.createTime ? dayjs(item2.createTime).tz(dayjs.tz.guess()).format('YYYY-MM-DD HH:mm:ss') : '-'}` }}</div>
                </div>
              </div>
            </div>
          </div>
          <div v-else w-full>
            <div mr-14px mt-14px w-full p-14px>
              <div flex>
                <div mr-10px>
                  {{ currentFollow?.recordType ? followTypeOptions.find(item => item.value === currentFollow?.recordType)?.label : '' }}
                </div>
                <div mt-2px flex-1 text-12px>
                  {{ sendTypeOption.find(item => item.value === currentFollow?.syncType)?.label || '-' }}
                </div>
                <n-button v-if="currentFollow?.recordStatus !== 'FINISH' && currentFollow?.recordType === 'SCALE'" type="primary" ghost color="#3B8FD9" mr-14px mt--8px w-80px @click="deleteFollowRecordData">
                  删除
                </n-button>
                <div>{{ statusOption.find(item => item.value === currentFollow?.recordStatus)?.label || '-' }}</div>
              </div>
              <div my-10px flex>
                <div w-50px>
                  备注：
                </div>
                <div v-if="!editRemark">
                  {{ currentFollow?.memo || '-' }}
                </div>
                <el-input
                  v-else
                  v-model="currentFollow.memo"
                  flex-1
                  :rows="2"
                  type="textarea"
                  placeholder="暂无备注"
                />
              </div>
              <div flex>
                <div mr-30px>
                  记录人: {{ currentFollow?.creator || '-' }}
                </div>
                <div flex-1>
                  创建时间: {{ currentFollow?.createTime ? dayjs(currentFollow?.createTime).tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss') : '-' }}
                </div>
                <SvgIcon v-if="!editRemark" class="ml-10px cursor-pointer" local-icon="slmc-icon-edit1" size="16" @click="editRemark = true" />
                <div v-else color="#3B8FD9" @click="saveEditData">
                  保存
                </div>
              </div>
            </div>
            <div mr-14px h-1px w-full flex-shrink-0 bg="#ccc" />

            <!-- 详情内容 -->
            <div mt-15px :class="(currentFollow?.recordStatus !== 'FINISH' && currentFollow?.recordType !== 'SCALE') ? 'h-[calc(100vh-410px)]' : 'h-[calc(100vh-380px)]'" overflow-auto>
              <component
                :is="taskDetailComponentMap[currentTask.projectType]"
                v-if="(currentFollow?.recordType === 'SCALE' || currentFollow?.recordType === 'EDU') && currentTask?.projectType"
                :task-id="currentTask.taskId"
                :task-status="currentTask.status"
                :project-id="currentTask.projectId"
                :project-type="currentTask.projectType"
                :check-inspect="currentTask.checkInspect"
                @commit-success="commitSuverySuccess"
              />
              <div v-else>
                <div>随访提醒内容</div>
                <div mt-14px flex flex-1 justify-between>
                  <!-- <div>提交时间：2023-03-20 19:29:00</div>
                  <div>填写人：自填写/医护填写</div> -->
                  <div>
                    随访完成时间：{{ currentFollow?.finishDate ? dayjs(currentFollow?.finishDate).tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss') : '-' }}
                  </div>
                  <div>
                    填写人：{{ currentFollow?.finishUser ? (currentFollow?.finishUser === 'DOCTOR' ? '医护填写' : '自填写') : '-' }}
                  </div>
                </div>

                <div mt-20px line-height-18px>
                  {{ currentFollow?.recordContent || '-' }}
                </div>
              </div>
            </div>
            <div v-if="currentFollow?.recordStatus !== 'FINISH' && currentFollow?.recordType !== 'SCALE'" w-full flex items-center justify-center>
              <n-button v-if="currentFollow?.recordStatus !== 'FINISH'" type="primary" color="#3B8FD9" mr-14px mt--8px @click="finishFollowRecordData">
                已完成
              </n-button>
              <n-button type="primary" ghost color="#3B8FD9" mr-14px mt--8px @click="deleteFollowRecordData">
                删除
              </n-button>
            </div>
          </div>
        </div>
      </template>
      <template v-else>
        <div class="h-[calc(100vh-255px)]" flex flex-1 items-center justify-center>
          <n-empty size="large" description="无数据" />
        </div>
      </template>
    </div>

    <BasicModal
      :visible="showAdd"
      title="新增随访记录"
      width="560"
      :footer-offset="150"
      @ok="handleBodyConfirm"
      @cancel="showAdd = false"
    >
      <div p-20px>
        <div flex>
          <div mt-8px w-90px>
            随访类型：
          </div>
          <n-select v-model:value="addSubject.recordType" :options="followTypeOptions" filterable @update:value="updateTypeChange" />
        </div>
        <div v-if="addSubject.recordType === 'SCALE' || addSubject.recordType === 'EDU'" mt-10px flex>
          <div mt-8px w-90px>
            {{ addSubject.recordType === 'SCALE' ? '随访问卷：' : '健康宣教：' }}
          </div>
          <n-select
            v-if="addSubject.recordType === 'EDU'" v-model:value="addSubject.scaleEduId" :options="edutList" value-field="educationId" label-field="title"
            filterable
          />
          <n-select
            v-if="addSubject.recordType === 'SCALE'" v-model:value="addSubject.scaleEduId" :options="surveyList" value-field="scaleId" label-field="scaleName"
            filterable
          />
        </div>
        <div v-if="addSubject.recordType === 'REMIND_REVIEW' || addSubject.recordType === 'REMIND_FOLLOW' || addSubject.recordType === 'NEXT_FOLLOW' || addSubject.recordType === 'INTRO'" mt-10px flex>
          <div mt-8px w-90px>
            {{ addSubject.recordType === 'REMIND_REVIEW' ? '提醒内容：' : '随访内容：' }}
          </div>
          <el-input
            v-model="addSubject.recordContent"
            flex-1
            :rows="3"
            type="textarea"
            placeholder="请输入"
          />
        </div>
        <div v-if="addSubject.recordType !== 'NEXT_FOLLOW'" mt-12px>
          <n-radio
            :checked="addSubject.syncPatient === 1"
            :value="1"
            name="是否发送到患者"
          >
            是否发送到患者
          </n-radio>

          <div mt-12px flex>
            <div mt-3px w-90px>
              发送方式：
            </div>
            <n-radio-group v-model:value="addSubject.syncType" name="radiogroup">
              <n-space item-style="display: flex;" :size="[40, 0]">
                <n-radio v-for="item in sendTypeOption" :key="item.value" :value="item.value">
                  {{ item.label }}
                </n-radio>
              </n-space>
            </n-radio-group>
          </div>
        </div>
        <div mt-10px flex>
          <div mt-8px w-90px>
            随访时间：
          </div>
          <n-date-picker
            v-model:formatted-value="addSubject.followDate"
            value-format="yyyy-MM-dd"
            type="date"
            clearable w-full
          />
        </div>
        <div mt-10px flex>
          <div mt-8px w-90px>
            备注说明：
          </div>
          <el-input
            v-model="addSubject.memo"
            flex-1
            :rows="3"
            type="textarea"
            placeholder="请输入"
          />
        </div>
      </div>
    </BasicModal>
  </n-spin>
</template>

<style scoped>
:deep(.n-timeline .n-timeline-item .n-timeline-item-timeline .n-timeline-item-timeline__circle){
  border:none
}

.card-content{
  --uno: w-183px cursor-pointer b-1 b-rd-3px p-10px bg-#f5f5f5 b-#f5f5f5;

  &:hover {
    --uno: bg-#fff b-1px b-#06AEA6;
  }
}

.i-card-active {
  --uno: bg-#F1FCFB b-1px b-#06AEA6 cursor-not-allowed;

  &:hover {
    --uno: bg-#F1FCFB b-1px b-#06AEA6;
  }
}
</style>
