<script setup lang="ts">
import type { FormValidationStatus } from 'wowjoy-vui/lib/form/src/interface'
import { cloneDeep } from 'lodash-es'
import type { FollowPlan } from '../type'
import { FollowPlanKey } from '../type'
import { useTemplate } from '../useTemplate'
import { useTabs } from './useTabs'
import { Breadcrumb } from '@/layouts/common'
import PageFooter from '@/components/PageFooter/index.vue'
import { editPlanDetailApi, getPlanDetailApi, saveToNewTemplate, updatePlanStateAPI } from '@/api/intelligentFollow/manage'
import { useAuthStore } from '@/store'
import AuthRange from '@/views/toolsFollow/components/AuthRange.vue'

const router = useRouter()
const { userInfo } = useAuthStore()
const { planLabels, getLabel } = useTemplate(userInfo.id)
const { currentTab, componentMap } = useTabs()

getLabel()

const route = useRoute()
const planId = route.query.id as string
const restore = route.query.restore as string
const seriesId = route.query.seriesId as string
const relationName = route.query.relationName as string
const createAt = route.query.createAt as string
const type = route.query.type as string
const mode = route.query.mode as string | undefined

const followPlan = ref<FollowPlan>({
  planTemplateName: '',
  planTemplateId: planId,
  planCycle: 'LONG_TREM',
  cycleType: 'YEAR',
  remindSwitch: false,
  lockSwitch: false,
  beforeOpenSwitch: false,
  followMethods: [],
  followMethod: '',
  cycleValue: null,
  createName: '',
  createId: userInfo.id,
  updateId: userInfo.id,
  updateName: userInfo.userName,
  routeCycleDay: 1,
  noFinishRemind: 0,
  noFinishLock: 0,
  beforeOpenDays: 0,
  routeCycleUnit: 'YEAR',
  planTemplateType: '',
  relations: [],
  relationIds: [],
  tempState: 2,
  newTempState: 2,
  childTemplateVos: [],
})

const currentPlanIndex = ref(-1)
provide('currentPlanIndex', currentPlanIndex)
provide(FollowPlanKey, followPlan)

const isLoading = ref(false)
const componentRef = ref<any>(null)

async function getPlanDetail() {
  try {
    isLoading.value = true
    const { data } = await getPlanDetailApi(planId)
    if (data) {
      followPlan.value.planTemplateName = data?.planTemplateName
      followPlan.value.newPlanTemplateName = data?.planTemplateName
      followPlan.value.planCycle = data?.planCycle || 'LONG_TREM'
      followPlan.value.cycleType = data?.cycleType || 'YEAR'
      followPlan.value.cycleValue = data?.cycleValue
      followPlan.value.routeCycleDay = data?.routeCycleDay || 1
      followPlan.value.noFinishRemind = data?.noFinishRemind
      followPlan.value.noFinishLock = data?.noFinishLock
      followPlan.value.beforeOpenDays = data?.beforeOpenDays
      followPlan.value.routeCycleUnit = data?.routeCycleUnit || 'YEAR'
      followPlan.value.tempState = data?.tempState || 2

      followPlan.value.newTempState = cloneDeep(data?.tempState || 2)
      followPlan.value.createName = data?.createName
      followPlan.value.planTemplateType = data?.planTemplateType

      followPlan.value.followMethod = (data?.followMethod || 'XCX')
      followPlan.value.followMethods = (data?.followMethod || 'XCX').split(',')

      if (data?.noFinishLock)
        followPlan.value.lockSwitch = true

      if (data?.noFinishRemind)
        followPlan.value.remindSwitch = true

      if (data?.beforeOpenDays)
        followPlan.value.beforeOpenSwitch = true

      if (!data?.childTemplateVos?.length)
        componentRef.value?.handleAddChildPlan()

      else
        followPlan.value.childTemplateVos = data?.childTemplateVos

      followPlan.value.relations = data?.relations || []
      followPlan.value.relationIds = (data?.relations || []).map(item => item.userId)

      if (data.childTemplateVos?.length)
        currentPlanIndex.value = 0
    }
  }
  catch (error) {

  }
  finally {
    isLoading.value = false
  }
}

const basicInputState = ref<FormValidationStatus>()
provide('basicInputState', basicInputState)

async function handlePlanSave() {
  try {
    if (followPlan.value.planTemplateType !== 'SBM') {
      if (!followPlan.value.childTemplateVos?.length) {
        window.$message.warning('请至少添加一个子计划')
        currentTab.value = 'PlanSetting'
        return false
      }

      for (let i = 0; i < followPlan.value.childTemplateVos.length; i++) {
        if (followPlan.value.childTemplateVos[i].executeType === 'CUSTOM') {
          if (!followPlan.value.childTemplateVos[i].executeEnd || !followPlan.value.childTemplateVos[i].executeStart) {
            window.$message.warning('请填写执行时间')
            currentPlanIndex.value = i
            basicInputState.value = 'error'
            currentPlanIndex.value = i
            currentTab.value = 'PlanSetting'
            return false
          }

          if (Number(followPlan.value.childTemplateVos[i].executeStart) > Number(followPlan.value.childTemplateVos[i].executeEnd)) {
            window.$message.warning('开始时间不能大于结束时间')
            currentPlanIndex.value = i
            basicInputState.value = 'error'
            currentPlanIndex.value = i
            currentTab.value = 'PlanSetting'
            return false
          }
        }

        if (!followPlan.value.childTemplateVos[i].taskTemplateCheck?.length
            && !followPlan.value.childTemplateVos[i].taskTemplateSales?.length
            && !followPlan.value.childTemplateVos[i].taskTemplateEdu?.length
        ) {
          window.$message.warning('请至少完善一项随访任务')
          currentPlanIndex.value = i
          currentTab.value = 'PlanSetting'
          return false
        }
      }

      basicInputState.value = undefined
    }

    const saveFormData = cloneDeep(followPlan.value)
    saveFormData.planTemplateName = saveFormData.newPlanTemplateName || ''
    saveFormData.tempState = saveFormData.newTempState
    const { data } = await editPlanDetailApi(saveFormData)
    if (data) {
      window.$message.success('保存成功')
      router.push({
        path: '/intelligentFollow/manage',
      })
    }
  }
  catch (error) {
    console.log(error)
  }
}

async function handlePausePlan() {
  try {
    const { data } = await updatePlanStateAPI({
      planTmpId: planId,
      tmpState: 1,
    })

    if (data) {
      followPlan.value.tempState = 1
      followPlan.value.newTempState = 1
      window.$message.success('暂停成功')
    }
  }
  catch (error) {

  }
}

const relationsRange = ref<any>([])
const visibleRange = ref('ALL_USER')
const labelId = ref(null)
const newPlanTemplateName = ref('')
const isSaveTemplateModalShow = ref(false)

function handleSaveTemplate() {
  relationsRange.value = []
  visibleRange.value = 'ALL_USER'
  isSaveTemplateModalShow.value = true
}

async function handleSaveTemplateConfirm() {
  try {
    isLoading.value = true
    if (!newPlanTemplateName.value) {
      window.$message.warning('请填写随访模版名称')
      return false
    }

    if (!labelId.value) {
      window.$message.warning('请选择模版分类')
      return false
    }

    const formData = cloneDeep(followPlan.value) as any

    formData.planTemplateType = 'SMB'
    formData.visibleRange = visibleRange.value
    formData.relationsRange = relationsRange.value
    formData.planTemplateName = newPlanTemplateName.value
    formData.labelId = labelId.value
    const { data } = await saveToNewTemplate(formData)
    if (data) {
      window.$message.success('保存模版成功')
      isSaveTemplateModalShow.value = false
    }
    else {
      window.$message.error('保存模版失败')
    }
  }
  catch (error) {
    window.$message.error('保存模版失败')
  }
  finally {
    isLoading.value = false
  }
}

function handleCancel() {
  window.$dialog.warning({
    title: '确定离开此页面？',
    content: '当前设置内容未保存，请确认是否离开',
    positiveText: '确定',
    negativeText: '取消',
    btnGhost: true,
    onPositiveClick: () => {
      router.go(-1)
    },
  })
}

const isAlertShow = computed(() => {
  return ['PlanSetting', 'BaseSetting'].includes(currentTab.value) && followPlan.value?.tempState === 3 && followPlan.value.planTemplateType !== 'SBF'
})

const isDisabled = computed(() => {
  return followPlan.value.tempState === 3 || mode === 'view'
})

provide('isDisabled', isDisabled)

onMounted(() => {
  const followPlanStoreInfo = window.localStorage.getItem('followPlanInfo')

  if (restore && followPlanStoreInfo) {
    const storeInfo = JSON.parse(followPlanStoreInfo)
    followPlan.value = storeInfo.followPlan
    currentPlanIndex.value = storeInfo.currentPlanIndex
    if (seriesId) {
      if (Number.isInteger(storeInfo?.surveyIndex)) {
        followPlan.value.childTemplateVos[currentPlanIndex.value].taskTemplateSales[storeInfo.surveyIndex].relationName = decodeURIComponent(relationName)
      }
      else {
        if (followPlan.value.childTemplateVos[currentPlanIndex.value].taskTemplateSales.map(item => item.relationName).includes(decodeURIComponent(relationName))) {
          window.$message.warning('问卷名称不能重复')
          return false
        }

        followPlan.value.childTemplateVos[currentPlanIndex.value].taskTemplateSales.push({
          relationId: seriesId,
          relationName: decodeURIComponent(relationName),
          recoveryNum: 0,
          createTime: createAt,
          isDefaultScale: type === 'SYSTEM',
        })
      }
    }
  }
  else {
    getPlanDetail()
  }

  window.localStorage.removeItem('followPlanInfo')
})
</script>

<template>
  <n-spin type="uni" :show="isLoading" size="medium">
    <div>
      <Breadcrumb
        :bread-list="[
          {
            title: '智能随访',
            link: null,
            key: 'intelligentFollow',
          },
          {
            title: '随访管理',
            link: '/intelligentFollow/manage',
            key: 'intelligentFollow_manage',
          },
          {
            title: followPlan.planTemplateName,
            link: null,
            key: 'intelligentFollow_manage_create',
          },
        ]"
      />
      <n-alert v-show="isAlertShow" relative top-14px mx-14px type="warning">
        已启动的随访计划请暂停后编辑！
        <template #suffix>
          <n-button size="small" type="primary" @click="handlePausePlan">
            暂停计划
          </n-button>
        </template>
      </n-alert>
      <PageCard :custom-h="isAlertShow ? 42 : 0" breadcrumb padding="14px 0px 0px 0px">
        <div pl-14px>
          <PageTitle>
            {{ followPlan.planTemplateName }}
          </PageTitle>

          <n-tabs v-model:value="currentTab" :width="120">
            <n-tab-pane name="PlanSetting" tab="随访任务设置" />
            <n-tab-pane name="BaseSetting" tab="项目设置" />
            <n-tab-pane v-if="followPlan.planTemplateType !== 'SBM' && mode !== 'view'" name="Patient" tab="患者管理" />
          </n-tabs>
        </div>

        <component :is="componentMap[currentTab]" ref="componentRef" />

        <PageFooter v-show="currentTab !== 'Patient' && !isDisabled">
          <template #default>
            <n-button v-if="currentTab === 'PlanSetting'" w-100px type="primary" @click="currentTab = 'BaseSetting'">
              下一步
            </n-button>
            <n-button v-if="currentTab === 'BaseSetting'" w-100px type="primary" @click="handlePlanSave">
              保 存
            </n-button>
            <n-button secondary w-112px type="primary" @click="handleSaveTemplate">
              另存为新模板
            </n-button>
            <n-button secondary w-100px @click="handleCancel">
              取 消
            </n-button>
          </template>
        </PageFooter>
      </PageCard>

      <n-modal
        v-model:show="isSaveTemplateModalShow"
        preset="card" :style="{ width: '720px' }"
        title="存为新模版" head-style="divide"
      >
        <div py-20px>
          <div flex="~ col" gap-14px>
            <div flex items-center gap-10px>
              <div w-100px flex items-center>
                <SvgIcon local-icon="slmc-icon-app_required1" size="14" class="text-#F36969" />
                <div text="#666">
                  随访模版名称
                </div>
              </div>
              <n-input v-model:value="newPlanTemplateName" class="!w-540px" show-count :maxlength="20" />
            </div>

            <div flex items-center gap-10px>
              <div w-100px flex items-center justify-end>
                <SvgIcon local-icon="slmc-icon-app_required1" size="14" class="text-#F36969" />
                <div text="#666">
                  模版分类
                </div>
              </div>
              <n-select
                v-model:value="labelId"
                label-field="labelName"
                value-field="labelId"
                :options="planLabels"
                class="!w-540px"
              />
            </div>

            <div flex items-center gap-10px>
              <div w-100px flex items-center justify-end>
                <SvgIcon local-icon="slmc-icon-app_required1" size="14" class="text-#F36969" />
                <div text="#666">
                  可见范围
                </div>
              </div>
              <AuthRange v-model:relationsRange="relationsRange" v-model:visibleRange="visibleRange" />
            </div>

            <div ml-110px>
              <n-button mr-16px w-100px type="primary" @click="handleSaveTemplateConfirm">
                保 存
              </n-button>
              <n-button w-100px @click="isSaveTemplateModalShow = false">
                取 消
              </n-button>
            </div>
          </div>
        </div>
      </n-modal>
    </div>
  </n-spin>
</template>
