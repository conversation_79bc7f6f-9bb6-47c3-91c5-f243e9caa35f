import type { ChildTemplateVO, PlanQuata, pointTimeVo, taskTemplateVo } from '../type'

export const pointTimeVoMeta: pointTimeVo = {
  intervalNum: 1,
  intervalUnit: 'DAY',
}

export const taskTemplateCheckMeta: taskTemplateVo = {
  relationId: null,
  relationName: '',
  relationType: null,
  showName: '',
  isRequired: false,
}

export const quotaDetailMeta: PlanQuata = {
  compareType: null,
  condValueOne: '',
  condValueTwo: '',
  warningIdList: [],
}

export const taskTemplateIndexMeta: taskTemplateVo = {
  relationId: null,
  relationName: '',
  isRequired: false,
  planQuatas: [
    {
      ...quotaDetailMeta,
    },
  ],
}

export function childTemplateMeta(name: string): ChildTemplateVO {
  return {
    executeType: 'LONG_TERM',
    executeStart: '',
    executeEnd: '',
    remindTime: '09:00:00',
    isCyclic: true,
    planChildName: name,
    pointTimeVos: [
      { ...pointTimeVoMeta },
    ],
    taskTemplateSales: [],
    taskTemplateEdu: [],
    taskTemplateIndex: [],
    taskTemplateCheck: [],
  }
}
