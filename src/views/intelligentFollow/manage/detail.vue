<script setup lang="ts">
import Record from './components/Record.vue'
import followManage from '@/views/specificDisease/patientDetail/components/followManage/index.vue'
import { Breadcrumb } from '@/layouts/common'
import type { PatientInfoModal } from '@/api/patient'
import { getPatientInfoApi } from '@/api/patient'
import { sexIcon } from '@/utils/common'
import { useRouterPush } from '@/hooks'
import { addNewPatientMark, getFollowMarks, getPatientMarks, updatePatientMarks } from '~/src/api/intelligentFollow/plan'

const isPageLoading = ref(false)
const route = useRoute()
const router = useRouter()
const { routerPush } = useRouterPush()
const patientId = route.query.patientId as string
const planId = route.query.planId as string

const followManageRef = ref<InstanceType<typeof followManage>>()

const planTemplateName = computed(() => {
  return followManageRef.value?.planTemplateName || ''
})

interface Patient extends PatientInfoModal {
  ageUnit: string
}
const showTagModal = ref(false)
const activeName = ref('detail')

/** 患者信息 */
const patientInfo = ref<Patient>()

async function getPatientInfo() {
  try {
    isPageLoading.value = true
    const { data } = await getPatientInfoApi<Patient>(patientId)
    if (data)
      patientInfo.value = data
  }
  catch (error) {
    console.log(error)
  }
  finally {
    isPageLoading.value = false
  }
}

function editTagHandle() {
  showTagModal.value = true
}

function goPatientDetail() {
  const param = {
    patientId: patientInfo.value?.id,
    patientName: patientInfo.value?.patientName,
    isEdit: false,
  }
  routerPush({
    name: 'specificDisease_detail',
    query: param,
  })
}

function toListPage() {
  router.replace({
    path: '/intelligentFollow/manage/create',
    query: {
      id: planId,
      tab: 'Patient',
    },
  })
}

/// 获取患者标签
const allMarks: any = ref([])
const selectMarks = ref([])
const newMark = ref('')
const showMarks = ref()
function getPatientMark() {
  getFollowMarks().then((res) => {
    allMarks.value = res?.data || []
  })
}
// 新增患者标签
function addNewMark() {
  if (newMark.value?.trim().length > 0) {
    addNewPatientMark(newMark.value).then((res) => {
      if (res?.data) {
        /// 新增成功, 刷新
        getPatientMark()
        newMark.value = ''
      }
    })
  }
}
// 更新患者标签
function updatePatientTag() {
  /// 更新患者标签
  const params = {
    patientId,
    markInfo: [],
  }
  const tags: any = []
  selectMarks.value?.forEach((item) => {
    tags.push({
      markId: item,
    })
  })
  params.markInfo = tags
  updatePatientMarks(params).then((res) => {
    if (res?.data) {
      showTagModal.value = false
      checkPatientTags()
    }
  })
}

// 查询患者标签
function checkPatientTags() {
  getPatientMarks(patientId).then((res) => {
    console.log(res?.data)
    const marks = res?.data?.markInfo
    if (marks) {
      const pp: any = []
      const p2: any = []
      marks.forEach((item) => {
        pp.push(item.markId)
        p2.push(item.markName)
      })
      selectMarks.value = pp
      showMarks.value = p2.join('，')
    }
    else {
      selectMarks.value = []
      showMarks.value = null
    }
  })
}

onMounted(() => {
  getPatientInfo()
  getPatientMark()
  checkPatientTags()
})
</script>

<template>
  <div>
    <Breadcrumb
      :bread-list="[
        {
          title: '智能随访',
          link: null,
          key: 'intelligentFollow',
        },
        {
          title: '随访管理',
          link: '/intelligentFollow/manage',
          key: 'intelligentFollow_manage',
        },
        {
          title: planTemplateName,
          link: `/intelligentFollow/manage/create?id=${planId}`,
          key: 'intelligentFollow_manage_create',
        },
        {
          title: `${patientInfo?.patientName}随访详情`,
          link: null,
          key: 'intelligentFollow_manage',
        },
      ]"
    />

    <n-spin type="uni" :show="isPageLoading" size="medium">
      <PageCard breadcrumb padding="14px 14px 0 14px">
        <div flex justify-between>
          <PageTitle>
            {{ patientInfo?.patientName }}随访详情
          </PageTitle>
          <div flex cursor-pointer items-center gap-10px text="#3B8FD9" @click="toListPage">
            <SvgIcon local-icon="slmc-icon-fanhui" size="16" />
            <span>返回患者列表</span>
          </div>
        </div>

        <div relative mt-15px h-48px w-full flex items-center gap-10px px-14px bg="#A5B8D1/20">
          <span text="#333 16px" font-600>
            {{ patientInfo?.patientName }}
          </span>
          <SvgIcon :local-icon="sexIcon(patientInfo?.sexName)" size="16" />
          <div text="#666 12px" w-48px flex-center py-4px bg="#fff" border="1px solid #d1d1d1 rd-3px">
            {{ patientInfo?.age ? patientInfo?.age + (patientInfo?.ageUnit || '岁') : '-' }}
          </div>
          <div text="#666">
            <HidePhone :phone="patientInfo?.phone || ''" />
          </div>
          <div ml-20px flex flex-1>
            <span>患者标记：</span>
            <span>{{ showMarks || '-' }}</span>
            <el-popover :visible="showTagModal" placement="bottom" :width="400">
              <p>编辑患者标记</p>
              <div class="content" my-10px>
                <el-checkbox-group v-if="allMarks?.length > 0" v-model="selectMarks" my-14px>
                  <el-checkbox v-for="mark in allMarks" :key="mark.markId" :label="mark?.markId" :value="mark.markId">
                    {{ mark.markName }}
                  </el-checkbox>
                </el-checkbox-group>
                <DataEmpty v-else show-text="暂无标签,您可新增标签" h-160px flex-col items-center justify-center />
                <div flex items-center>
                  <el-input v-model="newMark" style="width: 130px;" />
                  <div color="#3B8FD9" ml-8px cursor-pointer @click="addNewMark">
                    新增标签
                  </div>
                </div>
              </div>
              <div style="text-align: center; margin: 10px">
                <el-button @click="showTagModal = false">
                  取消
                </el-button>
                <el-button type="primary" @click="updatePatientTag">
                  保存
                </el-button>
              </div>
              <template #reference>
                <SvgIcon class="ml-10px cursor-pointer" local-icon="slmc-icon-edit1" size="16" @click="editTagHandle" />
              </template>
            </el-popover>
          </div>
          <div color="#3B8FD9" cursor-pointer @click="goPatientDetail">
            查看患者详情
          </div>
        </div>

        <n-tabs v-model:value="activeName" animated>
          <n-tab-pane name="detail" tab="随访详情">
            <followManage ref="followManageRef" />
          </n-tab-pane>
          <n-tab-pane name="record" tab="随访记录">
            <Record ref="followManageRef" />
          </n-tab-pane>
        </n-tabs>
      </PageCard>
    </n-spin>
  </div>
</template>

<style lang="scss" scoped>
:deep(.is-checked .el-checkbox__inner){
  background-color: #409eff !important;
  border-color: #409eff !important;
}
</style>
