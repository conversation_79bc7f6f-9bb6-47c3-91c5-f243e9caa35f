<script setup lang="ts">
import { useDialog } from 'wowjoy-vui'
import { useTemplate } from '../useTemplate'
import StatusBadge from './components/StatusBadge.vue'
import SmallCard from './components/SmallCard.vue'
import { Breadcrumb } from '@/layouts/common'
import TemplateAddCard from '@/views/toolsFollow/components/TemplateAddCard.vue'
import TemplateCard from '@/views/toolsFollow/components/TemplateCard.vue'
import { checkPlanTempNameAPI, createPlanApi, deletePlanApi, getPlanListApi, setHmaAPI } from '@/api/intelligentFollow/manage'
import { useAuthStore } from '@/store'

const router = useRouter()
const dialog = useDialog()
const { userInfo } = useAuthStore()
const { queryData, referenceData } = useTemplate(userInfo.id)

queryData()

const datas = ref<any[]>([])
const isLoading = ref(false)

function toDetailPage(item: any) {
  router.push({
    path: '/intelligentFollow/manage/create',
    query: {
      id: item.planTemplateId,
    },
  })
}

const search = ref('')
const iconMap: Record<string, string> = {
  XCX: 'slmc-icon-xiaochengxu1',
  DUX: 'slmc-icon-duanxin2',
  AIH: 'slmc-icon-AIdianhua2',
}

async function getList() {
  try {
    isLoading.value = true
    const { data } = await getPlanListApi({
      search: search.value,
      operateId: userInfo.id,
    })

    if (data) {
      datas.value = data?.map((item) => {
        const followMethods = item.followMethod?.split(',')?.map((item2: any) => {
          return {
            id: item2,
            icon: iconMap[item2],
          }
        })
        return {
          ...item,
          followMethods,
        }
      })
    }
  }
  catch (error) {

  }
  finally {
    isLoading.value = false
  }
}

function handleDelete(item: any) {
  dialog.warning({
    title: '确定删除本随访计划吗？',
    positiveText: '确定',
    negativeText: '取消',
    btnGhost: true,
    onPositiveClick: async () => {
      try {
        const { data } = await deletePlanApi({
          planTmpId: item.planTemplateId,
          operateId: userInfo.id,
        })
        if (data) {
          window.$message.success('删除成功')
          getList()
        }
        else {
          window.$message.error('删除失败')
        }
      }
      catch (error) {
        window.$message.error('删除失败')
      }
    },
  })
}

const isAddModalShow = ref(false)
const createType = ref('empty')

const planName = ref('')
async function handleSave() {
  try {
    if (!planName.value) {
      window.$message.warning('随访计划名称不能为空')
      return false
    }

    const { data: isAvailable } = await checkPlanTempNameAPI({
      planTempName: planName.value,
    })

    if (isAvailable) {
      window.$message.warning('随访计划名称不能重复')
      return false
    }

    let activeTemplateId = ''
    if (createType.value === 'template') {
      const findActive = referenceData.value.find((item: any) => item.isActive)
      if (findActive) {
        activeTemplateId = findActive.planTemplateId
      }
      else {
        window.$message.warning('请选择需要引用的模板')
        return false
      }
    }

    const { data } = await createPlanApi({
      createId: userInfo.id,
      createName: userInfo.userName,
      organId: userInfo.organId,
      planTemplateName: planName.value,
      planTmpId: activeTemplateId,
    })

    if (data) {
      isAddModalShow.value = false
      // window.$message.success('新增成功')
      router.push({
        path: '/intelligentFollow/manage/create',
        query: {
          id: data,
        },
      })
    }
  }
  catch (error) {
    window.$message.error('新增失败')
  }
}

function toPatientPage(item: any) {
  router.push({
    path: '/intelligentFollow/manage/create',
    query: {
      id: item.planTemplateId,
      tab: 'Patient',
    },
  })
}

function toTemplateCenterPage() {
  router.push({
    path: '/intelligentFollow/templateCenter',
    query: {
      planName: planName.value,
    },
  })
}

const hmaCount = computed(() => {
  return datas.value.filter((item: any) => item.isShow)?.length || 0
})

async function setHMA(item: any) {
  try {
    if (hmaCount.value >= 4 && !item.isShow) {
      window.$message.warning('最多只能设置4个HMA计划')
      return false
    }
    const { data } = await setHmaAPI({
      planTmpId: item.planTemplateId,
      operatorId: userInfo.id,
      isHma: !item.isShow,
    })

    if (data) {
      window.$message.success('设置成功')
      getList()
    }
  }
  catch (error) {
    window.$message.error('设置失败')
  }
}

onMounted(() => {
  getList()
})
</script>

<template>
  <div>
    <Breadcrumb
      :bread-list="[
        {
          title: '智能随访',
          link: null,
          key: 'intelligentFollow',
        },
        {
          title: '随访管理',
          link: null,
          key: 'intelligentFollow_manage',
        },
      ]"
    />
    <n-spin type="uni" :show="isLoading" size="medium">
      <PageCard breadcrumb>
        <PageTitle>
          随访管理
        </PageTitle>

        <n-input-group class="my-15px !w-350px">
          <n-input
            v-model:value="search"
            placeholder="请输入随访计划名称搜索"
            clearable
            @clear="() => {
              search = ''
              getList()
            }"
          />
          <n-button type="primary" class="!min-w-16px" @click="getList">
            <template #icon>
              <SvgIcon local-icon="slmc-icon-search" size="16" style="color: #fff; cursor: pointer;" />
            </template>
          </n-button>
        </n-input-group>

        <div flex="~ wrap" gap-14px>
          <TemplateAddCard h-170px title="新增随访计划" width="325px" @click="isAddModalShow = true" />
          <TemplateCard v-for="(item, index) in datas" :key="index" width="325px" hide-footer h-170px @click="toDetailPage(item)">
            <div mb-28px mt-6px h-40px flex justify-between gap-20px>
              <div text="#333 16px" w-256px font-500 leading-20px>
                {{ item.planTemplateName }}
              </div>
              <SvgIcon size="14" local-icon="slmc-icon-crumbs1" text="#999" />
            </div>

            <div flex justify-between>
              <n-popover trigger="hover">
                <template #trigger>
                  <div flex @click.stop="toPatientPage(item)">
                    <span text="#666 12px" class="flex-shrink-0">管理患者数：</span>
                    <span class="counter">{{ item.routePatientNum?.toLocaleString() || '0' }}</span>
                  </div>
                </template>
                点击跳转患者管理
              </n-popover>
              <div flex>
                <span text="#666 12px" class="flex-shrink-0">计划创建人：</span>
                <span text="#333 12px">{{ item.createName }}</span>
              </div>
            </div>

            <div mb-14px mt-11px h-1px w-full bg="#e8e8e8" />

            <div flex justify-between>
              <div flex items-center>
                <StatusBadge :state="item.tempState || 2" />
                <n-popover>
                  <template #trigger>
                    <div
                      :class="item.isShow ? '!bg-#5B96FD' : '!bg-#ccc'" text="#fff 12px"
                      ml-10px h-20px w-44px flex-center cursor-pointer b-rd-10px
                      hover:border="2px solid #53BDE7/50"
                      @click.stop="setHMA(item)"
                    >
                      HMA
                    </div>
                  </template>
                  <span>
                    {{ item.isShow ? '点击取消HMA标记' : '点击添加HMA标记' }}
                  </span>
                </n-popover>
              </div>

              <div flex items-center gap-10px>
                <div flex flex-shrink-0>
                  <SvgIcon
                    v-for="(method) in item.followMethods"
                    :key="method.id" class="relative flex-shrink-0"
                    size="20" :local-icon="method.icon"
                  />
                </div>
                <n-popover v-if="item.tempState !== 3 && item.createId === userInfo.id" placement="bottom" raw>
                  <template #trigger>
                    <div class="b-rd-3px" hover="bg-#FFFBE0" h-24px w-24px flex items-center justify-center @click.stop="() => {}">
                      <SvgIcon size="16" local-icon="slmc-icon-gengduo" text="#333" />
                    </div>
                  </template>
                  <div cursor-pointer px-32px py-10px text="14px" hover="bg-#FFFBE0" bg="#fff" @click="handleDelete(item)">
                    删除
                  </div>
                </n-popover>
              </div>
            </div>
          </TemplateCard>
        </div>
      </PageCard>

      <n-modal v-model:show="isAddModalShow" preset="card" :style="{ width: '720px' }" title="新增随访计划" head-style="divide">
        <div py-20px>
          <div flex items-center gap-10px>
            <div w-100px flex items-center text-right>
              <SvgIcon local-icon="slmc-icon-app_required1" size="14" class="text-#F36969" />
              <div text="#666">
                随访计划名称
              </div>
            </div>
            <n-input v-model:value="planName" class="!w-514px" show-count :maxlength="20" />
          </div>
          <div mt-20px flex items-center gap-10px>
            <div text="#666" w-100px text-right>
              创建方式
            </div>
            <n-radio-group v-model:value="createType">
              <n-radio mr-40px value="empty">
                创建空白随访
              </n-radio>
              <n-radio value="template">
                从模版引用
              </n-radio>
            </n-radio-group>
          </div>
          <div v-show="createType === 'template'">
            <div ml-110px mt-10px flex="~ wrap" gap-14px>
              <SmallCard
                v-for="i in referenceData" :key="i.planTemplateId" :name="i.planTemplateName" :is-active="i.isActive"
                @click="() => {
                  referenceData.forEach((item) => {
                    item.isActive = false
                  })
                  i.isActive = true
                }"
              />
            </div>
            <div ml-110px mt-14px flex cursor-pointer items-center gap-5px text="#3B8FD9" @click="toTemplateCenterPage">
              更多模版
              <SvgIcon local-icon="slmc-icon-go" text="#3B8FD9" />
            </div>
          </div>
          <div ml-110px mt-24px>
            <n-button mr-16px w-100px type="primary" @click="handleSave">
              保 存
            </n-button>
            <n-button w-100px @click="isAddModalShow = false">
              取 消
            </n-button>
          </div>
        </div>
      </n-modal>
    </n-spin>
  </div>
</template>

<style lang="scss" scoped>
.counter {
  --uno: text-#3B8FD9 text-20px font-700 relative bottom-3px
}

.small-t-card-active {
  border: 1px solid #06AEA6;
  --uno: bg-#E7F9F7 h-56px w-250px flex cursor-not-allowed items-center gap-10px b-rd-3px px-14px
}

.small-t-card-icon-action {
  color: #06AEA6;
}

.small-t-card {
  border: 1px solid #F5F5F5;
  --uno: bg-#F5F5F5 h-56px w-250px flex cursor-pointer items-center gap-10px b-rd-3px px-14px
}

.small-t-card:hover {
  border: 1px solid #06AEA6;
  --uno: bg-#fff;

  .small-t-card-icon {
    color: #06AEA6;
  }
}
</style>
