export interface FollowPlan {
  planTemplateId: string
  planTemplateName: string
  newPlanTemplateName?: string
  remindSwitch: boolean
  lockSwitch: boolean
  beforeOpenSwitch: boolean
  planCycle: 'LONG_TREM' | 'CUSTOM'
  cycleValue: number | null
  followMethods: string[]
  createId: string
  followMethod: string
  cycleType: 'YEAR' | 'MONTH' | 'WEEK' | 'DAY'
  createName: string
  updateId: string
  updateName: string
  routeCycleUnit: 'YEAR' | 'MONTH' | 'DAY'
  routeCycleDay: number | null
  noFinishRemind: number | null
  noFinishLock: number | null
  beforeOpenDays: number
  relations: {
    userId: string
    userName: string
  }[]
  relationIds: string[]
  /**
   * 2 待开始，1 已暂停，3已启用
   */
  tempState: number
  newTempState: number
  /**
   * SBP 专病管理方案
   * SBM 随访模板
   * SBF 随访方案
   * SBG 公共模板
   */
  planTemplateType: string
  childTemplateVos: ChildTemplateVO[]
}

export interface ChildTemplateVO {
  planChildName: string
  // 自定义开始/结束时间
  executeStart: string
  executeEnd: string
  // 执行时段: 全部时段，自定义
  executeType: 'LONG_TERM' | 'CUSTOM'
  remindTime: string | null
  pointTimeVos: pointTimeVo[]
  // 是否是周期任务
  isCyclic: boolean
  fixSbDesign?: string
  taskTemplateSales: taskTemplateVo[]
  taskTemplateEdu: taskTemplateVo[]
  taskTemplateCheck: taskTemplateVo[]
  taskTemplateIndex: taskTemplateVo[]
}

export interface taskTemplateVo {
  category?: 'SCALE' | 'EDUCATION' | 'CHECK_JC' | 'ZDZB'
  condition?: string
  conditionValue?: string
  isRequired?: boolean
  relationId: string | null
  relationName: string
  relationType?: string | null
  taskTemplateId?: string
  showName?: string
  recoveryNum?: number
  createTime?: string
  isDefaultScale?: boolean
  planQuatas?: PlanQuata[]
}

export interface pointTimeVo {
  intervalNum: number | null
  intervalUnit: 'DAY' | 'MONTH' | 'WEEK' | 'YEAR'
}

// export interface Inspect {
//   inspectType: string | null
//   inspectName: string | null
//   customName: string | null
//   isMust: number
// }

export interface PlanQuata {
  compareType: string | null
  condValueOne: string
  condValueTwo: string
  warningIdList: string[]
}

type Tabs = 'Patient' | 'PlanSetting' | 'BaseSetting' | 'FollowManage'

export const TabKey: InjectionKey<Ref<Tabs>> = Symbol('Tab')
export const FollowPlanKey: InjectionKey<Ref<FollowPlan>> = Symbol('FollowPlan')
