<script setup lang='ts'>
import dayjs from 'dayjs'
import { Breadcrumb } from '@/layouts/common'
import { getPlanListApi2 } from '@/api/intelligentFollow/manage'
import { useAuthStore } from '~/src/store'
import { getFollowTODOList2 } from '@/api/intelligentFollow/toDo'
import { sexIcon } from '@/utils/common'
import { useRouterPush } from '~/src/hooks/userRouterPush'

const searchParams = reactive({
  patientNm: '',
  planTemplateId: null,
  taskStatus: null,
  followDateStart: '',
  followDateEnd: '',
})
const dateRange = ref()
const taskStatuss = ref()
const tableRef = ref()
const planTemplates: any = ref([])
const tableData = ref([])
const { userInfo } = useAuthStore()
const { routerPush } = useRouterPush()
const statusTypes = [
  {
    label: '已完成',
    value: 'FINISH',
  },
  {
    label: ' 已逾期',
    value: 'OVERTIME',
  },
  {
    label: '未开始',
    value: 'WAIT',
  },
  {
    label: '进行中',
    value: 'START',
  },
]

const statusMap = {
  WAIT: '未开始',
  START: '进行中',
  FINISH: '已完成',
  OVERTIME: '已逾期',
}

const mygStatusOptions = {
  0: {
    title: '待建档',
    color: '#45A8E6',
    showEdit: true,
  },
  1: {
    title: '完善中',
    color: '#FF9B54',
    showEdit: true,
  },
  2: {
    title: '已建档',
    color: '#4ACFB1',
    showEdit: false,
  },
  3: {
    title: '拒绝建档',
    color: '#FF0000',
    showEdit: true,
  },
  4: {
    title: '申请建档',
    color: '#4ACFB1',
    showEdit: true,
  },
}

// 分页器
const paginationReactive = reactive({
  page: 1,
  size: 10,
  total: '',
  pageSizes: [10, 20, 50, 100],
})

function getPlanTemplates() {
  getPlanListApi2({
    search: '',
    operateId: userInfo.id,
  }).then((res) => {
    planTemplates.value = res?.data || []
  })
}

function getTheDataFromNet() {
  getFollowTODOList2({ ...searchParams, page: paginationReactive.page, size: paginationReactive.size }).then((res) => {
    console.log(res)
    tableData.value = res?.data?.records
    paginationReactive.total = res?.data.total
  })
}

onMounted(() => {
  getPlanTemplates()
  getTheDataFromNet()
})

function handleSearchClick() {
  paginationReactive.page = 1
  paginationReactive.size = 10
  getTheDataFromNet()
}

function handleResetClick() {
  searchParams.patientNm = null
  searchParams.planTemplateId = null
  searchParams.taskStatus = null
  searchParams.followDateStart = null
  searchParams.followDateEnd = null
  dateRange.value = null
  paginationReactive.page = 1
  paginationReactive.size = 10
  getTheDataFromNet()
}

function dateRangeChange() {
  if (dateRange.value?.length > 0) {
    searchParams.followDateStart = dateRange.value[0]
    searchParams.followDateEnd = dateRange.value[1]
  }
  else {
    searchParams.followDateStart = ''
    searchParams.followDateEnd = ''
  }
  console.log(dateRange.value)
}

function jumpToFollowDetail(item) {
  /// 跳转到病人详情
  console.log(item)
  routerPush({
    name: 'intelligentFollow_manage_detail',
    query: {
      patientId: item.patientId,
      planId: item?.planTemplateId,
    },
  })
}

function handleLook(row: any, goEdit?: boolean) {
  const { patientId, patientNm } = row
  const param = {
    patientId,
    patientName: patientNm,
    isEdit: false,
  }
  routerPush({
    name: 'specificDisease_detail',
    query: param,
  })
}
</script>

<template>
  <div>
    <Breadcrumb
      :bread-list="[
        {
          title: '智能随访',
          link: null,
          key: 'intelligentFollow',
        },
        {
          title: '随访待办',
          link: null,
          key: 'intelligentFollow_todo2',
        },
      ]"
    />
    <PageCard breadcrumb>
      <PageTitle class="mb-14px">
        随访待办
      </PageTitle>
      <div flex flex-wrap items-center style="gap: 20px">
        <div flex items-center>
          <span color="#666" mr-10px w-85px text-14px>姓名 / 病历号</span>
          <el-input v-model="searchParams.patientNm" placeholder="请输入姓名或病历号" style="width: 180px" />
        </div>
        <div flex items-center>
          <span color="#666" mr-10px text-14px>随访时间</span>
          <el-date-picker
            v-model="dateRange" style="width: 230px" type="daterange" clearable
            :disabled-date="disabledDate" unlink-panels :default-value="[dayjs().subtract(1, 'month'), dayjs()]"
            value-format="YYYY-MM-DD" start-placeholder="开始日期" end-placeholder="结束日期" @change="dateRangeChange"
          />
        </div>
        <div flex items-center>
          <span color="#666" mr-10px text-14px>随访方案</span>
          <el-select-v2
            v-model="searchParams.planTemplateId"
            clearable :options="planTemplates" :props="{
              label: 'planTemplateName',
              value: 'planTemplateId',
            }" filterable placeholder="请选择" style="width: 200px"
          >
            <template #default="{ item }">
              <el-tooltip placement="right" :content="item.planTemplateName">
                <div overflow-hidden style="text-overflow: ellipsis">
                  {{ item.planTemplateName }}
                </div>
              </el-tooltip>
            </template>
          </el-select-v2>
        </div>
        <div flex items-center>
          <span color="#666" mr-10px text-14px>任务状态</span>
          <el-select
            v-model="searchParams.taskStatus"
            multiple
            collapse-tags
            collapse-tags-tooltip
            :max-collapse-tags="2"
            clearable
            filterable placeholder="请选择" style="width: 200px"
          >
            <el-option v-for="(item, index) in statusTypes" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div flex items-center>
          <n-button type="primary" class="mr-10px" @click="handleSearchClick">
            查询
          </n-button>
          <n-button class="mr-10px" @click="handleResetClick">
            重置
          </n-button>
        </div>
      </div>
      <div mt-16px>
        <el-table
          ref="tableRef" stripe :data="tableData"
        >
          <el-table-column align="center" label="序号" type="index" width="80" />
          <el-table-column label="患者信息" width="240">
            <template #default="{ row }">
              <div flex items-center>
                <div w-70px>
                  {{ row.patientNm }}
                </div>
                <SvgIcon size="16" :local-icon="sexIcon(row.sexName)" />
                <div mx-6px>
                  {{ row.age }}
                </div>
                <div>
                  {{ row?.phone }}
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="治疗时间" prop="followDate" width="120" :formatter="({ joinTime }) => {
              return dayjs(joinTime).format('YYYY-MM-DD')
            }"
          />
          <el-table-column
            label="档案状态" prop="followDate" width="120"
          >
            <template #default="{ row }">
              <div cursor-pointer @click="handleLook(row, true)">
                {{ mygStatusOptions[row.diagnoseRecordStatus].title }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="随访意愿" prop="followWish" width="120" :formatter="({ followWish }) => {
              return followWish || '-'
            }"
          />
          <el-table-column label="下阶段随访任务">
            <template #default="{ row }">
              <el-popover
                v-if="row.followTask"
                placement="left"
                :width="230"
                trigger="hover"
              >
                <template #reference>
                  <div flex items-center>
                    <div>
                      {{ row.followDate ? dayjs(row.followDate).format('YYYY-MM-DD') : '' }}
                    </div>
                    <div ml-10px>
                      {{ statusMap[row.followStatus] }}
                    </div>
                  </div>
                </template>
                <div>
                  <div v-for="(item, index) in row.followTask" :key="index" flex items-center>
                    <div>{{ item.taskName }}</div>
                    <div>{{ item.followDate ? dayjs(item.followDate).format('YYYY-MM-DD') : '' }}</div>
                  </div>
                </div>
              </el-popover>
              <div v-else>
                -
              </div>
            </template>
          </el-table-column>
          <el-table-column
            min-width="210"
            label="上次就就诊记录" :formatter="({ visitDate, visitDay, visitDept, visitDoctor }) => {
              return visitDate ? `${dayjs(visitDate).format('YYYY-MM-DD')}  ${visitDept} ${visitDoctor}  距今${visitDay}天` : '-'
            }"
          />
          <el-table-column
            label="操作"
            width="60"
          >
            <template #default="{ row }">
              <div uno-link @click="jumpToFollowDetail(row)">
                查看
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div flex justify-end>
          <n-pagination
            v-model:page="paginationReactive.page" v-model:page-size="paginationReactive.size"
            :item-count="paginationReactive.total" :page-sizes="paginationReactive.pageSizes" show-size-picker show-quick-jumper mt-14px
            @update:page-size="getTheDataFromNet"
            @update:page="getTheDataFromNet"
          />
        </div>
      </div>
    </PageCard>
  </div>
</template>

<style lang="scss" scoped>

</style>
