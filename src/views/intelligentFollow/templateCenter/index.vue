<script setup lang="ts">
import dayjs from 'dayjs'
import { useTemplate } from '../useTemplate'
import { Breadcrumb } from '@/layouts/common'
import TemplateCard from '@/views/toolsFollow/components/TemplateCard.vue'
import { useAuthStore } from '@/store'
import { checkPlanTempNameAPI, createPlanApi, deletePlanApi } from '@/api/intelligentFollow/manage'

const { userInfo } = useAuthStore()
const { queryData, ownTemplates, commonTemplates, getLabel, planLabels, isLoading } = useTemplate(userInfo.id)

const router = useRouter()
const route = useRoute()

const nameFromRoute = route.query.planName as string | undefined
const isAddPlanModalShow = ref(false)
let selectedTemplateId = ''

queryData()
getLabel()

function handleTypeClick(index: number) {
  planLabels.value.forEach((item, index2) => {
    item.isActive = !!(index === index2)
  })
}

const currentActiveId = computed(() => {
  return planLabels.value.find(item => item.isActive)?.labelId
})

function handleReferenceClick(item: any) {
  selectedTemplateId = item.planTemplateId
  isAddPlanModalShow.value = true
}

const planName = ref(nameFromRoute)

async function handleSaveTemplateConfirm() {
  try {
    if (!planName.value) {
      window.$message.warning('随访计划名称不能为空')
      return false
    }

    const { data: isAvailable } = await checkPlanTempNameAPI({
      planTempName: planName.value,
    })

    if (isAvailable) {
      window.$message.warning('随访计划名称不能重复')
      return false
    }

    const { data } = await createPlanApi({
      createId: userInfo.id,
      createName: userInfo.userName,
      planTmpId: selectedTemplateId,
      organId: userInfo.organId,
      planTemplateName: planName.value,
    })

    if (data) {
      isAddPlanModalShow.value = false
      // window.$message.success('新增成功')
      router.push({
        path: '/intelligentFollow/manage/create',
        query: {
          id: data,
        },
      })
    }
  }
  catch (error) {
    window.$message.error('新增失败')
  }
}

function handleEditClick(item: any) {
  router.push({
    path: '/intelligentFollow/manage/create',
    query: {
      id: item.planTemplateId,
    },
  })
}

function handleDelete(item: any) {
  window.$dialog.warning({
    title: '确定删除本随访计划吗？',
    positiveText: '确定',
    negativeText: '取消',
    btnGhost: true,
    onPositiveClick: async () => {
      try {
        const { data } = await deletePlanApi({
          planTmpId: item.planTemplateId,
          operateId: userInfo.id,
        })
        if (data) {
          window.$message.success('删除成功')
          queryData()
        }

        else { window.$message.error('删除失败') }
      }
      catch (error) {
        window.$message.error('删除失败')
      }
    },
  })
}

async function toPlanDetailPage(item) {
  try {
    router.push({
      path: '/intelligentFollow/manage/create',
      query: {
        id: item.planTemplateId,
        mode: 'view',
      },
    })
  }
  catch (error) {
    console.log(error)
  }
}

function goBack() {
  router.replace({
    path: '/intelligentFollow/manage',
  })
}
</script>

<template>
  <div>
    <Breadcrumb
      :bread-list="[
        {
          title: '智能随访',
          link: null,
          key: 'intelligentFollow',
        },
        {
          title: '模版中心',
          link: null,
          key: 'intelligentFollow_templateCenter',
        },
      ]"
    />

    <n-spin type="uni" :show="isLoading" size="medium">
      <PageCard breadcrumb>
        <PageTitle>
          模板中心
        </PageTitle>

        <div my-14px flex items-center gap-10px>
          <div sub-title />
          <div>我的模版</div>
          <SvgIcon local-icon="slmc-icon-information_line" size="14" />
          <div text="#666">
            您可以直接引用以下模版或编辑生成新的随访方案使用
          </div>
        </div>

        <div v-if="ownTemplates?.length > 0" flex="~ wrap" gap-14px>
          <TemplateCard
            v-for="(item, index) in ownTemplates" :key="index" width="325px" h-204px
          >
            <div flex="~ col">
              <div text="#333 16px" font-500 leading-20px>
                {{ item.planTemplateName || '-' }}
              </div>
              <div bg="#e8e8e8" mb-20px mt-14px h-1px w-full />
              <div flex>
                <div text="#666 12px" class="flex-shrink-0">
                  适用场景：
                </div>
                <div text="#333 12px">
                  {{ item.profile || '-' }}
                </div>
              </div>

              <div mt-10px flex>
                <div text="#666 12px" class="flex-shrink-0">
                  创建日期：
                </div>
                <div text="#333 12px" flex>
                  {{ dayjs(item.createTime).format('YYYY-MM-DD') }}
                </div>
              </div>
            </div>

            <template #footer>
              <div h-full flex items-center justify-center>
                <div
                  flex items-center gap-10px @click.stop="() => {
                    handleReferenceClick(item)
                  }"
                >
                  <SvgIcon local-icon="slmc-icon-kaidanjilu" size="14" />
                  <span text="14px #3B8FD9">引用模板</span>
                </div>
                <template v-if="nameFromRoute === undefined">
                  <div ml-14px mr-28px h-14px w-1px bg="#3B8FD9" />
                  <div
                    flex items-center gap-10px @click.stop="handleEditClick(item)"
                  >
                    <SvgIcon local-icon="slmc-icon-edit1" size="14" />
                    <span text="14px #3B8FD9">编辑</span>
                  </div>
                  <div mx-28px h-14px w-1px bg="#3B8FD9" />
                  <div flex items-center gap-10px @click="handleDelete(item)">
                    <SvgIcon local-icon="slmc-icon-delete2" size="14" />
                    <span text="14px #3B8FD9">删除</span>
                  </div>
                </template>
              </div>
            </template>
          </TemplateCard>
        </div>

        <div v-else flex justify-center>
          <n-empty size="large" description="无数据" />
        </div>

        <div mt-30px flex items-center gap-10px>
          <div sub-title />
          <div>公共模板</div>
          <SvgIcon local-icon="slmc-icon-information_line" size="14" />
          <div text="#666">
            根据专病指南和专家共识整合而成的慢病管理随访方案，您可在对应业务中引用以下模板
          </div>
        </div>

        <n-button-group my-14px>
          <n-button
            v-for="(item, index) in planLabels"
            :key="item.labelId"
            :ghost="!item.isActive"
            :type="item.isActive ? 'primary' : 'default'"
            @click="handleTypeClick(index)"
          >
            {{ item.labelName }}
          </n-button>
        </n-button-group>
        <div v-if="commonTemplates[currentActiveId]" flex="~ wrap" gap-14px>
          <TemplateCard v-for="(item) in commonTemplates[currentActiveId]" :key="item.planTemplateId" width="325px" h-204px @click="toPlanDetailPage(item)">
            <div flex="~ col">
              <div text="#333 16px" font-500 leading-20px>
                {{ item.planTemplateName }}
              </div>

              <div bg="#e8e8e8" mb-20px mt-14px h-1px w-full />
              <div flex>
                <div text="#666 12px" class="flex-shrink-0">
                  适用场景：
                </div>
                <div text="#333 12px" flex>
                  {{ item.profile || '-' }}
                </div>
              </div>

              <div mt-10px flex>
                <div text="#666 12px" class="flex-shrink-0">
                  创建日期：
                </div>
                <div text="#333 12px" flex>
                  {{ dayjs(item.createTime).format('YYYY-MM-DD') }}
                </div>
              </div>
            </div>

            <template #footer>
              <div h-full flex items-center justify-center @click.stop="handleReferenceClick(item)">
                <div flex items-center gap-10px>
                  <SvgIcon local-icon="slmc-icon-kaidanjilu" size="14" />
                  <span text="14px #3B8FD9">引用模板</span>
                </div>
              </div>
            </template>
          </TemplateCard>
        </div>
        <div v-else flex justify-center>
          <n-empty size="large" description="无数据" />
        </div>
      </PageCard>
    </n-spin>

    <n-modal
      v-model:show="isAddPlanModalShow"
      preset="card" :style="{ width: '560px' }"
      title="设置随访计划名称" head-style="divide"
    >
      <div py-20px>
        <div flex items-center gap-10px>
          <div w-100px flex items-center>
            <SvgIcon local-icon="slmc-icon-app_required1" size="14" class="text-#F36969" />
            <div text="#666">
              随访计划名称
            </div>
          </div>
          <n-input v-model:value="planName" class="!w-370px" show-count :maxlength="20" />
        </div>
        <div ml-110px mt-20px>
          <n-button mr-16px w-100px type="primary" @click="handleSaveTemplateConfirm">
            保 存
          </n-button>
          <n-button w-100px @click="isAddPlanModalShow = false">
            取 消
          </n-button>
        </div>
      </div>
    </n-modal>

    <PageFooter v-if="nameFromRoute !== undefined">
      <n-button w-100px @click="goBack">
        返 回
      </n-button>
    </PageFooter>
  </div>
</template>
