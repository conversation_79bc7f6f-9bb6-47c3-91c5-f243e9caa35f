<script setup lang="ts">
import { NEllipsis } from 'wowjoy-vui'
import dayjs from 'dayjs'
import { Breadcrumb } from '@/layouts/common'
import BasicTable, { RowAction } from '~/src/components/Table'
import { checkPlanTempNameAPI, createPlanApi, getSpecPlanAPI } from '@/api/intelligentFollow/manage'
import { useAuthStore } from '@/store'

const tableRef = ref<InstanceType<typeof BasicTable> | null>(null)
const columns = createColumns()
const actionColumns = createActionColumns()
const router = useRouter()
const { userInfo } = useAuthStore()

let selectedTemplateId = ''
const isAddPlanModalShow = ref(false)
const planName = ref('')

function createColumns() {
  return [
    {
      title: '序号',
      key: 'index',
      width: 50,
      render(_: any, index: number) {
        return index + 1
      },
    },
    {
      title: '专病管理方案',
      key: 'specPlanName',
      width: 120,
      render(row: any) {
        return h(NEllipsis, {
          lineClamp: '2',
        }, row.specPlanName)
      },
    },
    {
      title: '适用管理人群',
      key: 'sortMemo',
      width: 150,
      ellipsis: {
        tooltip: true,
        lineClamp: 2,
      },
    },
    {
      title: '方案简介',
      key: 'specPlanMemo',
      width: 150,
      ellipsis: {
        tooltip: true,
        lineClamp: 2,
      },
    },
    {
      title: '方案组成',
      key: 'formula',
      width: 150,
      ellipsis: {
        tooltip: true,
        lineClamp: 2,
      },
    },
    {
      title: '管理患者数',
      key: 'patientNum',
      width: 90,
      render(row: any) {
        return h('span',
          {
            class: 'uno-link',
            onClick: () => {
              router.push({
                path: '/intelligentFollow/manage/create',
                query: {
                  id: row.specPlanId,
                  tab: 'Patient',
                },
              })
            },
          }, row.patientNum)
      },
    },
    {
      title: '创建时间',
      key: 'createTime',
      width: 90,
      render(row: any) {
        return h('span', {
        }, dayjs(row.createTime).format('YYYY-MM-DD'))
      },
    },
  ]
}

function createActionColumns() {
  return {
    title: '操作',
    key: 'cz',
    width: 100,
    fixed: 'right',
    render(row: any) {
      return h(RowAction, {
        actions: [
          {
            label: '引用模板',
            onClick: () => {
              selectedTemplateId = row.specPlanId
              isAddPlanModalShow.value = true
              planName.value = ''
            },
            type: 'primary',
            text: true,
          },
        ],
      })
    },
  }
}

const tableParams = ref({
  queryName: '',
})

async function loadDataTable() {
  return new Promise((resolve) => {
    getSpecPlanAPI({ ...tableParams.value }).then((res) => {
      resolve({
        data: {
          records: res.data,
        },
      })
    })
  })
}

async function handleSaveTemplateConfirm() {
  try {
    if (!planName.value) {
      window.$message.warning('随访计划名称不能为空')
      return false
    }

    const { data: isAvailable } = await checkPlanTempNameAPI({
      planTempName: planName.value,
    })

    if (isAvailable) {
      window.$message.warning('随访计划名称不能重复')
      return false
    }

    const { data } = await createPlanApi({
      createId: userInfo.id,
      createName: userInfo.userName,
      planTmpId: selectedTemplateId,
      organId: userInfo.organId,
      planTemplateName: planName.value,
    })

    if (data) {
      isAddPlanModalShow.value = false
      window.$message.success('新增成功')
      router.push({
        path: '/intelligentFollow/manage/create',
        query: {
          id: data,
        },
      })
    }
    else {
      window.$message.error('新增失败')
    }
  }
  catch (error) {
    window.$message.error('新增失败')
  }
}
</script>

<template>
  <div>
    <Breadcrumb route-name="intelligentFollow_plan" />
    <PageCard breadcrumb render-bread>
      <PageTitle class="flex">
        专病管理方案
      </PageTitle>
      <n-input-group class="mt-15px !w-350px">
        <n-input
          v-model:value="tableParams.queryName"
          placeholder="请输入随访计划名称搜索"
          clearable
          @clear="() => {
            tableParams.queryName = ''
            tableRef!.fetch()
          }"
        />
        <n-button type="primary" class="!min-w-16px" @click="tableRef!.fetch()">
          <template #icon>
            <SvgIcon local-icon="slmc-icon-search" size="16" style="color: #fff; cursor: pointer;" />
          </template>
        </n-button>
      </n-input-group>
      <div mt-14px w-full>
        <BasicTable
          ref="tableRef"
          class="table"
          :columns="columns"
          :request="loadDataTable"
          :row-key="(row:any) => row.id"
          :action-column="actionColumns"
          :pagination="false"
          :scroll-x="1300"
          striped
        />
      </div>
    </PageCard>

    <n-modal
      v-model:show="isAddPlanModalShow"
      preset="card" :style="{ width: '560px' }"
      title="设置随访计划名称" head-style="divide"
    >
      <div py-20px>
        <div flex items-center gap-10px>
          <div w-100px flex items-center>
            <SvgIcon local-icon="slmc-icon-app_required1" size="14" class="text-#F36969" />
            <div text="#666">
              随访计划名称
            </div>
          </div>
          <n-input v-model:value="planName" class="!w-370px" show-count :maxlength="20" />
        </div>
        <div ml-110px mt-20px>
          <n-button mr-16px w-100px type="primary" @click="handleSaveTemplateConfirm">
            保 存
          </n-button>
          <n-button w-100px @click="isAddPlanModalShow = false">
            取 消
          </n-button>
        </div>
      </div>
    </n-modal>
  </div>
</template>

<style lang="scss" scoped>
:deep(.n-ellipsis){
  white-space: normal
}
</style>
