import { getCommonTemplate, getLabelByTypeAPI, getOwnTemplate } from '@/api/intelligentFollow/manage'

export function useTemplate(id: string) {
  const isLoading = ref(false)

  const planLabels = ref<any[]>([])

  const ownTemplates = ref<any[]>([])

  const commonTemplates = ref<Record<string, any[]>>({})

  const referenceData = ref<Record<string, any>[]>([])

  async function queryData() {
    try {
      isLoading.value = true

      const [ownResponse, commonResponse] = await Promise.all([
        getOwnTemplate(id),
        getCommonTemplate(id),
      ])

      ownTemplates.value = ownResponse.data || []
      commonTemplates.value = commonResponse.data || {}

      referenceData.value = ownTemplates.value.concat(
        ...Object.values(commonTemplates.value),
      ).slice(0, 6).map((item) => {
        return {
          ...item,
          isActive: false,
        }
      })
    }
    catch (error) {

    }
    finally {
      isLoading.value = false
    }
  }

  async function getLabel() {
    try {
      const { data } = await getLabelByTypeAPI('PLAN_TMP')
      planLabels.value = data?.map((item, index) => {
        return {
          ...item,
          isActive: index === 0,
        }
      }) || []
    }
    catch (error) {

    }
  }

  return {
    planLabels,
    ownTemplates,
    commonTemplates,
    queryData,
    referenceData,
    getLabel,
    isLoading,
  }
}
