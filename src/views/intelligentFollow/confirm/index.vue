<script setup lang="ts">
import { useDebounceFn } from '@vueuse/core'
import { cloneDeep } from 'lodash-es'
import dayjs from 'dayjs'
import { Breadcrumb } from '@/layouts/common'
import { addToPlanAPI, getFollowPatientNewAPI, queryImportDoctorsNames, queryImportPatientsNames, refuseFollowAPI } from '~/src/api/intelligentFollow/manage'
import { useAuthStore } from '~/src/store'
import { sexIcon } from '@/utils/common'

interface Option {
  label: string
  value: string
}

const { userInfo } = useAuthStore()
const tableRef = ref<any>()

const typeOptions = [
  // { label: '全部', value: '' },
  // { label: '男', value: '男' },
  // { label: '女', value: '女' },
  { label: '门诊', value: 'OUTPATIENT' },
  { label: '住院', value: 'OUTHOSPITAL' },
]

const patientNameOptions = ref<Option[]>([])
let originalPatientNameData: any[] = []
/**  搜索患者姓名 */
const handleSearchPatientName = useDebounceFn(async (query: string) => {
  try {
    const res = await queryImportPatientsNames(query) as any

    patientNameOptions.value = res?.data?.records.map((item: any) => {
      return {
        label: item,
        value: item,
      }
    })

    if (originalPatientNameData?.length === 0)
      originalPatientNameData = cloneDeep(patientNameOptions.value)
  }
  catch (error) {
    console.log(error)
  }
}, 500)

const doctorNameOptions = ref<Option[]>([])
let originalDoctorNameData: any[] = []
/** 搜索医生姓名 */
const handleSearchDoctorName = useDebounceFn(async (query?: string) => {
  try {
    const res = await queryImportDoctorsNames({
      name: query,
      organId: userInfo.organId,
    }) as any

    doctorNameOptions.value = res.data.map((item: any) => {
      return {
        label: item.doctorName,
        value: item.doctorName,
      }
    })

    if (originalDoctorNameData?.length === 0)
      originalDoctorNameData = cloneDeep(doctorNameOptions.value)
  }
  catch (error) {
    console.log(error)
  }
}, 500)

const searchForm = ref({
  operateId: userInfo.id,
  // queryType: 'waitPatient',
  typeName: '门诊',
  queryType: 'OUTPATIENT',
  queryNm: '',
  doctorName: '',
  // patientNmList: [],
  // manageDoctorList: [],
})

const pageForm = ref({
  page: 1,
  size: 10,
  total: 0,
})

const tableData = ref<any[]>()
const isLoading = ref(false)

function handleSearchClick() {
  getTableData()
}

function handleResetClick() {
  searchForm.value.typeName = ''
  searchForm.value.patientNmList = []
  searchForm.value.manageDoctorList = []
  pageForm.value.page = 1
  pageForm.value.size = 10
  getTableData()
}

const editPlanIndex = ref(-1)

const oldPlan = {
  id: '',
  name: '',
}

function handleClickEditBtn(row: any, index: number) {
  oldPlan.id = row.suggestPlanId
  oldPlan.name = row.suggestPlanName
  editPlanIndex.value = index
}

function handleConfirm() {
  editPlanIndex.value = -1
}

function handleCancel(row: any) {
  row.suggestPlanId = oldPlan.id
  row.suggestPlanName = oldPlan.name
  editPlanIndex.value = -1
}

async function handleJoinGroup(row: any) {
  try {
    if (!row.suggestPlanId) {
      window.$message.warning('请选择随访计划')
      return false
    }

    const { data } = await addToPlanAPI({
      createId: userInfo.id,
      createName: userInfo.userName,
      operatorId: userInfo.id,
      operatorName: userInfo.userName,
      patientId: row.patientId,
      planTmpId: row.suggestPlanId,
    })

    if (data) {
      window.$message.success('加入成功')
      getTableData()
    }
  }
  catch (error) {

  }
}

async function handleRefuseGroup(row: any) {
  console.log(row)
  try {
    const { data } = await refuseFollowAPI({
      operatorId: userInfo.id,
      operatorName: userInfo.userName,
      patientId: row.patientId,
    })

    if (data) {
      window.$message.success('拒绝成功')
      getTableData()
    }
  }
  catch (error) {

  }
}

const multiplePatient = ref<any[]>()

function handleMultipleClick() {
  const requests: Promise<any>[] = []
  if (multiplePatient.value) {
    multiplePatient.value.forEach((item: any) => {
      requests.push(
        addToPlanAPI({
          createId: userInfo.id,
          createName: userInfo.userName,
          operatorId: userInfo.id,
          operatorName: userInfo.userName,
          patientId: item.patientId,
          planTmpId: item.suggestPlanId,
        }),
      )
    })

    Promise.allSettled(requests).then((results) => {
      // window.$message.success('批量入组成功')
      // getTableData()
      let succ = true
      for (let index = 0; index < results?.length; index++) {
        const res = results[index]
        if (!res.value?.data) {
          succ = false
          continue
        }
        else {
          getTableData()
        }
      }
      if (succ) {
        window.$message.success('批量入组成功')
        getTableData()
      }
    })
  }
}

async function getTableData() {
  try {
    isLoading.value = true
    editPlanIndex.value = -1
    const { data } = await getFollowPatientNewAPI<any>({
      ...searchForm.value,
      page: pageForm.value.page,
      size: pageForm.value.size,
    })

    tableData.value = data.records.map((item: any) => {
      const planOptions = Object.entries(item.suggestPlan).map(([key, value]) => {
        return {
          label: value,
          value: key,
        }
      })

      const haveSuggestPlan = !item.planTemplateName || item.planTemplateName.length === 0
      if (haveSuggestPlan) {
        planOptions.unshift({
          label: '-',
          value: '',
        })
      }

      const suggestPlanId = haveSuggestPlan ? '' : Object.keys(item.suggestPlan)[0]
      const suggestPlanName = haveSuggestPlan ? '-' : Object.values(item.suggestPlan)[0]
      return {
        ...item,
        isHidden: true,
        planOptions,
        suggestPlanId,
        suggestPlanName,
      }
    })
    pageForm.value.total = Number(data.total)
  }
  catch (error) {

  }
  finally {
    isLoading.value = false
  }
}

onMounted(() => {
  handleSearchDoctorName('')
  handleSearchPatientName('')
  getTableData()
})
</script>

<template>
  <div>
    <Breadcrumb route-name="intelligentFollow_confirm" />
    <n-spin type="uni" :show="isLoading" size="medium">
      <PageCard breadcrumb>
        <PageTitle class="mb-14px">
          待入组患者
        </PageTitle>

        <n-form
          label-placement="left"
          :show-feedback="false"
        >
          <div class="flex-start flex flex-wrap gap-20px">
            <n-form-item label="类型" path="sex" class="basis-262px">
              <n-select v-model:value="searchForm.queryType" placeholder="请选择" :options="typeOptions" style="width: 224px;" />
            </n-form-item>
            <n-form-item label="姓名" path="patientName" class="basis-262px">
              <!-- <n-select
                v-model:value="searchForm.patientNmList"
                filterable placeholder="请选择(可多选)"
                :options="patientNameOptions"
                clearable
                remote
                style="width: 224px;"
                multiple
                :max-tag-count="1"
                @search="handleSearchPatientName"
                @update:show="patientNameOptions = originalPatientNameData"
              /> -->
              <n-input v-model="searchForm.queryNm" />
            </n-form-item>
            <n-form-item label="主治医生" path="doctorName" class="basis-290px">
              <!-- <n-select
                v-model:value="searchForm.manageDoctorList"
                filterable placeholder="请选择(可多选)"
                :options="doctorNameOptions"
                clearable
                remote
                multiple
                :max-tag-count="1"
                style="width: 224px;"
                @search="handleSearchDoctorName"
                @update:show="doctorNameOptions = originalDoctorNameData"
              /> -->
              <n-input v-model="searchForm.doctorName" />
            </n-form-item>
            <n-form-item>
              <n-button type="primary" class="mr-10px" @click="handleSearchClick">
                查询
              </n-button>
              <n-button @click="handleResetClick">
                重置
              </n-button>
            </n-form-item>
          </div>
        </n-form>
        <div class="my-12px">
          <n-button type="primary" @click="handleMultipleClick">
            批量入组
          </n-button>
        </div>

        <el-table
          ref="tableRef" stripe :data="tableData"
          max-height="calc(100vh - 300px)"
          @selection-change="(val:any) => {
            multiplePatient = val
          }"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column label="序号" type="index" width="80" />
          <el-table-column label="患者信息" min-width="180">
            <template #default="{ row }">
              <div flex items-center gap-5px>
                <span>{{ row.patientName }}</span>
                <SvgIcon size="16" :local-icon="sexIcon(row.sex)" />
                <span>{{ row.age }}</span>
              </div>
            </template>
          </el-table-column>
          <el-table-column min-width="120" label="病历号" prop="recordId" :formatter="({ recordId }) => recordId || '-'" />
          <el-table-column label="手机号" min-width="150">
            <template #default="{ row }">
              <HidePhone :phone="row.phone || ''" />
            </template>
          </el-table-column>
          <el-table-column label="推荐随访计划" min-width="270">
            <template #default="{ row, $index }">
              <div v-if="editPlanIndex === $index" flex items-center>
                <n-select
                  v-model:value="row.suggestPlanId"
                  :options="row.planOptions"
                  class="!w-180px" @update:value="(_val, option:any) => {
                    row.suggestPlanName = option.label
                  }"
                />
                <SvgIcon class="mx-10px cursor-pointer" local-icon="slmc-icon-done_line" size="16" @click="handleConfirm" />
                <SvgIcon class="cursor-pointer" local-icon="slmc-icon-wrong_line" size="16" @click="handleCancel(row)" />
              </div>
              <div v-else flex items-center gap-5px>
                <span>{{ row.suggestPlanName }}</span>
                <SvgIcon class="flex-shrink-0 cursor-pointer" local-icon="slmc-icon-edit1" size="16" @click="handleClickEditBtn(row, $index)" />
              </div>
            </template>
          </el-table-column>
          <!-- <el-table-column min-width="250" label="就诊时间" prop="treatDate" :formatter="({ treatDate }) => treatDate || '-'" /> -->
          <el-table-column
            prop="treatDate" label="就诊时间" min-width="120" :formatter="({ treatDate }) => {
              return treatDate ? dayjs(treatDate).format('YYYY-MM-DD') : '-'
            }"
          />
          <el-table-column min-width="150" label="就诊科室" prop="departmentName" :formatter="({ departmentName }) => departmentName || '-'" />
          <el-table-column min-width="120" label="主治医生" prop="doctorName" :formatter="({ doctorName }) => doctorName || '-'" />
          <el-table-column fixed="right" label="操作" width="100">
            <template #default="{ row }">
              <div flex items-center gap-5px>
                <span uno-link @click="handleJoinGroup(row)">同意</span>
                <div h-14px w-1px bg="#3B8FD9" />
                <span uno-link @click="handleRefuseGroup(row)">拒绝</span>
              </div>
            </template>
          </el-table-column>
          <template #empty>
            <div h-300px flex items-center justify-center>
              <DataEmpty />
            </div>
          </template>
        </el-table>

        <div v-if="pageForm.total > 0" mt-20px w-full flex justify-end>
          <n-pagination
            v-model:page="pageForm.page"
            v-model:page-size="pageForm.size"
            :item-count="pageForm.total"
            :page-sizes="[5, 10, 20, 30]"
            show-size-picker
            show-quick-jumper
            @update:page="getTableData"
            @update:page-size="() => {
              pageForm.page = 1
              getTableData()
            }"
          />
        </div>
      </PageCard>
    </n-spin>
  </div>
</template>
