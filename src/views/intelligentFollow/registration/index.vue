<script setup lang="ts">
import { NPopover, useMessage } from 'wowjoy-vui'
import { Breadcrumb } from '@/layouts/common'
import { ButtonSearch } from '@/components/Search'
import { BasicTable, RowAction } from '@/components/Table'
import { formartPhoneNumberMiss } from '~/src/utils/common/format'
import { SvgIcon } from '~/src/components/Icon'
import { sexIcon } from '@/utils/common'

const tableRef = ref<InstanceType<typeof BasicTable> | null>(null)
const columns = createColumns()
const actionColumns = createActionColumns()
const message = useMessage()
// 分页器
const paginationReactive = reactive({
  page: 1,
  pageSize: 10,
  pageSizes: [10, 20, 50, 100],
})

const btns = [
  {
    label: '二维码',
    icon: 'slmc-icon-erweima',
    click: () => {

    },
  },
  {
    label: '身份证',
    icon: 'slmc-icon-shenfenzheng',
    click: () => {

    },
  }, {
    label: '医保卡',
    icon: 'slmc-icon-yibaoka',
    click: () => {

    },
  }, {
    label: '就诊卡',
    icon: 'slmc-icon-jiuzhenka',
    click: () => {

    },
  },

]

function createColumns() {
  return [
    {
      title: '序号',
      key: 'index',
      width: 40,
      render(_: PatientRecord, index: number) {
        return index + 1
      },
    },
    {

      title: '患者信息',
      key: 'info',
      width: 90,
      render(row: any) {
        return h('div', { class: 'flex items-center' }, [
          h('div', null, `${row.patientName} | `),
          h(SvgIcon, {
            class: 'mx-3px',
            localIcon: sexIcon(row.gender),
            size: 16,
          }),
          h('div', null, ` | ${row.age}`),
        ])
      },
    },
    {
      title: '手机号',
      key: 'phone',
      width: 90,
      render(row: any) {
        return h(NPopover, {
          trigger: 'hover',
        },
        {
          trigger: () => [
            h('div', {
              onClick: () => {
                row.checkPhone = !row.checkPhone
              },
            }, row.checkPhone ? row.phone : formartPhoneNumberMiss(row.phone)),
          ],
          default: [h('div', null, row.checkPhone ? '点击隐藏手机号' : '点击显示完整手机号')],
        },
        )
      },
    },
    {
      title: '当前诊断',
      key: 'diagnosis',
      width: 105,
    },
    {
      title: '随访方案',
      key: 'follow',
      width: 180,
    },
    {
      title: '管理医生',
      key: 'doctor',
      width: 70,
    },
    {
      title: '当前访视',
      key: 'visit',
      width: 60,
    },
  ]
}
function createActionColumns() {
  return {
    title: '操作',
    key: 'cz',
    width: 120,
    fixed: 'right',
    render(row: PatientRecord) {
      const { primaryIndex, patientName, id, slmcNo, age } = row
      const params = {
        patientName,
        primaryIndex,
        slmcNo,
        patientId: id,
        age,
      }
      return h(RowAction, {
        actions: [
          {
            label: '预问诊',
            onClick: () => handleGoSLMCView(params),
            type: 'primary',
            text: true,
          },
          {
            label: '特征测量',
            onClick: () => handleGoMedicalRecord(params),
            type: 'primary',
            text: true,
          },
          {
            label: '编辑',
            onClick: () => handleGoMedicalRecord(params),
            type: 'primary',
            text: true,
          },
        ],
      })
    },
  }
}

function loadDataTable(res: { size: number; start: number }) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        data: {
          records: [{
            patientName: '张三',
            age: '33',
            gender: '男',
            phone: '15658112878',
            diagnosis: '头痛脚痛,浑身都很痛',
            follow: '随访方案名称随访方案名称随访最多二十个字',
            doctor: '王丫丫',
            visit: 'V1',
          }],
          total: '1',
          current: '1',
          size: '10',
        },
      })
    }, 200)
  })
}
// 搜索
function handleSearch(params: any) {
  // tableParams = {
  //   ...tableParams,
  //   ...params,
  // }

  // const payload = { ...tableParams, ...params, start: 1 }
  // tableRef?.value?.fetch(payload)

}
function buttonClick(index) {
  message.info('功能开发中')
}
</script>

<template>
  <div>
    <Breadcrumb route-name="intelligentFollow_registration" />
    <PageCard breadcrumb render-bread>
      <PageTitle class="flex">
        患者登记
      </PageTitle>
      <div mt-15px flex items-center>
        <ButtonSearch
          placeholder="患者姓名/手机号/身份证"
          width="350px"
          @emit-search="handleSearch"
        />
        <div ml-20px flex flex-1 items-center justify-between>
          <div>
            <n-button v-for="(item, index) in btns" :key="index" class="my-button" secondary mr-10px @click="buttonClick(index)">
              <template #icon>
                <SvgIcon :local-icon="item.icon" style="width: 20px;height: 17px;" />
              </template>
              {{ item.label }}
            </n-button>
          </div>
          <n-button type="primary">
            新增患者
          </n-button>
        </div>
      </div>
      <div mt-14px w-full>
        <BasicTable
          ref="tableRef"
          class="table"
          :columns="columns"
          :request="loadDataTable"
          :row-key="(row:any) => row.id"
          :action-column="actionColumns"
          :pagination="paginationReactive"
          :scroll-x="1350"
          striped
        />
      </div>
    </PageCard>
  </div>
</template>

<style lang="scss" scoped>
.my-button{

  &:hover{
    color: #06AEA6;
    border:1px solid #06AEA6;
    :deep(.n-button__border){
      border:none
    }
    :deep(.n-button__state-border){
      border:none
    }
  }
  &:active{
    background: #D8F6F5;
  }
}
</style>
