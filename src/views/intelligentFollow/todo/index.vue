<script setup lang="ts">
import StateCard from './components//StateCard.vue'
import PatientCard from './components//PatientCard.vue'
import { useRouterPush } from '@/hooks'
import { Breadcrumb } from '@/layouts/common'
import waitFurtherVisitImg from '@/assets/images/intelligentFollow/waitFurtherVisit.png'
import finishFurtherVisit from '@/assets/images/intelligentFollow/finishFurtherVisit.png'
import waitFollow from '@/assets/images/intelligentFollow/waitFollow.png'
import finishFollow from '@/assets/images/intelligentFollow/finishFollow.png'
import expiredImg from '@/assets/images/intelligentFollow/expired.png'
import waitFoinGroupImg from '@/assets/images/intelligentFollow/joinGroup.png'

import activeWaitFurtherVisitImg from '@/assets/images/intelligentFollow/active_waitFurtherVisit.png'
import activeFinishFurtherVisit from '@/assets/images/intelligentFollow/active_finishFurtherVisit.png'
import activeWaitFollow from '@/assets/images/intelligentFollow/active_waitFollow.png'
import activeFinishFollow from '@/assets/images/intelligentFollow/active_finishFollow.png'
import activeExpiredImg from '@/assets/images/intelligentFollow/active_expired.png'
import activeWaitJoinGroupImg from '@/assets/images/intelligentFollow/active_joinGroup.png'

import { getFollowTODOList } from '@/api/intelligentFollow/toDo'
import { useAuthStore } from '~/src/store/modules/auth'

/*
TODO:
1.面包屑字体大小
2. 表头颜色
3.表格行高
*/
const { routerPush } = useRouterPush()
const auth = useAuthStore()

const stateMap: Record<string, any> = {
  waitReturnVisit: {
    active: activeWaitFurtherVisitImg,
    default: waitFurtherVisitImg,
    text: '待复诊',
  },
  revisited: {
    active: activeFinishFurtherVisit,
    default: finishFurtherVisit,
    text: '已复诊',
  },
  waitFollowUp: {
    active: activeWaitFollow,
    default: waitFollow,
    text: '今日待随访',
  },
  followedUp: {
    active: activeFinishFollow,
    default: finishFollow,
    text: '今日已随访',
  },
  overdue: {
    active: activeExpiredImg,
    default: expiredImg,
    text: '逾期患者数',
  },
  waitEnteringGroup: {
    active: activeWaitJoinGroupImg,
    default: waitFoinGroupImg,
    text: '待入组患者',
  },
}

const currentState = ref({
  type: 'waitVisit',
  id: 'waitReturnVisit',
})

const stateCardList = ref([
  { img: stateMap.waitReturnVisit.active, text: stateMap.waitReturnVisit.text, active: true, id: 'waitReturnVisit', type: 'waitVisit', count: 21 },
  { img: stateMap.revisited.default, text: stateMap.revisited.text, active: false, type: 'alreadyVisit', id: 'revisited', count: 21 },
  { img: stateMap.waitFollowUp.default, text: stateMap.waitFollowUp.text, active: false, id: 'waitFollowUp', type: 'waitFollow', count: 22 },
  { img: stateMap.followedUp.default, text: stateMap.followedUp.text, active: false, id: 'followedUp', type: 'alreadyFollow', count: 11 },
  { img: stateMap.overdue.default, type: 'overPatient', text: stateMap.overdue.text, active: false, id: 'overdue', count: 0 },
  { img: stateMap.waitEnteringGroup.default, text: stateMap.waitEnteringGroup.text, active: false, id: 'waitEnteringGroup', type: 'waitPatient', count: 22 },
])

const isTopTwo = computed(() => {
  return stateCardList.value.findIndex(item => item.active) < 2
})

function onChangeStateCard(type: string) {
  if (type === 'waitPatient') {
    routerPush({ name: 'intelligentFollow_confirm' })
    return
  }
  stateCardList.value.forEach((card) => {
    card.active = card.type === type
    const stateText = card.type === type ? 'active' : 'default'
    card.img = stateMap[card.id][stateText]
    if (card.type === type)
      currentState.value = card
  })

  getDataFromNet()
}

const patientCardList = ref<any[]>([])

const isLoading = ref(false)
function getDataFromNet() {
  isLoading.value = true
  getFollowTODOList({
    operateId: auth.userInfo.id,
    queryType: currentState.value.type,
    size: 10000,
  }).then((res: any) => {
    patientCardList.value = res?.data?.planPatient?.records ?? []
    stateCardList.value.forEach((card) => {
      card.count = res?.data[card.id] ?? 0
    })
  }).finally(() => {
    isLoading.value = false
  })
}

/// 跳转到随访管理详情
function jumpToDetail(item) {
  // manage/detail?patientId=1726064491349680129&planId=yg2
  console.log(item)
  routerPush({
    name: 'intelligentFollow_manage_detail',
    query: {
      patientId: item.patientId,
      planId: item.planTemplateId,
    },
  })
}

onMounted(() => {
  getDataFromNet()
})
</script>

<template>
  <div>
    <Breadcrumb route-name="intelligentFollow_todo" />
    <n-spin type="uni" :show="isLoading" size="medium">
      <PageCard breadcrumb>
        <PageTitle class="mb-14px">
          随访待办
        </PageTitle>
        <div class="mb-22px w-full flex justify-between gap-14px">
          <StateCard v-for="(card) in stateCardList" :key="card.id" :state="card" @click="() => onChangeStateCard(card.type)" />
        </div>
        <SectionTitle class="mb-16px">
          患者信息-{{ stateMap[currentState.id].text }}
        </SectionTitle>
        <div v-if="patientCardList?.length" class="mb-22px flex flex-wrap gap-14px">
          <PatientCard v-for="card in patientCardList" :key="card.slmcNo" cursor-pointer :is-top-two="isTopTwo" :state="card" @click="jumpToDetail(card)" />
        </div>
        <div v-else relative w-full flex-center>
          <n-empty absolute top-200px size="large" description="无数据" />
        </div>
      </PageCard>
    </n-spin>
  </div>
</template>
