<script lang='ts' setup>
import { computed } from 'vue'
import femaleImg from '@/assets/images/intelligentFollow/patient_female.png'
import maleImg from '@/assets/images/intelligentFollow/patient_male.png'
import { SvgIcon } from '@/components/Icon'

interface Props {
  state: {
    sexName: string
    patientNm: string
    slmcNo: string
    department: string
    manageDoctorName: string
    planTemplateName: string
    visitDate: string
    followMethod: string
    residentName: string
  }
  isTopTwo: boolean
}
const props = defineProps<Props>()

const avatar = computed(() => {
  return props.state.sexName === '女' ? femaleImg : maleImg
})

const method = computed(() => {
  const showIconMap: any = {
    XCX: {
      show: false,
    },
    DUX: {
      show: false,
    },
    AIH: {
      show: false,
    },
  }
  if (props.state?.followMethod && props.state?.followMethod?.length > 0) {
    const arr = props.state.followMethod.split(',')
    arr.forEach((item) => {
      showIconMap[item].show = true
    })
  }

  return showIconMap
})
function handleTime(time: string) {
  if (time == null || time.length === 0)
    return '-'
  else if (time.length >= 10)
    return time.substring(0, 10)
  else
    return time
}
</script>

<template>
  <section class="patientCard" :class="isTopTwo ? 'h-197px' : 'h-173px'">
    <div class="flex-start mb-20px flex items-center">
      <img h-40px w-40px :src="avatar" alt="">
      <div class="ml-10px flex flex-col">
        <span class="mb-6px text-16px fw-500">
          {{ state.patientNm ?? '-' }}
        </span>
        <span class="text-#666 opacity-90">{{ state.slmcNo }}</span>
      </div>
    </div>
    <div>
      <div v-if="isTopTwo" class="mb-10px" flex>
        <span flex-shrink-0 class="text-#666">科室名称：</span>
        <n-ellipsis max-w-100px>
          {{ state.department ?? '-' }}
        </n-ellipsis>
      </div>
      <div class="mb-10px" flex>
        <span class="text-#666" flex-shrink-0> {{ isTopTwo ? '主治医生：' : '管理医生：' }} </span>
        <n-ellipsis max-w-100px>
          {{ (isTopTwo ? state.residentName : state.manageDoctorName) || '-' }}
        </n-ellipsis>
      </div>
      <div class="mb-14px">
        <span class="text-#666">随访计划：</span><span>
          <n-ellipsis style="max-width: 100px">
            {{ state.planTemplateName }}
          </n-ellipsis>
        </span>
      </div>
    </div>
    <div class="patientCard-bottom">
      <div>
        <span class="text-#666">上次就医：</span><span>{{ handleTime(state?.visitDate) }}</span>
      </div>
      <div class="flex-start flex">
        <SvgIcon v-if="method.XCX.show" local-icon="slmc-icon-xiaochengxu1" size="20px" class="z-3 -mr-3px" />
        <SvgIcon v-if="method.AIH.show" local-icon="slmc-icon-AIdianhua2" size="20px" class="z-2 -mr-3px" />
        <SvgIcon v-if="method.DUX.show" local-icon="slmc-icon-duanxin2" size="20px" />
      </div>
    </div>
  </section>
</template>

<style scoped lang="scss">
.patientCard {
    width: 288px;
    border: 1px solid #d1d1d1;
    border-radius: 3px;
    padding: 20px 20px 10px 20px;
    &-bottom{
        display: flex;
        justify-content: space-between;
        border-top: 1px solid #E8E8E8;
        padding-top: 10px;
    }
}
</style>
