<script lang='ts' setup>
interface Props {
  state: {
    img: string
    text: string
    active: boolean
    id: string
    count: number
  }
}
defineProps<Props>()
</script>

<template>
  <div class="stateCard" :class="[state.active ? 'activeCard' : 'defaultCard']">
    <div class="flex flex-col">
      <span class="text-20px" :class="{ 'text-#fff': state.active }">{{ state.count }}</span>
      <span class="t mt-8px text-12px" :class="[state.active ? ' text-#fff' : 'text-#999']">{{ state.text }}</span>
    </div>
    <img h-40px w-40px :src="state.img" alt="">
  </div>
</template>

<style scoped lang="scss">
.stateCard {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
    cursor: pointer;

    height: 80px;
    min-width: 162px;
   flex:1;
    border-radius: 3px;

}
.activeCard{
    background: #3ac9a8;
    box-shadow: 0px 4px 8px 0px rgba(58,201,168,0.30);
}
.defaultCard {
    background: rgba(165,184,209,0.15);
}
</style>
