<script setup lang='ts'>
import dayjs from 'dayjs'
import { Breadcrumb } from '@/layouts/common'
import { getPlanListApi2 } from '@/api/intelligentFollow/manage'
import { useAuthStore } from '~/src/store'
import { getTheFollowTongJiCount } from '@/api/intelligentFollow/toDo'

const searchParams = reactive({
  planTemplateId: null,
  followDateStart: dayjs().startOf('month').format('YYYY-MM-DD'),
  followDateEnd: dayjs().endOf('month').format('YYYY-MM-DD'),
})
const planTemplates = ref([])
const dateRange = ref([dayjs().startOf('month'), dayjs().endOf('month')])
const tableData = ref()
const { userInfo } = useAuthStore()

function getPlanTemplates() {
  getPlanListApi2({
    search: '',
    operateId: userInfo.id,
  }).then((res) => {
    planTemplates.value = res?.data || []
  })
}

function getTheDataFromNet() {
  getTheFollowTongJiCount(searchParams).then((res) => {
    tableData.value = res?.data
  })
}

function dateRangeChange() {
  if (dateRange.value?.length > 0) {
    searchParams.followDateStart = dateRange.value[0]
    searchParams.followDateEnd = dateRange.value[1]
  }
  else {
    searchParams.followDateStart = ''
    searchParams.followDateEnd = ''
  }
  console.log(dateRange.value)
}

function handleSearchClick() {
  getTheDataFromNet()
}

onMounted(() => {
  getPlanTemplates()
  getTheDataFromNet()
})
</script>

<template>
  <div flex-col>
    <Breadcrumb
      :bread-list="[
        {
          title: '智能随访',
          link: null,
          key: 'intelligentFollow',
        },
        {
          title: '随访统计',
          link: null,
          key: 'intelligentFollow_tongJi',
        },
      ]"
    />
    <PageCard breadcrumb>
      <PageTitle class="mb-14px">
        随访统计
      </PageTitle>
      <div flex flex-wrap items-center style="gap: 20px">
        <div flex items-center>
          <span color="#666" mr-10px text-14px>随访时间</span>
          <el-date-picker
            v-model="dateRange" style="width: 230px" type="daterange" clearable
            unlink-panels
            value-format="YYYY-MM-DD" start-placeholder="开始日期" end-placeholder="结束日期" @change="dateRangeChange"
          />
        </div>
        <div flex items-center>
          <span color="#666" mr-10px text-14px>随访方案</span>
          <el-select-v2
            v-model="searchParams.planTemplateId"
            clearable :options="planTemplates" :props="{
              label: 'planTemplateName',
              value: 'planTemplateId',
            }" filterable placeholder="请选择" style="width: 200px"
          >
            <template #default="{ item }">
              <el-tooltip placement="right" :content="item.planTemplateName">
                <div overflow-hidden style="text-overflow: ellipsis">
                  {{ item.planTemplateName }}
                </div>
              </el-tooltip>
            </template>
          </el-select-v2>
        </div>
        <div flex items-center>
          <n-button type="primary" class="mr-10px" @click="handleSearchClick">
            查询
          </n-button>
        </div>
      </div>
      <div mt-16px flex-col flex-1 style="overflow: hidden">
        <el-table scrollbar-always-on show-summary :style="{ maxHeight: '100%' }" :data="tableData" style="width: 100%">
          <el-table-column
            align="center" label="日期" :formatter="({ followDate }) => {
              return followDate
            }"
          />
          <el-table-column align="center" prop="putNum" label="建档数" />
          <el-table-column label="随访任务" align="center">
            <el-table-column align="center" prop="patientNum" label="患者数" />
            <el-table-column align="center" prop="finishNum" label="已完成" />
            <el-table-column align="center" prop="startNum" label="进行中" />
            <el-table-column align="center" prop="overNum" label="已逾期" />
            <el-table-column align="center" prop="noStartNum" label="未开始" />
          </el-table-column>
          <el-table-column label="随访记录" align="center">
            <el-table-column align="center" prop="patientInfoNum" label="患者数" />
            <el-table-column align="center" prop="finishInfoNum" label="已完成" />
            <el-table-column align="center" prop="startInfoNum" label="进行中" />
            <el-table-column align="center" prop="overInfoNum" label="已逾期" />
            <el-table-column align="center" prop="noStartInfoNum" label="未开始" />
          </el-table-column>
        </el-table>
      </div>
    </PageCard>
  </div>
</template>

<style lang="scss" scoped>
:deep(.pageCard-content){
    display: flex;
    flex-direction: column;
}
</style>
