<script setup lang="ts">
import dayjs from 'dayjs'
import { NButton } from 'wowjoy-vui'
import { computed, reactive, ref } from 'vue'
import type HighSearch from '@/views/dataCenter/components/HighSearch.vue'
import { RenderPhone } from '@/components/business/UiRender'
import { BasicTable } from '@/components/Table'
import { isPhone, sexIcon } from '@/utils/common'
import { SvgIcon } from '@/components/Icon'
import { useAuthStore } from '@/store'
import type { PatientRecord } from '@/api/specificDisease'
import { exportInpatientData, getInpatientListApi } from '@/api/patient/patient'

const auth = useAuthStore()
const userInfo = auth.userInfo
const range = ref([dayjs().subtract(1, 'week').valueOf(), dayjs().valueOf()])
const pickerOptions = ref()

const tableRef = ref<InstanceType<typeof BasicTable> | null>(null)
const hightSearchRef = ref<InstanceType<typeof HighSearch>>()

/**
 * 渲染患者姓名列
 * @param row 行数据
 */
function renderPatientNameInfo(row: any) {
  const { patientName, sexName, age, ageUnit } = row

  const localIcon = sexIcon(sexName)
  return h('div', {
    class: 'flex ',
  }, [
    h('span', {
      class: 'truncate',
    }, patientName),
    sexName && h(SvgIcon, {
      size: 16,
      class: 'mx-6px ',
      localIcon,

    }),
    h('span', { class: 'truncate' }, { default: () => age ? `${age}${ageUnit || '岁'}` : '' }),
  ])
}
/**
 * 渲染手机号信息
 * @param row 行信息
 */
function renderPhoneInfo(row: PatientRecord) {
  const { phone } = row

  // 判断是否手机号 ，再脱敏
  if (isPhone(phone))
    return h(RenderPhone, { phone })

  else return phone
}

// 表格列初始化
function createColumns() {
  return [
    {
      type: 'selection',
      width: 55,
    },
    {
      title: '序号',
      key: 'index',
      width: 70,
      render(_: PatientRecord, index: number) {
        return index + 1
      },
    },
    {
      title: '患者信息',
      key: 'patientName',
      width: 180,
      render(row: PatientRecord) {
        return renderPatientNameInfo(row)
      },
    },
    {
      title: '入院时间',
      key: 'treatDate',
      width: 210,
      // render(row: PatientRecord) {
      //   return renderPatientTagInfo(row)
      // },
    },
    {
      title: '手机号',
      key: 'phone',
      width: 150,
      render(row: any) {
        return renderPhoneInfo(row)
      },
    },

    {
      title: '病历号',
      key: 'patientRecordNo',
      width: 150,
      ellipsis: false,
    },

    // {
    //   title: '责任医生',
    //   key: 'manageDoctorName',
    //   width: 120,
    // },
    {
      title: '加入SLMC日期',
      key: 'createTime',
      width: 120,
      render(row: PatientRecord) {
        const { createTime } = row
        return createTime.substring(0, 10)
      },
    },
  ]
}

const columns = createColumns()
/** 默认结尾时间 */
const DEFAULT_END_TIME = dayjs().valueOf()
/** 默认开始时间，往前推半年 */
const START_END_TIME = dayjs().subtract(1, 'week').valueOf()

// 分页器
const paginationReactive = reactive({
  page: 1,
  pageSize: 10,
  pageSizes: [10, 20, 50, 100],
})

const tableParams = reactive({
  size: 10,
  start: 1,
  startTime: START_END_TIME,
  endTime: DEFAULT_END_TIME,
})

const tableTotal = ref('0')
// 加载表格数据
async function loadDataTable(res: { size: number; start: number; isPageChange?: boolean }) {
  if (!res.isPageChange)
    hightSearchRef?.value?.syncGroupList()
  console.log('userInfo====', userInfo)
  const params = {
    userId: userInfo.id,
    start: res.start,
    size: res.size,
    startTime: tableParams.startTime && dayjs(tableParams.startTime).format('YYYY-MM-DD 00:00:00'),
    endTime: tableParams.endTime && dayjs(tableParams.endTime).format('YYYY-MM-DD 23:59:59'),
  }
  const result = await getInpatientListApi<PatientRecord>({ ...params, ...res })
  tableTotal.value = result.data.total

  return result
}

/** 表格选中的key */
const selectPatientKeys = ref<string[]>([])

function showWarning() {
  window.$message.warning('请完善高级搜索条件')
}

const dropdownOptions = computed(() => {
  return [
    {
      label: `导出选中（共${selectPatientKeys.value.length}条）`,
      key: 'selected',
      value: 'selected',
      disabled: selectPatientKeys.value.length <= 0,
    },
    {
      label: `导出所有（共${tableTotal.value}条）`,
      key: 'all',
      value: 'all',
    },
  ]
})

const searchParamsForModal = ref('')
const exportCount = ref<'selected' | 'all'>('selected')

function handleDropdownSelect(key: 'selected' | 'all') {
  const highSearchParams = hightSearchRef?.value?.genSearchParams()
  const params = {
    list: highSearchParams ? highSearchParams.list : [],
    startTime: tableParams.startTime && dayjs(tableParams.startTime).format('YYYY-MM-DD 00:00:00'),
    endTime: tableParams.endTime && dayjs(tableParams.endTime).format('YYYY-MM-DD 23:59:59'),
  }

  searchParamsForModal.value = JSON.stringify({
    ...params,
    ...highSearchParams,
  })
  console.log('导出====', key)
  exportCount.value = key
  if (key === 'selected') {
    ///
    const pams = {
      userId: userInfo.id,
      userName: userInfo.userName,
      allExport: false,
      idList: selectPatientKeys.value,
    }

    exportInpatientData(pams).then(() => {
      window.$message.success('正在导出中，请到导出记录中下载记录文件')
    })
  }
  else {
    const pams = {
      userId: userInfo.id,
      userName: userInfo.userName,
      allExport: true,
      startTime: tableParams.startTime && dayjs(tableParams.startTime).format('YYYY-MM-DD 00:00:00'),
      endTime: tableParams.endTime && dayjs(tableParams.endTime).format('YYYY-MM-DD 23:59:59'),
    }
    exportInpatientData(pams).then(() => {
      window.$message.success('正在导出中，请到导出记录中下载记录文件')
    })
  }
  // 导出的接口啦
}

function updatePickerOptions() {
  const startDate = range.value[0] ? new Date(range.value[0]).getTime() : null
  const endDate = range.value[1] ? new Date(range.value[1]).getTime() : null

  pickerOptions.value = {
    disabledDate: (time) => {
      if (startDate) {
        const oneWeekLater = startDate + 30 * 24 * 60 * 60 * 1000 // 起始日期后一周时间
        return time.getTime() < startDate || time.getTime() > oneWeekLater // 禁用一周以外的日期
      }
      return false
    },
  }
}

/// 时间禁用
function disabledDate(time: Date) {
  return time.getTime() > Date.now()
}

function onConfirm(val: number | number[] | null) {
  console.log('v====', val)
  if (val && val.length === 2) {
    // 如果已经选择了两个日期
    const startDate = new Date(val[0]).getTime()
    const endDate = new Date(val[1]).getTime()

    // 计算起始日期后一周的时间
    const oneWeekLater = startDate + 30 * 24 * 60 * 60 * 1000

    // 更新 pickerOptions 禁用日期逻辑
    updatePickerOptions()

    tableParams.startTime = dayjs(val[0]).valueOf()
    // 如果结束日期超出起始日期7天，则进行修正
    if (endDate > oneWeekLater) {
      range.value = [val[0], new Date(oneWeekLater)]
      window.$message.warning('俩个日期不能超过一个月')
      val[1] = oneWeekLater
      tableParams.endTime = oneWeekLater
    }
    else {
      tableParams.endTime = dayjs(val[1]).valueOf()
    }
  }
}
</script>

<template>
  <div>
    <div class="specificDiseaseList">
      <PageCard>
        <PageTitle class="mb-14px">
          住院数据
        </PageTitle>
        <n-form
          label-placement="left"
          :model="tableParams"
          :show-feedback="false"
        >
          <div class="mb-14px flex flex-wrap justify-start gap-x-20px gap-y-14px">
            <n-form-item label="入院时间">
              <el-date-picker
                v-model="range"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                :picker-options="pickerOptions"
                :disabled-date="disabledDate"
                @change="onConfirm"
              />

              <!-- <n-date-picker v-model:value="tableParams.startTime" clearable type="date" style="width: 204px;" />
              <span class="mx-10px">—</span>
              <n-date-picker v-model:value="tableParams.endTime" clearable type="date" style="width: 204px;" /> -->
            </n-form-item>

            <NButton type="primary" @click="tableRef?.reload()">
              查询
            </NButton>

            <n-dropdown :options="dropdownOptions" trigger="click" @select="handleDropdownSelect">
              <NButton :disabled="tableTotal === '0'" type="primary" ghost icon-placement="right">
                导出
                <template #icon>
                  <SvgIcon local-icon="slmc-icon-drop_down" size="12" class="icon-drop_down" />
                </template>
              </NButton>
            </n-dropdown>
          </div>
        </n-form>

        <BasicTable
          ref="tableRef"
          v-model:checked-row-keys="selectPatientKeys"
          :columns="columns"
          :request="loadDataTable"
          :row-key="(row:any) => row.id"
          :pagination="paginationReactive"
          :scroll-x="1400"
          striped
        />
        <div relative>
          <div
            v-show="!hightSearchRef?.isGroupAllEmpey && hightSearchRef?.isGroupListEmpty"
            absolute bottom-0px right-0 z-100 h-28px w-628px cursor-pointer bg-red op-0
            @click="showWarning"
          />
        </div>
      </PageCard>
    </div>
  </div>
</template>

<style scoped lang="scss">
.specificDiseaseList {
    .followStatus{
        width: 56px;
        height: 18px;
        color: #fff;
        border-radius: 9px;
        text-align: center;
        line-height: 18px;

    }
}

.vertifycode {
  margin-left: -2px;
}
</style>
