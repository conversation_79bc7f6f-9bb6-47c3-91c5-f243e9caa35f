<script setup lang="ts">
import dayjs from 'dayjs'
import { localStg } from '@/utils'
import { useAuthStore } from '@/store'
import { exportDataRecord, reExportData } from '@/api/patient/patient'

const tableData = ref<any[]>([])
const { userInfo } = useAuthStore()
const PREFIX = '/heper-api'

const page = ref({
  page: 1,
  size: 10,
  total: 0,
})

async function getTableData() {
  try {
    const { data } = await exportDataRecord<any>({
      size: page.value.size,
      start: page.value.page,
      userId: userInfo.id,
      userName: userInfo.userName,
    })
    tableData.value = data.records
    page.value.total = Number(data.total)
  }
  catch (error) {

  }
}

async function download(url: string, fileName: string) {
  try {
    const response = await fetch(`https://rs.cminfo.net${url}`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json;charset=UTF-8',
        'slmcToken': localStg.get('accessToken') || '',
      },
    })

    if (response.status === 200) {
      response.blob().then((blob) => {
        const link = document.createElement('a')
        link.href = window.URL.createObjectURL(blob)
        link.download = fileName || '人工肝导出' + '.xlsx'
        link.click()
        window.URL.revokeObjectURL(link.href)
      })
    }
    else {
      window.$message.error('下载失败')
    }
  }
  catch (error) {
    window.$message.error('下载失败')
    console.log(error)
  }
}

async function redownload(id) {
  try {
    const { data } = await reExportData(id)
    if (data) {
      window.$message.success('操作成功')
      getTableData()
    }
  }
  catch (error) {
    console.log(error)
    window.$message.error('操作失败')
  }
}

onMounted(() => {
  getTableData()
})
</script>

<template>
  <div>
    <!-- <Breadcrumb
      :bread-list="[
        { title: '数据导出', key: 'dataCenter', link: '/exportdata/inpatient' },
        { title: '导出记录', key: 'dataCenter_records', link: null },
      ]"
    /> -->

    <PageCard breadcrumb>
      <PageTitle class="mb-14px">
        导出记录
      </PageTitle>

      <el-table :data="tableData" show-overflow-tooltip>
        <el-table-column type="index" width="80" label="序号" />
        <el-table-column label="文件名称" :formatter="({ fileName }) => fileName || '-'" />
        <el-table-column label="操作人" :formatter="({ userName }) => userName || '-'" />
        <el-table-column label="操作时间" :formatter="({ createTime }) => dayjs(createTime).format('YYYY-MM-DD HH:mm:ss') || '-'" />
        <el-table-column label="状态">
          <template #default="{ row }">
            <div flex items-center gap-6px>
              <SvgIcon v-if="row.exportStatus === '导出失败'" local-icon="slmc-icon-dot" size="6" color="red" />
              <SvgIcon v-else-if="row.exportStatus === '导出成功'" local-icon="slmc-icon-dot" size="6" color="#4ACFB1" />
              <SvgIcon v-else local-icon="slmc-icon-dian-cheng" size="12" />
              {{ row.exportStatus }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" fixed="right">
          <template #default="{ row }">
            <template v-if="userInfo.id === row.userId">
              <a v-if="row.exportStatus === '导出成功'" :href="`http://10.20.32.21:31332${row.fileUrl}`" cursor-pointer style="color: #3B8FD9;" text="下载">下载</a>
              <span v-else-if="row.exportStatus === '导出失败'" cursor-pointer text="#3B8FD9" @click="redownload(row.id)">再次导出</span>
              <span v-else text="#3B8FD9/30" cursor-not-allowed>下载</span>
            </template>

            <template v-else>
              <span text="#3B8FD9/30" cursor-not-allowed>下载</span>
            </template>
          </template>
        </el-table-column>

        <template #empty>
          <div h-300px flex items-center justify-center>
            <DataEmpty />
          </div>
        </template>
      </el-table>

      <div v-if="page.total > 0" mt-20px w-full flex justify-end>
        <n-pagination
          v-model:page="page.page"
          v-model:page-size="page.size"
          :item-count="page.total"
          :page-sizes="[5, 10, 20, 30]"
          show-size-picker
          show-quick-jumper
          @update:page="getTableData"
          @update:page-size="() => {
            page.page = 1
            getTableData()
          }"
        />
      </div>
    </PageCard>
  </div>
</template>
