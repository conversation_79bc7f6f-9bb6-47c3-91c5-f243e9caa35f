<script setup lang="ts">
import type { FormInst } from 'wowjoy-vui'
import CardModal from './CardModal.vue'
import { addPlan, batchUpdate, doctorList, editPatient, editorPlan, getSmsTemplateAPI, insertPatient, nameIsAvailable, sendSms } from '~/src/api/followCenter/manerge'
import { useAuthStore } from '~/src/store'
import { getTagListAll } from '~/src/api/followCenter/tag'
import type { AddPatient } from '~/src/api/patient/type'

const props = defineProps({
  modelShow: {
    type: Object as PropType<any>,
    default: () => {},
  },
  tagList: {
    type: Array as PropType<any>,
    default: () => [],
  },
  organId: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['update', 'updateTableData', 'updateCheckPatients'])

const auth = useAuthStore()

const userInfo = auth.userInfo
const checkPatients = ref<any>([])
const cardType = ref<any>(null)
const tableData = ref<string[]>([])

// 获取短信模板列表
async function getSmsTemplate() {
  try {
    const { data } = await getSmsTemplateAPI<string[]>()
    if (data)
      tableData.value = data
  }
  catch (error) {

  }
}

const IsInsertPlan = ref(false)
const statusList = [
  { value: 'FOLLOW', label: '随访中' },
  { value: 'FINISH', label: '完成' },
  { value: 'REFUSE', label: '拒绝随访' },
  { value: 'DEAD', label: '死亡' },
  { value: 'LOSS', label: '失访' },
]

const cardValue = ref(false)

const channel = ref([
  { label: '手机短信', value: '1' },
  // { label: '微信公众号 ', value: '2' },
  // { label: '微信服务号', value: '3' },
])

const tagList = ref([])

// const radio = ref(null)

const formValue = ref<any>({
  days: null,
  isShare: null,
  doctorId: null,
  plan: 'CHB',
  planTemplateId: null,
  planTemplateName: null,
  planTemplateSort: null,
})

const changePlanModel = ref<any>({
  planStart: 'CHB',
  planEnd: null,
  isShare: '',
  doctor: '',
  planTemplateId: '',
  planTemplateName: '',
  planTemplateSort: '',
})
const changeDoctorModel = ref<any>({
  doctorId: null,
  doctorName: '',
})
const patientRegisterModel = ref<AddPatient>({
  addPatientType: 'SFZ',
  doctorId: '',
  doctorName: '',
  medicalInsuranceNo: '',
  patientIdCardNum: '',
  patientIdCardType: '',
  patientName: '',
  patientRecordNo: '',
  primaryIndex: '',
  userId: '',
  operateType: '0',
  phone: '',
  createOrganId: userInfo.organId,

})

const postMessageModel = ref({
  msgType: '1',
  msgWays: ['1'],
  text: '',
  radio: 0,
})

const modifyLabelModal = ref({
  addTagIds: [],
  removeTagIds: [],
})

const ModifyStatusModal = ref({
  patientStatus: null,
})

const planAOps = ref([
  { label: '乙肝', value: 'CHB' },
  { label: '脂肪肝', value: 'FLB' },
  { label: '丙肝', value: 'HCV' },
])

const planAOpsB = ref<any>([])

const IncludedRule = {
  doctorId: {
    required: true,
    message: '请选择管理医生',
    trigger: 'blur',
  },
  plan: {
    required: true,
    message: '请输入随访计划',
    trigger: ['input', 'blur'],
  },
}
const PostMsgRule = {
  msgWays: {
    required: true,
    trigger: 'change',
    type: 'array',
    message: '请选择消息渠道',
  },
  text: {
    required: true,
    trigger: ['blur', 'input'],
    message: '请输入消息模板',
  },
  // radio: {
  //   required: true,
  //   trigger: 'change',
  //   message: '请选择消息渠道',
  // },
}
const patientRules = {
  patientName: {
    required: true,
    trigger: 'blur',
    message: '请输入姓名',
  },
  patientIdCardNum: {
    required: true,
    validator(_: any, value: string) {
      return validateIDCard(value)
    },
    trigger: 'blur',
    message: '请输入正确的身份证号码',
  },
  phone: {
    validator(_: any, value: string) {
      return validatePhoneNumber(value)
    },
    required: true,
    trigger: 'blur',
    message: '请输入正确的手机号',
  },

}

const doctorRules = {
  doctorId: {
    required: true,
    trigger: ['blur', 'change'],
    message: '请选择管理医生',
  },
}

const statusRules = {
  patientStatus: {
    required: true,
    trigger: ['blur', 'change'],
    message: '请选择患者状态',
  },
}

const modeShow = reactive({
  IncludedModal: false,
  ChangePlanModal: false,
  changeDoctorShow: false,
  patientRegisterShow: false,
  postMessageModel: false,
  modifyLabelModal: false,
  modifyStatusModal: false,
})

const formRef = ref<FormInst | null>(null)
const addFormRef = ref<FormInst | null>(null)
const doctorRef = ref<FormInst | null>(null)
const postMsgRef = ref<FormInst | null>(null)
const tagRef = ref<FormInst | null>(null)
const StatusRef = ref<FormInst | null>(null)
const inFollowFormRef = ref<FormInst | null>(null)

const doctorOpts = ref<any>([])

watch(() => props.modelShow, (model) => {
  modeShow.IncludedModal = model.IncludedModal
  modeShow.ChangePlanModal = model.ChangePlanModal
  modeShow.changeDoctorShow = model.changeDoctorShow
  modeShow.patientRegisterShow = model.patientRegisterShow
  modeShow.postMessageModel = model.postMessageModel
  modeShow.modifyLabelModal = model.modifyLabelModal
  modeShow.modifyStatusModal = model.modifyStatusModal

  if (modeShow.changeDoctorShow) {
    setTimeout(() => {
      // getDoctorList()
    }, 2000)
  }

  // getNamePlan()
}, { immediate: true, deep: true })

watch(() => modeShow, (model) => {
  emit('update', model)
}, { immediate: true, deep: true })

const selectedPatients = ref<any>([])

watch(() => props.tagList, (model) => {
  selectedPatients.value = model
}, { deep: true, immediate: true })

// watch(() => props.organId, (model) => {
//   // console.log(model)
// }, { immediate: true, deep: true })

function validateIDCard(idCard: string) {
  // 身份证号码校验逻辑
  // 校验长度
  if (idCard.length !== 18)
    return false
  // 校验格式
  const regExp = /^[1-9]\d{16}(\d|X|x)$/
  if (!regExp.test(idCard))
    return false
  // 校验校验码
  const factors = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2, 1]
  const parityBit = '10X98765432'
  let sum = 0
  for (let i = 0; i < 17; i++)
    sum += Number.parseInt(idCard[i]) * factors[i]
  const modulus = sum % 11
  const checkCode = parityBit[modulus]
  if (idCard[17].toUpperCase() !== checkCode)
    return false

  return true // 校验通过
}

function getDoctorList() {
  doctorList({
    organId: props.organId ? props.organId : userInfo.organId,
    name: '',
  }).then((res) => {
    doctorOpts.value = res.data
  }).catch((err) => {
    console.log(err)
  })
}

function renderText(key: string, compare: string) {
  if (!planAOpsB.value[key])
    return

  const renderText = planAOpsB.value[key].find((v: any) => v.planTemplateId === compare)
  if (renderText)
    return renderText.planTemplateMemo
  else
    return null
}
function renderPlan(key: string, compare: string) {
  if (!planAOpsB.value[key])
    return

  const renderText = planAOpsB.value[key].find((v: any) => v.planTemplateId === compare)
  if (renderText)
    return renderText.sortMemo
  else
    return null
}
function validatePhoneNumber(phoneNumber: string) {
  // 手机号校验逻辑
  // 校验长度
  if (phoneNumber.length !== 11)
    return false

  // 校验格式
  const regExp = /^1[3456789]\d{9}$/

  if (!regExp.test(phoneNumber))
    return false

  return true // 校验通过
}

function flatMapArray(array) {
  if (array.length === 0) {
    return []
  }
  else {
    const flattenedTags = [].concat(...array.map((item: any) => item.tag && item.tag.split(',')))
    const uniqueTags = [...new Set(flattenedTags)]
    return uniqueTags
  }
}
// function flatMapTags(array: any): any {
//   if (array.length !== 0) {
//     const flattenedTags = array.flatMap((item: any) => {
//       return item.tagId ? item.tagId.split(',') : []
//     })
//     const uniqueTags = [...new Set(flattenedTags)]
//     return uniqueTags
//   }
// }

function mouseenter(item: any) {
  if (selectedPatients.value.length > 1)
    item.hover = true
}
function mouseleave(item: any) {
  item.hover = false
}

function deleteTag(_: any, index: number) {
  if (selectedPatients.value.length === 1)
    return

  selectedPatients.value.splice(index, 1)
}

// 保存
async function messageButtonClick() {
  try {
    const errors = await postMsgRef.value?.validate()
    const phones = selectedPatients.value.filter((i: any) => i.phone).map((i: any) => i.phone).join(',')
    let content = ''
    if (postMessageModel.value.msgType === '1')
      content = tableData.value[postMessageModel.value.radio]

    else
      content = postMessageModel.value.text

    if (!errors) {
      const params = {
        content,
        phones,
        organId: userInfo.organId,
      }

      const { data } = await sendSms(params)

      if (data) {
        resetMsg()
        window.$message.success('发送成功!')
        modeShow.postMessageModel = false
      }
    }
  }
  catch (error) {

  }
}

// 重置消息弹窗
function resetMsg() {
  postMessageModel.value = {
    msgType: '1',
    msgWays: ['1'],
    text: '',
    radio: 0,
  }
}

function getTagListFunc() {
  getTagListAll({
    tagName: '',
    organId: userInfo.organId,
  }).then((res: any) => {
    checkPatients.value = res.data
  })
}

function saveChangePlan(_: any) {
  const params = {
    ...changePlanModel.value,
    primaryIndex: [...selectedPatients.value.map(item => item.primaryIndex)],
  }
  // console.log(params)
  if (!params.planTemplateId) {
    window.$message.error('请选择随访计划')
    return
  }
  editorPlan(params).then((res: any) => {
    if (res.data !== null) {
      modeShow.ChangePlanModal = false
      window.$message.success('保存成功')
      emit('updateTableData')
    }
  })
}

function saveTagHandle() {
  const params = {
    ...modifyLabelModal.value,
    primaryIndexes: selectedPatients.value.map((v: any) => v.primaryIndex),
    originId: userInfo.organId,
    userId: userInfo.id,
    userName: userInfo.userName,
  }

  batchUpdate(params).then((res: any) => {
    if (res.data !== null) {
      modeShow.modifyLabelModal = false
      window.$message.success('保存成功')
      checkPatients.value = []
      emit('updateCheckPatients', [])
      emit('updateTableData')
    }
  })
  // tagRef.value?.validate((errors) => {
  //   if (!errors)
  //     console.log('success')
  // })
}

function saveInFollow() {
  inFollowFormRef.value?.validate((errors) => {
    if (!errors) {
      const params = {
        ...formValue.value,
        primaryIndex: selectedPatients.value.map((v: any) => v.primaryIndex),
      }
      addPlan(params).then((res: any) => {
        if (res.data !== null) {
          modeShow.IncludedModal = false
          window.$message.success('保存成功')
          emit('updateTableData')
          checkPatients.value = []
        }
      })
    }
  })
}

function cardHandler() {
  cardType.value = 1
  cardValue.value = true
}

function getNamePlan() {
  nameIsAvailable('').then((res: any) => {
    // console.log(res)
    const opts = planAOpsB.value = res.data
    planAOpsB.value = opts
  })
}

function saveChangeDoctor() {
  // console.log(changeDoctorModel.value)
  doctorRef.value?.validate(async (errors: any) => {
    if (!errors) {
      window.$dialog.create({
        title: `患者当前管理医院为${userInfo?.organName},变更管理医生后,管理医院将切换为本院,是否继续操作?`,
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: () => {
          if (!IsInsertPlan.value) {
            const params = {
              ...changeDoctorModel.value,
              primaryIndex: selectedPatients.value.map((v: any) => v.primaryIndex),
            }
            editPatient(params).then((res: any) => {
              if (res.data !== null) {
                modeShow.changeDoctorShow = false
                window.$message.success('保存成功')
                emit('updateTableData')
                checkPatients.value = []
                emit('updateCheckPatients', [])
              }
            })
          }
          else {
            const params = {
              ...patientRegisterModel.value,
              doctorId: changeDoctorModel.value.doctorId,
              operateType: '2',
              doctorName: changeDoctorModel.value.doctorName,
            }
            insertPatient(params).then((res: any) => {
              if (res.data !== null) {
                modeShow.changeDoctorShow = false
                modeShow.patientRegisterShow = false
                window.$message.success('保存成功')
                emit('updateTableData')
                checkPatients.value = []
                IsInsertPlan.value = false
              }
            })
          }
        },
      })
    }
  })
}

function changePlanStart() {
  changePlanModel.value.planEnd = ''
}

function changePlanStart2() {
  formValue.value.planTemplateId = null
}
function changePlanEnd(item: any, opts: any) {
  if (opts) {
    changePlanModel.value.planTemplateSort = opts.planTemplateSort
    changePlanModel.value.planTemplateId = opts.planTemplateId
    changePlanModel.value.planTemplateName = opts.planTemplateName
  }
}
function changePlanEnd2(_: any, opts: any) {
  if (opts) {
    formValue.value.planTemplateSort = opts.planTemplateSort
    formValue.value.planTemplateId = opts.planTemplateId || null
    formValue.value.planTemplateName = opts.planTemplateName
  }
}
function updateIncludedModal() {
  // console.log('updateIncludedModal')
  formValue.value.planTemplateId = ''
  formValue.value.planTemplateName = ''
  formValue.value.planTemplateSort = ''
  formValue.value.doctorId = ''
  formValue.value.plan = ''
  formValue.value.planTemplateSort = ''
  modeShow.IncludedModal = false
}
function afterEnter(key: string) {
  // console.log('afterchange')
  changeDoctorModel.value.doctorId = null
  changeDoctorModel.value.doctorName = null
  if (key === 'doctor') {
    getDoctorList()
  }

  else if (key === 'modify') {
    modifyLabelModal.value.addTagIds = []
    modifyLabelModal.value.removeTagIds = []
    getTagListFunc()
  }
}

function updatePatientStatus() {
  // modifyStatusModal.value.patientStatus = ''
  StatusRef.value?.validate((errors) => {
    if (!errors) {
      const params = {
        ...ModifyStatusModal.value,
        primaryIndex: selectedPatients.value.map((v: any) => v.primaryIndex),
      }
      editPatient(params).then((res: any) => {
        if (res.data !== null)
          modeShow.modifyStatusModal = false
        window.$message.success('保存成功')
        emit('updateTableData')
        checkPatients.value = []
        emit('updateCheckPatients', [])
        ModifyStatusModal.value.patientStatus = null
      })
    }

    else { console.log(errors) }
  })
}

async function addPatient() {
  try {
    const errors = await addFormRef.value?.validate()
    if (errors)
      return
    // 木酱紫
    // 18814815952
    const patientIdCardNum = patientRegisterModel.value.patientIdCardNum
    const { data } = await insertPatient(patientRegisterModel.value)

    const key = Object.keys(data)
    // modeShow.patientRegisterShow = false
    //   window.$message.success('患者登记成功')
    //   emit('updateTableData')
    if (key.includes('0') && !key.includes('1')) {
      modeShow.patientRegisterShow = false
      window.$message.success('保存成功')
      emit('updateTableData')
    }

    if (key.includes('1')) {
      window.$dialog.create({
        title: '患者已存在列表,是否查找该患者',
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: () => {
          // console.log(patientRegisterModel.value)
          // window.$message.success('我就知道')
          modeShow.patientRegisterShow = false
          // window.$message.success('保存成功')
          emit('updateTableData', patientIdCardNum)
        },
      })
    }
    if (key.includes('2')) {
      const hospitalName = Object.values(data).map((value: any) => value.toString())[0]

      window.$dialog.create({
        title: `患者已在${hospitalName}登记且未纳入随访,是否转移患者至本院？`,
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: () => {
          // window.$message.success('我就知道')
          // console.log(patientRegisterModel.value)
          const params = {
            ...patientRegisterModel.value,
            operateType: '2',
          }
          insertPatient(params).then((res: any) => {
            if (res.data) {
              modeShow.patientRegisterShow = false
              // window.$message.success('保存成功')
              emit('updateTableData')
            }
          })
        },
      })
    }
    if (key.includes('3')) {
      const hospitalName = Object.values(data).map((value: any) => value.toString())[0]

      window.$dialog.create({
        title: `患者已在${hospitalName}登记且已纳入随访,是否转移患者至本院？`,
        positiveText: '确定',
        negativeText: '取消',
        onPositiveClick: () => {
          const params = {
            ...patientRegisterModel.value,
            operateType: '3',
          }
          insertPatient(params).then((res: any) => {
            if (res.data) {
              modeShow.changeDoctorShow = true
              IsInsertPlan.value = true
            }
          })
        },
      })
    }

    // else {
    //   window.$message.error('患者登记失败')
    // }
  }
  catch (error) {
    console.log(error)
  }
}

onMounted(() => {
  getNamePlan()
  getSmsTemplate()
})
</script>

<template>
  <n-modal
    v-model:show="modeShow.IncludedModal"
    preset="card"
    :style="{ width: '560px' }"
    title="纳入随访"
    :mask-closable="false"
    head-style="divide"
    :on-update-show="updateIncludedModal"
    :on-after-leave="() => {
      formValue.doctorId = null
      formValue.planTemplateId = null
      formValue.planTemplateName = null
      formValue.planTemplateSort = null
    }"
  >
    <div>
      <n-form
        v-if="modeShow.IncludedModal"
        ref="inFollowFormRef"
        label-placement="left"
        class="my-10px mr-30px"
        require-mark-placement="left"
        :model="formValue"
        :label-width="100"
        :rules="IncludedRule"
      >
        <n-form-item label="管理医生" path="doctorId">
          <n-select
            v-model:value="formValue.doctorId"
            placeholder="请选择"
            :options="doctorOpts"
            label-field="doctorName"
            value-field="doctorId"
          />
        </n-form-item>

        <n-form-item path="plan" label="随访计划">
          <n-select
            v-model:value="formValue.plan"
            placeholder="请选择随访计划"
            :maxlength="50"
            :options="planAOps"
            @change="changePlanStart2()"
          />
          <div class="mx-10px">
            —
          </div>
          <n-select
            v-model:value="formValue.planTemplateId"
            placeholder="请选择"
            label-field="planTemplateName"
            :disabled="!formValue.plan"
            value-field="planTemplateId"
            :maxlength="50"
            :options="planAOpsB[formValue.plan]"
            @change="changePlanEnd2"
          />
        </n-form-item>
        <n-form-item v-if="renderPlan(formValue.plan, formValue.planTemplateId)" path="phone" label="随访计划介绍">
          <div class="bg-#f5f5f5" style="border: 1px solid #d1d1d1;border-radius: 3px;" w-full p-10px>
            {{ renderPlan(formValue.plan, formValue.planTemplateId) }}
          </div>
        </n-form-item>
        <n-form-item v-if="renderText(formValue.plan, formValue.planTemplateId) " path="phone" label="随访路径">
          <div w-full class="bg-#f5f5f5" p-10px style="border: 1px solid #d1d1d1;border-radius: 3px;">
            {{ renderText(formValue.plan, formValue.planTemplateId) }}
          </div>
        </n-form-item>
      </n-form>
    </div>
    <template #footer>
      <div mb-14px ml-100px flex items-center>
        <n-button type="primary" @click="saveInFollow">
          保存
        </n-button>
        <n-button
          ml-20px @click="() => {
            modeShow.IncludedModal = false
            checkPatients.value = []
          }"
        >
          取消
        </n-button>
      </div>
    </template>
  </n-modal>
  <n-modal
    v-model:show="modeShow.ChangePlanModal"
    preset="card"
    :style="{ width: '560px', minHeight: '250px' }"
    title="变更随访计划"
    :mask-closable="false"
    head-style="divide"
    :on-after-leave="() => {
      changePlanModel.planEnd = null
      changePlanModel.planTemplateId = null
      changePlanModel.planTemplateName = null

    }"
  >
    <div>
      <n-form
        ref="formRef"
        label-placement="left"
        class="my-10px mr-30px"
        require-mark-placement="left"
        :model="changePlanModel"
        :label-width="110"
      >
        <n-form-item path="phone" label="随访计划" mt-20px>
          <n-select
            v-model:value="changePlanModel.planStart"
            placeholder="请输入"
            :maxlength="50"
            :options="planAOps"
            @change="changePlanStart"
          />
          <div class="mx-10px">
            —
          </div>
          <n-select
            v-model:value="changePlanModel.planEnd"
            placeholder="请选择"
            label-field="planTemplateName"
            value-field="planTemplateId"
            :maxlength="50"
            :options="planAOpsB[changePlanModel.planStart]"
            @change="changePlanEnd"
          />
        </n-form-item>
        <n-form-item path="phone" label="随访计划介绍">
          <div v-if="renderPlan(changePlanModel.planStart, changePlanModel.planTemplateId)" class="bg-#f5f5f5" style="border: 1px solid #d1d1d1;border-radius: 3px;" w-full p-10px>
            {{ renderPlan(changePlanModel.planStart, changePlanModel.planTemplateId) }}
          </div>
          <div v-else w-full class="bg-#f5f5f5" p-10px style="border: 1px solid #d1d1d1;border-radius: 3px;">
            -
          </div>
        </n-form-item>
        <n-form-item path="phone" label="随访路径">
          <div v-if="renderText(changePlanModel.planStart, changePlanModel.planEnd)" w-full class="bg-#f5f5f5" p-10px style="border: 1px solid #d1d1d1;border-radius: 3px;">
            {{ renderText(changePlanModel.planStart, changePlanModel.planEnd) }}
          </div>
          <div v-else w-full class="bg-#f5f5f5" p-10px style="border: 1px solid #d1d1d1;border-radius: 3px;">
            -
          </div>
        </n-form-item>
      </n-form>
    </div>
    <template #footer>
      <div mb-14px ml-110px flex items-center>
        <n-button type="primary" @click="saveChangePlan">
          保存
        </n-button>
        <n-button ml-20px @click="modeShow.ChangePlanModal = false">
          取消
        </n-button>
      </div>
    </template>
  </n-modal>
  <n-modal
    v-model:show="modeShow.changeDoctorShow"
    preset="card"
    :style="{ width: '560px' }"
    title="变更管理医生"
    :mask-closable="false"
    head-style="divide"
    :on-after-leave="afterEnter('doctor')"
  >
    <div h-50px>
      <n-form
        ref="doctorRef"
        label-placement="left"
        class="my-14px mr-30px"
        require-mark-placement="left"
        :model="changeDoctorModel"
        :rules="doctorRules"
        :label-width="100"
      >
        <n-form-item path="doctorId" label="管理医生" mt-20px>
          <n-select
            v-model:value="changeDoctorModel.doctorId"
            placeholder="请选择管理医生"
            :maxlength="50"
            label-field="doctorName"
            value-field="doctorId"
            :options="doctorOpts || []"
            @change="(_:any, item:any) => { changeDoctorModel.doctorName = item.doctorName }"
          />
        </n-form-item>
      </n-form>
    </div>
    <template #footer>
      <div mb-14px ml-100px flex items-center>
        <n-button type="primary" @click="saveChangeDoctor">
          保存
        </n-button>
        <n-button ml-20px @click="modeShow.changeDoctorShow = false">
          取消
        </n-button>
      </div>
    </template>
  </n-modal>
  <n-modal
    v-model:show="modeShow.patientRegisterShow"
    preset="card"
    :style="{ width: '560px' }"
    title="患者登记"
    :mask-closable="false"
    :on-after-leave="() => {
      patientRegisterModel.patientName = ''
      patientRegisterModel.patientIdCardNum = ''
      patientRegisterModel.phone = ''
      patientRegisterModel.patientRecordNo = ''
    }"
    head-style="divide"
  >
    <div>
      <n-form
        ref="addFormRef"
        label-placement="left"
        class="mr-30px"
        require-mark-placement="left"
        :model="patientRegisterModel"
        :rules="patientRules"
        :label-width="100"
      >
        <n-form-item label="快速登记" mt-24px>
          <n-space flex items-center>
            <!-- <button class="btn_search" :class="cardType === 1 ? 'active' : ''" @click="cardHandler">
              身份证
            </button> -->
            <button class="btn_search" :class="cardType === 2 ? 'active' : ''" @click="cardType = 2">
              电子医保
            </button>
            <button class="btn_search" :class="cardType === 3 ? 'active' : ''" @click="cardType = 3">
              医保卡
            </button>
          </n-space>
          <CardModal v-model="cardValue" />
        </n-form-item>
        <n-form-item path="patientName" label="患者姓名">
          <n-input
            v-model:value="patientRegisterModel.patientName"
            placeholder="请输入患者姓名"
            :maxlength="50"
          />
        </n-form-item>
        <n-form-item path="patientIdCardNum" label="身份证号">
          <n-input
            v-model:value="patientRegisterModel.patientIdCardNum"
            placeholder="请输入身份证号"
            :maxlength="50"
          />
        </n-form-item>
        <n-form-item path="phone" label="手机号">
          <n-input
            v-model:value="patientRegisterModel.phone"
            placeholder="请输入手机号"
            :maxlength="50"
          />
        </n-form-item>
        <n-form-item label="就诊卡号">
          <n-input
            v-model:value="patientRegisterModel.patientRecordNo"
            placeholder="请输入就诊卡号"
            :maxlength="50"
          />
        </n-form-item>
      </n-form>
    </div>
    <template #footer>
      <div mb-14px ml-100px flex items-center>
        <n-button type="primary" @click="addPatient">
          保存
        </n-button>
        <n-button ml-20px @click="modeShow.patientRegisterShow = false">
          取消
        </n-button>
      </div>
    </template>
  </n-modal>

  <n-modal
    v-model:show="modeShow.postMessageModel"
    preset="card"
    :style="{ width: '852px', height: '592px' }"
    title="发送消息"
    :mask-closable="false"
    :on-after-leave="resetMsg"
    head-style="divide"
  >
    <div>
      <n-form
        ref="postMsgRef"
        label-placement="left"
        class="my-10px mr-30px"
        require-mark-placement="left"
        :model="postMessageModel"
        :rules="PostMsgRule"
        :label-width="90"
      >
        <n-form-item path="" label="消息类型">
          <n-radio-group v-model:value="postMessageModel.msgType" name="radiogroup2">
            <n-radio value="1" mr-10px>
              <div mr-40px>
                消息模板
              </div>
            </n-radio>
            <n-radio value="2" mr-10px>
              <div ml-2px>
                自定义消息
              </div>
            </n-radio>
          </n-radio-group>
        </n-form-item>
        <n-form-item path="msgWays" label="消息渠道">
          <n-checkbox-group v-model:value="postMessageModel.msgWays" disabled>
            <n-space mt-5px>
              <n-checkbox v-for="item in channel" :key="item.label" :value="item.value" mr-10px>
                <span ml-5px>
                  {{ item.label }}
                </span>
              </n-checkbox>
            </n-space>
          </n-checkbox-group>
        </n-form-item>
        <div v-if=" postMessageModel.msgType === '1'">
          <n-form-item label="消息模板" path="radio">
            <!-- <n-input v-model:value="postMessageModel.radio" /> -->
            <el-table :data="tableData" style="width: 100%;" max-height="300" stripe>
              <el-table-column prop="date" label="短信内容">
                <template #default="scope">
                  <el-radio v-model="postMessageModel.radio" :label="scope.$index">
                    {{ scope.row }}
                  </el-radio>
                </template>
              </el-table-column>
            </el-table>
          </n-form-item>
          <n-form-item label=" ">
            <SvgIcon local-icon="slmc-icon-information_line" size="14" class="text-primary" mr-5px cursor-pointer />
            <span text-12px>
              如需新增短信模板，请联系SLMC管理员
            </span>
          </n-form-item>
        </div>
        <div v-if=" postMessageModel.msgType === '2'">
          <n-form-item path="text" label="消息内容">
            <n-input
              v-model:value="postMessageModel.text"
              placeholder="请输入"
              type="textarea"
              show-count h-full
              maxlength="500"
              :autosize="{
                minRows: 10,
                maxRows: 15,
              }"
            />
          </n-form-item>
          <n-form-item path="phone" label=" ">
            <SvgIcon local-icon="slmc-icon-information_line" size="14" class="text-primary" mr-5px cursor-pointer />
            <span text-12px>
              每条短信最多发送70个字，超过70个字，系统将会自动拆分，分多次发送
            </span>
          </n-form-item>
        </div>
      </n-form>
    </div>
    <template #footer>
      <div mb-14px ml-85px flex items-center>
        <n-button type="primary" @click="messageButtonClick">
          保存
        </n-button>
        <n-button ml-20px @click="modeShow.postMessageModel = false">
          取消
        </n-button>
      </div>
    </template>
  </n-modal>
  <n-modal
    v-model:show="modeShow.modifyLabelModal"
    preset="card"
    :style="{ width: '722px' }"
    title="修改标签"
    head-style="divide"
    :mask-closable="false"
    :on-after-enter="afterEnter('modify')"
  >
    <div>
      <n-form
        ref="tagRef"
        label-placement="left"
        class="my-10px mr-30px"
        require-mark-placement="left"
        :model="modifyLabelModal"
        :label-width="80"
      >
        <n-form-item path="phone" label="已选患者" mt-24px>
          <div>
            <n-space class="bg-#f8f8f8" w-590px p-10px>
              <n-tag v-for="(item, index) in selectedPatients" :key="index">
                <template #default>
                  <div flex @mouseenter="mouseenter(item)" @mouseleave="mouseleave(item)">
                    <span mx-5px>
                      {{ item.patientName }}
                    </span>
                    <img v-show="item.hover && selectedPatients.length > 1" ml-5px src="@/assets/images/delete.svg" alt="" h-10px w-10px @click="deleteTag(item, index)">

                    <!-- <SvgIcon cursor-pointer local-icon="slmc-icon-deletex2" size="10" class="text-#06AEA6" @click="deleteTag(item, index)" /> -->
                  </div>
                </template>
              </n-tag>
            </n-space>
          </div>
        </n-form-item>
        <n-form-item path="phone" label="已有标签:">
          <div v-if=" flatMapArray(selectedPatients || []).filter(item => item !== null).length > 0">
            <span v-for="(item, index) in flatMapArray(selectedPatients || []).filter(item => item !== null)" :key="index">
              {{ item || '-' }}
              <n-divider v-if="item && index !== flatMapArray(selectedPatients || []).filter(item => item !== null).length - 1" vertical color="#d1d1d1" />
            </span>
          </div>
          <div v-else>
            -
          </div>
        </n-form-item>
        <n-form-item path="phone" label="添加标签">
          <n-select
            v-model:value="modifyLabelModal.addTagIds"
            placeholder="请选择"
            :maxlength="50"
            multiple
            label-field="tagName"
            value-field="tagId"
            :options="checkPatients"
          />
        </n-form-item>
        <n-form-item path="phone" label="删除标签">
          <n-select
            v-model:value="modifyLabelModal.removeTagIds"
            multiple
            placeholder="请选择"
            label-field="tagName"
            value-field="tagId"
            :maxlength="50"
            :options="checkPatients"
          />
        </n-form-item>
      </n-form>
    </div>
    <template #footer>
      <div mb-16px ml-80px flex items-center>
        <n-button type="primary" @click="saveTagHandle">
          保存
        </n-button>
        <n-button ml-20px @click="modeShow.modifyLabelModal = false">
          取消
        </n-button>
      </div>
    </template>
  </n-modal>
  <n-modal
    v-model:show="modeShow.modifyStatusModal"
    preset="card"
    :style="{ width: '560px' }"
    title="修改患者状态"
    :mask-closable="false"
    head-style="divide"
    :on-after-enter="() => {
      ModifyStatusModal.patientStatus = null
    }"
  >
    <div h-50px>
      <n-form
        ref="StatusRef"
        label-placement="left"
        class="my-14px mr-30px"
        require-mark-placement="left"
        :model="ModifyStatusModal"
        :rules="statusRules"
        :label-width="100"
      >
        <n-form-item path="patientStatus" label="患者状态" mt-20px>
          <n-select
            v-model:value="ModifyStatusModal.patientStatus"
            placeholder="请选择患者状态"
            :maxlength="50"
            :options="statusList"
          />
        </n-form-item>
      </n-form>
    </div>
    <template #footer>
      <div mb-14px ml-100px flex items-center>
        <n-button type="primary" @click="updatePatientStatus">
          保存
        </n-button>
        <n-button ml-20px @click="modeShow.modifyStatusModal = false">
          取消
        </n-button>
      </div>
    </template>
  </n-modal>
</template>

<style scoped lang="scss">
.btn_search{
  width: 84px;
  height: 32px;
  border: 1px solid #d1d1d1;
  background: #ffffff;
  border-radius: 3px;
  &:hover{
    border: 1px solid #06AEA6;
    color: #06AEA6;
  }
}
.active{
  background: #ecf8ff;
  color: #06AEA6;
  border: 1px solid #06AEA6;
}

:deep(.n-form-item .n-form-item-feedback-wrapper) {
    min-height: 14px !important;
  }
</style>
