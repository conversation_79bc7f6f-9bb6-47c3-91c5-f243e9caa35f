<script lang='ts' setup>
import { useWindowSize } from '@vueuse/core'
import { NTooltip } from 'wowjoy-vui'
import { doctorList, nameIsAvailable } from '~/src/api/followCenter/manerge'
import { getTagListAll } from '~/src/api/followCenter/tag'
import { getSbOrganList } from '~/src/api/organization/organization'
import { flattenChildren, localStg } from '~/src/utils'

const props = defineProps({
  patientType: {
    type: String,
    default: '1',
  },
  queryParams: {
    default: null,
  },
})

const emits = defineEmits(['findAll', 'doctorList'])

const patientType = ref<any>(null)

const userInfo = localStg.get('userInfo')
const isTopOrgan = ref(true)
const organList = ref<any>(null)
function getOrganList() {
  getSbOrganList().then((res) => {
    organList.value = flattenChildren(res.data || [])
    // const isExit = organList.value.find((item: any) => item.id === userInfo?.organId)
    // if (isExit.parentId === '0')
    //   isTopOrgan.value = false
  })

  // getOrganApi({
  //   size: 100,
  //   start: 1,
  //   searchName: '',

  // }).then((res) => {
  //   organList.value = flattenChildren(res.data)
  //   console.log(res.data, 'organList')
  //   const isExit = organList.value.find((item: any) => item.id === userInfo?.organId)
  //   // console.log(isExit, 'isExit')
  //   if (isExit.parentId === '0')
  //     // console.log('11111')
  //     isTopOrgan.value = false
  // })
}

const planAOps = ref([
  { label: '乙肝', value: 'CHB' },
  { label: '脂肪肝', value: 'FLB' },
  { label: '丙肝', value: 'HCV' },
])

const sexOpts = ref([
  { label: '全部', value: '' },
  { label: '男', value: '男' },
  { label: '女', value: '女' },
])
const tagsOpts = ref<any>([])

const isFollowOpts = ref([
  { label: '全部', value: '' },
  { label: '已纳入', value: '1' },
  { label: '未纳入', value: '0' },
])
const followStatusOpts = ref([
  { label: '全部', value: '' },
  { label: '待完成', value: '1' },
  { label: '已完成', value: '2' },
])

const patientStatus = ref([
  // { label: '全部', value: '' },
  { label: '全部', value: 'All' },
  { label: '随访中', value: 'FOLLOW' },
  { label: '失访', value: 'LOSS' },
  { label: '完成', value: 'FINISH' },
  { label: '拒绝随访', value: 'REFUSE' },
  { label: '死亡', value: 'DEAD' },
])

const patientFeedBack = [
  { value: '', label: '全部' },
  { value: 'DD', label: '待定' },
  { value: 'JS', label: '接受' },
  { value: 'WYD', label: '未应答' },
  { value: 'YQ', label: '延期' },
  { value: 'TD', label: '退订' },
]

const planAOpsB = ref<any>([])
const doctorRef = ref<any>([])
export type ModelType = Partial<typeof model.value>
const model = ref<any>({
  sexName: '',
  phone: '',
  planTemplateId: '',
  planTemplateId2: '',
  ageStart: null,
  ageEnd: null,
  flowPlan: '',
  flowPlan2: '',
  doctorId: '',
  doctorNm: '',
  patientTag: [],
  v1VisitStart: '',
  v1VisitEnd: '',
  patientStatus: [],
  patientFeedback: '',
  inFollow: '',
  lastVisitStart: '',
  lastVisitEnd: '',
  lastVisitRateStart: '',
  organId: userInfo?.organId,
  followStatus: '',
  patientName: '',
  lastVisitRateEnd: '',
  followDate: null,
  nextFollow: null,
  overDayStart: '0',
  overDayEnd: '15',
  patientType: props.patientType,
  size: 10,
})

const basicModel = reactive({
  organId: userInfo?.organId,
  patientName: '',
})

const showSearchModal = ref(false)

watch(() => props.patientType, (value) => {
  patientType.value = value
  if (patientType.value === 'OVERTIME') {
    model.value.overDayStart = 0
    model.value.overDayEnd = 15
  }
}, { immediate: true })

watch(() => props.queryParams, (v) => {
  // 默认值赋值
  if (props.queryParams) {
    showSearchModal.value = true
    model.value = Object.assign(model.value, props.queryParams)
    /// 并且执行查询
    setTimeout(() => {
      handleSearchClick()
    }, 300)
  }
})

function handleSearchClick() {
  emits('findAll', { ...model.value, ...basicModel })
}

function updatePatientStatus(val: any, options) {
  nextTick(() => {
    if (val.includes('All')) {
      model.value.patientStatus = [
        'FOLLOW',
        'LOSS',
        'FINISH',
        'REFUSE',
        'DEAD',
      ] as any
    }
  })
}
function handleResetClick() {
  // console.log('重置')
  const resetModel = {
    sexName: '',
    phone: '',
    planTemplateId: '',
    planTemplateId2: '',
    ageStart: null,
    ageEnd: null,
    flowPlan: '',
    flowPlan2: '',
    doctorId: '',
    doctorNm: '',
    patientTag: [],
    v1VisitStart: '',
    v1VisitEnd: '',
    patientStatus: [],
    patientFeedback: '',
    inFollow: '',
    lastVisitStart: '',
    lastVisitEnd: '',
    lastVisitRateStart: '',
    organId: userInfo?.organId,
    followStatus: '',
    patientName: '',
    lastVisitRateEnd: '',
    followDate: null,
    nextFollow: null,
    overDayStart: null,
    overDayEnd: null,
    patientType: props.patientType,
    size: 10,
  }
  Object.assign(model.value, resetModel)
  basicModel.patientName = ''
  emits('findAll', model)
}
const { width } = useWindowSize()
const computedCols = computed(() => {
  if (unref(width) >= 1920)
    return 4
  if (unref(width) >= 1660)
    return 4
  if (unref(width) >= 1280)
    return 3
  return 3
})

function getNamePlan() {
  nameIsAvailable('').then((res: any) => {
    const opts = planAOpsB.value = res.data
    planAOpsB.value = opts
  })
}
function renderOption({ node, option }: any) {
  return h(NTooltip, null, {
    trigger: () => node,
    default: () => `${option.planTemplateName}`,
  })
}

function getTags() {
  getTagListAll({
    tagName: '',
    organId: userInfo?.organId,
  }).then((res: any) => {
    tagsOpts.value = res.data
  })
}

function showAdvanceSearch() {
  showSearchModal.value = !showSearchModal.value
  getNamePlan()
  getTags()
}

function getDoctorList() {
  doctorList({
    organId: userInfo?.organId,
    name: '',
  }).then((res: any) => {
    // console.log(res)
    doctorRef.value = []
    doctorRef.value = res.data
    doctorRef.value.unshift({
      doctorId: '',
      doctorName: '全部',
    })
    emits('doctorList', res.data)
  }).catch((err) => {
    console.log(err)
  })
}
defineExpose({
  model,
  basicModel,
})

onMounted(() => {
  getOrganList()
  getDoctorList()
})
</script>

<template>
  <div>
    <div class="searchBox" ml--20px>
      <n-form
        :label-width="90"
        label-placement="left"
        :model="basicModel"
      >
        <n-grid :cols="4">
          <n-form-item-gi label="机构名称" path="gender">
            <n-select
              v-model:value="basicModel.organId"
              placeholder="请选择"
              label-field="organName"
              value-field="id"
              clearable
              :options="organList || []"
              @change="(val) => {
                // console.log(val)
                model.organId = val
                emits('findAll', model)
              }"
            />
          </n-form-item-gi>
          <n-form-item-gi label="患者姓名" path="gender">
            <n-input
              v-model:value="basicModel.patientName"
              placeholder="请输入"
            />
          </n-form-item-gi>
          <n-form-item-gi label="" path="gender">
            <span ml-20px w-70px cursor-pointer @click="showAdvanceSearch">高级搜索</span>
            <SvgIcon v-show="!showSearchModal" local-icon="slmc-icon-jiantou" size="10" class="text-primary" cursor-pointer />
            <SvgIcon v-show="showSearchModal" local-icon="slmc-icon-jiantou" size="10" class="text-primary" rotate-180 transform cursor-pointer />
            <n-button v-show="!showSearchModal" type="primary" class="mr-20px" ml-20px @click="handleSearchClick">
              查询
            </n-button>
            <n-button v-show="!showSearchModal" @click="handleResetClick">
              重置
            </n-button>
          </n-form-item-gi>
        </n-grid>
      </n-form>
    </div>

    <n-form
      v-show="showSearchModal"
      :label-width="108"
      p-20px
      pb-0px
      label-placement="left"
      :model="model"
      style="background-color: #f8f8f8;border: 1px solid #e6e6e6;border-radius: 3px"
    >
      <n-grid :cols="computedCols" :x-gap="20">
        <n-form-item-gi label="性别">
          <n-select v-model:value="model.sexName" placeholder="请输入" :options="sexOpts" />
        </n-form-item-gi>
        <n-form-item-gi label="年龄" path="age">
          <n-input-number v-model:value="model.ageStart" placeholder="请输入" :show-button="false" min="1" :max="999">
            <template #suffix>
              岁
            </template>
          </n-input-number>
          <div class="mx-10px">
            —
          </div>
          <n-input-number v-model:value="model.ageEnd" placeholder="请输入" :show-button="false">
            <template #suffix>
              岁
            </template>
          </n-input-number>
        </n-form-item-gi>
        <n-form-item-gi label="手机号">
          <n-input v-model:value="model.phone" placeholder="请输入" />
        </n-form-item-gi>
        <n-form-item-gi label="随访计划">
          <n-select v-model:value="model.planTemplateId2" clearable placeholder="请选择" :options="planAOps" @change="() => { model.planTemplateId2 = '' }" />
          <div class="mx-10px">
            —
          </div>
          <n-select
            v-model:value="model.planTemplateId" label-field="planTemplateName"
            :render-option="renderOption"
            value-field="planTemplateId" placeholder="请输入" :options="planAOpsB[model.planTemplateId2]"
          >
            <template #empty>
              <div class="text-#333333" flex flex-nowrap items-center text-12px>
                <span>
                  暂无数据
                </span>
              </div>
            </template>
          </n-select>
        </n-form-item-gi>
        <n-form-item-gi label="管理医生">
          <n-select v-model:value="model.doctorId" clearable placeholder="请输入" value-field="doctorId" label-field="doctorName" :options="doctorRef" />
        </n-form-item-gi>
        <n-form-item-gi label="患者标签">
          <n-select
            v-model:value="model.patientTag" :options="tagsOpts"
            multiple
            label-field="tagName" value-field="tagId" placeholder="请选择"
          />
        </n-form-item-gi>
        <n-form-item-gi v-if="patientType === 'ALL'" label="患者状态">
          <n-select v-model:value="model.patientStatus" multiple placeholder="全部" clearable :options="patientStatus" @update-value="updatePatientStatus" />
        </n-form-item-gi>

        <n-form-item-gi v-if="patientType === 'ALL'" label="是否纳入随访">
          <n-select v-model:value="model.inFollow" placeholder="请输入" :options="isFollowOpts" />
        </n-form-item-gi>
        <n-form-item-gi label="V1完整度">
          <n-input v-model:value="model.v1VisitStart" placeholder="请输入">
            <template #suffix>
              <span color="#999999">%</span>
            </template>
          </n-input>
          <div class="mx-10px">
            —
          </div>
          <n-input v-model:value="model.v1VisitEnd" placeholder="请输入">
            <template #suffix>
              <span color="#999999">%</span>
            </template>
          </n-input>
        </n-form-item-gi>
        <n-form-item-gi label="最新访视阶段">
          <n-input v-model:value="model.lastVisitStart" placeholder="请输入">
            <template #prefix>
              <span class="text-#999999">
                V
              </span>
            </template>
          </n-input>
          <div class="mx-10px">
            —
          </div>
          <n-input v-model:value="model.lastVisitEnd" placeholder="请输入">
            <template #prefix>
              <span class="text-#999999">
                V
              </span>
            </template>
          </n-input>
        </n-form-item-gi>
        <n-form-item-gi label="最新访视完整度">
          <n-input-number v-model:value="model.lastVisitRateStart" placeholder="请输入" :show-button="false" min="0">
            <template #suffix>
              <span class="text-#999999">
                %
              </span>
            </template>
          </n-input-number>
          <div class="mx-10px">
            —
          </div>
          <n-input-number v-model:value="model.lastVisitRateEnd" placeholder="请输入" :show-button="false" max="100">
            <template #suffix>
              <span class="text-#999999">
                %
              </span>
            </template>
          </n-input-number>
        </n-form-item-gi>
        <n-form-item-gi v-if="patientType === 'TODAY' || patientType === 'OVERTIME'" label="患者反馈">
          <n-select v-model:value="model.patientFeedback" placeholder="请输入" :options="patientFeedBack" />
        </n-form-item-gi>
        <n-form-item-gi v-if="patientType === 'TODAY'" label="随访状态">
          <n-select
            v-model:value="model.followStatus"
            clearable placeholder="请输入"
            :options="followStatusOpts"
          />
        </n-form-item-gi>
        <n-form-item-gi v-if="patientType === 'OVERTIME'" label="逾期天数">
          <n-input-number v-model:value="model.overDayStart" :show-button="false" placeholder="请输入" :min="0" :max="999">
            <template #suffix>
              <span color="#999999">天</span>
            </template>
          </n-input-number>
          <div class="mx-10px">
            —
          </div>
          <n-input-number v-model:value="model.overDayEnd" :show-button="false" placeholder="请输入" :min="model.overDayStart + 1">
            <template #suffix>
              <span color="#999999">天</span>
            </template>
          </n-input-number>
        </n-form-item-gi>
        <n-form-item-gi label="加入随访时间">
          <n-date-picker
            v-model:value="model.followDate"
            placeholder="yyyy-mm-dd"
            type="daterange"
          />
        </n-form-item-gi>
        <n-form-item-gi label="下次随访时间">
          <n-date-picker
            v-model:value="model.nextFollow"
            placeholder="yyyy-mm-dd"
            type="daterange"
          />
        </n-form-item-gi>
      </n-grid>
      <n-grid :cols="5">
        <n-form-item-gi />
        <n-form-item-gi />
        <n-form-item-gi ml-20px>
          <n-button type="primary" class="mr-20px" @click="handleSearchClick">
            查询
          </n-button>
          <n-button @click="handleResetClick">
            重置
          </n-button>
        </n-form-item-gi>
      </n-grid>
    </n-form>
  </div>
</template>

<style  lang="scss" scoped>
  :deep(.searchBox .n-form-item .n-form-item-feedback-wrapper) {
    min-height: 10px !important;
  }
</style>
