<script setup lang="ts">
import dayjs from 'dayjs'
import { useMessage } from 'wowjoy-vui'
import type { ModelType } from './Search.vue'
import SearchForm from './Search.vue'
import EditorPlan from './EditorPlan.vue'
import { queryFollowPatient } from '~/src/api/followCenter/manerge'
import { useAuthStore } from '~/src/store/modules/auth'
import { FeedbackEnum } from '@/constants/user'
import { iconName } from '@/utils/common'

defineOptions({
  name: 'FollowTag',
})

const props = defineProps({
  patientType: {
    type: String,
    default: 'ALL',
  },
  tableData: {
    type: Array,
    default: () => [],
  },
  queryParams: {
    default: null,
  },
})

const auth = useAuthStore()

// console.log(auth.userInfo)
const userInfo = auth.userInfo
const patientType = ref<any>(null)
const searchRef = ref<any>(null)

const organId = ref<string>(null)

const router = useRouter()

// import LayoutBreadcrumb from '@/components/layouts/breadcrumb/index.vue'

const cardType = ref<any>('')
const saveTemplate = ref<any>(null)

const message = useMessage()

const activeStyle = { color: '#06AEA6', borderColor: '#06AEA6', textColor: '#FFF' }
const unActiveStyle = { color: '#cccccc', borderColor: '#cccccc', textColor: '#FFF' }

const checkPatients = ref<any>([])

const modelShow = reactive({
  IncludedModal: false,
  ChangePlanModal: false,
  changeDoctorShow: false,
  patientRegisterShow: false,
  postMessageModel: false,
  modifyLabelModal: false,
  modifyStatusModal: false,
})

const page = reactive({
  page: 1,
  size: 10,
  total: 0,

})
const tableData = ref(props.tableData)

watch(() => props.patientType, (newValue) => {
  patientType.value = newValue
}, { immediate: true })

function updateModelShowEvent(value: any) {
  Object.assign(modelShow, value)
}
// const { routerPush } = useRouterPush()

function renderStatus(value: string) {
  switch (value) {
    case 'LOSS':
      return '失访'
    case 'FOLLOW':
      return '随访中'
    case 'FINISH':
      return '完成'
    case 'REFUSE':
      return '拒绝随访'
    case 'DEAD':
      return '死亡'
    default:
      return '-'
  }
}
function selectChange(selection: any) {
  if (selection)
    checkPatients.value = selection

  else
    checkPatients.value = ['0']
}

function selectAllChange(selection: any) {
  checkPatients.value = selection
}
function isFollowUp() {
  return checkPatients.value.length > 0 && checkPatients.value.map((item: any) => item.status)
}

function handleClick(model: any, value: any, row?: any) {
  const isFlow = isFollowUp()
  if (model === 'patientRegister') {
    modelShow.patientRegisterShow = value
    return
  }
  if (model === 'IncludedModal') {
    // console.log(row)
    checkPatients.value = [row]
    modelShow.IncludedModal = value

    return
  }

  if (!isFlow) {
    message.warning('至少选择一项')
    return
  }

  if (model === 'postMessageModel') {
    modelShow.postMessageModel = value
    return
  }

  if (isFlow?.includes(0)) {
    message.warning('未纳入随访患者不允许进行该操作，请取消勾选')
    return
  }

  if (model === 'ChangePlanModal')
    modelShow.ChangePlanModal = value
  if (model === 'changeDoctorShow')
    modelShow.changeDoctorShow = value

  if (model === 'modifyLabelModal')
    modelShow.modifyLabelModal = value
  if (model === 'modifyStatusModal')
    modelShow.modifyStatusModal = value

  // modelShow[model] = value
}

function visitClick(row: any) {
  router.push({
    name: 'patient_file',
    query: {
      patientId: row.patientId,
      patientName: row.patientName,
      primaryIndex: row.primaryIndex,
      slmcNo: row.slmcNo,
      tabName: 'SlmcInterview',
      source: 'follow',
      age: row.age,
    },
  })
}
// function postQuestionnaire(_: any) {

// }

function updateCheckPatients() {
  checkPatients.value = []
}

function findAll(model: ModelType) {
  const params: any = {
    ...model,
    joinDateStart: model.followDate ? dayjs(model.followDate[0]).format('YYYY-MM-DD 00:00:00') : '',
    joinDateEnd: model.followDate ? dayjs(model.followDate[1]).format('YYYY-MM-DD  23:59:59') : '',
    nextFollowDateStart: model.nextFollow ? dayjs(model.nextFollow[0]).format('YYYY-MM-DD 00:00:00') : '',
    nextFollowDateEnd: model.nextFollow ? dayjs(model.nextFollow[1]).format('YYYY-MM-DD  23:59:59') : '',
    // joinDateStart: '',
    // joinDateEnd: '',
    // nextFollowDateStart: '',
    // nextFollowDateEnd: '',
    patientType: props.patientType,
    size: 10,
    page: 1,
  }

  if (params.ageStart > params.ageEnd) {
    window.$message.warning('请输入正确的年龄范围')
    return
  }
  if (params.lastVisitRateStart > params.lastVisitRateEnd) {
    window.$message.warning('请输入正确的最新访视完整度范围')
    return
  }
  if (params.v1VisitStart > params.v1VisitEnd) {
    window.$message.warning('请输入V1完整度范围')
    return
  }

  if (params.lastVisitStart > params.lastVisitEnd) {
    window.$message.warning('请输入最新访视阶段范围')
    return
  }
  delete params.followDate
  delete params.nextFollow
  page.page = 1
  saveTemplate.value = params

  queryFollowPatient(params).then((res: any) => {
    // console.log(res)
    tableData.value = res.data.records
    page.total = res.data.total

    // console.log(tableData.value)
  })
  // tableData.value = []
}

function initDoctorData(data: any) {
  console.log(data)
}
function initTableData(val?: any, name: any = false) {
  let params
  if (val) {
    console.log(val)
    params = val
    params.overDayStart = val.overDayStart ? val.overDayStart : ''
    params.overDayEnd = val.overDayStart ? val.overDayEnd : ''
    params.patientName = name ? val.patientName : searchRef.value.basicModel.patientName
    params.page = page.page
    params.size = page.size
    // params.overDayStart = searchRef.value.model.overDayStart
    // params.overDayEnd = searchRef.value.model.overDayEnd
    params.joinDateStart = val.followDate ? dayjs(val.followDate[0]).format('YYYY-MM-DD 00:00:00') : ''
    params.joinDateEnd = val.followDate ? dayjs(val.followDate[1]).format('YYYY-MM-DD  23:59:59') : ''
    params.nextFollowDateStart = val.nextFollow ? dayjs(val.nextFollow[0]).format('YYYY-MM-DD 00:00:00') : ''
    params.nextFollowDateEnd = val.nextFollow ? dayjs(val.nextFollow[1]).format('YYYY-MM-DD  23:59:59') : ''
  }
  else {
    const model = {
      sexCode: '',
      phone: '',
      planTemplateId: '',
      planTemplateId2: '',
      ageStart: '',
      ageEnd: '',
      flowPlan: '',
      flowPlan2: '',
      doctorId: '',
      doctorNm: '',
      patientTag: [],
      v1VisitStart: '',
      v1VisitEnd: '',
      patientStatus: [],
      inFollow: '',
      lastVisitStart: '',
      lastVisitEnd: '',
      lastVisitRateStart: '',
      organId: userInfo?.organId,
      patientName: '',
      lastVisitRateEnd: '',
      followDate: null,
      nextFollow: null,
    }
    params = {
      ...model,
      joinDateStart: '',
      joinDateEnd: '',
      nextFollowDateStart: '',
      nextFollowDateEnd: '',
      patientType: props.patientType,
      size: page.size,
      page: page.page,
    }
  }

  queryFollowPatient(params).then((res: any) => {
    tableData.value = res.data.records.map((item: any) => {
      return {
        ...item,
        // inFollow: item.isFollow ? '0' : '1',
      }
    })
    page.total = res.data.total
  })
}

function updateTable(patientName?: any) {
  // watch(() => searchRef.value.model, (newValue) => {
  //   console.log(newValue, 'newValue')
  // })
  initTableData(Object.assign(searchRef.value.model, searchRef.value.basicModel, { patientIdCardNum: patientName }), true)
}

function getLiverDiseaseName(type: 'CHB' | 'FLB' | 'HCV') {
  const liverDiseaseTypes = {
    CHB: '乙肝',
    FLB: '脂肪肝',
    HCV: '丙肝',
  }

  return liverDiseaseTypes[type] || null
}

onMounted(() => {
  // console.log('mounted')
  initTableData()
  watch(() => searchRef.value.basicModel.organId, (newValue) => {
  // console.log(newValue, 'newValue')
  // provide('organId',newValue);
    organId.value = newValue
  }, { immediate: true })
})
defineExpose({
  findAll,
})
</script>

<template>
  <div w-full>
    <div>
      <n-space flex items-center>
        <div ml-5px color="#939393">
          读卡搜索
        </div>
        <!-- <button class="btn_search" :class="cardType === 1 ? 'active' : ''" @click="cardType = 1">
          身份证
        </button> -->
        <button class="btn_search" :class="cardType === 2 ? 'active' : ''" @click="cardType = 2">
          电子医保
        </button>
        <button class="btn_search" :class="cardType === 3 ? 'active' : ''" @click="cardType = 3">
          医保卡
        </button>
      </n-space>
    </div>
    <n-divider dashed margin="14px 0 14px 0" />

    <div mb-14px mt-10px>
      <SearchForm ref="searchRef" :query-params="props.queryParams" :patient-type="patientType" @find-all="findAll" @doctor-list="initDoctorData" />
    </div>
    <n-space ml-5px mt-10px>
      <n-button type="primary" :disabled="organId !== userInfo?.organId" @click="handleClick('patientRegister', true)">
        患者登记
      </n-button>
      <n-button type="primary" :disabled="organId !== userInfo?.organId" secondary @click="handleClick('postMessageModel', true)">
        发送消息
      </n-button>
      <n-button type="primary" :disabled="organId !== userInfo?.organId" secondary @click="handleClick('modifyLabelModal', true)">
        修改标签
      </n-button>
      <n-button type="primary" :disabled="organId !== userInfo?.organId" secondary @click="handleClick('ChangePlanModal', true)">
        变更随访计划
      </n-button>
      <n-button type="primary" :disabled="organId !== userInfo?.organId" secondary @click="handleClick('changeDoctorShow', true)">
        变更管理医生
      </n-button>
      <n-button type="primary" :disabled="organId !== userInfo?.organId" secondary @click="handleClick('modifyStatusModal', true)">
        修改患者状态
      </n-button>
    </n-space>
    <el-table
      :data="tableData"

      :header-cell-style="{
        background: '#ECF6FF',
        color: '#333333',
        fontSize: '14px',
        borderBottom: '1px solid rgba(204,204,204,0.50)',
      }"
      :cell-style="{
        fontSize: '14px',
      }"
      stripe ml-2px
      class="mt-[20px]"
      @select="selectChange"
      @selectAll="selectAllChange"
    >
      <el-table-column v-if="organId === userInfo?.organId" type="selection" width="55" />
      <el-table-column label="序号" type="index" width="80">
        <template #default="scope">
          {{ (page.page - 1) * page.size + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        v-if="patientType === 'ALL'"
        label="是否纳入随访"
        show-overflow-tooltip
        prop="status"
        width="120"
      >
        <template #default="{ row }">
          <n-tag round :bordered="false" :color="row.status === 1 ? activeStyle : unActiveStyle" h-18px>
            {{ row.status === 1 ? '已纳入' : '未纳入' }}
          </n-tag>
        </template>
      </el-table-column>
      <el-table-column
        label="患者信息"
        show-overflow-tooltip
        width="140"
      >
        <template #default="{ row }">
          <div>{{ row.slmcNo }}</div>
          <div flex items-center>
            {{ row.patientName || '-' }}｜
            <div>
              <SvgIcon v-if="row.sexName" :local-icon="iconName(row.sexName)" size="14" class="text-primary" mr-5px cursor-pointer />
              <div v-else>
                -
              </div>
            </div>
            ｜{{ row.age || '-' }}
          </div>
          <div>{{ row.phone || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        label="随访信息"
        show-overflow-tooltip
        width="220"
      >
        <template #default="{ row }">
          <div>加入随访:{{ row.joinTime ? dayjs(row.joinTime).format('YYYY-MM-DD') : "-" }}</div>
          <div>最近就诊:{{ row.visitTime ? dayjs(row.visitTime).format('YYYY-MM-DD') : "-" }}</div>
          <div>
            下次随访:{{ row.nextFollowTime ? dayjs(row.nextFollowTime).format('YYYY-MM-DD') : "-" }}
            <span v-if="row.nextFollowTime">
              （{{ (new Date(row.nextFollowTime).setHours(0, 0, 0, 0) - Math.floor(new Date().setHours(0, 0, 0, 0))) / (1000 * 60 * 60 * 24) }}天）
            </span>
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column
        v-if="patientType === 'OVERTIME'"
        label="逾期天数"
        show-overflow-tooltip
        align="center"
      >
        <template #default="{ row }">
          <div>{{ row.overDay || '-' }}</div>
        </template>
      </el-table-column> -->
      <el-table-column
        v-if="patientType === 'OVERTIME'"
        label="逾期天数"
        show-overflow-tooltip
        align="center"
      >
        <template #default="{ row }">
          <div>{{ row.overDay || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        label="随访计划"
        show-overflow-tooltip
        prop="flowPlan"
        width="200"
      >
        <template #default="{ row }">
          <div>
            {{ getLiverDiseaseName(row.planSortName) }}
          </div>
          <div>{{ row.planName || '-' }}</div>
        </template>
      </el-table-column>
      <el-table-column
        label="管理医生"
        show-overflow-tooltip
        prop="manageDoctorName"
        :formatter="(row:any) => row.manageDoctorName || '-'"
      />
      <el-table-column
        label="患者标签"
        show-overflow-tooltip
      >
        <template #default="{ row }">
          <div v-if="row.tag" class="text-container">
            <n-ellipsis :line-clamp="3" :tooltip="false">
              <span v-for="(item, index) in row.tag.split(',')" :key="item">
                {{ item }}{{ index === row.tag.split(',').length - 1 ? '' : ',' }}
                <span v-if="index === row.tag.split(',').length ">
                  ....
                </span>
              </span>
            </n-ellipsis>
          </div>
          <n-space v-else>
            -
          </n-space>

          <!-- <div w-140px>
          </div> -->
        </template>
      </el-table-column>
      <el-table-column
        label="V1完整度"
        show-overflow-tooltip
        prop="v1Visit"
        align="right"
        width="100"
      >
        <template #default="{ row }">
          <span
            color="#3B8FD9"
            cursor-pointer
            @click="() => {
              router.push({
                name: 'follow_questionnaire',
                query: {
                  patientId: row.patientId,
                  patientName: row.patientName,
                  slmcNo: row.slmcNo,
                  tabName: 'SlmcInterview',
                  source: 'follow',
                  primaryIndex: row.primaryIndex,
                  fromPage: 'patient',
                  phaseTemplateCode: '1',
                  age: row.age,
                },
              })
            }"
          >{{ row.v1Visit ? `${row.v1Visit}%` : "-" }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="最新访视"
        prop="latestView"
        align="right"
        width="120"
      >
        <template #default="{ row }">
          <div v-if="row.phaseList" flex justify-end>
            <span>
              V{{ row.nowPhase }} {{ row.nowPhaseRate }}%
            </span>
            <n-popover trigger="hover">
              <template #trigger>
                <img src="@/assets/images/workspace/fangshi.svg" alt="">
              </template>
              <div v-for="(item, index) in Object.entries(row.phaseList).map(([key, value]) => ({ [key]: value }))" :key="index">
                <span mr-5px>
                  V{{ index + 1 }}
                </span>
                <span>
                  {{ item[index + 1] }}%
                </span>
              </div>
            </n-popover>
          </div>
          <div v-else>
            -
          </div>
          <!-- <select id="mySelect" v-model="row.latestView">
                    <option v-for="item in v7SelectList" :key="item.value" :value="item.value">
                      {{ item.label }}
                    </option>
                  </select> -->
        </template>
      </el-table-column>
      <el-table-column
        v-if="patientType === 'ALL'"
        label="患者状态"
        show-overflow-tooltip
        prop="patientStatus"
        width="120"
      >
        <template #default="{ row }">
          <div v-if="row.patientStatus" flex items-center justify-start>
            <div
              v-if="row.patientStatus === 'FINISH'"
              style="width: 6px;height: 6px;background: #4acfb1;border-radius: 50%;"
              mr-10px
            />
            <div
              v-if="row.patientStatus === 'LOSS'"
              style="width: 6px;height: 6px;background: #cccccc;border-radius: 50%;"
              mr-10px
            />
            <div
              v-if="row.patientStatus === 'FOLLOW'"
              style="width: 6px;height: 6px;background: #ff9b54;border-radius: 50%;"
              mr-10px
            />
            <div
              v-if="row.patientStatus === 'REFUSE'"
              style="width: 6px;height: 6px;background: #f36969;border-radius: 50%;"
              mr-10px
            />
            <div
              v-if="row.patientStatus === 'DEAD'"
              style="width: 6px;height: 6px;background: #a5b8d1;border-radius: 50%;"
              mr-10px
            />
            <div inline-block>
              {{ renderStatus(row.patientStatus) }}
            </div>
          </div>
          <div v-else flex items-center justify-start>
            -
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="patientType === 'TODAY'"
        label="随访状态"
        show-overflow-tooltip
        prop="followStatus"
        width="100"
      >
        <template #default="{ row }">
          <div v-if="row.patientStatus" flex items-center justify-start>
            <div
              v-if="row.followStatus === '2'"
              style="width: 6px;height: 6px;background: #4acfb1;border-radius: 50%;"
              mr-10px
            />
            <div
              v-if="row.followStatus === '1'"
              style="width: 6px;height: 6px;background: #ff9b54;border-radius: 50%;"
              mr-10px
            />
            <div inline-block>
              {{ row.followStatus === '2' ? '已完成' : '待完成' }}
            </div>
          </div>
          <div v-else flex items-center justify-center>
            -
          </div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="patientType !== 'ALL'"
        label="患者反馈"
        show-overflow-tooltip
        prop="patientStatus"
        align="center"
      >
        <template #default="{ row }">
          {{ FeedbackEnum[row.patientFeedback] || '-' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200" fixed="right">
        <template #default="{ row }">
          <span
            v-if=" row.status !== 1 && patientType === 'ALL'"
            class="mr-[10px] cursor-pointer text-[#3B8FD9]"
            @click="handleClick('IncludedModal', true, row)"
          >
            纳入随访
          </span>
          <span
            v-if=" row.status !== 1 && patientType === 'ALL'"
            class="mr-[10px] text-[#3B8FD9]"
          >
            |
          </span>
          <span
            class="mr-[10px] cursor-pointer text-[#3B8FD9]"
            @click="visitClick(row)"
          >SLMC访视</span>
          <!-- <span
            class="mr-[10px] cursor-pointer text-[#3B8FD9]"
            @click="postQuestionnaire(row)"
          >发送问卷</span> -->
        </template>
      </el-table-column>
      <template #empty>
        <div text-center>
          <div h-120px>
            <div>
              <n-empty />
              <!-- <n-empty mode="none" /> -->
            </div>
          </div>
          <div ml-120px text-center>
            无数据
          </div>
        </div>
      </template>
    </el-table>
    <n-space v-if="tableData.length > 0" justify="end" my-20px>
      <n-pagination
        v-model:page="page.page"
        :item-count="page.total"
        size="medium"
        :page-sizes="[10, 20, 30, 40]"
        show-quick-jumper
        show-size-picker
        @update-page="() => {
          updateTable()
        }"
        @update:page-size="(size) => {
          page.size = size
          page.page = 1
          updateTable()
        }"
      />
    </n-space>
  </div>
  <EditorPlan
    :model-show="modelShow"
    :organ-id="organId"
    :tag-list="checkPatients"
    @update-check-patients="updateCheckPatients"
    @update-model-show="updateModelShowEvent"
    @update="updateModelShowEvent"
    @update-table-data="updateTable"
  />
</template>

<style scoped lang="scss">
.addSubAdmin {
    position: relative;
    .permissionScope {
        display: flex;
        align-items: center;
        padding: 14px;
        background: #f5f5f5;
    }
}
.btn_search{
  width: 84px;
  height: 32px;
  border: 1px solid #d1d1d1;
  background: #ffffff;
  border-radius: 3px;
  &:hover{
    border: 1px solid #06AEA6;
    color: #06AEA6;
  }
}
.active{
  background: #ecf8ff;
  color: #06AEA6;
  border: 1px solid #06AEA6;
}
.text-container {
overflow: hidden;
white-space:pre-wrap;
text-overflow: ellipsis;
word-break: break-all;
line-break: anywhere;
max-height: 90px;
overflow: hidden;
}
</style>
