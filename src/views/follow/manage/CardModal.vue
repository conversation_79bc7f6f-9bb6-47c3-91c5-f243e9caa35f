<script setup lang="ts">
const props = defineProps({
  modelValue: {
    type: Object as PropType<any>,
    default: () => {},
  },
})
const emit = defineEmits(['update:modelValue'])
const modelShow = ref(false)

watch(() => props.modelValue, (value) => {
  modelShow.value = value
})
watch(() => modelShow.value, (value) => {
  emit('update:modelValue', value)
})
</script>

<template>
  <n-modal
    v-model:show="modelShow"
    preset="card"
    :style="{ width: '580px', height: '364px' }"
    title="身份证读卡"
    head-style="divide"
  >
    <div mt-20px flex items-center justify-center>
      请将身份证放置在读卡器上
    </div>
    <div mt-10px flex items-center justify-center>
      <img src="@/assets/images/workspace/cardNo.gif" alt="" w-282px>
    </div>
    <template #footer>
      <div mb-14px flex items-center justify-center>
        <n-button @click="emit('update:modelValue', false)">
          取消
        </n-button>
      </div>
    </template>
  </n-modal>
</template>
