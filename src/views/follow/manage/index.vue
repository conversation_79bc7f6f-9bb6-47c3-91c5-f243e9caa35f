<script setup lang="ts">
// import LayoutBreadcrumb from '@/components/layouts/breadcrumb/index.vue'
import dayjs from 'dayjs'
import Tabs from './tabs.vue'
import { Breadcrumb } from '@/layouts/common'
import type { BreadLit } from '@/layouts/common'

defineOptions({
  name: 'FollowTag',
})
const tabRef = ref<any>(null)

const currentTab = ref('a')
const queryParams = ref()

export interface TableDataItem {
  patientName: string
  sexCode: string
  phone: string
  planTemplateId: string
  planTemplateId2: string
  ageStart: string
  ageEnd: string
  flowPlan: string
  flowPlan2: string
  doctorId: string
  doctorNm: string
  patientTag: string
  v1VisitStart: string
  v1VisitEnd: string
  patientStatus: string
  inFollow: string
  lastVisitStart: string
  lastVisitEnd: string
  lastVisitRateStart: string
  lastVisitRateEnd: string
  followDate: [number, number]
  nextFollow: [number, number]
}

const tableData: TableDataItem[] = [
]
const tableData2 = [] as any
const tableData3 = [] as any
const route = useRoute()

const renderBread: BreadLit[] = [
  {
    title: '随访中心',
    link: null,
    key: 'follow',
  },
  {
    title: '随访管理',
    link: null,
    key: 'follow_manage',
  },
]

onMounted(() => {
  /// 获取 path 参数
  currentTab.value = route.query?.tab || 'a'
  if (route.query?.isToday) {
    /// 今日--

    queryParams.value = {
      followDate: [dayjs().valueOf(), dayjs().valueOf()],
    }
  }
})
</script>

<template>
  <div h-full>
    <Breadcrumb :bread-list="renderBread" />
    <PageCard breadcrumb>
      <n-tabs v-model:value="currentTab" animated type="card">
        <n-tab-pane name="a" tab="所有患者">
          <Tabs v-if="currentTab === 'a'" ref="tabRef" :table-data="tableData" patient-type="ALL" :query-params="queryParams" />
        </n-tab-pane>
        <n-tab-pane name="b" tab="今日随访患者">
          <Tabs v-if="currentTab === 'b'" ref="tabRef" :table-data="tableData2" patient-type="TODAY" />
        </n-tab-pane>
        <n-tab-pane name="c" tab="逾期患者">
          <Tabs v-if="currentTab === 'c'" ref="tabRef" :table-data="tableData3" patient-type="OVERTIME" />
        </n-tab-pane>
      </n-tabs>
    </PageCard>
  </div>
</template>

<style scoped lang="scss">
.addSubAdmin {
    position: relative;
    .permissionScope {
        display: flex;
        align-items: center;
        padding: 14px;
        background: #f5f5f5;
    }
}
.btn_search{
  width: 84px;
  height: 32px;
  border: 1px solid #d1d1d1;
  background: #ffffff;
  border-radius: 3px;
  &:hover{
    border: 1px solid #06AEA6;
    color: #06AEA6;
  }
}
.active{
  background: #ecf8ff;
  color: #666666;
  border: 1px solid #06AEA6;
}

// :deep(.n-tabs-nav-scroll-wrapper) {
//   // height: calc(100vh - 110px);
//   // position: fixed;
//   overflow-y: auto !important;
// }
:deep(.n-tabs-pane-wrapper) {
  max-height:  calc(100vh - 200px);
  overflow-y: auto;
  // position: fixed;
  // overflow-y: auto !important;
}
</style>
