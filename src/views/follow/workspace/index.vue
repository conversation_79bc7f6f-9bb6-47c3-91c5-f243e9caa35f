<script setup lang="ts">
import { useWindowSize } from '@vueuse/core'
import { getCountIndex } from '~/src/api/followCenter/workspace'
import { useAuthStore } from '~/src/store'
import { Breadcrumb } from '@/layouts/common'
import type { BreadLit } from '@/layouts/common'

const { width } = useWindowSize()

const auth = useAuthStore()

const userInfo = auth.userInfo
const computedCols = computed(() => {
  if (unref(width) >= 1920)
    return 5
  if (unref(width) >= 1660)
    return 4
  return 3
})

const cardList = ref<any>([

])
function getStatusDescription(type: string) {
  switch (type) {
    case 'JS':
      return '接受'
    case 'YQ':
      return '延期'
    case 'DD':
      return '待定'
    case 'WYD':
      return '未应答'
    case 'TD':
      return '退订'
    default:
      return '未知类型'
  }
}

function getCounterData() {
  getCountIndex({
    userId: userInfo.id,
    organId: userInfo.organId,
  }).then((res) => {
    cardList.value = res.data
  })
}

function sumArray(array: any) {
  let sum = 0
  for (let i = 0; i < array.length; i++)
    sum += array[i].total

  return sum
}

const renderBread: BreadLit[] = [
  {
    title: '随访中心',
    link: null,
    key: 'follow',
  },
  {
    title: '工作台',
    link: null,
    key: 'follow_workspace',
  },
]

onMounted(() => {
  getCounterData()
})

const titleMap: any = {
  overtime: '逾期随访计划患者数 (15天内)：',
  today: '今日随访计划患者数：',
  yesterday: '昨日随访计划患者数：',
}
</script>

<template>
  <div>
    <Breadcrumb :bread-list="renderBread" />
    <PageCard breadcrumb render-bread>
      <PageTitle class="flex">
        <div>工作台</div>
      </PageTitle>
      <div>
        <div v-for="(item, key) of cardList" :key="item.exit">
          <div my-10px flex items-center>
            <div>{{ titleMap[key] }}</div>
            <div color="#FF9B54" text-18px>
              {{ sumArray(item) }}
            </div>
          </div>
          <div flex="~ wrap" gap-20px :cols="computedCols">
            <div v-for="(i, index) in item" :key="index">
              <div flex-shrink-0 class="2xl:w-300px lg:w-190px" :class="`card_background${index + 1}`">
                <div mx-20px my-10px flex items-center>
                  <img v-if="i.type === 'YQ'" h-16px w-16px src="@/assets/images/workspace/yanqi.png" alt="">
                  <img v-if="i.type === 'DD'" h-16px w-16px src="@/assets/images/workspace/daiding.png" alt="">
                  <img v-if="i.type === 'JS'" h-16px w-16px src="@/assets/images/workspace/jieshou.png" alt="">
                  <img v-if="i.type === 'TD'" h-16px w-16px src="@/assets/images/workspace/tuiding.png" alt="">
                  <img v-if="i.type === 'WYD'" h-16px w-16px src="@/assets/images/workspace/yingda.png" alt="">
                  <span ml-10px text="12px #666">
                    {{ getStatusDescription(i.type) }}:
                  </span>
                  <span ml-5px text="16px #333">
                    {{ i.total }}
                  </span>
                </div>
                <n-divider color="#e8e8e8" dashed margin="10px 0px 10px 0px" />
                <div mt-15px flex justify-around>
                  <div text-right>
                    <p mx-10px mb-5px color="#4ACFB1" text="18px">
                      {{ i.finishedNum }}
                    </p>
                    <p color="#999999" text="12px">
                      已完成
                    </p>
                  </div>
                  <div text-center>
                    <p mx-10px mb-5px color="#F36969" text="18px">
                      {{ i.unfinishedNum }}
                    </p>
                    <p color="#999999" text="12px">
                      未完成
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <n-divider color="#e8e8e8" dashed margin="20px 0px 20px 0px" />
        </div>
      </div>
    </PageCard>
  </div>
</template>

<style scoped lang="scss">
.card_background1 {
  height: 120px;
  background: linear-gradient(180deg,rgba(74,207,177,0.10), rgba(255,255,255,0.00));
  border: 1px solid rgba(74,207,177,0.50);
  border-radius: 3px;
  &::before{
    content: '';
    display: block;
    border-radius: 3px 3px 0px 0px;
    border: 2px solid #4acfb1;
  }
}
.card_background2 {
  height: 120px;
  background: linear-gradient(180deg,rgba(69,168,230,0.10), rgba(255,255,255,0.00) 100%);
  border: 1px solid #45a8e6;
  border-radius: 3px;
  &::before{
    content: '';
    display: block;
    border-radius: 3px 3px 0px 0px;
    border: 2px solid #45a8e6;
  }
}

.card_background3 {
  height: 120px;
  background: linear-gradient(180deg,rgba(255,155,84,0.10), rgba(255,255,255,0.00));
  border: 1px solid rgba(255,155,84,0.50);
  border-radius: 3px;
  &::before{
    content: '';
    display: block;
    border-radius: 3px 3px 0px 0px;
    border: 2px solid #FF9B54;
  }
}
.card_background4 {
height: 120px;
background: linear-gradient(180deg,rgba(165,184,209,0.10), rgba(255,255,255,0.00));
border: 1px solid rgba(165,184,209,0.50);
border-radius: 3px;
  &::before{
    content: '';
    display: block;
    border-radius: 3px 3px 0px 0px;
    border: 2px solid #a5b8d1;
  }
}
.card_background5 {
  height: 120px;
  background: linear-gradient(180deg,rgba(204,204,204,0.20), rgba(255,255,255,0.00));
  border: 1px solid rgba(209,209,209,0.50);
  border-radius: 3px;
  &::before{
    content: '';
    display: block;
    border-radius: 3px 3px 0px 0px;
    border: 2px solid #D1D1D1;
  }
}
</style>
