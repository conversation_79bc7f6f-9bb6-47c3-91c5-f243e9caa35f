<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useQuestion } from '@/hooks/follow'
import { useAppStore } from '@/store'

const emits = defineEmits(['save', 'cancel'])

const { siderCollapse } = storeToRefs(useAppStore())

const isLastTask = inject('isLastTask') as boolean

const { setStatus, questionStatus } = useQuestion()

function onSave() {
  emits('save')
}

function onNext() {
  emits('save', true)
}

function onCancel() {
  setStatus('check')
  emits('cancel')
}
</script>

<template>
  <div class="fixed-footer" z-100 :class="siderCollapse ? 'w-[calc(100vw-308px)]' : 'w-[calc(100vw-528px)]' ">
    <template v-if="questionStatus === 'edit'">
      <n-button w-100px type="primary" @click="onSave">
        保 存
      </n-button>
      <n-button v-if="!isLastTask" ghost w-100px type="primary" @click="onNext">
        保存并继续
      </n-button>
      <n-button secondary w-100px @click="onCancel">
        取 消
      </n-button>
    </template>
    <n-button secondary w-100px @click="$router.go(-1)">
      返 回
    </n-button>
  </div>
</template>

<style lang="scss" scoped>
.fixed-footer {
  height: 60px;
  position: fixed;
  box-shadow: 0px -1px 0px 0px #e8e8e8 inset, 0px 1px 0px 0px #e8e8e8 inset;
  background: #f9f9f9;
  bottom: 14px;
  right: 14px;
  justify-content: center;
  align-items: center;
  display: flex;
  gap: 20px;
}
</style>
