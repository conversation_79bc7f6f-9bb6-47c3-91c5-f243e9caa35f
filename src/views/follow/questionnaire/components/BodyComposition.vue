<script setup lang="ts">
import type { Answer } from '../types'
import ButtonAction from './ButtonAction.vue'
import EditorBtn from './EditorBtn.vue'
import { useQuestion } from '@/hooks/follow'

const { questionStatus, saveAnswer, questions, resetQuestions, currentTask } = useQuestion()

function setQuestion() {
  questions.value = [
    {
      id: 'BodyComposition-a-1',
      name: '细胞内水分 (L)',
      content: null,
    },
    {
      id: 'BodyComposition-a-2',
      name: '细胞外水分 (L)',
      content: null,
    },
    {
      id: 'BodyComposition-a-3',
      name: '蛋白质 (kg)',
      content: null,
    },
    {
      id: 'BodyComposition-a-4',
      name: '无机盐 (kg)',
      content: null,
    },
    {
      id: 'BodyComposition-a-5',
      name: '体脂肪 (kg)',
      content: null,
    },
    {
      id: 'BodyComposition-a-6',
      name: '体内水分',
      content: null,
    },
    {
      id: 'BodyComposition-a-7',
      name: '肌肉量',
      content: null,
    },
    {
      id: 'BodyComposition-a-8',
      name: '去脂肪质量 (瘦体重)',
      content: null,
    },
    {
      id: 'BodyComposition-a-9',
      name: '体重',
      content: null,
    },
    {
      id: 'BodyComposition-b-1',
      name: '肌肉量 (kg)',
      content: {
        rightUp: null,
        leftUp: null,
        body: null,
        rightBottom: null,
        leftBottom: null,
      },
    },
    {
      id: 'BodyComposition-b-2',
      name: '脂肪量 (kg)',
      content: {
        rightUp: null,
        leftUp: null,
        body: null,
        rightBottom: null,
        leftBottom: null,
      },
    },
    {
      id: 'BodyComposition-b-3',
      name: '水分 (L)',
      content: {
        rightUp: null,
        leftUp: null,
        body: null,
        rightBottom: null,
        leftBottom: null,
      },
    },
    {
      id: 'BodyComposition-c-1',
      name: '骨骼肌',
      content: null,
      unit: 'kg',
    },
    {
      id: 'BodyComposition-c-2',
      name: '腹部皮下脂肪',
      content: null,
      unit: 'cm²',
    },
    {
      id: 'BodyComposition-c-3',
      name: '腹部内脏脂肪',
      content: null,
      unit: 'cm²',
    },
    {
      id: 'BodyComposition-c-4',
      name: '基础代谢率',
      content: null,
      unit: 'kcal',
    },
  ]
}

const isWriteAll = computed(() => {
  return questions.value.slice(0, 5).every(item => item.content)
})

const countAll = computed(() => {
  return questions.value.slice(0, 5).reduce((prev, curr) => {
    return prev + curr.content
  }, 0)
})

setQuestion()

function spanMethod({
  columnIndex,
  rowIndex,
}: any) {
  if (columnIndex >= 3 && rowIndex === 0) {
    return {
      rowspan: columnIndex - 1,
      colspan: 1,
    }
  }
}

function onSave(isNext = false) {
  const answers: Answer[] = questions.value.map((item) => {
    return {
      questionId: item.id,
      content: JSON.stringify(item.content),
    }
  })

  saveAnswer(answers, '', isNext)
}

function onCancel() {
  if (currentTask?.value?.writeId)
    resetQuestions()

  else
    setQuestion()
}
</script>

<template>
  <div mb-10px flex justify-between>
    <div flex items-center gap-10px>
      <div sub-title />
      <div>人体成分分析</div>
    </div>
    <EditorBtn />
  </div>
  <el-table
    :data="questions.slice(0, 5)" border
    :span-method="spanMethod"
    cell-class-name="my-cell"
    :style="{
      '--el-table-border': '1px solid #d1d1d1',
      'border': '1px solid #d1d1d1',
    }"
  >
    <el-table-column width="130">
      <template #header>
        <div text-center leading-none>
          项目
        </div>
      </template>
      <template #default="{ row }">
        <div ml-12px>
          {{ row.name }}
        </div>
      </template>
    </el-table-column>
    <el-table-column>
      <template #header>
        <div text-center leading-none>
          测量值
        </div>
      </template>
      <template #default="{ row }">
        <n-input-number
          v-if="questionStatus === 'edit'"
          v-model:value="row.content"
          clearable
          :show-button="false" w-full
          :precision="2"
          :min="0"
          :max="1000"
        />
        <di v-else ml-7px>
          {{ row.content || '-' }}
        </di>
      </template>
    </el-table-column>
    <el-table-column>
      <template #header>
        <div text-center leading-none>
          百分比(%)
        </div>
      </template>
      <template #default="{ row }">
        <n-input v-if="questionStatus === 'edit'" :value=" isWriteAll ? (row.content / countAll * 100).toFixed(2) : null " clearable placeholder="自动生成" disabled />
        <div v-else ml-7px>
          {{ isWriteAll ? (row.content / countAll * 100).toFixed(2) : '-' }}
        </div>
      </template>
    </el-table-column>
    <el-table-column>
      <template #header>
        <div text-center leading-none>
          体内水分
        </div>
      </template>
      <template #default="{ $index }">
        <template v-if="$index === 0">
          <n-input-number v-if="questionStatus === 'edit'" v-model:value="questions[5].content" clearable :precision="2" :show-button="false" :min="0" :max="1000" />
          <div v-else ml-7px>
            {{ questions[5].content || '-' }}
          </div>
        </template>
        <div v-else />
      </template>
    </el-table-column>
    <el-table-column>
      <template #header>
        <div text-center leading-none>
          肌肉量
        </div>
      </template>
      <template #default="{ $index }">
        <template v-if="$index === 0">
          <n-input-number v-if="questionStatus === 'edit'" v-model:value="questions[6].content" clearable :precision="2" :show-button="false" :min="0" :max="1000" />
          <div v-else ml-7px>
            {{ questions[6].content || '-' }}
          </div>
        </template>
        <div v-else />
      </template>
    </el-table-column>
    <el-table-column>
      <template #header>
        <div text-center leading-none>
          去脂肪质量
        </div>
        <div text-center>
          (瘦体重)
        </div>
      </template>
      <template #default="{ $index }">
        <template v-if="$index === 0">
          <n-input-number v-if="questionStatus === 'edit'" v-model:value="questions[7].content" clearable :precision="2" :show-button="false" :min="0" :max="1000" />
          <div v-else ml-7px>
            {{ questions[7].content || '-' }}
          </div>
        </template>
        <div v-else />
      </template>
    </el-table-column>
    <el-table-column label="体重">
      <template #header>
        <div text-center leading-none>
          体重
        </div>
      </template>
      <template #default="{ $index }">
        <template v-if="$index === 0">
          <n-input-number
            v-if="questionStatus === 'edit'" v-model:value="questions[8].content"
            :precision="2" :show-button="false" :min="0" :max="1000"
            clearable
          />
          <div v-else ml-7px>
            {{ questions[8].content || '-' }}
          </div>
        </template>
        <div v-else />
      </template>
    </el-table-column>
  </el-table>

  <div my-20px flex items-center gap-10px>
    <div sub-title />
    <div>节段肌肉与脂肪分析</div>
  </div>

  <el-table
    :data="questions.slice(9, 11)"
    border cell-class-name="my-cell"
    :style="{
      '--el-table-border': '1px solid #d1d1d1',
      'border': '1px solid #d1d1d1',
    }"
  >
    <el-table-column width="130">
      <template #header>
        <div text-center leading-none>
          项目
        </div>
      </template>
      <template #default="{ row }">
        <div ml-12px>
          {{ row.name }}
        </div>
      </template>
    </el-table-column>
    <el-table-column>
      <template #header>
        <div text-center leading-none>
          右上肢
        </div>
      </template>
      <template #default="{ row }">
        <n-input-number
          v-if="questionStatus === 'edit'" v-model:value="row.content.rightUp"
          :precision="2" :show-button="false" :min="0" :max="1000"
          clearable
        />
        <div v-else ml-7px>
          {{ row.content.rightUp || '-' }}
        </div>
      </template>
    </el-table-column>
    <el-table-column>
      <template #header>
        <div text-center leading-none>
          左上肢
        </div>
      </template>
      <template #default="{ row }">
        <n-input-number
          v-if="questionStatus === 'edit'" v-model:value="row.content.leftUp"
          :precision="2" :show-button="false" :min="0" :max="1000" clearable
        />
        <div v-else ml-7px>
          {{ row.content.leftUp || '-' }}
        </div>
      </template>
    </el-table-column>
    <el-table-column label="躯干">
      <template #header>
        <div text-center leading-none>
          躯干
        </div>
      </template>
      <template #default="{ row }">
        <n-input-number
          v-if="questionStatus === 'edit'" v-model:value="row.content.body"
          :precision="2" :show-button="false" :min="0" :max="1000" clearable
        />
        <div v-else ml-7px>
          {{ row.content.body || '-' }}
        </div>
      </template>
    </el-table-column>
    <el-table-column>
      <template #header>
        <div text-center leading-none>
          右下肢
        </div>
      </template>
      <template #default="{ row }">
        <n-input-number
          v-if="questionStatus === 'edit'" v-model:value="row.content.rightBottom"
          :precision="2" :show-button="false" :min="0" :max="1000" clearable
        />
        <div v-else ml-7px>
          {{ row.content.rightBottom || '-' }}
        </div>
      </template>
    </el-table-column>
    <el-table-column>
      <template #header>
        <div py-5px text-center leading-none>
          左下肢
        </div>
      </template>
      <template #default="{ row }">
        <n-input-number
          v-if="questionStatus === 'edit'" v-model:value="row.content.leftBottom"
          :precision="2" :show-button="false" :min="0" :max="1000" clearable
        />
        <div v-else ml-7px>
          {{ row.content.leftBottom || '-' }}
        </div>
      </template>
    </el-table-column>
  </el-table>

  <div flex="~ wrap" mb-24px mt-14px gap-x-20px gap-y-14px>
    <div v-for="(item, index) in questions.slice(12, 16)" :key="index" flex items-center gap-10px>
      <div color="#666" w-84px text-right>
        {{ item.name }}
      </div>
      <n-input-number
        v-if="questionStatus === 'edit'" v-model:value="item.content" class="!w-267px"
        :precision="2" :show-button="false" :min="0" :max="1000" clearable
      >
        <template #suffix>
          <span color="#999">{{ item.unit }}</span>
        </template>
      </n-input-number>
      <div v-else w-267px>
        {{ item.content ? item.content + item.unit : '-' }}
      </div>
    </div>
  </div>

  <ButtonAction @save="onSave" @cancel="onCancel" />
</template>

<style lang="scss" scoped>
:deep(.el-table th.el-table__cell) {
  text-align: center;
}

:deep(.el-table__row:hover .el-table__cell) {
  background-color: #fff!important;
}
</style>

<style lang="scss">
.my-cell {
  .cell {
    padding: 0 3px;
  }
}
</style>
