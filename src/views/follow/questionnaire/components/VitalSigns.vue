<script setup lang="ts">
import { useRoute } from 'vue-router'
import type { Answer } from '../types'
import ButtonAction from './ButtonAction.vue'
import EditorBtn from './EditorBtn.vue'
import { isNull, isNumber } from '@/utils/common'
import { useQuestion } from '@/hooks/follow'

const { questionStatus, saveAnswer, questions, resetQuestions, currentTask } = useQuestion()
const route = useRoute()

const patientAge = computed(() => {
  let age = route.query.age as string
  age = age?.replace('岁', '')
  return Number(age)
})

function setQuestion() {
  questions.value = [
    {
      id: 'VitalSigns-0',
      type: 'inputText',
      label: '血压',
      content: null,
      unit: 'mmHg',
      required: true,
    },
    {
      id: 'VitalSigns-1',
      type: 'input',
      label: '心率',
      content: null,
      unit: 'bpm',
      required: true,
    },
    {
      id: 'VitalSigns-2',
      type: 'input',
      label: '呼吸',
      content: null,
      unit: '次/分',
      required: false,
    },
    {
      id: 'VitalSigns-3',
      type: 'input',
      label: '体温',
      precision: 1,
      content: null,
      unit: '°C',
      required: false,
    },
    {
      id: 'VitalSigns-4',
      type: 'input',
      label: '身高',
      content: null,
      unit: 'cm',
      required: true,
    },
    {
      id: 'VitalSigns-5',
      type: 'input',
      label: '体重',
      precision: 1,
      content: null,
      unit: 'kg',
      required: true,
    },
    {
      id: 'VitalSigns-6',
      type: 'text',
      label: '体质指数BMI',
      content: null,
      unit: 'kg/m²',
      required: true,
    },
    {
      id: 'VitalSigns-7',
      type: 'input',
      label: '肩宽',
      content: null,
      unit: 'cm',
      required: false,
    },
    {
      id: 'VitalSigns-8',
      type: 'input',
      label: '头围',
      content: null,
      precision: 1,
      unit: 'cm',
      required: false,
    },
    {
      id: 'VitalSigns-9',
      type: 'text',
      label: '腰身比(WSR)',
      content: null,
      unit: '',
      required: false,
    },
    {
      id: 'VitalSigns-10',
      type: 'input',
      label: '腰围',
      precision: 1,
      content: null,
      unit: 'cm',
      required: true,
    },
    {
      id: 'VitalSigns-11',
      type: 'input',
      label: '大腿围',
      content: null,
      unit: 'cm',
      required: false,
    },
    {
      id: 'VitalSigns-12',
      type: 'input',
      label: '上臀围',
      content: null,
      unit: 'cm',
      required: false,
    },
    {
      id: 'VitalSigns-13',
      type: 'input',
      label: '肱三头肌皮褶厚度',
      content: null,
      unit: 'mm',
      required: false,
    },
    {
      id: 'VitalSigns-14',
      type: 'input',
      label: '踝围',
      content: null,
      unit: 'cm',
      required: false,
    },
    {
      id: 'VitalSigns-15',
      type: 'input',
      label: '小腿围',
      content: null,
      unit: 'cm',
      required: false,
    },
    {
      id: 'VitalSigns-16',
      type: 'input',
      label: '上臂肌围 (MAMC)',
      content: null,
      unit: 'cm',
      required: false,
    },
    {
      id: 'VitalSigns-17',
      type: 'input',
      label: '胸围',
      content: null,
      unit: 'cm',
      required: false,
    },
    {
      id: 'VitalSigns-18',
      type: 'input',
      label: '颈围',
      content: null,
      precision: 1,
      unit: 'cm',
      required: false,
    },
    {
      id: 'VitalSigns-19',
      type: 'text',
      label: '腰臀比(WHR)',
      content: null,
      unit: '',
      required: false,
    },
    {
      id: 'VitalSigns-20',
      type: 'input',
      label: '臀围',
      precision: 1,
      content: null,
      unit: 'cm',
      required: false,
    },
    {
      id: 'VitalSigns-21',
      type: 'placeholder',
      content: null,
    },
    {
      id: 'VitalSigns-22',
      type: 'text',
      label: '体表面积简易公式',
      content: null,
      unit: 'm²',
      required: false,
      formula: 'BSA = [体重(kg) x 身高(cm) / 3600]^0.5',
    },
    {
      id: 'VitalSigns-23',
      type: 'text',
      label: '儿童体表面积通用公式',
      content: null,
      unit: 'm²',
      required: false,
      formula: '体重小于30kg儿童体表面积=体重(kg)*0.035+0.1<br>体重30kg以上儿童体表面积=1.05+(体重-30)*0.02<br>儿童：（0，18）岁',
    },
    {
      id: 'VitalSigns-24',
      type: 'text',
      label: 'DuBois体表面积通用公式',
      content: null,
      unit: 'm²',
      required: false,
      formula: 'BSA = [体重(kg)^0.425 x 身高(cm)^ 0.725] x 0.007184',
    },
    {
      id: 'VitalSigns-25',
      type: 'text',
      label: '许文生氏 (Stevenson)公式',
      content: null,
      unit: 'm²',
      required: false,
      formula: 'BSA=0.0061×身高(cm)+ 0.0128×体重(kg)-0.1529',
    },
    {
      id: 'VitalSigns-26',
      type: 'text',
      label: '中国人体表面积通用公式',
      content: null,
      unit: 'm²',
      required: false,
      formula: 'BSA=0.0061×身高(cm)+0.0124×体重(kg)-0.0099',
    },
    {
      id: 'VitalSigns-27',
      type: 'placeholder',
      content: null,
    },
  ]
}

setQuestion()

const needWatch = computed(() => {
  return questions.value.filter((_i, index) => index === 4 || index === 5 || index === 10 || index === 20)
})

watch(needWatch, () => {
  const height = questions.value[4].content
  const weight = questions.value[5].content
  const waist = questions.value[10].content
  const hips = questions.value[20].content

  // 身高体重
  if (isNumber(height) && isNumber(weight)) {
    // bmi
    questions.value[6].content = Number((weight / (height / 100) ** 2).toFixed(1))
    // 体表面积BSA
    questions.value[22].content = Number(Math.sqrt(weight * height / 3600).toFixed(3))
    // 儿童体表面积
    if (patientAge.value < 18 && patientAge.value > 0) {
      if (weight <= 30)
        questions.value[23].content = Number((weight * 0.035 + 0.1).toFixed(3))
      else
        questions.value[23].content = Number((1.05 + (weight - 30) * 0.02).toFixed(3))
    }
    else {
      questions.value[23].content = null
    }

    // DuBois体表面积
    questions.value[24].content = Number((weight ** 0.425 * height ** 0.725 * 0.007184).toFixed(3))

    // Stevenson
    questions.value[25].content = Number((0.0061 * height + 0.0128 * weight - 0.1529).toFixed(3))

    // 中国人体表面积
    questions.value[26].content = Number((0.0061 * height + 0.0124 * weight - 0.0099).toFixed(3))
  }
  else {
    questions.value[6].content = null
    questions.value[22].content = null
    questions.value[23].content = null
    questions.value[24].content = null
    questions.value[25].content = null
    questions.value[26].content = null
  }

  // 腰身比
  if (isNumber(waist) && isNumber(height))
    questions.value[9].content = Number((waist / height).toFixed(1))
  else
    questions.value[9].content = null

  // 腰臀比
  if (isNumber(waist) && isNumber(hips))
    questions.value[19].content = Number((waist / hips).toFixed(1))
  else
    questions.value[19].content = null
},
{
  deep: true,
},
)

function onSave(next = false): void | null {
  // if (questions.value[0].content === null) {
  //   window.$message.warning('请输入血压')
  //   return null
  // }

  // if (questions.value[1].content === null) {
  //   window.$message.warning('请输入心率')
  //   return null
  // }

  // if (questions.value[4].content === null) {
  //   window.$message.warning('请输入身高')
  //   return null
  // }

  // if (questions.value[5].content === null) {
  //   window.$message.warning('请输入体重')
  //   return null
  // }

  // if (questions.value[10].content === null) {
  //   window.$message.warning('请输入腰围')
  //   return null
  // }

  const answers: Answer[] = questions.value.filter(i => i.type !== 'placeholder').map((item) => {
    return {
      questionId: item.id,
      // name: item.label || '',
      content: JSON.stringify(item.content),
    }
  })

  const requiredArr = questions.value.filter(i => i.required)
  const answeredArr = requiredArr.filter(i => !isNull(i.content))

  const finishRate = (((answeredArr?.length || 0) / requiredArr.length) * 100).toFixed(2)

  saveAnswer(answers, finishRate, next)
}

function onCancel() {
  if (currentTask?.value?.writeId)
    resetQuestions()

  else
    setQuestion()
}
</script>

<template>
  <div mb-10px flex justify-between>
    <div flex items-center gap-10px>
      <div sub-title />
      <div>生命体征和人体测量学</div>
    </div>
    <EditorBtn />
  </div>
  <div w-full flex="~ wrap" border="1px solid #d1d1d1" b-b-none b-r-none>
    <div v-for="(question, index) in questions.slice(0, 22)" :key="index" w-full flex class="flex-1/2">
      <div class="item-label" w-186px bg="#F5F5F5" p-10px>
        <div flex justify-end>
          <SvgIcon
            v-if="question?.required"
            local-icon="slmc-icon-app_required1"
            size="14"
            class="text-#F36969"
          />
          <div color="#666">
            {{ question.label }}
          </div>
        </div>
      </div>
      <div class="item-label" flex flex-1 items-center p-2px>
        <template v-if="questionStatus === 'edit'">
          <n-input-number
            v-if="question.type === 'input'"
            v-model:value="questions[index].content"
            :precision="question.precision || 0"
            clearable
            w-full
            :show-button="false"
            :max="1000"
            :min="0"
          >
            <template #suffix>
              <span color="#999">{{ question.unit }}</span>
            </template>
          </n-input-number>
          <n-input
            v-else-if="question.type === 'inputText'"
            v-model:value="questions[index].content"
            clearable
            :maxlength="20"
            w-full
          />
          <div v-else-if="question.type === 'text'" ml-8px>
            {{ question.content ? (`${question.content} ${String(question.unit)}`) : '-' }}
          </div>
        </template>
        <template v-else-if="question.type === 'placeholder'" />
        <template v-else>
          <div ml-8px>
            {{ question.content ? (`${question.content} ${String(question.unit)}`) : '-' }}
          </div>
        </template>
      </div>
    </div>
  </div>

  <div mb-10px mt-20px flex justify-between>
    <div flex items-center gap-10px>
      <div sub-title />
      <div>人体表面积BSA</div>
    </div>
  </div>
  <div w-full flex="~ wrap" border="1px solid #d1d1d1" b-b-none b-r-none>
    <div v-for="(question, index) in questions.slice(22, 28)" :key="index" w-full flex class="flex-1/2">
      <div class="item-label" w-208px bg="#F5F5F5" p-10px>
        <div flex items-center justify-end gap-6px>
          <div color="#666">
            {{ question.label }}
          </div>
          <n-tooltip trigger="hover">
            <template #trigger>
              <SvgIcon
                v-if="question?.type !== 'placeholder'"
                local-icon="slmc-icon-information"
                size="16"
                color="#a1a1a1"
              />
              <div v-else />
            </template>
            <div v-html="question?.formula" />
          </n-tooltip>
        </div>
      </div>
      <div class="item-label" flex flex-1 items-center p-2px>
        <div v-if="question.type === 'placeholder'" />
        <div v-else ml-8px>
          {{ question.content ? (`${question.content} ${String(question.unit)}`) : '-' }}
        </div>
      </div>
    </div>
  </div>
  <ButtonAction @save="onSave" @cancel="onCancel" />
</template>

<style scoped>
.item-label {
  border-right: 1px solid #d1d1d1;
  border-bottom: 1px solid #d1d1d1;
}
</style>
