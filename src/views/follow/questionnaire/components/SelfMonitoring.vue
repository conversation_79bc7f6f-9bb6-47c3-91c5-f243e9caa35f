<script setup lang="ts">
import type { Answer } from '../types'
import ButtonAction from './ButtonAction.vue'
import EditorBtn from './EditorBtn.vue'
import { isNumber } from '~/src/utils/common/is'
import { useQuestion } from '@/hooks/follow'

const { isEdit, saveAnswer, questions, resetQuestions, currentTask } = useQuestion()

function setQuestion() {
  questions.value = [
    {
      id: 'SelfMonitoring-1',
      name: '身高体重',
      content: {
        height: null,
        weight: null,
        waist: null,
      },
    },
  ]
}

setQuestion()

const bmi = computed(() => {
  const height = questions.value[0].content.height
  const weight = questions.value[0].content.weight
  if (isNumber(height) && isNumber(weight))
    return Number((weight / (height / 100) ** 2).toFixed(1))
  else
    return null
})

function onSave(isNext = false) {
  // if (questions.value[0].content.height === null) {
  //   window.$message.warning('请输入身高')
  //   return false
  // }

  // if (questions.value[0].content.weight === null) {
  //   window.$message.warning('请输入体重')
  //   return false
  // }

  // if (questions.value[0].content.waist === null) {
  //   window.$message.warning('请输入腰围')
  //   return false
  // }

  let answerCount = 0

  if (questions.value[0].content.height)
    answerCount++

  if (questions.value[0].content.weight)
    answerCount++

  if (questions.value[0].content.waist)
    answerCount++

  if (bmi.value)
    answerCount++

  const scaleConclusion = ((answerCount / 4) * 100).toFixed(2)

  const answers: Answer[] = questions.value.map((item) => {
    return {
      questionId: item.id,
      content: JSON.stringify(item.content),
    }
  })

  saveAnswer(answers, scaleConclusion, isNext)
}

function onCancel() {
  if (currentTask?.value?.writeId)
    resetQuestions()

  else
    setQuestion()
}
</script>

<template>
  <div mb-10px flex justify-between>
    <div flex items-center gap-10px>
      <div sub-title />
      <div>自我监测问卷</div>
    </div>
    <EditorBtn />
  </div>
  <div class="question-header">
    请填写以下信息
  </div>
  <div class="question-body" flex="~ col" gap-20px>
    <div flex items-center>
      <SvgIcon
        local-icon="slmc-icon-app_required1"
        size="14"
        class="text-#F36969"
      />
      <div mr-10px>
        身高
      </div>
      <n-input-number v-model:value="questions[0].content.height" :disabled="!isEdit" :precision="1" :min="0" :show-button="false" class="!w-200px">
        <template #suffix>
          <span color="#999">
            cm
          </span>
        </template>
      </n-input-number>
    </div>

    <div flex items-center>
      <SvgIcon
        local-icon="slmc-icon-app_required1"
        size="14"
        class="text-#F36969"
      />
      <div mr-10px>
        体重
      </div>
      <n-input-number v-model:value="questions[0].content.weight" :disabled="!isEdit" :precision="1" :min="0" :show-button="false" class="!w-200px">
        <template #suffix>
          <span color="#999">
            kg
          </span>
        </template>
      </n-input-number>
    </div>

    <div flex items-center>
      <SvgIcon
        local-icon="slmc-icon-app_required1"
        size="14"
        class="text-#F36969"
      />
      <div mr-10px>
        腰围
      </div>
      <n-input-number v-model:value="questions[0].content.waist" :disabled="!isEdit" :precision="1" :min="0" :show-button="false" class="!w-200px">
        <template #suffix>
          <span color="#999">
            cm
          </span>
        </template>
      </n-input-number>
    </div>

    <div flex items-center>
      <SvgIcon
        local-icon="slmc-icon-app_required1"
        size="14"
        class="text-#F36969"
      />
      <div mr-10px>
        BMI
      </div>
      <div>
        {{ bmi ? `${bmi}kg/m²` : '-' }}
      </div>
    </div>
  </div>
  <ButtonAction @save="onSave" @cancel="onCancel" />
</template>
