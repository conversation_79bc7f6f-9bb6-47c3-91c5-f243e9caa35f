<script setup lang="ts">
import { useRoute } from 'vue-router'
import dayjs from 'dayjs'
import type { Answer } from '../types'
import DrugHistory from './MedicalHistory/DrugHistory.vue'
import Symptoms from './MedicalHistory/Symptoms.vue'
import ButtonAction from './ButtonAction.vue'
import Disease from './MedicalHistory/Disease.vue'
import Wine from './MedicalHistory/Wine.vue'
import Family from './MedicalHistory/Family.vue'
import EditorBtn from './EditorBtn.vue'
import { useQuestion } from '@/hooks/follow'

const route = useRoute()
const phaseTemplateCode = Number(route.query.phaseTemplateCode)

const { isEdit, saveAnswer, questions: question, resetQuestions, currentTask } = useQuestion()

function setQuestion() {
  question.value = [
    {
      id: 'MedicalHistory-e-1',
      name: '您是否接受过手术治疗',
      content: {
        checked: false,
        name: null,
        date: null,
      },
    },
  ]
}

setQuestion()

function onQuestionERadioUpdate() {
  question.value[0].content.name = null
  question.value[0].content.date = null
}

const symptomsRef = ref<InstanceType<typeof Symptoms> | null>(null)
const drugHistoryRef = ref<InstanceType<typeof DrugHistory> | null>(null)
const diseaseRef = ref<InstanceType<typeof Disease> | null>(null)
const wineRef = ref<InstanceType<typeof Wine> | null>(null)
const familyRef = ref<InstanceType<typeof Family> | null>(null)

function onSave(isNext = false) {
  try {
    let answerA: Answer[] = []
    if (symptomsRef.value) {
      answerA = symptomsRef.value!.question.map((item) => {
        return {
          questionId: item.id,
          // name: item.name,
          content: JSON.stringify(item.content),
        }
      })
    }

    let answerB: Answer[] = []
    if (drugHistoryRef.value) {
      answerB = drugHistoryRef.value.question.map((item) => {
        return {
          questionId: item.id,
          // name: item.name,
          content: JSON.stringify(item.content),
        }
      })
    }

    let answerC: Answer[] = []
    if (diseaseRef.value) {
      answerC = diseaseRef.value.question.map((item) => {
        return {
          questionId: item.id,
          // name: item.name,
          content: JSON.stringify(item.content),

        }
      })
    }

    let answerD: Answer | null = null
    if (wineRef.value) {
      answerD = {
        questionId: wineRef.value.question[0].id,
        // name: wineRef.value!.question.name,
        content: JSON.stringify(wineRef.value!.question[0].content),
      }
    }

    let answerE: Answer[] = []
    if (familyRef.value) {
      answerE = familyRef.value?.question.map((item) => {
        return {
          questionId: item.id,
          // name: item.name,
          content: JSON.stringify(item.content),
        }
      })
    }

    const answerSurgery: Answer = {
      questionId: question.value[0].id,
      // name: question.name,
      content: JSON.stringify(question.value[0].content),
    }

    const answerAll: Answer[]
    = [
      ...answerA,
      ...answerB,
      ...answerC,
      answerSurgery,
      ...answerE,
    ]

    if (answerD)
      answerAll.push(answerD)

    saveAnswer(answerAll, '', isNext)
  }
  catch (error) {
    console.log(error)
  }
}

function onCancel() {
  symptomsRef.value?.onCancel()
  drugHistoryRef.value?.onCancel()
  diseaseRef.value?.onCancel()
  wineRef.value?.onCancel()
  familyRef.value?.onCancel()

  if (currentTask?.value?.writeId)
    resetQuestions()
  else
    setQuestion()
}
</script>

<template>
  <div mb-10px flex justify-between>
    <div flex items-center gap-10px>
      <div sub-title />
      <div>现病史-症状</div>
    </div>
    <EditorBtn />
  </div>
  <Symptoms ref="symptomsRef" />
  <DrugHistory ref="drugHistoryRef" />
  <Disease v-if="phaseTemplateCode <= 1" ref="diseaseRef" />

  <template v-if="phaseTemplateCode <= 1">
    <div mb-10px mt-20px flex items-center gap-10px>
      <div sub-title />
      <div>既往史-手术史</div>
    </div>
    <div class="question-header">
      <div mr-10px>
        您是否接受过手术治疗
      </div>
      <n-radio-group v-model:value="question[0].content.checked" :disabled="!isEdit" @update:value="onQuestionERadioUpdate">
        <div flex items-center gap-40px>
          <n-radio :value="false">
            否
          </n-radio>
          <n-radio :value="true">
            是
          </n-radio>
        </div>
      </n-radio-group>
    </div>
    <div v-if="question[0].content.checked" class="question-body" flex gap-20px>
      <div flex items-center gap-10px>
        <div color="#666">
          手术名称
        </div>
        <n-input v-model:value="question[0].content.name" :disabled="!isEdit" clearable class="2xl:!w-400px xl:!w-260px" />
      </div>
      <div flex items-center gap-10px>
        <div color="#666">
          手术日期
        </div>
        <n-date-picker
          v-model:formatted-value="question[0].content.date"
          :disabled="!isEdit"
          :is-date-disabled="(ts:number) => dayjs().isBefore(dayjs(ts))"
          value-format="yyyy-MM-dd"
          type="month" clearable
          class="2xl:!w-400px xl:!w-260px"
        />
      </div>
    </div>
  </template>

  <Wine ref="wineRef" />
  <Family v-if="phaseTemplateCode <= 1" ref="familyRef" />
  <ButtonAction @save="onSave" @cancel="onCancel" />
</template>
