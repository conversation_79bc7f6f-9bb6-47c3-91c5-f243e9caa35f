<script setup lang="ts">
import { useQuestion } from '@/hooks/follow'

const { isEdit, questions: question, resetQuestions, currentTask } = useQuestion()

function setQuestion() {
  question.value = [
    {
      id: 'MedicalHistory-f-1',
      name: '乙肝',
      content: {
        checked: false,
        relatives: null,
      },
    },
    {
      id: 'MedicalHistory-f-2',
      name: '丙肝',
      content: {
        checked: false,
        relatives: null,
      },
    },
    {
      id: 'MedicalHistory-f-3',
      name: '肝硬化',
      content: {
        checked: false,
        relatives: null,
      },
    },
    {
      id: 'MedicalHistory-f-4',
      name: '肝癌',
      content: {
        checked: false,
        relatives: null,
      },
    },
    {
      id: 'MedicalHistory-f-5',
      name: '以上均无',
      content: {
        checked: false,
        relatives: null,
      },
    },
  ]
}

setQuestion()

const relations = [
  {
    label: '祖父',
    value: 0,
  },
  {
    label: '祖母',
    value: 1,
  },
  {
    label: '外祖父',
    value: 2,
  },
  {
    label: '外祖母',
    value: 3,
  },
  {
    label: '父亲',
    value: 10,
  },
  {
    label: '母亲',
    value: 11,
  },
  {
    label: '兄',
    value: 4,
  },
  {
    label: '弟',
    value: 5,
  },
  {
    label: '姐',
    value: 6,
  },
  {
    label: '妹',
    value: 7,
  },
  {
    label: '子',
    value: 8,
  },
  {
    label: '女',
    value: 9,
  },
]

function onCheckboxUpdate($event: boolean, index: number) {
  question.value[index].content.relatives = null
  if ($event)
    question.value[4].content.checked = false
}

function handleCleanAll() {
  question.value.forEach((item, index) => {
    if (index !== 4) {
      item.content.checked = false
      item.content.relatives = null
    }
  })
}

function onCancel() {
  if (currentTask?.value?.writeId)
    resetQuestions()

  else
    setQuestion()
}

defineExpose({
  question,
  onCancel,
})
</script>

<template>
  <div mb-10px mt-20px flex items-center gap-10px>
    <div sub-title />
    <div>家族史</div>
  </div>
  <div class="question-header">
    您有无以下家族史？
  </div>
  <div class="question-body" flex="~ col" gap-20px>
    <div v-for="(item, index) in question.slice(0, 4)" :key="index" flex>
      <n-checkbox v-model:checked="item.content.checked" :disabled="!isEdit" @update:checked="($event) => onCheckboxUpdate($event, index)">
        <div ml-10px w-56px>
          {{ item.name }}
        </div>
      </n-checkbox>
      <div v-if="item.content.checked" flex items-center gap-10px>
        <div color="#666">
          亲属列表
        </div>
        <n-select v-model:value="item.content.relatives" :disabled="!isEdit" multiple :options="relations" class="2xl:w-690px lg:w-420px" />
      </div>
    </div>
    <n-checkbox v-model:checked="question[4].content.checked" :disabled="!isEdit" @update:checked="handleCleanAll">
      <div ml-10px w-56px>
        以上均无
      </div>
    </n-checkbox>
  </div>
</template>
