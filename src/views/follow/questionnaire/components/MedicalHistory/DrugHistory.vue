<script setup lang="ts">
import dayjs from 'dayjs'
import { medicineDictAPI } from '@/api/followCenter'
import { useQuestion } from '@/hooks/follow'

const { isEdit, questions: question, resetQuestions, currentTask } = useQuestion()
interface Drug {
  id: string
  name: string | null
  startDate: string | null
  isStop: boolean | null
}

function setQuestion() {
  question.value = [
    {
      id: 'MedicalHistory-b-1',
      name: '您是否使用过或正在使用抗病毒药物',
      optionKey: 'kbd',
      content: {
        checked: false,
        drugIds: null,
        drugs: null,
      },
    },
    {
      id: 'MedicalHistory-b-2',
      name: '您近期是否使用抗炎保肝药物',
      optionKey: 'bg',
      content: {
        checked: false,
        drugIds: null,
        drugs: null,
      },
    },
    {
      id: 'MedicalHistory-b-3',
      name: '您近期是否使用抗纤维化药物',
      optionKey: 'kxwh',
      content: {
        checked: false,
        drugIds: null,
        drugs: null,
      },
    },
    {
      id: 'MedicalHistory-b-4',
      name: '您近期是否使用肝损伤药物',
      optionKey: 'gss',
      content: {
        checked: false,
        drugIds: null,
        drugs: null,
      },
    },
    {
      id: 'MedicalHistory-b-5',
      name: '您近期是否使用降脂/降糖/降压药物',
      optionKey: 'jzy',
      content: {
        checked: false,
        drugIds: null,
        drugs: null,
      },
    },
  ]
}

setQuestion()

const drugOption = reactive<Record<string, any[] | undefined>>({
  kbd: undefined,
  bg: undefined,
  kxwh: undefined,
  gss: undefined,
  jzy: undefined,
})

function onDrugUpdate(_val: string[], options: any[], index: number) {
  const currentDrugs = question.value[index].content.drugs

  const newDrugs: Drug[] = options?.map((item) => {
    return {
      id: item.id,
      name: item.name === '其他' ? null : item.name,
      startDate: null,
      isStop: null,
    }
  })

  newDrugs.forEach((item) => {
    const find = currentDrugs?.find((i: any) => i.id === item.id)
    if (find) {
      item.startDate = find.startDate
      item.isStop = find.isStop
      item.name = find.name
    }
  })

  question.value[index].content.drugs = newDrugs
}

function onHaveDrugUpdate(_val: boolean, index: number) {
  question.value[index].content.drugIds = null
  question.value[index].content.drugs = null
}

async function getDict() {
  try {
    const dictResArray = [
      medicineDictAPI<any>('KBDYW'),
      medicineDictAPI<any>('BGYW'),
      medicineDictAPI<any>('KXWHYW'),
      medicineDictAPI<any>('GSSYW'),
      medicineDictAPI<any>('JZYW'),
      medicineDictAPI<any>('JTYW'),
      medicineDictAPI<any>('JYYW'),
    ]

    const allDictData = await Promise.all(dictResArray)
    drugOption.kbd = allDictData[0].data
    drugOption.bg = allDictData[1].data
    drugOption.kxwh = allDictData[2].data
    drugOption.gss = allDictData[3].data
    drugOption.jzy = [...allDictData[4].data, ...allDictData[5].data, ...allDictData[6].data]
  }
  catch (error) {

  }
}

function onCancel() {
  if (currentTask?.value?.writeId)
    resetQuestions()

  else
    setQuestion()
}

onMounted(() => {
  getDict()
})

defineExpose({
  question,
  onCancel,
})
</script>

<template>
  <div mb-10px mt-20px flex justify-between>
    <div flex items-center gap-10px>
      <div sub-title />
      <div>现病史-用药史</div>
    </div>
  </div>
  <div flex="~ col" gap-14px>
    <div v-for="(item, index) in question" :key="index">
      <div class="question-header">
        <div mr-10px flex flex-shrink-0 gap-6px>
          <div>
            {{ item.name }}
          </div>
          <n-tooltip v-if="index > 0" trigger="hover">
            <template #trigger>
              <SvgIcon
                local-icon="slmc-icon-information"
                size="16"
                color="#a1a1a1"
              />
            </template>
            近期：V1指近3个月，V2开始指与上次访视间隔期间
          </n-tooltip>
        </div>
        <n-radio-group
          v-model:value="question[index].content.checked"
          :disabled="!isEdit" mr-10px
          @update:value="($event) => onHaveDrugUpdate($event, index)"
        >
          <div flex gap-40px>
            <n-radio :value="false">
              无
            </n-radio>
            <n-radio :value="true">
              有
            </n-radio>
          </div>
        </n-radio-group>
        <n-tree-select
          v-if="question[index].content.checked"
          v-model:value="question[index].content.drugIds"
          :disabled="!isEdit"
          :options="drugOption[item.optionKey!]"
          children-field="child"
          max-tag-count="responsive"
          :virtual-scroll="false"
          :consistent-menu-width="false"
          label-field="name"
          clearable
          key-field="id"
          check-strategy="child"
          class="2xl:w-385px xl:w-340px"
          multiple filterable
          @update:value="(val, options) => onDrugUpdate(val, options, index)"
        />
      </div>
      <div v-if="item.content.drugIds?.length" class="question-body">
        <div flex="~ col" gap-14px>
          <div v-for="(_item, _index) in item.content.drugs" :key="_index" flex items-center gap-20px>
            <div v-if="!_item.id.includes('QiTa')" color="#333" w-230px text-right>
              {{ _item.name }}
            </div>
            <div v-else flex items-center gap-10px>
              <div flex-shrink-0>
                其他
              </div>
              <n-input v-model:value="_item.name" clearable :disabled="!isEdit" class="!w-192px" placeholder="请输入其他药品" />
            </div>
            <div flex items-center gap-10px>
              <div color="#666" flex-shrink-0>
                首次用药时间
              </div>
              <n-date-picker
                v-model:formatted-value="_item.startDate"
                :disabled="!isEdit"
                :is-date-disabled="(ts:number) => dayjs().isBefore(dayjs(ts))"
                clearable
                class="2xl:w-400px xl:w-136px"
                placeholder="yyyy-mm-dd"
                type="date"
                value-format="yyyy-MM-dd"
              />
            </div>

            <div flex items-center gap-10px>
              <div color="#666" flex-shrink-0>
                是否停用
              </div>
              <n-radio-group v-model:value="_item.isStop" :disabled="!isEdit">
                <div flex gap-40px>
                  <n-radio :value="false">
                    否
                  </n-radio>
                  <n-radio :value="true">
                    是
                  </n-radio>
                </div>
              </n-radio-group>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
