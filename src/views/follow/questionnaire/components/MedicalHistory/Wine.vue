<script setup lang="ts">
import { useQuestion } from '@/hooks/follow'

const { isEdit, questions: question, resetQuestions, currentTask } = useQuestion()

function setQuestion() {
  question.value = [
    {
      id: 'MedicalHistory-g-1',
      name: '有无饮酒情况',
      content: {
        checked: false,
        startAge: null,
        time: null,
        rate: null,
        type: null,
        otherValue: null,
        degree: null,
        average: null,
        isQuitDrink: null,
        quitDrinkYear: null,
      },
    },
  ]
}

setQuestion()

function onQuestionRadioUpdate() {
  question.value[0].content.startAge = null
  question.value[0].content.time = null
  question.value[0].content.rate = null
  question.value[0].content.type = null
  question.value[0].content.otherValue = null
  question.value[0].content.degree = null
  question.value[0].content.average = null
}

const rateOptions = [
  {
    label: '次/天',
    value: 'day',
  },
  {
    label: '次/周',
    value: 'week',
  },
  {
    label: '次/月',
    value: 'month',
  },
]

const wineOptions = [
  {
    label: '白酒 (一盅≈15ml)',
    value: 'white',
  },
  {
    label: '红酒 (一杯≈150ml)',
    value: 'red',
  },
  {
    label: '啤酒 (一瓶≈330ml)',
    value: 'beer',
  },
  {
    label: '其他',
    value: 'other',
  },
]

function onWineTypeUpdate(val: string) {
  if (val !== 'other')
    question.value[0].content.otherValue = null
}

function onCancel() {
  if (currentTask?.value?.writeId)
    resetQuestions()

  else
    setQuestion()
}

defineExpose({
  question,
  onCancel,
})
</script>

<template>
  <div mb-10px mt-20px flex items-center gap-10px>
    <div sub-title />
    <div>既往史-饮酒史</div>
  </div>

  <div class="question-header">
    <div mr-10px>
      有无饮酒情况
    </div>
    <n-radio-group v-model:value="question[0].content.checked" :disabled="!isEdit" @update:value="onQuestionRadioUpdate">
      <div flex items-center gap-40px>
        <n-radio :value="false">
          无
        </n-radio>
        <n-radio :value="true">
          有
        </n-radio>
      </div>
    </n-radio-group>
  </div>
  <div v-if="question[0].content.checked" class="question-body">
    <div flex="~ col" gap-14px>
      <div flex items-center gap-10px>
        <div color="#666">
          起始饮酒年龄
        </div>
        <n-input-number v-model:value="question[0].content.startAge" :min="0" :max="1000" clearable :disabled="!isEdit" class="2xl:!w-400px lg:!w-180px" :precision="0" :show-button="false">
          <template #suffix>
            <span color="#999">岁</span>
          </template>
        </n-input-number>
      </div>
      <div flex items-center gap-10px>
        <div color="#666" w-84px flex-shrink-0 text-right>
          饮酒频率
        </div>
        <n-input-group>
          <n-input-number v-model:value="question[0].content.time" clearable :min="0" :max="1000" :disabled="!isEdit" class="2xl:!w-318px lg:!w-98px" :precision="0" :show-button="false" />
          <n-select v-model:value="question[0].content.rate" clearable :disabled="!isEdit" w-82px :options="rateOptions" />
        </n-input-group>
      </div>

      <div flex gap-10px>
        <div color="#666" relative top-5px w-84px flex-shrink-0 text-right>
          主要饮酒种类
        </div>
        <n-radio-group v-model:value="question[0].content.type" :disabled="!isEdit" @update:value="onWineTypeUpdate">
          <div flex="~ wrap" items-center class="gap-y-10px 2xl:gap-x-200px lg:gap-x-10px">
            <div v-for="(item, index) in wineOptions" :key="index">
              <n-radio :value="item.value">
                {{ item.label }}
              </n-radio>
              <n-input
                v-if="index === 3"
                v-model:value="question[0].content.otherValue"
                :maxlength="1000"
                clearable
                ml-10px
                :disabled="question[0].content.type !== 'other' || !isEdit" class="2xl:!w-240px lg:!w-116px"
              />
            </div>
          </div>
        </n-radio-group>
      </div>

      <div flex gap-10px>
        <div w-84px flex-shrink-0 />
        <div h-1px w-full border="1px dashed #ccc" />
      </div>

      <div flex gap-10px>
        <div w-84px flex-shrink-0 />
        <div mr-20px flex items-center gap-10px>
          <div>请填写度数</div>
          <n-input-number v-model:value="question[0].content.degree" clearable :min="0" :max="1000" :disabled="!isEdit" class="2xl:!w-224px lg:!w-100px" :precision="0" :show-button="false">
            <template #suffix>
              <span color="#999">度</span>
            </template>
          </n-input-number>
        </div>
        <div flex items-center gap-10px>
          <div>平均每日饮酒量</div>
          <n-input-number v-model:value="question[0].content.average" clearable :min="0" :max="10000" :disabled="!isEdit" class="2xl:!w-224px lg:!w-100px" :precision="0" :show-button="false">
            <template #suffix>
              <span color="#999">ml</span>
            </template>
          </n-input-number>
        </div>
      </div>

      <div flex gap-10px>
        <div color="#666" relative top-5px w-84px flex-shrink-0 text-right>
          是否戒酒
        </div>

        <n-radio-group v-model:value="question[0].content.isQuitDrink" :disabled="!isEdit">
          <div flex>
            <n-radio mr-40px :value="false">
              否
            </n-radio>
            <n-radio :value="true">
              是
            </n-radio>
            <div v-if="question[0].content.isQuitDrink" relative bottom-5px ml-10px flex items-center>
              <div mr-10px>
                已戒酒
              </div>
              <n-input-number v-model:value="question[0].content.quitDrinkYear" :disabled="!isEdit" :precision="1" :min="0" class="2xl:!w-224px lg:!w-100px" :show-button="false">
                <template #suffix>
                  <span color="#999">年</span>
                </template>
              </n-input-number>
            </div>
          </div>
        </n-radio-group>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.n-base-selection .n-base-selection-label,
.n-base-selection:not(.n-base-selection--disabled).n-base-selection--active .n-base-selection-label {
  background: #fafafa;
}
</style>
