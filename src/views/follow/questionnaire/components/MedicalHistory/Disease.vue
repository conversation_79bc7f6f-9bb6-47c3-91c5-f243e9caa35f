<script setup lang="ts">
import { useQuestion } from '@/hooks/follow'

const { isEdit, questions, resetQuestions, currentTask } = useQuestion()

function setQuestion() {
  questions.value = [
    {
      id: 'MedicalHistory-c-1',
      name: '糖尿病',
      content: {
        checked: false,
        duration: null,
      },
    },
    {
      id: 'MedicalHistory-c-2',
      name: '高血压',
      content: {
        checked: false,
        duration: null,
      },
    },
    {
      id: 'MedicalHistory-c-3',
      name: '血脂异常',
      content: {
        checked: false,
        duration: null,
      },
    },
    {
      id: 'MedicalHistory-c-4',
      name: '冠心病',
      content: {
        checked: false,
        duration: null,
      },
    },
    {
      id: 'MedicalHistory-c-5',
      name: '其他',
      content: {
        otherValue: null,
        checked: false,
        duration: null,
      },
    },
    {
      id: 'MedicalHistory-c-6',
      name: '以上均无',
      content: {
        otherValue: null,
        checked: false,
        duration: null,
      },
    },
    {
      id: 'MedicalHistory-d-1',
      name: '肝硬化',
      content: {
        checked: false,
        duration: null,
      },
    },
    {
      id: 'MedicalHistory-d-2',
      name: '肝脏肿瘤',
      content: {
        checked: false,
        duration: null,
      },
    },
    {
      id: 'MedicalHistory-d-3',
      name: '妊娠急性脂肪肝',
      content: {
        checked: false,
        duration: null,
      },
    },
    {
      id: 'MedicalHistory-d-4',
      name: '自身免疫性肝病',
      content: {
        checked: false,
        duration: null,
      },
    },
    {
      id: 'MedicalHistory-d-5',
      name: '肝移植术后',
      content: {
        otherValue: null,
        checked: false,
        duration: null,
      },
    },
    {
      id: 'MedicalHistory-d-6',
      name: '其他',
      content: {
        otherValue: null,
        checked: false,
        duration: null,
      },
    },
    {
      id: 'MedicalHistory-d-7',
      name: '以上均无',
      content: {
        otherValue: null,
        checked: false,
        duration: null,
      },
    },
  ]
}

setQuestion()

function handleCleanAll(type: string) {
  if (type === 'c') {
    questions.value.forEach((item, index) => {
      if (index >= 0 && index < 5) {
        item.content.checked = false
        item.content.duration = null
        item.content.otherValue = null
      }
    })
  }
  else {
    questions.value.forEach((item, index) => {
      if (index >= 6 && index < 12) {
        item.content.checked = false
        item.content.duration = null
        item.content.otherValue = null
      }
    })
  }
}

function onCheckboxChange($event: boolean, index: number, type: string) {
  questions.value[index].content.duration = null
  questions.value[index].content.otherValue = null
  if ($event) {
    if (type === 'c')
      questions.value[5].content.checked = false
    else
      questions.value[12].content.checked = false
  }
}

function onCancel() {
  if (currentTask?.value?.writeId)
    resetQuestions()

  else
    setQuestion()
}

defineExpose({
  question: questions,
  onCancel,
})
</script>

<template>
  <div mb-10px mt-20px flex justify-between>
    <div flex items-center gap-10px>
      <div sub-title />
      <div>既往史-疾病史</div>
    </div>
  </div>
  <div class="question-header">
    您有无以下常见慢性病史？
  </div>
  <div class="question-body">
    <div flex="~ col" gap-20px>
      <div v-for="(item, index) in questions.slice(0, 4)" :key="item.id" flex items-center gap-20px>
        <n-checkbox v-model:checked="item.content.checked" :disabled="!isEdit" @update:checked="($event) => onCheckboxChange($event, index, 'c')">
          <div ml-10px w-56px>
            {{ item.name }}
          </div>
        </n-checkbox>
        <div v-if="item.content.checked" flex items-center gap-10px>
          <div color="#666" flex-shrink-0>
            患病时长
          </div>
          <n-input-number v-model:value="item.content.duration" :min="0" :max="1000" clearable :disabled="!isEdit" :show-button="false" class="2xl:w-400px xl:w-180px" :precision="1">
            <template #suffix>
              <span color="#999">年</span>
            </template>
          </n-input-number>
        </div>
      </div>
      <div flex items-center>
        <n-checkbox v-model:checked="questions[4].content.checked" :disabled="!isEdit" @update:checked="($event) => onCheckboxChange($event, 5, 'c')">
          <div mx-10px>
            其他
          </div>
        </n-checkbox>
        <template v-if="questions[4].content.checked">
          <n-input
            v-model:value="questions[4].content.otherValue"
            clearable
            :disabled="!isEdit"
            placeholder="请输入其他药品"
            :maxlength="1000"
            mr-20px
            class="2xl:w-230px xl:!w-192px"
          />
          <div color="#666" mr-10px flex-shrink-0>
            患病时长
          </div>
          <n-input-number v-model:value="questions[4].content.duration" clearable :min="0" :max="1000" :disabled="!isEdit" :show-button="false" class="2xl:w-400px xl:w-180px" :precision="1">
            <template #suffix>
              <span color="#999">年</span>
            </template>
          </n-input-number>
        </template>
      </div>
      <n-checkbox v-model:checked="questions[5].content.checked" :disabled="!isEdit" @update:checked="() => handleCleanAll('c')">
        <div ml-10px>
          以上均无
        </div>
      </n-checkbox>
    </div>
  </div>

  <div class="question-header" mt-14px>
    您有无以下肝脏疾病史？
  </div>
  <div class="question-body">
    <div flex="~ col" gap-20px>
      <div v-for="(item, index) in questions.slice(6, 11)" :key="item.id" flex items-center gap-20px>
        <n-checkbox v-model:checked="item.content.checked" :disabled="!isEdit" @update:checked="($event) => onCheckboxChange($event, index + 6, 'd')">
          <div ml-10px w-100px>
            {{ item.name }}
          </div>
        </n-checkbox>
        <div v-if="item.content.checked" flex items-center gap-10px>
          <div color="#666" flex-shrink-0>
            患病时长
          </div>
          <n-input-number v-model:value="item.content.duration" clearable :min="0" :max="1000" :disabled="!isEdit" :show-button="false" class="2xl:w-400px xl:w-180px" :precision="1">
            <template #suffix>
              <span color="#999">年</span>
            </template>
          </n-input-number>
        </div>
      </div>
      <div flex items-center>
        <n-checkbox v-model:checked="questions[11].content.checked" :disabled="!isEdit" @update:checked="($event) => onCheckboxChange($event, 11, 'd')">
          <div mx-10px>
            其他
          </div>
        </n-checkbox>
        <template v-if="questions[11].content.checked">
          <n-input
            v-model:value="questions[11].content.otherValue"
            :disabled="!isEdit"
            placeholder="请输入其他药品"
            :maxlength="1000"
            clearable
            mr-20px
            class="2xl:!w-230px xl:!w-192px"
          />
          <div color="#666" mr-10px flex-shrink-0>
            患病时长
          </div>
          <n-input-number v-model:value="questions[11].content.duration" clearable :min="0" :max="1000" :disabled="!isEdit" :show-button="false" class="2xl:w-400px xl:w-180px" :precision="1">
            <template #suffix>
              <span color="#999">年</span>
            </template>
          </n-input-number>
        </template>
      </div>
      <n-checkbox v-model:checked="questions[12].content.checked" :disabled="!isEdit" @update:checked="() => handleCleanAll('d')">
        <div ml-10px>
          以上均无
        </div>
      </n-checkbox>
    </div>
  </div>
</template>
