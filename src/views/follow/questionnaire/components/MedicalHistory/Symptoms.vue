<script setup lang="ts">
import dayjs from 'dayjs'
import { useQuestion } from '@/hooks/follow'

const { isEdit, questions: question, resetQuestions, currentTask } = useQuestion()

function setQuestion() {
  question.value = [
    {
      id: 'MedicalHistory-a-1',
      name: '腹胀',
      content: {
        checked: false,
        startDate: null,
        duration: null,
      },
    },
    {
      id: 'MedicalHistory-a-2',
      name: '腹痛',
      content: {
        checked: false,
        startDate: null,
        duration: null,
      },
    },
    {
      id: 'MedicalHistory-a-3',
      name: '食欲不振',
      content: {
        checked: false,
        startDate: null,
        duration: null,
      },
    },
    {
      id: 'MedicalHistory-a-4',
      name: '恶心',
      content: {
        checked: false,
        startDate: null,
        duration: null,
      },
    },
    {
      id: 'MedicalHistory-a-5',
      name: '呕吐',
      content: {
        checked: false,
        startDate: null,
        duration: null,
      },
    },
    {
      id: 'MedicalHistory-a-6',
      name: '腹泻',
      content: {
        checked: false,
        startDate: null,
        duration: null,
      },
    },
    {
      id: 'MedicalHistory-a-7',
      name: '便秘',
      content: {
        checked: false,
        startDate: null,
        duration: null,
      },
    },
    {
      id: 'MedicalHistory-a-8',
      name: '乏力',
      content: {
        checked: false,
        startDate: null,
        duration: null,
      },
    },
    {
      id: 'MedicalHistory-a-9',
      name: '黄疸',
      content: {
        checked: false,
        startDate: null,
        duration: null,
      },
    },
    {
      id: 'MedicalHistory-a-10',
      name: '以上均无',
      content: {
        checked: false,
        startDate: null,
        duration: null,
      },
    },
  ]
}

setQuestion()

function onCheckboxChange($event: boolean, index: number) {
  question.value[index].content.duration = null
  question.value[index].content.startDate = null
  if ($event)
    question.value[9].content.checked = false
}

function handleCleanAll() {
  question.value.forEach((item, index) => {
    if (index !== 9) {
      item.content.checked = false
      item.content.duration = null
      item.content.startDate = null
    }
  })
}

function onCancel() {
  if (currentTask?.value?.writeId)
    resetQuestions()

  else
    setQuestion()
}

defineExpose({
  question,
  onCancel,
})
</script>

<template>
  <div class="question-header">
    您近三个月有如下情况吗？
  </div>
  <div class="question-body">
    <div flex="~ col" gap-14px>
      <div v-for="(item, index) in question.slice(0, 9)" :key="index" flex items-center gap-20px>
        <n-checkbox v-model:checked="question[index].content.checked" :disabled="!isEdit" @update:checked="($event) => onCheckboxChange($event, index)">
          <div ml-10px w-56px>
            {{ item.name }}
          </div>
        </n-checkbox>
        <template v-if="item.content.checked">
          <div flex items-center gap-10px>
            <div color="#666" flex-shrink-0>
              症状开始时间
            </div>
            <n-date-picker
              v-model:formatted-value="item.content.startDate"
              :disabled="!isEdit"
              :is-date-disabled="(ts:number) => dayjs().isBefore(dayjs(ts))"
              class="2xl:w-400px xl:w-180px"
              type="date"
              clearable
              value-format="yyyy-MM-dd"
            />
          </div>
          <div flex items-center gap-10px>
            <div color="#666" flex-shrink-0>
              症状持续时间
            </div>
            <n-input-number
              v-model:value="item.content.duration"
              :disabled="!isEdit"
              class="2xl:w-400px xl:w-180px"
              :precision="1"
              :min="0"
              :max="100000"
              clearable
              :show-button="false"
            >
              <template #suffix>
                <span color="#999">天</span>
              </template>
            </n-input-number>
          </div>
        </template>
      </div>
      <n-checkbox v-model:checked="question[9].content.checked" :disabled="!isEdit" @update:checked="handleCleanAll">
        <div ml-10px w-56px>
          以上均无
        </div>
      </n-checkbox>
    </div>
  </div>
</template>
