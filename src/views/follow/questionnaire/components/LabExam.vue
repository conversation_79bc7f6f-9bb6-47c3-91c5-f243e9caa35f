<script setup lang="ts">
import { CurrentTaskKey } from '../types'
import ButtonAction from './ButtonAction.vue'
import DataEmpty from '@/components/Table/src/components/DataEmpty.vue'
import { taskDetailAPI } from '@/api/followCenter'
import { useQuestion } from '@/hooks/follow'

const tableData = ref<any[]>([])

const currentTask = inject(CurrentTaskKey)!
const isLoading = ref(false)

const { setStatus } = useQuestion()
setStatus('check')

async function getData() {
  try {
    isLoading.value = true
    tableData.value = []
    if (currentTask.value?.taskId) {
      const { data } = await taskDetailAPI<any>(currentTask.value.taskId)
      tableData.value = data.questionAnswers.map((item: any) => {
        let answer = {}
        if (item.answer?.content)
          answer = JSON.parse(item.answer?.content)

        return {
          ...item,
          ...answer,
        }
      })
    }
  }
  catch (error) {
    console.log(error)
  }
  finally {
    isLoading.value = false
  }
}

watch(currentTask, () => {
  getData()
}, {
  deep: true,
})
</script>

<template>
  <n-spin type="uni" size="medium" :show="isLoading">
    <div mb-15px flex items-center gap-10px>
      <div sub-title />
      <div>{{ currentTask?.scaleName }}</div>
    </div>

    <el-table :max-height="660" :data="tableData" show-overflow-tooltip stripe>
      <el-table-column label="指标名称" min-width="100" :formatter="(row: any) => row.questionContent || '-' " />
      <el-table-column label="结果" width="100">
        <template #default="{ row }">
          <div v-if="!row?.hint">
            {{ row.result || '-' }}
          </div>
          <div v-else color="#F36969" flex items-center>
            {{ row.result || '-' }}
            {{ row.hint || '-' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="单位" width="100" :formatter="(row: any) => row.unit?.replaceAll(' ', '') || '-'" />
      <el-table-column label="参考范围" min-width="100" :formatter="(row: any) => row.refValue || '-'" />

      <template #empty>
        <div flex justify-center>
          <DataEmpty />
        </div>
      </template>
    </el-table>
  </n-spin>
  <ButtonAction />
</template>
