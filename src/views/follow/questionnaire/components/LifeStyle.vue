<script setup lang="ts">
import type { Answer } from '../types'
import ButtonAction from './ButtonAction.vue'
import EditorBtn from './EditorBtn.vue'
import { useQuestion } from '@/hooks/follow'

const { isEdit, questions, saveAnswer, resetQuestions, currentTask } = useQuestion()

function setQuestion() {
  questions.value = [
    {
      id: 'LifeStyle-1',
      content: null,
    },
    {
      id: 'LifeStyle-2',
      content: null,
    },
    {
      id: 'LifeStyle-3',
      content: {
        checkbox: [],
        otherValue: null,
      },
    },
    {
      id: 'LifeStyle-4',
      content: null,
    },
    {
      id: 'LifeStyle-5',
      content: {
        radio: null,
        otherValue: null,
      },
    },
    {
      id: 'LifeStyle-6',
      content: null,
    },
    {
      id: 'LifeStyle-6-1',
      content: {
        year: null,
        month: null,
        count: null,
      },
    },
    {
      id: 'LifeStyle-6-2',
      content: null,
    },
    {
      id: 'LifeStyle-7',
      content: null,
    },
    {
      id: 'LifeStyle-8',
      content: null,
    },
    {
      id: 'LifeStyle-9',
      content: null,
    },
    {
      id: 'LifeStyle-10',
      content: null,
    },
    {
      id: 'LifeStyle-11',
      content: {
        items: null,
        medical: null,
      },
    },
  ]
}

setQuestion()

const sports = ['散步、太极拳等养生操', '跑步、跳绳、骑行、游泳', '球类运动', '器械、力量健身类', '其他']
const travelWays = ['步行', '骑自行车', '公交、地铁', '私家车', '其他']
const sleepWays = ['基本不', '适量运动助眠', '食物助眠（如牛奶）', '药物助眠', '其他']

function onSave(isNext = false) {
  const answers: Answer[] = questions.value.map((item) => {
    return {
      questionId: item.id,
      content: JSON.stringify(item.content),
    }
  })

  saveAnswer(answers, '', isNext)
}

function onCancel() {
  if (currentTask?.value?.writeId)
    resetQuestions()

  else
    setQuestion()
}

function handleSportCheckboxChange(index: number) {
  if (index === 4)
    questions.value[2].content.otherValue = null
}

function handleTravelRadioClick(index: number) {
  if (index === 4)
    questions.value[4].content.otherValue = null
}

function handleSleepCheckboxClick(index: number) {
  if (index === 3)
    questions.value[12].content.medical = null

  if (index === 4)
    questions.value[12].content.otherValue = null
}
</script>

<template>
  <div mb-10px flex items-center justify-between>
    <div flex items-center gap-10px>
      <div sub-title />
      <div>运动习惯</div>
    </div>
    <EditorBtn />
  </div>

  <div class="question-header">
    1. 您每周锻炼几次
  </div>
  <div class="question-body">
    <n-radio-group v-model:value="questions[0].content" :disabled="!isEdit">
      <div flex gap-40px>
        <n-radio :value="0">
          ≥ 3次
        </n-radio>
        <n-radio :value="1">
          &lt; 3次
        </n-radio>
      </div>
    </n-radio-group>
  </div>

  <div class="question-header" mt-10px>
    2. 您每次锻炼时长
  </div>
  <div class="question-body">
    <n-radio-group v-model:value="questions[1].content" :disabled="!isEdit">
      <div flex gap-40px>
        <n-radio :value="0">
          ≥ 30分钟
        </n-radio>
        <n-radio :value="1">
          &lt; 30分钟
        </n-radio>
      </div>
    </n-radio-group>
  </div>

  <div class="question-header" mt-10px>
    3. 您通常选择哪类运动项目
  </div>
  <div class="question-body">
    <n-checkbox-group v-model:value="questions[2].content.checkbox" :disabled="!isEdit">
      <div flex="~ col" gap-20px>
        <template v-for="(sport, index) in sports" :key="index">
          <div flex items-center gap-10px>
            <n-checkbox
              :value="index" @click="_$event => handleSportCheckboxChange(index)"
            >
              <div mx-10px flex-shrink-0>
                {{ sport }}
              </div>
            </n-checkbox>
          </div>
          <n-input
            v-if="questions[2].content.checkbox.includes(4) && index === 4"
            v-model:value="questions[2].content.otherValue"
            type="textarea"
            :maxlength="100" class="!w-350px" :disabled="!isEdit"
          />
        </template>
      </div>
    </n-checkbox-group>
  </div>

  <div class="question-header" mt-10px>
    4. 您每天的坐姿时间
  </div>
  <div class="question-body">
    <n-radio-group v-model:value="questions[3].content" :disabled="!isEdit">
      <div flex gap-40px>
        <n-radio :value="0">
          ≥ 2小时
        </n-radio>
        <n-radio :value="1">
          &lt; 2小时
        </n-radio>
      </div>
    </n-radio-group>
  </div>

  <div class="question-header" mt-10px>
    5. 您最常用的出行方式
  </div>
  <div class="question-body">
    <n-radio-group v-model:value="questions[4].content.radio" :disabled="!isEdit" @update:value="questions[4].content.otherValue = null">
      <div flex="~ col" gap-20px>
        <template v-for="(item, index) in travelWays" :key="index">
          <div flex items-center gap-20px>
            <n-radio :value="index" @click="_$event => handleTravelRadioClick(index)">
              <div ml-10px>
                {{ item }}
              </div>
            </n-radio>
          </div>
          <n-input
            v-if="questions[4].content.radio === 4 && index === 4"
            v-model:value="questions[4].content.otherValue"
            type="textarea" :maxlength="100" class="!w-350px"
            :disabled="!isEdit"
          />
        </template>
      </div>
    </n-radio-group>
  </div>

  <div mb-10px mt-20px flex items-center gap-10px>
    <div sub-title />
    <div>吸烟习惯</div>
  </div>

  <div class="question-header" mt-10px>
    <div mr-20px>
      6. 您是否吸烟?
    </div>
    <n-radio-group v-model:value="questions[5].content" :disabled="!isEdit">
      <div flex gap-40px>
        <n-radio :value="0">
          是
        </n-radio>
        <n-radio :value="1">
          否
        </n-radio>
      </div>
    </n-radio-group>
  </div>
  <div v-if="questions[5].content === 0" class="question-body" flex="~ col" gap-20px>
    <div flex items-center>
      <div mr-20px flex-shrink-0>
        7. 您吸烟多长时间了
      </div>
      <n-input-group>
        <n-input-number v-model:value="questions[6].content.year" clearable :disabled="!isEdit" :precision="0" :min="0" :show-button="false" class="!w-100px">
          <template #suffix>
            <span color="#999">年</span>
          </template>
        </n-input-number>
        <n-input-number v-model:value="questions[6].content.month" clearable :disabled="!isEdit" :precision="0" :min="0" :show-button="false" class="!w-100px">
          <template #suffix>
            <span color="#999">月</span>
          </template>
        </n-input-number>
      </n-input-group>
    </div>

    <div flex items-center>
      <div mr-20px flex-shrink-0>
        8. 您现在平均每天吸多少支烟
      </div>
      <n-input-number v-model:value="questions[6].content.count" clearable :disabled="!isEdit" :precision="0" :min="0" :show-button="false" class="!w-100px">
        <template #suffix>
          <span color="#999">支</span>
        </template>
      </n-input-number>
    </div>

    <div flex="~ wrap" items-center gap-40px>
      <div flex-shrink-0>
        9. 您有戒烟的意愿吗
      </div>
      <n-radio-group v-model:value="questions[7].content" :disabled="!isEdit">
        <div flex gap-10px>
          <n-radio :value="0">
            有
          </n-radio>
          <n-radio :value="1">
            没有
          </n-radio>
          <n-radio :value="2">
            有想法，但戒不掉
          </n-radio>
        </div>
      </n-radio-group>
    </div>
  </div>

  <div mb-10px mt-20px flex items-center gap-10px>
    <div sub-title />
    <div>睡眠习惯</div>
  </div>

  <div class="question-header" mt-10px>
    10. 您晚上的休息时间一般在
  </div>
  <div class="question-body">
    <n-radio-group v-model:value="questions[8].content" :disabled="!isEdit">
      <div flex="~ wrap" class="ssm:flex-col lg:flex-row" gap-40px>
        <n-radio
          v-for="(item, index) in ['22:00之前', '22:00~23:00', '23:00~0:00', '0:00之后']" :key="index"
          :value="index"
        >
          {{ item }}
        </n-radio>
      </div>
    </n-radio-group>
  </div>

  <div class="question-header" mt-10px>
    11. 您每天一般睡眠时间有多久？
  </div>
  <div class="question-body">
    <n-radio-group v-model:value="questions[9].content" :disabled="!isEdit">
      <div flex="~ wrap" class="ssm:flex-col lg:flex-row" gap-40px>
        <n-radio
          v-for="(item, index) in ['&lt; 6小时', '6-7小时', '7-8小时', '&gt; 8小时']"
          :key="index" :value="index"
        >
          {{ item }}
        </n-radio>
      </div>
    </n-radio-group>
  </div>

  <div class="question-header" mt-10px>
    12. 您从上床到入睡通常需要
  </div>
  <div class="question-body">
    <n-radio-group v-model:value="questions[10].content" :disabled="!isEdit">
      <div flex="~ wrap" class="ssm:flex-col lg:flex-row" gap-40px>
        <n-radio
          v-for="(item, index) in ['&le; 15分钟', '16-30分钟', '31-60分钟', '&gt; 60分钟']"
          :key="index" :value="index"
        >
          {{ item }}
        </n-radio>
      </div>
    </n-radio-group>
  </div>

  <div class="question-header" mt-10px>
    13. 您起夜次数多吗？
  </div>
  <div class="question-body">
    <n-radio-group v-model:value="questions[11].content" :disabled="!isEdit">
      <div flex="~ wrap" class="ssm:flex-col lg:flex-row" gap-40px>
        <n-radio
          v-for="(item, index) in ['0~1次', '2~3次', '&ge; 4次']"
          :key="index" :value="index"
        >
          {{ item }}
        </n-radio>
      </div>
    </n-radio-group>
  </div>

  <div class="question-header" mt-10px>
    14. 您采用过助眠措施吗?
  </div>
  <div class="question-body">
    <n-checkbox-group v-model:value="questions[12].content.checkbox" :disabled="!isEdit">
      <div flex="~ col" gap-20px>
        <template v-for="(item, index) in sleepWays" :key="index">
          <div flex="~ col" gap-10px>
            <n-checkbox :value="index" @click="_$event => handleSleepCheckboxClick(index)">
              <div mx-10px>
                {{ item }}
              </div>
            </n-checkbox>
            <n-input
              v-if="questions[12].content.checkbox?.includes(3) && index === 3"
              v-model:value="questions[12].content.medical"
              type="textarea"
              :disabled="!isEdit" clearable
              :maxlength="100"
              placeholder="请输入药名" class="!w-350px"
            />
            <n-input
              v-if="questions[12].content.checkbox?.includes(4) && index === 4"
              v-model:value="questions[12].content.otherValue"
              type="textarea"
              :disabled="!isEdit" clearable
              :maxlength="100"
              class="!w-350px"
            />
          </div>
        </template>
      </div>
    </n-checkbox-group>
  </div>
  <ButtonAction @save="onSave" @cancel="onCancel" />
</template>
