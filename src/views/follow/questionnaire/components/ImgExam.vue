<script setup lang="ts">
import { CurrentTaskKey } from '../types'
import ButtonAction from './ButtonAction.vue'
import { useQuestion } from '@/hooks/follow'
import { taskDetailAPI } from '@/api/followCenter'

const datas = reactive<Record<string, string | null>>({
  examItem: null,
  imageSight: null,
  imageDiagnosis: null,
  performer: null,
})

const { setStatus } = useQuestion()
setStatus('check')

const isEmpty = computed(() => {
  return !datas.examItem && !datas.imageSight && !datas.imageDiagnosis && !datas.performer
})

const currentTask = inject(CurrentTaskKey)!
const isLoading = ref(false)
async function getData() {
  try {
    isLoading.value = true
    datas.examItem = null
    datas.imageSight = null
    datas.imageDiagnosis = null
    datas.performer = null
    if (currentTask.value?.taskId) {
      const { data } = await taskDetailAPI<any>(currentTask.value?.taskId)
      const answer = JSON.parse(data.answers[0]?.content)
      Object.keys(answer).forEach((i) => {
        datas[i] = answer[i]
      })
    }
  }
  catch (error) {
    console.log(error)
  }
  finally {
    isLoading.value = false
  }
}

watch(currentTask, () => {
  getData()
}, {
  deep: true,
})
</script>

<template>
  <template v-if="isEmpty">
    <div h-full flex-center>
      <n-spin type="uni" size="medium" :show="isLoading">
        <n-empty size="large" mode="none" description="无数据" />
      </n-spin>
    </div>
  </template>
  <template v-else>
    <n-spin type="uni" size="medium" :show="isLoading">
      <div mb-15px flex items-center gap-10px>
        <div sub-title />
        <div>{{ currentTask?.scaleName }}</div>
      </div>

      <div flex="~ col" gap-14px>
        <div flex>
          <div color="#666">
            检查项目：
          </div>
          <div color="#333">
            {{ datas.examItem || '-' }}
          </div>
        </div>

        <div flex>
          <div flex-shrink-0 color="#666">
            检查描述：
          </div>
          <div bg="#f4f5f6" w-full break-all b-rd-3px p-14px leading-20px>
            {{ datas.imageSight || '-' }}
          </div>
        </div>

        <div flex>
          <div flex-shrink-0 color="#666">
            检查结论：
          </div>
          <div color="#333" leading-20px>
            {{ datas.imageDiagnosis || '-' }}
          </div>
        </div>

        <div flex>
          <div flex-shrink-0 color="#666">
            报告医生：
          </div>
          <div color="#333">
            {{ datas.performer || '-' }}
          </div>
        </div>
      </div>
    </n-spin>
  </template>
  <ButtonAction />
</template>
