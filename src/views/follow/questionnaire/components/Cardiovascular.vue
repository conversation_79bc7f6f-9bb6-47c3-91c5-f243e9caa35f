<script setup lang="ts">
import type { Answer } from '../types'
import ButtonAction from './ButtonAction.vue'
import EditorBtn from './EditorBtn.vue'
import { isNumber } from '@/utils/common'
import type { Question } from '@/hooks/follow'
import { useQuestion } from '@/hooks/follow'

const { isEdit, questions, saveAnswer, resetQuestions, currentTask } = useQuestion()

const pressures = ['<110', '110-119', '120-129', '130-139', '140-149', '150-159', '160-179', '≥180']

function setQuestion() {
  questions.value = [
    {
      id: 'Cardiovascular-1',
      name: '年龄',
      content: null,
    },
    {
      id: 'Cardiovascular-2',
      name: '性别',
      content: null,
    },
    {
      id: 'Cardiovascular-3',
      name: '身高等',
      content: {
        height: null,
        weight: null,
        waist: null,
        bmi: null,
        bloodSugar: null,
      },
    },
    {
      id: 'Cardiovascular-4',
      name: '收缩压',
      content: null,
    },
    {
      id: 'Cardiovascular-6',
      name: '您有无糖尿病家族史(父母、亲兄弟姐妹、子女)？',
      content: null,
    },
    {
      id: 'Cardiovascular-7',
      name: '您是否每周运动≥3次，且每次运动≥30分钟？',
      content: null,
    },
    {
      id: 'Cardiovascular-8',
      name: '您是否有血脂异常[总胆固醇（TC）≥5.2mmol/L 或 高密度脂蛋白胆固醇（HDL-C）≤0.91 mmol/L 或 甘油三酯（TG）≥2.22 mmol/L]或正在接受他汀类等调脂药治疗?',
      content: null,
    },
    {
      id: 'Cardiovascular-9',
      name: '您是否患有多囊卵巢综合征或妊娠期糖尿病？',
      content: null,
    },
    {
      id: 'Cardiovascular-10',
      name: '您是否患有心血管疾病？',
      content: null,
    },
    {
      id: 'Cardiovascular-11',
      name: '您是否长期使用类固醇类药物（糖皮质激素类、盐皮质激素类、性激素类等）、抗精神病药物、抗抑郁药物？',
      content: null,
    },
    {
      id: 'Cardiovascular-12',
      name: '您是否吸烟？',
      content: null,
    },
  ]
}

setQuestion()

const bmi = computed(() => {
  const height = Number(questions.value[2].content.height)
  const weight = Number(questions.value[2].content.weight)
  if (isNumber(height) && isNumber(weight)) {
    const bmi = Number((weight / (height / 100) ** 2).toFixed(1))
    questions.value[2].content.bmi = bmi
    return bmi
  }
  else {
    return null
  }
})

function findContentByQuestionId(questionId: string, questions: Question[]) {
  return questions.find(question => question.id === questionId)?.content
}

function onSave(isNext = false) {
  const age = Number(findContentByQuestionId('Cardiovascular-1', questions.value))
  const gender = Number(findContentByQuestionId('Cardiovascular-2', questions.value))
  const waist = Number(findContentByQuestionId('Cardiovascular-3', questions.value).waist)
  // 空腹血糖
  const fastingBloodSugar = Number(findContentByQuestionId('Cardiovascular-3', questions.value).bloodSugar)
  // 收缩压
  const pressure = Number(findContentByQuestionId('Cardiovascular-4', questions.value))

  // 糖尿病家族史
  const diabetesFamily = Number(findContentByQuestionId('Cardiovascular-6', questions.value))
  // 血脂异常
  const dyslipidemia = Number(findContentByQuestionId('Cardiovascular-8', questions.value))
  const isSmoke = Number(findContentByQuestionId('Cardiovascular-12', questions.value))

  console.log({
    age,
    gender,
    waist,
    fastingBloodSugar,
    pressure,
    diabetesFamily,
    dyslipidemia,
    isSmoke,
  })

  if (gender === null) {
    window.$message.warning('请选择性别')
    return false
  }

  const answers: Answer[] = questions.value.map((item) => {
    return {
      questionId: item.id,
      // name: item.name,
      content: JSON.stringify(item.content),
    }
  })

  // 糖尿病风险评估
  let diabetesNumber = 0

  if (age >= 25 && age <= 34)
    diabetesNumber += 4
  else if (age >= 35 && age <= 39)
    diabetesNumber += 8
  else if (age >= 40 && age <= 44)
    diabetesNumber += 11
  else if (age >= 45 && age <= 49)
    diabetesNumber += 12
  else if (age >= 50 && age <= 54)
    diabetesNumber += 13
  else if (age >= 55 && age <= 59)
    diabetesNumber += 15
  else if (age >= 60 && age <= 64)
    diabetesNumber += 16
  else if (age >= 65)
    diabetesNumber += 18

  // 计算性别和腰围
  if (gender === 0) {
    diabetesNumber += 2

    if (waist >= 75 && waist < 80)
      diabetesNumber += 3
    else if (waist >= 80 && waist < 85)
      diabetesNumber += 5
    else if (waist >= 85 && waist < 90)
      diabetesNumber += 7
    else if (waist >= 90 && waist < 95)
      diabetesNumber += 8
    else if (waist >= 95)
      diabetesNumber += 10
  }
  else if (gender === 1) {
    if (waist >= 70 && waist < 75)
      diabetesNumber += 3
    else if (waist >= 75 && waist < 80)
      diabetesNumber += 5
    else if (waist >= 80 && waist < 85)
      diabetesNumber += 7
    else if (waist >= 85 && waist < 90)
      diabetesNumber += 8
    else if (waist >= 90)
      diabetesNumber += 10
  }

  if (bmi.value) {
    if (bmi.value >= 22 && bmi.value < 24)
      diabetesNumber += 1
    else if (bmi.value >= 24 && bmi.value < 30)
      diabetesNumber += 3
    else if (bmi.value >= 30)
      diabetesNumber += 5
  }

  // 计算收缩压
  switch (pressure) {
    case 0:
      break
    case 1:
      diabetesNumber += 1
      break
    case 2:
      diabetesNumber += 3
      break
    case 3:
      diabetesNumber += 6
      break
    case 4:
      diabetesNumber += 7
      break
    case 5:
      diabetesNumber += 8
      break
    case 6:
      diabetesNumber += 10
      break
    case 7:
      diabetesNumber += 10
      break
  }

  if (diabetesFamily === 0)
    diabetesNumber += 2

  // 心血管风险评估
  let cardioNumber = 0

  // 年龄
  if (age >= 40 && age <= 44)
    cardioNumber += 1
  else if (age >= 45 && age <= 49)
    cardioNumber += 2
  else if (age >= 50 && age <= 54)
    cardioNumber += 3
  else if (age >= 55 && age <= 59)
    cardioNumber += 4
  else if (age >= 60)
    cardioNumber += Math.floor((age - 60) / 5) + 4

  // bmi
  if (bmi.value) {
    if (bmi.value >= 24 && bmi.value < 28)
      cardioNumber += 1
    else if (bmi.value >= 28)
      cardioNumber += 2
  }

  // 血脂异常
  if (dyslipidemia === 0)
    cardioNumber += 1

  if (gender === 0) {
    switch (pressure) {
      case 0:
        cardioNumber += -2
        break
      case 1:
        cardioNumber += -2
        break
      case 2:
        break
      case 3:
        cardioNumber += 1
        break
      case 4:
        cardioNumber += 2
        break
      case 5:
        cardioNumber += 2
        break
      case 6:
        cardioNumber += 5
        break
      case 7:
        cardioNumber += 8
        break
    }

    if (isSmoke === 0)
      cardioNumber += 2

    if (fastingBloodSugar >= 7)
      cardioNumber += 1
  }
  else if (gender === 1) {
    switch (pressure) {
      case 0:
        cardioNumber += -2
        break
      case 1:
        cardioNumber += -2
        break
      case 2:
        break
      case 3:
        cardioNumber += 1
        break
      case 4:
        cardioNumber += 2
        break
      case 5:
        cardioNumber += 2
        break
      case 6:
        cardioNumber += 3
        break
      case 7:
        cardioNumber += 4
        break
    }

    if (isSmoke === 0)
      cardioNumber += 1

    if (fastingBloodSugar >= 7)
      cardioNumber += 2
  }

  let haveCardio = false
  let haveDiabetes = false

  if (cardioNumber > 10)
    haveCardio = true

  if (diabetesNumber >= 25)
    haveDiabetes = true

  if (age >= 40 || (bmi.value && bmi.value >= 24) || pressure >= 4 || (fastingBloodSugar >= 6.1 && fastingBloodSugar < 7))
    haveDiabetes = true

  if (
    Number(findContentByQuestionId('Cardiovascular-6', questions.value)) === 0
      || Number(findContentByQuestionId('Cardiovascular-7', questions.value)) === 1
      || Number(findContentByQuestionId('Cardiovascular-8', questions.value)) === 0
      || Number(findContentByQuestionId('Cardiovascular-9', questions.value)) === 0
      || Number(findContentByQuestionId('Cardiovascular-10', questions.value)) === 0
      || Number(findContentByQuestionId('Cardiovascular-11', questions.value)) === 0
  )
    haveDiabetes = true

  const scaleConclusion = {
    haveCardio,
    haveDiabetes,
    diabetesNumber,
    cardioNumber,
    text: '',
  }

  if (haveCardio)
    scaleConclusion.text += '心血管病高风险,'

  if (haveDiabetes)
    scaleConclusion.text += '糖尿病高风险,'

  saveAnswer(answers, JSON.stringify(scaleConclusion), isNext)
}

function onCancel() {
  if (currentTask?.value?.writeId)
    resetQuestions()

  else
    setQuestion()
}
</script>

<template>
  <div mb-10px flex items-center justify-between>
    <div flex items-center gap-10px>
      <div sub-title />
      <div>心血管疾病及糖尿病风险筛查问卷</div>
    </div>
    <EditorBtn />
  </div>
  <div flex="~ col" gap-10px>
    <div>
      <div class="question-header">
        1. 请问您的年龄是？
      </div>
      <div class="question-body">
        <n-input-number
          v-model:value="questions[0].content"
          :disabled="!isEdit"
          :min="0"
          :max="200"
          class="!w-200px" :show-button="false" :precision="0"
        >
          <template #suffix>
            <span color="#999">岁</span>
          </template>
        </n-input-number>
      </div>
    </div>

    <div>
      <div class="question-header">
        <SvgIcon
          local-icon="slmc-icon-app_required1"
          size="14"
          class="text-#F36969"
        />
        2. 请问您的性别是？
      </div>
      <div class="question-body">
        <n-radio-group v-model:value="questions[1].content" :disabled="!isEdit">
          <n-radio :value="0" mr-10px>
            男
          </n-radio>
          <n-radio :value="1">
            女
          </n-radio>
        </n-radio-group>
      </div>
    </div>

    <div>
      <div class="question-header">
        3. 请填写以下信息
      </div>
      <div class="question-body" flex="~ col" gap-20px>
        <div flex items-center gap-10px>
          <div>身高</div>
          <n-input-number v-model:value="questions[2].content.height" :disabled="!isEdit" :precision="1" :min="0" :show-button="false" class="!w-200px">
            <template #suffix>
              <span color="#999">
                cm
              </span>
            </template>
          </n-input-number>
        </div>

        <div flex items-center gap-10px>
          <div>体重</div>
          <n-input-number v-model:value="questions[2].content.weight" :disabled="!isEdit" :precision="1" :min="0" :show-button="false" class="!w-200px">
            <template #suffix>
              <span color="#999">
                kg
              </span>
            </template>
          </n-input-number>
        </div>

        <div flex items-center gap-10px>
          <div>腰围</div>
          <n-input-number v-model:value="questions[2].content.waist" :disabled="!isEdit" :precision="1" :min="0" :show-button="false" class="!w-200px">
            <template #suffix>
              <span color="#999">
                cm
              </span>
            </template>
          </n-input-number>
        </div>

        <div flex items-center gap-10px>
          <div>
            BMI
          </div>
          <div>
            {{ bmi ? `${bmi}kg/m²` : '-' }}
          </div>
        </div>
      </div>
    </div>

    <div>
      <div class="question-header">
        4. 您最近一次测得的空腹血糖是
      </div>
      <div class="question-body">
        <n-input-number v-model:value="questions[2].content.bloodSugar" :disabled="!isEdit" class="!w-200px" :show-button="false" :precision="1">
          <template #suffix>
            <span color="#999">mmol/L</span>
          </template>
        </n-input-number>
      </div>
    </div>

    <div>
      <div class="question-header">
        5. 您的收缩压（mmHg）是？（测量血压时的高压即为收缩压）
      </div>
      <div class="question-body">
        <n-radio-group v-model:value="questions[3].content" :disabled="!isEdit">
          <div flex="~ wrap" class="ssm:flex-col lg:flex-row" gap-20px>
            <n-radio v-for="(item, index) in pressures" :key="index" :value="index">
              {{ item }}
            </n-radio>
          </div>
        </n-radio-group>
      </div>
    </div>

    <template v-for="(item, _index) in questions.slice(4, 12)" :key="_index">
      <div>
        <div class="question-header">
          {{ _index + 6 }}. {{ item.name }}
        </div>
        <div class="question-body">
          <n-radio-group v-model:value="item.content" :disabled="!isEdit">
            <div flex="~ wrap" class="ssm:flex-col lg:flex-row" gap-20px>
              <n-radio :value="0">
                {{ _index === 0 ? '有' : '是' }}
              </n-radio>
              <n-radio :value="1">
                {{ _index === 0 ? '无' : '否' }}
              </n-radio>
            </div>
          </n-radio-group>
        </div>
      </div>
    </template>

    <ButtonAction @save="onSave" @cancel="onCancel" />
  </div>
</template>
