<script setup lang="ts">
import { useRoute } from 'vue-router'
import { useQuestion } from '@/hooks/follow'

const route = useRoute()
const isEditor = route.query.isEditor as string
const { questionStatus, toggleStatus } = useQuestion()
</script>

<template>
  <div v-if="questionStatus === 'check' && isEditor !== 'false'" flex cursor-pointer items-center gap-10px @click="toggleStatus">
    <SvgIcon
      local-icon="slmc-icon-edit1"
      size="14"
      text-primary
    />
    <div color-primary>
      编辑
    </div>
  </div>
</template>
