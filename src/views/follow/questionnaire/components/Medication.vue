<script setup lang="ts">
import type { Answer } from '../types'
import ButtonAction from './ButtonAction.vue'
import EditorBtn from './EditorBtn.vue'
import { useQuestion } from '@/hooks/follow'

const { isEdit, questions, saveAnswer, currentTask, resetQuestions } = useQuestion()

const numberOfDrugs = ['0种', '1种', '2~3种', '4~5种', '>5种']
const adverseReactions = ['恶心、干呕', '便秘', '腹泻', '头晕', '皮疹、瘙痒', '其他', '无']

function setQuestion() {
  questions.value = [
    {
      id: 'Medication-1',
      name: '您服用的药物种数是',
      content: null,
    },
    {
      id: 'Medication-2',
      name: '在过去的7天里，您忘记按时服用药物的频率是',
      content: null,
    },
    {
      id: 'Medication-3',
      name: '您服药的主要不良反应是？',
      content: {
        checkbox: null,
        otherValue: null,
      },
    },
    {
      id: 'Medication-4',
      name: '当自我感觉良好时，你会停止服用药物',
      content: null,
    },
    {
      id: 'Medication-5',
      name: '在周末或者度假时，你会停止服用药物',
      content: null,
    },
    {
      id: 'Medication-6',
      name: '您因为不良反应而停止服用药物',
      content: null,
    },
    {
      id: 'Medication-7',
      name: '您觉得对病情没有什么帮助而停止服用药物',
      content: null,
    },
    {
      id: 'Medication-8',
      name: '您服用的药量比医生处方的要少',
      content: null,
    },
    {
      id: 'Medication-9',
      name: '您由于经济方面的困难而停止服用药物',
      content: null,
    },
    {
      id: 'Medication-10',
      name: '您因为特殊的服药方式而停止服用药物（比如药与食物同服）',
      content: null,
    },
    {
      id: 'Medication-11',
      name: '您因为对药物存在疑惑而停止服用药物（比如对药物的厂商，药物属于进口/国产，便宜的药物疗效存在疑虑）',
      content: null,
    },
  ]
}

setQuestion()

function onHaveDrugUpdate() {
  questions.value.forEach((item, index) => {
    if (index > 0) {
      if (index === 2) {
        item.content = {
          items: null,
          otherValue: null,
        }
      }
      else {
        item.content = null
      }
    }
  })
}

function onSave(isNext = false) {
  const answers: Answer[] = questions.value.map((item) => {
    return {
      questionId: item.id,
      // name: item.name,
      content: JSON.stringify(item.content),
    }
  })

  const number = questions.value
    .filter((_item, index) => index !== 0 && index !== 2)
    .reduce((pre, cur) => {
      if (cur.content === 0)
        return 4 + pre
      else if (cur.content === 1)
        return 2 + pre

      return pre
    }, 0)

  const scaleConclusion = number > 2 ? '依从性较差' : '依从性较好'

  saveAnswer(answers, scaleConclusion, isNext)
}

function onCancel() {
  if (currentTask?.value?.writeId)
    resetQuestions()

  else
    setQuestion()
}

function handleReactionCheckboxClick(index: number) {
  if (index === 5)
    questions.value[2].content.otherValue = null
}
</script>

<template>
  <div mb-10px flex items-center justify-between>
    <div flex items-center gap-10px>
      <div sub-title />
      <div>药物依从性调查问卷</div>
    </div>
    <EditorBtn />
  </div>
  <div class="question-header">
    1. 您服用的药物种数是
  </div>
  <div class="question-body">
    <n-radio-group v-model:value="questions[0].content" :disabled="!isEdit" @update:value="onHaveDrugUpdate">
      <div flex="~ wrap" class="ssm:flex-col lg:flex-row" gap-40px>
        <n-radio v-for="(item, index) in numberOfDrugs" :key="index" :value="index">
          {{ item }}
        </n-radio>
      </div>
    </n-radio-group>
  </div>

  <template v-if="questions[0].content > 0">
    <div mt-10px class="question-header">
      2. 在过去的7天里，您忘记按时服用药物的频率是
    </div>
    <div class="question-body">
      <n-radio-group v-model:value="questions[1].content" :disabled="!isEdit">
        <div flex="~ wrap" class="ssm:flex-col lg:flex-row" gap-40px>
          <n-radio v-for="(item, index) in ['&ge;3次', '1~2次', '0次']" :key="index" :value="index">
            {{ item }}
          </n-radio>
        </div>
      </n-radio-group>
    </div>

    <div mt-10px class="question-header">
      3. 您服药的主要不良反应是？
    </div>
    <div class="question-body">
      <n-checkbox-group v-model:value="questions[2].content.checkbox" :disabled="!isEdit">
        <div flex="~ col" gap-20px>
          <template v-for="(item, index) in adverseReactions" :key="index">
            <n-checkbox :value="index" @click="_$event => handleReactionCheckboxClick(index)">
              <div ml-10px>
                {{ item }}
              </div>
            </n-checkbox>
            <n-input
              v-if="questions[2].content.checkbox?.includes(5) && index === 5"
              v-model:value="questions[2].content.otherValue"
              type="textarea"
              :maxlength="100"
              :disabled="!isEdit"
              class="!w-350px"
            />
          </template>
        </div>
      </n-checkbox-group>
    </div>

    <div v-for="(item, index) in questions.slice(3, 11)" :key="index">
      <div mt-10px class="question-header" leading-none>
        {{ `${index + 4}. ${item.name}` }}
      </div>
      <div class="question-body">
        <n-radio-group v-model:value="item.content" :disabled="!isEdit">
          <div flex="~ wrap" class="ssm:flex-col lg:flex-row" gap-40px>
            <n-radio v-for="(option, i) in ['总是', '有时', '从未']" :key="i" :value="i">
              {{ option }}
            </n-radio>
          </div>
        </n-radio-group>
      </div>
    </div>
  </template>

  <ButtonAction @save="onSave" @cancel="onCancel" />
</template>
