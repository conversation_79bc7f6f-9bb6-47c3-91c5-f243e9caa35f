<script setup lang="ts">
import type { Answer } from '../types'
import ButtonAction from './ButtonAction.vue'
import EditorBtn from './EditorBtn.vue'
import { useQuestion } from '@/hooks/follow'

const { isEdit, saveAnswer, questions, currentTask, resetQuestions } = useQuestion()

function setQuestion() {
  questions.value = [
    {
      id: 'Dietary-1',
      name: '您每天吃多少谷物？（如大米、小麦、玉米）',
      options: ['250g~400g（1~2个拳头大小）', '100~250g', '低于100g'],
      content: null,
    },
    {
      id: 'Dietary-2',
      name: '您每天吃多少薯类？（如红薯、土豆、芋头）',
      options: ['50~100g（0.5~1个拳头大小）', '低于50g', '几乎不吃'],
      content: null,
    },
    {
      id: 'Dietary-3',
      name: '您每周会吃几次杂粮？（如荞麦、燕麦）',
      options: ['3次及以上', '1~2次/周', '几乎不吃'],
      content: null,
    },
    {
      id: 'Dietary-4',
      name: '您每天吃多少肉类食物？（如鸡肉、猪肉）',
      options: ['40~75g（1~1.5掌心大小）', '20~40g', '低于20g'],
      content: null,
    },
    {
      id: 'Dietary-5',
      name: '您每周会吃几次红肉？（如猪肉牛肉羊肉）',
      options: ['2~3次/周', '1次/周', '几乎不吃'],
      content: null,
    },
    {
      id: 'Dietary-6',
      name: '您每天吃多少个鸡蛋',
      options: ['1~2个', '1个', '几乎不吃'],
      content: null,
    },
    {
      id: 'Dietary-7',
      name: '您每天吃多少豆类食物？（如黄豆、黑豆）',
      options: ['15~20g（单手一小捧）', '10~15g', '低于10g'],
      content: null,
    },
    {
      id: 'Dietary-8',
      name: '您每天吃多少蔬菜？',
      options: ['300~500g（双手4~5大捧）', '150~200g', '几乎不吃'],
      content: null,
    },
    {
      id: 'Dietary-9',
      name: '您每周吃水产品食物的次数是？',
      options: ['4次及以上', '1~3次/周', '几乎不吃'],
      content: null,
    },
    {
      id: 'Dietary-10',
      name: '您每周吃多少份坚果？（1份为20g)',
      options: ['3~4份/周', '1~2份/周', '几乎不吃'],
      content: null,
    },
    {
      id: 'Dietary-11',
      name: '您每天吃多少水果？',
      options: ['250~400g（1~2个拳头大小）', '100~250g', '低于100g'],
      content: null,
    },
    {
      id: 'Dietary-12',
      name: '您每天喝多少牛奶或者奶制品？',
      options: ['300~500毫升', '100~300毫升', '几乎不喝'],
      content: null,
    },
    {
      id: 'Dietary-13',
      name: '您每天喝多少水？',
      options: ['1500~1700ml以上（7~8杯水以上）', '1500ml以下（7杯水以下）', '不渴不喝'],
      content: null,
    },
  ]
}

setQuestion()

function onSave(isNext = false) {
  const answers: Answer[] = questions.value.map((item) => {
    return {
      questionId: item.id,
      content: item.content,
      // name: item.name,
    }
  })
  saveAnswer(answers, '', isNext)
}

function onCancel() {
  if (currentTask?.value?.writeId)
    resetQuestions()

  else
    setQuestion()
}
</script>

<template>
  <div mb-10px flex items-center justify-between>
    <div flex items-center gap-10px>
      <div sub-title />
      <div>膳食营养调查问卷</div>
    </div>
    <EditorBtn />
  </div>
  <div flex="~ col" gap-20px>
    <div v-for="(item, index) in questions" :key="index">
      <div class="question-header">
        {{ `${index + 1}. ${item.name}` }}
      </div>
      <div class="question-body">
        <n-radio-group v-model:value="item.content" :disabled="!isEdit">
          <div flex="~ wrap" class="ssm:flex-col lg:flex-row" gap-40px>
            <n-radio v-for="(option, i) in item.options" :key="i" :value="i">
              {{ option }}
            </n-radio>
          </div>
        </n-radio-group>
      </div>
    </div>
    <ButtonAction @save="onSave" @cancel="onCancel" />
  </div>
</template>
