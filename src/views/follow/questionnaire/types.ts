import type { InjectionKey, Ref } from 'vue'
import type { Tabs } from '@/api/followCenter'

export interface TaskData {
  scaleName: string
  scaleId: string
  taskFinishedRate: string
  isCountFinishedRate: boolean
  planId: string
  taskId: string
  planPhaseId: string
  primaryIndex: string
  reportNo: string
  planTemplateId: string
  taskTemplateId: string
  writeId: string | null
}

export interface Question {
  id: string
  type: 'input' | 'placeholder' | 'text'
  label?: string
  content: number | null
  unit?: string | null
  required?: boolean
  formula?: string
}

export interface Answer {
  questionId: string
  content: any
  name?: string
}

export type QuestionStatus = 'check' | 'edit'

export const QuestionStatusKey: InjectionKey<Ref<QuestionStatus>> = Symbol('QuestionStatus')
export const CurrentTaskKey: InjectionKey<Ref<TaskData | null>> = Symbol('TaskData')
export const HandleNextTaskKey: InjectionKey<() => void> = Symbol('HandleNextTask')
export const CurrentTaskAnswersKey: InjectionKey<Ref<Answer[] | null>> = Symbol('CurrentTaskAnswers')!
export const getTasksKey: InjectionKey<(tab?: Tabs | null, isResetIndex?: boolean) => void> = Symbol('getTasks')
