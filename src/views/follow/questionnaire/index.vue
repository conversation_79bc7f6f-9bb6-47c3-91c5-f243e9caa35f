<script setup lang="ts">
import { useRoute } from 'vue-router'
import { CurrentTaskAnswers<PERSON>ey, CurrentTaskKey, HandleNextTaskKey, QuestionStatusKey, getTasksKey } from './types'
import type { Answer, QuestionStatus, TaskData } from './types'
import LabExam from './components/LabExam.vue'
import ImgExam from './components/ImgExam.vue'
import type { Tabs } from '@/api/followCenter'
import { formatRate } from '@/utils'
import { phaseParamAPI, taskDetailAPI, taskPhaseAPI, taskPhaseV0API } from '@/api/followCenter'
import { Breadcrumb } from '@/layouts/common'
import type { BreadLit } from '@/layouts/common'

defineOptions({
  name: 'Questionnaire',
})

const route = useRoute()
let planPhaseId = ''
let planId = ''
let planTemplateId = ''
// 阶段状态
const phaseTemplateCode = route.query?.phaseTemplateCode as string
const primaryIndex = route.query?.primaryIndex as string
const patientId = route.query?.patientId as string
const slmcNo = route.query?.slmcNo as string
const source = route.query?.source as string
const patientName = route.query?.patientName as string
const age = route.query?.age as string
let isEditor = route.query?.isEditor as string

async function init() {
  const fromPage = route.query.fromPage
  if (fromPage === 'patient') {
    try {
      const { data } = await phaseParamAPI<any>({
        primaryIndex,
        phaseCode: phaseTemplateCode,
      })

      planId = data.planId
      planTemplateId = data.planTemplateId
      planPhaseId = data.planPhaseId
      isEditor = data.isEdit.toString()
      getTasks('BASE_INFO')
    }
    catch (error) {
      console.log(error)
    }
  }
  else if (fromPage === 'slmc') {
    planPhaseId = route.query.planPhaseId as string
    planId = route.query.planId as string
    planTemplateId = route.query.planTemplateId as string
    getTasks('BASE_INFO')
  }
}

const componentMap: Record<string, Component> = {}
const modules: Record<string, any> = import.meta.glob('./components/*.vue', { eager: true })
for (const key in modules) {
  const component = modules[key].default as Component
  componentMap[key.replace('./components/', '').replace('.vue', '')] = component
}

const questionStatus = ref<QuestionStatus>('check')

provide(QuestionStatusKey, questionStatus)

const currentTab = ref<Tabs>('BASE_INFO')
const currentTask = ref<TaskData | null>(null)

provide(CurrentTaskKey, currentTask)

const taskTableRef = ref<any>(null)
const taskTableData = ref<TaskData[]>([])

// 当前选中的任务序号
const currentTaskIndex = computed(() => {
  return taskTableData.value.findIndex(item => item.scaleId === currentTask.value?.scaleId)
})

const isLastTask = computed(() => {
  return currentTaskIndex.value === taskTableData.value.length - 1
})

provide('isLastTask', isLastTask)

const pageLoading = ref(false)
provide('pageLoading', pageLoading)

async function getTasks(tab?: Tabs | null, resetIndex = true) {
  try {
    pageLoading.value = true
    tab = tab || currentTab.value
    if (phaseTemplateCode === '0') {
      const { data } = await taskPhaseV0API<any>({
        primaryIndex,
        showPosition: tab,
      })

      if (data)
        taskTableData.value = data
    }
    else {
      if (!planId)
        return

      const { data } = await taskPhaseAPI<any>({
        planId,
        planPhaseId,
        showPosition: tab,
        planTemplateId,
      })

      taskTableData.value = data
    }

    if (resetIndex) {
      currentTask.value = taskTableData.value[0]
      taskTableRef.value!.setCurrentRow(taskTableData.value[0])
    }
    else {
      currentTask.value = taskTableData.value[currentTaskIndex.value]
      taskTableRef.value!.setCurrentRow(taskTableData.value[currentTaskIndex.value])
    }
  }
  catch (error) {
    console.log(error)
  }
  finally {
    pageLoading.value = false
  }
}
provide(getTasksKey, getTasks)

const currentTaskAnswers = ref<Answer[] | null>(null)

const rightElRef = ref<HTMLElement | null>(null)
async function handleCurrentChange(data: TaskData) {
  try {
    pageLoading.value = true
    currentTask.value = data

    if (['BASE_INFO', 'MEDICAL_HISTORY'].includes(currentTab.value)) {
      questionStatus.value = 'check'

      if (data?.taskId) {
        const { data: taskDetail } = await taskDetailAPI<any>(data?.taskId)

        // 不允许编辑
        if (isEditor === 'false') {
          questionStatus.value = 'check'
        }
        else {
          // 判断是否第一次填写
          if (taskDetail.writeId === null)
            questionStatus.value = 'edit'
        }

        currentTaskAnswers.value = taskDetail.answers?.map((answer: Answer) => {
          return {
            questionId: answer.questionId,
            content: answer.content,
            // name: answer.name,
          }
        })
      }
      else {
        questionStatus.value = 'edit'
      }
    }
    nextTick(() => {
      rightElRef.value!.scrollTo({
        top: 0,
        behavior: 'smooth',
      })
    })
  }
  catch (error) {
    console.log(error)
  }
  finally {
    pageLoading.value = false
  }
}

provide(CurrentTaskAnswersKey, currentTaskAnswers)

function handleNextTask() {
  taskTableRef.value!.setCurrentRow(taskTableData.value[currentTaskIndex.value + 1])
  nextTick(() => {
    rightElRef.value!.scrollTo({
      top: 0,
      behavior: 'smooth',
    })
  })
}

provide(HandleNextTaskKey, handleNextTask)

// @ts-expect-error anyway
function handleTabChange(val) {
  getTasks(val)
}

const renderBread = computed(() => {
  const followList: BreadLit[] = [
    {
      title: '随访中心',
      link: '/follow',
      key: 'follow',
    },
    {
      title: '随访管理',
      link: '/follow/manage',
      key: 'follow_manage',
    },
  ]

  const patientList: BreadLit[] = [
    { title: '患者数据库', link: '/patient', key: 'patient' },
    { title: '患者列表', link: '/patient/list', key: 'patient_list' },
  ]

  const common: BreadLit[] = [
    {
      title: `${patientName || '未知病人'}档案信息`,
      link: `/patient/file?patientId=${patientId}&patientName=${patientName}&primaryIndex=${primaryIndex}&slmcNo=${slmcNo}&tabName=SlmcInterview&age=${age}&source=${source}`,
      key: 'patient_file',
    },
    {
      title: questionStatus.value === 'edit' ? '录入信息' : '查看信息',
      link: null,
      key: 'follow_questionnaire',
    },
  ]

  if (source === 'follow')
    return [...followList, ...common]
  else
    return [...patientList, ...common]
})

onMounted(() => {
  init()
})
</script>

<template>
  <n-spin type="uni" size="medium" :show="pageLoading">
    <Breadcrumb :bread-list="renderBread" />

    <PageCard breadcrumb bottom-zero>
      <PageTitle class="mb-14px">
        {{ questionStatus === 'edit' ? '录入信息' : '查看信息' }}
      </PageTitle>
      <!-- @vue-skip -->
      <n-tabs
        v-model:value="currentTab" animated :width="104"
        @update:value="handleTabChange"
      >
        <n-tab-pane name="BASE_INFO" tab="基础信息采集" />
        <n-tab-pane name="MEDICAL_HISTORY" tab="病史采集" />
        <n-tab-pane name="INSPECT" tab="实验室检查" />
        <n-tab-pane name="ICONOGRAPHY" tab="影像学检查" />
      </n-tabs>
      <div class="h-[calc(100%-83px)]" flex gap-13px>
        <div class="!w-252px">
          <el-table
            ref="taskTableRef"
            :data="taskTableData"
            stripe
            show-overflow-tooltip
            highlight-current-row
            @current-change="handleCurrentChange"
          >
            <el-table-column min-width="152" label="项目" prop="scaleName" />
            <el-table-column width="100" align="right">
              <template #header>
                <div flex justify-end gap-6px>
                  <n-tooltip trigger="hover" placement="bottom">
                    <template #trigger>
                      <div flex items-center justify-end>
                        <img w-16px src="@/assets/images/information.svg" alt="">
                      </div>
                    </template>
                    <div w-270px>
                      非必要填写的问卷，完成度用【-】表示，且不计入完成度计算
                    </div>
                  </n-tooltip>
                  <div>
                    完成度
                  </div>
                </div>
              </template>
              <template #default="{ row }">
                <div ml-20px>
                  {{ row.isCountFinishedRate
                    ? row.finishRate ? formatRate(row.finishRate) : '0.00%'
                    : '-' }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div bg="#ccc" h-full w-1px />
        <div ref="rightElRef" w-full overflow-auto pb-100px pr-20px>
          <template v-if="['BASE_INFO', 'MEDICAL_HISTORY'].includes(currentTab)">
            <component :is="componentMap[currentTask.scaleId]" v-if="currentTask?.scaleId" />
          </template>
          <template v-else-if="currentTab === 'INSPECT' ">
            <LabExam />
          </template>
          <template v-else>
            <ImgExam />
          </template>
        </div>
      </div>
    </PageCard>
  </n-spin>
</template>

<style>
.question-header {
  --uno: bg-#ECF6FF w-full flex items-center p-14px text-#666;
  border: 1px solid #d1d1d1;
  border-radius: 3px 3px 0px 0px;
}

.question-body {
  --uno: p-14px;
  border: 1px solid #d1d1d1;
  border-top: none;
  border-radius: 0 0 3px 3px;
}
</style>
