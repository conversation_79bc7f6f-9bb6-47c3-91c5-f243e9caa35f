<script setup lang='ts'>
import { Breadcrumb } from '@/layouts/common'
import { useAuthStore } from '~/src/store'

const router = useRouter()
const pageOptions = reactive({
  start: 1,
  size: 10,
  total: 0,
})

const statusOptions = {
  '3': {
    label: '已中止',
    color: 'bg-#CCCCCC',
    key: 3,
  },
  '2': {
    label: '已完成',
    color: 'bg-#4ACFB1',
    key: 2,
  },
  '1': {
    label: '进行中',
    color: 'bg-#FF9B54',
    key: 1,
  },
  '0': {
    label: '待开始',
    color: 'bg-#45A8E6',
    key: 0,
  },
  '-1': {
    label: '未发布',
    color: 'bg-#A5B8D1',
    key: -1,
  },
}

const searchParams = reactive({
  topicName: null,
  shortName: null,
  principal: null,
})

const tableData = ref()
const { userInfo } = useAuthStore()

function getDataFromNet() {

}

function handleSearchClick() {
  getDataFromNet()
}

function handleResetClick() {
  searchParams.topicName = null
  searchParams.shortName = null
  searchParams.principal = null
  getDataFromNet()
}

function editTheHistory(row, isEdit) {
//   router.push({
//     path: '/researcher/scaleDetail',
//     query: {
//       scaleId: row.scaleId,
//     },
//   })

  /// 编辑或者查看病史信息

}

onMounted(() => {
  getDataFromNet()
})
</script>

<template>
  <div class="page">
    <Breadcrumb
      :bread-list="[
        {
          title: '科研管理',
          link: null,
          key: 'researcher',
        },
        {
          title: '病史信息管理',
          link: null,
          key: 'researcher_historyManage',
        },
      ]"
    />
    <PageCard breadcrumb style="display: flex;flex-direction: column;">
      <PageTitle class="mb-2px">
        病史信息管理
      </PageTitle>
      <div mt-14px flex flex-wrap items-center style="gap: 14px 20px;">
        <div flex items-center>
          <span color="#666" mr-10px text-14px>课题名称</span>
          <el-input v-model="searchParams.topicName" style="width: 210px;" placeholder="请输入" />
        </div>
        <div flex items-center>
          <span color="#666" mr-10px text-14px>课题简称</span>
          <el-input v-model="searchParams.shortName" style="width: 210px;" placeholder="请输入" />
        </div>
        <div flex items-center>
          <span color="#666" mr-10px text-14px>课题负责人</span>
          <el-input v-model="searchParams.principal" style="width: 210px;" placeholder="请输入" />
        </div>
        <div flex items-center>
          <n-button type="primary" class="mr-10px" @click="handleSearchClick">
            查询
          </n-button>
          <n-button @click="handleResetClick">
            重置
          </n-button>
        </div>
      </div>
      <el-table scrollbar-always-on :data="tableData" stripe mt-14px overflow-y-auto>
        <el-table-column label="序号" width="80" type="index" header-align="center" align="center" />
        <el-table-column width="280" label="课题名称" prop="topicName" />
        <el-table-column
          label="课题类型"
          prop="type"
          width="130"
        />
        <el-table-column label="课题类型" prop="type" width="120" />
        <el-table-column label="课题简称" prop="shortName" width="140" />
        <el-table-column label="课题负责人" prop="principal" width="100" />
        <el-table-column label="课题状态" width="96">
          <template #default="{ row }">
            <div flex items-center>
              <div h-6px w-6px rounded :class="statusOptions[row.status]?.color" />
              <div ml-6px>
                {{ statusOptions[row.status]?.label }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="90">
          <template #default="{ row }">
            <div v-if="row.principal === userInfo.userName && row.status === '-1'" text-14px color="#3B8FD9" @click="editTheHistory(row, true)">
              编辑
            </div>
            <div v-else text-14px color="#3B8FD9" @click="editTheHistory(row, false)">
              查看
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div flex justify-end>
        <n-pagination
          v-model:page="pageOptions.start" v-model:page-size="pageOptions.size"
          :item-count="pageOptions.total" :page-sizes="[5, 10, 20, 30]" show-size-picker show-quick-jumper mt-14px
          @update:page-size="getDataFromNet"
          @update:page="getDataFromNet"
        />
      </div>
    </PageCard>
  </div>
</template>

<style lang="scss" scoped></style>
