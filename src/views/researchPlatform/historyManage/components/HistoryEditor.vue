<script setup lang='ts'>
const questions = ref([])
const questionType = {
  /// 单选题
  0: {
    required: true,
    title: '单选题',
    type: 'radio',
    options: [{
      label: '否',
      value: '否',
      tip: '',
      showTip: false,
      default: false,
    },
    {
      label: '是',
      value: '是',
      tip: '',
      showTip: false,
      default: false,
    }],
  },
  /// 多选题
  1: {
    required: true,
    title: '多选题',
    type: 'checkbox',
    options: [{
      label: '',
      value: '',
      tip: '',
      showTip: false,
      default: false,
    }],
  },

}

/// 多选添加
function addGroupItem(kindex, item) {
  item.options.splice(kindex + 1, 0, {
    label: '',
    value: '',
    tip: '',
    showTip: false,
    default: false,
  })

  console.log(item.options)
}

function delGroupItem(kindex, item) {
  item.options.splice(kindex, 1)
}

function addTheHistoryItems() {
  questions.value.push(questionType['0'])
}
</script>

<template>
  <div class="history-content">
    <div v-if="questions?.length === 0" flex-col flex-1 items-center justify-center>
      <div flex items-center>
        <n-empty />
        <div ml-16px>
          <div color="#999">
            无数据
          </div>
          <n-button type="primary" ghost mt-14px @click="addTheHistoryItems">
            添加题目
          </n-button>
        </div>
      </div>
    </div>
    <div v-else mx-105px flex-1>
      <div v-for="(item, index) in questions" :key="index" class="question-item">
        <div flex items-center justify-between>
          <div text-16px font-500>
            题目1
          </div>
          <div p-4px>
            <img h-16px w-16px src="@/assets/svg-icon/project-delete.svg">
          </div>
        </div>
        <el-divider style="margin: 10px 0 20px 0;" />
        <el-form label-position="right" label-width="70" :model="item">
          <el-form-item label="题目类型">
            <div w-full flex items-center>
              <el-select v-model="item.type" style="width: 240px;">
                <el-option label="判断题" value="radio" />
                <el-option label="多选题" value="checkbox" />
              </el-select>
              <n-checkbox v-model:checked="item.required" ml-24px>
                <span ml-10px>必选</span>
              </n-checkbox>
            </div>
          </el-form-item>
          <el-form-item
            prop="title"
            label="题目"
            :rules="[
              {
                required: true,
                message: '请输入题目',
                trigger: ['change', 'blur'],
              }]"
          >
            <div w-full flex items-center>
              <el-input v-model="item.title" type="textarea" :rows="3" resize="none" placeholder="请输入" maxlength="100" show-word-limit style="flex: 1;" />
            </div>
          </el-form-item>
          <div v-if="item.type === 'radio'">
            <el-form-item
              v-for="(choose, kindex) in item.options"
              :key="kindex" :prop="`options.${kindex}.label`" :rules="[
                {
                  required: true,
                  message: '请输入选项',
                  trigger: ['change', 'blur'],
                }]" :label="`选项${kindex + 1}`"
            >
              <div w-full flex items-center>
                <el-input v-model="choose.label" style="width:100px;" />
                <n-checkbox v-model:checked="choose.default" ml-24px>
                  <span ml-10px>默认选项</span>
                </n-checkbox>
                <n-checkbox v-model:checked="choose.showTip" ml-24px>
                  <span ml-10px>选项提示</span>
                </n-checkbox>
                <el-input v-if="choose.showTip" v-model="choose.tip" placeholder="请输入" style="flex:1;margin-left: 10px;" />
              </div>
            </el-form-item>
          </div>
          <div v-if="item.type === 'checkbox'">
            <el-form-item
              v-for="(choose, kindex) in item.options"
              :key="kindex" :prop="`options.${kindex}.label`" :rules="[
                {
                  required: true,
                  message: '请输入选项',
                  trigger: ['change', 'blur'],
                }]" :label="`选项${kindex + 1}`"
            >
              <div w-full flex items-center>
                <el-input v-model="choose.label" style="flex: 1;" />
                <n-checkbox v-model:checked="choose.default" ml-24px>
                  <span ml-10px>默认选项</span>
                </n-checkbox>
                <n-checkbox v-model:checked="choose.showTip" ml-24px>
                  <span ml-10px>选项提示</span>
                </n-checkbox>
                <div ml-24px flex items-center>
                  <img h-16px w-16px src="@/assets/images/keyan_group_add.png" @click="addGroupItem(kindex, item)">
                  <img :style="{ visibility: item.options.length > 2 ? 'visible' : 'hidden' }" ml-10px h-16px w-16px src="@/assets/images/keyan_group_del.png" @click="delGroupItem(kindex, item)">
                </div>
              </div>
              <div v-if="choose.showTip" mt-8px style="width: 100%" flex items-center>
                <div color="#666">
                  提示信息
                </div>
                <el-input v-model="choose.tip" placeholder="请输入" class="group-tip" style="margin-left: 10px;" />
              </div>
            </el-form-item>
          </div>
        </el-form>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.history-content {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;

  .question-item {
    padding: 14px 20px 20px 14px;
    background: #f8f8f8;
    border: 1px solid #e8e8e8;
  }

  .group-tip{
    width: calc(100% - 344px);

    :deep(.el-input__wrapper){
      box-shadow: 0 0 0 1px #dcdfe6 inset !important;
    }
  }

  :deep(.n-empty) {
    --n-icon-size: 160px !important;
  }
}
</style>
