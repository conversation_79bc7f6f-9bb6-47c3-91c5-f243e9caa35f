<script setup lang='ts'>
import HistoryEditor from './components/HistoryEditor.vue'
import { Breadcrumb } from '@/layouts/common'
import { useAuthStore } from '~/src/store'

const { userInfo } = useAuthStore()

function getDataFromNet() {

}

/// 添加题目
function addTheHistoryItems() {

}

onMounted(() => {
  getDataFromNet()
})
</script>

<template>
  <div class="page">
    <Breadcrumb
      :bread-list="[
        {
          title: '科研管理',
          link: null,
          key: 'researcher',
        },
        {
          title: '病史信息管理',
          link: null,
          key: 'researcher_historyManage',
        },
        {
          title: '病史信息详情',
          link: null,
          key: 'researcher_historyManage_historyDetail',
        },
      ]"
    />
    <PageCard breadcrumb style="display: flex;flex-direction: column;">
      <PageTitle class="mb-2px">
        病史信息编辑
      </PageTitle>
      <div mt-15px flex items-center rounded-10px px-8px py-6px style="background: linear-gradient(90deg, #dbeef3, #d5e6ff)">
        <img src="@/assets/svg-icon/researcher-detail-icon.svg" w-96px>
        <div flex-1>
          <div text-18px font-500 style="line-height: 26px">
            {{ topicInfo?.name || '-' }}
          </div>
          <div text-14px>
            <span color="#666">课题负责人:</span>
            <span color="#333">bb</span>
          </div>
        </div>
      </div>
      <el-divider style="margin: 14px 0;" />
      <HistoryEditor flex-1 />
    </PageCard>
  </div>
</template>

<style lang="scss" scoped>
.page{

}
</style>
