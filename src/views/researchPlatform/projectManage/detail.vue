<script setup lang="ts">
import { storeToRefs } from 'pinia'
import dayjs from 'dayjs'
import DesignProject from './components/DesignProject.vue'
import SubjectList from './components/SubjectList.vue'
import ProjectInfo from './components/ProjectInfo.vue'
import AnalysisData from './components/AnalysisData.vue'
import { Breadcrumb } from '@/layouts/common'
import { useReseacherStore } from '~/src/store'
import { publishTheTopic } from '~/src/api/research'
import { router } from '~/src/router'

const route = useRoute()
const reseacherStore = useReseacherStore()
const { topicInfo, topicDetailTab } = storeToRefs(reseacherStore)
const topicId = route.query?.topicId as string
const jumpToEdit = ref(route.query?.isEdit === 'true')
const currentTab = route.query?.currentTab as string
const currentPage = ref(currentTab ?? topicDetailTab.value)

const statusOptions = {
  '3': {
    label: '已中止',
    color: 'bg-#CCCCCC',
    key: 3,
  },
  '2': {
    label: '已完成',
    color: 'bg-#4ACFB1',
    key: 2,
  },
  '1': {
    label: '进行中',
    color: 'bg-#FF9B54',
    key: 1,
  },
  '0': {
    label: '待开始',
    color: 'bg-#45A8E6',
    key: 0,
  },
  '-1': {
    label: '未发布',
    color: 'bg-#A5B8D1',
    key: -1,
  },
}
const editDesign = ref(false)
const rules = reactive({
  rangeTime: [{
    required: true,
    message: '请填写试验周期',
  }],
})
const ruleForm: any = reactive({
  rangeTime: null,
})
const publishRef = ref()
const showRangeTime = ref(false)
function tabChangeBefore(name, oldName) {
  if (oldName === 'info')
    jumpToEdit.value = false

  /// / 把route上参数加上
  updateQueryParams(name)
  return true
}

function updateQueryParams(value) {
  reseacherStore.updateTopicTab(value)
}

// onUnmounted(() => {
//   debugger
//   reseacherStore.updateTopicTab('info')
// })

onMounted(() => {
  console.log(route)
  nextTick(() => {
    if (topicId?.length > 0) {
    // getTheTopicDetail(topicId).then(res => {
    //   topicInfo.value = res.data
    //   // console.log(res)
    // })
      reseacherStore.getInfo(topicId)
    }
  })
  console.log(currentPage)
})

function refreshTopicInfo() {
  reseacherStore.getInfo(topicId)
}

/// 发布课题
function publishTheTpic() {
  /// 查看状态值
  if (topicInfo.value?.designStatus !== 2) {
    /// 没有试验流程, 不允许发布, 去填写实验流程
    editDesign.value = true
    currentPage.value = 'design'
    setTimeout(() => {
      /// 还原一下
      editDesign.value = false
    }, 100)
    window.$message.warning('请完善实验流程后发布')
  }
  else if (topicInfo.value?.designStatus === 2) {
    /// 可以发布
    showRangeTime.value = true
  }
}

function visibleChange(v) {
  if (!v) {
    showRangeTime.value = false
    ruleForm.rangeTime = null
  }
}

function throttle(func, wait) {
  let lastCall = 0

  return function (...args) {
    const now = new Date().getTime()
    if (lastCall && now < lastCall + wait) {
      // 不调用了
    }
    else {
      lastCall = now
      func.apply(this, args)
    }
  }
}

function publishApi(params) {
  publishTheTopic(params).then((res) => {
    if (res?.data) {
      window.$message.success('发布成功', { duration: 300 })
      router.back()
    }
  })
}

const publishThrottle = throttle(handlePublishConfirm, 300)

function handlePublishConfirm() {
  /// 发布
  publishRef.value.validate((valid, fields) => {
    if (valid) {
      const params = {
        id: topicInfo.value?.id,
        startTime: `${ruleForm.rangeTime[0]} 00:00:00`,
        endTime: `${ruleForm.rangeTime[1]} 23:59:59`,
      }
      // throttle(publishApi, 500)
      publishApi(params)
      // console.log(';xxx')
    }
  })
}

onUnmounted(() => {
  // console.log('GGGGGGGGGGGGGG')
  reseacherStore.clearAll()
})

function canUsePublish() {
  const us = reseacherStore.getPower(topicInfo.value?.director)
  return us && topicInfo.value?.status === -1
}

// watch(() => topicId, () => {
//   if (topicId?.length > 0) {
//     // getTheTopicDetail(topicId).then(res => {
//     //   topicInfo.value = res.data
//     //   // console.log(res)
//     // })
//     console.log('刷新')
//     reseacherStore.getInfo(topicId)
//   }
// }, { immediate: true })

/// /  大彩蛋. 临时变更课题的默认病史
</script>

<template>
  <div style="overflow: hidden; display: flex;flex-direction: column;">
    <Breadcrumb
      :bread-list="[
        {
          title: '科研管理',
          link: null,
          key: 'researcher',
        },
        {
          title: '课题管理',
          link: '/',
          key: 'researcher_projectManage',
        },
        {
          title: '课题详情',
          link: null,
          key: 'researcher_projectManage_detail',
        },
      ]"
    />
    <div flex-col class="content-bg">
      <div p-14px>
        <div flex items-center rounded-10px px-8px py-6px style="background: linear-gradient(90deg, #dbeef3, #d5e6ff)">
          <img src="@/assets/svg-icon/researcher-detail-icon.svg" w-96px>
          <div flex-1>
            <div text-18px font-500 style="line-height: 26px">
              {{ topicInfo?.name || '-' }}
            </div>
            <div mt-8px flex items-center>
              <div>
                <span text-14px color="#666">负责人:</span>
                <span ml-6px>{{ topicInfo?.director || '-' }}</span>
              </div>
              <div ml-80px flex items-center>
                <span text-14px color="#666">课题状态:</span>
                <div
                  ml-6px :class="statusOptions[topicInfo?.status || 0].color" h-20px flex items-center rounded-10px
                  px-10px text-12px color="#fff"
                >
                  {{ statusOptions[topicInfo?.status || 0].label }}
                </div>
              </div>
              <div v-if="topicInfo?.status !== -1" ml-80px>
                <span text-14px color="#666">试验周期:</span>
                <span ml-6px>{{ `${dayjs(topicInfo?.startTime).format('YYYY-MM-DD')} － ${dayjs(topicInfo?.endTime).format('YYYY-MM-DD')}` }}</span>
              </div>
            </div>
            <n-button v-if="canUsePublish()" mb-10px mt-14px color="#FF9B54" @click="publishTheTpic">
              发布
            </n-button>
          </div>
          <div class="bg-#fff" h-100px w-217px flex-col items-center rounded-6px style="border: 1px solid #d3dce7">
            <div
              text-14px style="
                            padding: 0 10px;
                            height: 26px;
                            display: flex;
                            align-items: center;
                            align-self: flex-start;
                            justify-content: center;
                            background: #ffc569;
                            border-radius: 6px 0px 14px 0px;
                        "
            >
              受试者入组进度
            </div>
            <div mb-20px mt-8px text-28px color="#586D88">
              {{ `${topicInfo?.rate ?? '0'}%` }}
            </div>
            <div w-full px-10px>
              <el-progress color="#06AEA6" w-full :show-text="false" :percentage="topicInfo?.rate" :stroke-width="12" />
            </div>
          </div>
        </div>
      </div>
      <div class="tabs-page" m-14px mt-0px>
        <n-tabs v-model:value="currentPage" type="card" tab-style="width: 124px;" @before-leave="tabChangeBefore">
          <n-tab-pane name="info" tab="课题信息">
            <ProjectInfo :is-edit="jumpToEdit" :topic-id="topicId" />
          </n-tab-pane>
          <n-tab-pane name="design" tab="设计试验流程">
            <DesignProject :topic-id="topicId" :edit="editDesign" @update-design="refreshTopicInfo" />
          </n-tab-pane>
          <n-tab-pane name="manage" tab="受试者管理">
            <SubjectList :topic-id="topicId" />
          </n-tab-pane>
          <n-tab-pane name="data" tab="数据分析记录">
            <AnalysisData />
          </n-tab-pane>
        </n-tabs>
      </div>
    </div>
    <BasicModal
      :min-height="90"
      ok-text="发布"
      :visible="showRangeTime" title="试验周期" width="400" :footer-offset="-25"
      @ok="publishThrottle" @visible-change="visibleChange"
    >
      <el-form
        ref="publishRef"
        :model="ruleForm"
        :rules="rules"
        label-width="auto"
      >
        <div px-26px pt-30px>
          <el-form-item flex label="试验周期" prop="rangeTime">
            <el-date-picker
              v-model="ruleForm.rangeTime"
              unlink-panels type="daterange" value-format="YYYY-MM-DD"
              style="width: 270px" range-separator="－" start-placeholder="开始日期" end-placeholder="结束日期"
            />
          </el-form-item>
        </div>
      </el-form>
    </BasicModal>
  </div>
</template>

<style lang="scss" scoped>
.content-bg{
  flex:1;
  overflow: hidden;
}
.tabs-page {
  flex: 1;
  overflow: hidden;
  background: #fff;
  box-shadow: 0px 1px 4px 0px rgba(202, 202, 202, 0.50);
}

:deep(.n-tabs){
  height: 100%;
}

:deep(.n-tab-pane){
    overflow-y: auto;
    flex: 1;
    display: flex;
    flex-direction: column;
}

:deep(.n-tab-pane) {
  padding-top: 0;
}
</style>
