<script setup lang='ts'>
import dayjs from 'dayjs'
import { Breadcrumb } from '@/layouts/common'
import { deleteTheTopic, getReseacherList, getTopicList, getTopicNameList, stopTheTopic } from '~/src/api/research'
import { useAuthStore, useReseacherStore } from '~/src/store'

import './detail.vue' /// / 先导入模块,为了加载下一层更快

const router = useRouter()
const { userInfo } = useAuthStore()
const reseacher = useReseacherStore()

const topicType = ['国家课题', '省部级课题', '市厅级课题', '院校级课题', '企业级课题', '社会组织级课题', '其他']
const searchParams = reactive({
  name: null,
  type: null,
  status: null,
  director: null,
})

const ketiFzrOptions = reactive({
  data: [],
  props: {
    label: 'userName',
    value: 'userName',
  },
})

const pageOptions = reactive({
  page: 1,
  size: 10,
  total: 0,
})
const loadingOptions: any = reactive({
  loading: false,
  data: [],
})

const tableData = ref()

const statusOptions = {
  '3': {
    label: '已中止',
    color: 'bg-#CCCCCC',
    key: 3,
  },
  '2': {
    label: '已完成',
    color: 'bg-#4ACFB1',
    key: 2,
  },
  '1': {
    label: '进行中',
    color: 'bg-#FF9B54',
    key: 1,
  },
  '0': {
    label: '待开始',
    color: 'bg-#45A8E6',
    key: 0,
  },
  '-1': {
    label: '未发布',
    color: 'bg-#A5B8D1',
    key: -1,
  },
}

function getTopicName(query) {
  console.log(query)
  if (query != null) {
    loadingOptions.loading = true
    getTopicNameList({
      name: query,
      rsUserId: userInfo?.rsUserId || userInfo.id,
    }).then((res) => {
      loadingOptions.loading = false
      loadingOptions.data = res.data
    })
  }
}

/// 搜索
function handleSearchClick() {
  console.log(searchParams)
  getDataFromNet()
}
function handleResetClick() {
  searchParams.name = null
  searchParams.status = null
  searchParams.director = null
  searchParams.type = null
}

/// 新增
function handleAddClick() {
  router.push({
    path: '/researcher/projectManage/create',
  })
}

/// 编辑
function handleEditClick(row, edit) {
  useReseacherStore().updateTopicTab('info')
  router.push({
    path: '/researcher/projectManage/detail',
    query: {
      topicId: row.id,
      isEdit: edit,
    },
  })
}

/// 删除
function handleDeleteClick(row, isStop?: boolean) {
  window.$dialog.warning({
    title: isStop ? '确定中止吗? ' : '确定删除？',
    content: isStop ? '' : '一旦删除，不可恢复，请谨慎操作！',
    positiveText: '确定',
    negativeText: '取消',
    btnGhost: true,
    onPositiveClick: () => {
      /// 删除
      if (isStop) {
        stopTheTopic(row.id).then((res) => {
          if (res.data) {
            window.$message.success('中止成功')
            getDataFromNet()
          }
        })
      }
      else {
        deleteTheTopic(row.id).then((res) => {
          if (res.data) {
            window.$message.success('删除成功')
            getDataFromNet()
          }
        })
      }
    },
  })
}

function getDataFromNet() {
  getTopicList({
    status: searchParams.status?.join(','),
    name: searchParams.name?.join('^'),
    type: searchParams.type?.join(','),
    director: searchParams.director?.join(','),
    size: pageOptions.size,
    start: pageOptions.page,
    rsUserId: userInfo?.rsUserId || userInfo.id,
  }).then((res) => {
    console.log(res)
    tableData.value = res?.data?.records || [],
    pageOptions.total = Number(res?.data?.total || 0)
  })
}

onMounted(() => {
  getDataFromNet()
  /// 负责人列表
  getReseacherList({
    size: 10000,
    start: 1,
  }).then((res) => {
    ketiFzrOptions.data = res?.data?.records || []
  })
})
</script>

<template>
  <div class="page">
    <Breadcrumb
      :bread-list="[
        {
          title: '科研管理',
          link: null,
          key: 'researcher',
        },
        {
          title: '课题管理',
          link: null,
          key: 'researcher_projectManage',
        },
      ]"
    />
    <PageCard breadcrumb style="display:flex;flex-direction: column;">
      <PageTitle class="mb-2px">
        课题管理
      </PageTitle>
      <div mt-14px flex flex-wrap items-center style="gap: 14px 20px;">
        <div flex items-center>
          <span color="#666" mr-10px text-14px>课题名称</span>
          <el-select
            v-model="searchParams.name"
            fit-input-width
            clearable collapse-tags :max-collapse-tags="1" remote-show-suffix collapse-tags-tooltip :reserve-keyword="false"
            multiple filterable remote placeholder="请输入" :remote-method="getTopicName" :loading="loadingOptions.loading"
            style="width: 240px"
          >
            <el-option
              v-for="(nitem, index) in loadingOptions.data" :key="index" :label="nitem.name"
              :value="nitem.name"
            >
              <el-tooltip :enterable="false" placement="right">
                <template #content>
                  <span>{{ nitem.name }}</span>
                </template>
                <span
                  style="max-width: 185px;
    text-overflow: ellipsis;
    overflow: hidden;display:inline-block"
                >
                  {{ nitem.name }}
                </span>
              </el-tooltip>
            </el-option>
          </el-select>
        </div>
        <div flex items-center>
          <span color="#666" mr-10px text-14px>课题负责人</span>
          <el-select-v2
            v-model="searchParams.director" collapse-tags collapse-tags-tooltip :max-collapse-tags="2" filterable multiple
            clearable placeholder="请选择" style="width: 240px" :options="ketiFzrOptions.data"
            :props="ketiFzrOptions.props"
          />
        </div>
        <div flex items-center>
          <span color="#666" mr-10px text-14px>课题类别</span>
          <el-select v-model="searchParams.type" clearable style="width: 240px" placeholder="请选择">
            <el-option v-for="(item, index) in topicType" :key="index" :label="item" :value="item" />
          </el-select>
        </div>
        <div flex items-center>
          <span color="#666" mr-10px text-14px>课题状态</span>
          <el-select
            v-model="searchParams.status" clearable collapse-tags collapse-tags-tooltip :max-collapse-tags="2"
            multiple placeholder="请选择" style="width: 240px"
          >
            <el-option
              v-for="(item, index) in statusOptions" :key="index" :label="item.label"
              :value="item.key"
            />
          </el-select>
        </div>
        <div flex items-center>
          <n-button type="primary" class="mr-10px" @click="handleSearchClick">
            查询
          </n-button>
          <n-button @click="handleResetClick">
            重置
          </n-button>
        </div>
      </div>
      <n-button mt-14px w-84px type="primary" class="mr-10px" @click="handleAddClick">
        新增课题
      </n-button>
      <el-table
        stripe mt-14px overflow-y-auto :data="tableData" @row-dblclick="(row) => {
          handleEditClick(row, false)
        }"
      >
        <el-table-column type="index" label="序号" width="58" />
        <el-table-column label="课题名称" :formatter="({ name }) => name || '-'" min-width="304" />
        <el-table-column label="课题类别" :formatter="({ type }) => type || '-'" width="110" />
        <el-table-column
          label="研究方向" :formatter="({ researchDirection }) => researchDirection || '-'"
          min-width="304"
        />
        <el-table-column
          label="主研究单位" show-overflow-tooltip :formatter="({ organName }) => organName || '-'"
          width="166"
        />
        <el-table-column label="课题负责人" :formatter="({ director }) => director || '-'" width="100" />
        <el-table-column label="受试者入组进度" min-width="130">
          <template #default="{ row }">
            <div flex items-center>
              <el-progress
                color="#06AEA6" flex-1 :show-text="false" :percentage="Number(row.rate)"
                :stroke-width="12"
              />
              <span ml-6px>{{ `${Number(row.rate)}%` }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="课题状态" width="96">
          <template #default="{ row }">
            <div flex items-center>
              <div h-6px w-6px rounded :class="statusOptions[row.status]?.color" />
              <div ml-6px>
                {{ statusOptions[row.status]?.label }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="课题试验周期"
          width="200"
        >
          <template #default="{ row }">
            <div>
              {{ row.startTime ? `${row.startTime ? dayjs(row.startTime).format('YYYY-MM-DD') : '-'} 至 ${row.endTime ? dayjs(row.endTime).format('YYYY-MM-DD') : '-'}` : '-' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" fixed="right" min-width="100">
          <template #default="{ row }">
            <div v-if="reseacher.getPower(row.director)">
              <div v-if="row.status === 0 || row.status === -1" flex items-center gap-5px>
                <span uno-link @click="handleEditClick(row, true)">编辑</span>
                <div h-14px w-1px bg="#3B8FD9" />
                <span uno-link @click="handleDeleteClick(row)">删除</span>
              </div>
              <div v-if="row.status === 1" flex items-center gap-5px>
                <span uno-link @click="handleEditClick(row, false)">查看</span>
                <div h-14px w-1px bg="#3B8FD9" />
                <span uno-link @click="handleDeleteClick(row, true)">中止</span>
              </div>
              <div v-if="row.status === 2 || row.status === 3">
                <span uno-link @click="handleEditClick(row, false)">查看</span>
              </div>
            </div>
            <div v-else>
              <span uno-link @click="handleEditClick(row, false)">查看</span>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div flex justify-end>
        <n-pagination
          v-model:page="pageOptions.page" v-model:page-size="pageOptions.size"
          :item-count="pageOptions.total" :page-sizes="[5, 10, 20, 30]" show-size-picker show-quick-jumper mt-14px
          @update:page-size="getDataFromNet"
          @update:page="getDataFromNet"
        />
      </div>
    </PageCard>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-progress-bar__outer) {
  background-color: #e8e8e8;
}

:deep(.el-select-v2__selected-item) {
  flex: 1
}
:deep(.el-select__tags-text){
  max-width: 106px !important;
}
</style>
