<script setup lang="ts">
import _ from 'lodash'
import UploadView from './components/UploadView.vue'
import { Breadcrumb } from '@/layouts/common'

import { addNewTopic, generateFileId, getReseacherList, getTheTopicDetail, postTopicGroups, updateTheTopic } from '~/src/api/research'
import { router } from '~/src/router'
import { useReseacherStore } from '~/src/store'
import { newHistory } from '~/src/constants/researcherHistorys'

/// 既可以作为单独页面, 也可作为 组件,  用一套
const props = defineProps({
  ketiID: {
    type: String,
  },
})

const emit = defineEmits(['save', 'cancel'])
// const userInfo =
const uploadId: any = ref('')

const topicType = ['国家课题', '省部级课题', '市厅级课题', '院校级课题', '企业级课题', '社会组织级课题', '其他']

/// 是新增还是编辑课题
const isAdd = ref(props.ketiID == null || props.ketiID.length === 0)

const formEl = ref()
const isEditUser = ref(false)

const role = ref([
  {
    role: '课题负责人',
  },
  {
    role: '科研人员',
  },
])

const rangeTime = ref()
const tableDatas = ref()

const commitParams: any = reactive({
  name: '',
  englishName: '',
  registrationNo: '',
  type: '',
  shortName: '',
  approvalTime: '',
  // endTime: '',
  // startTime: '',
  sampleNum: '',
  researchStandard: '',
  researchDirection: '',
  rsTopicUserDTOList: [],
})

// const tableDatas = ref();

const pageOptions = reactive({
  page: 1,
  size: 10,
  total: 10,
})

const showAddMember = ref(false)
const addRules = {
  userName: {
    required: true,
    message: '姓名不能为空',
    trigger: ['blur', 'change'],
  },
  role: {
    required: true,
    message: '内容不能为空',
    trigger: ['blur', 'change'],
  },
  type: {
    required: true,
    message: '内容不能为空',
    trigger: ['blur'],
  },
}
const memberValues = reactive({
  userName: '',
})

const loadingOptions = reactive({
  loading: false,
  data: [],
})

const showGroupModal = ref(false)
const groupFromRef = ref()
const groupText = ref()
const groups = reactive({
  group: [{
    text: '',
  }],
})
const tempSaveGroup = ref([])

/// 获取研究院列表
function getReseacher(query) {
  if (query != null) {
    loadingOptions.loading = true
    getReseacherList({
      userName: query,
      start: 1,
      size: 1000,
    }).then((res) => {
      /// shit 过滤一下 去除已有的
      const filterArr = _.differenceBy((res?.data?.records || []), commitParams?.rsTopicUserDTOList, 'id')
      loadingOptions.data = filterArr
      console.log(commitParams?.rsTopicUserDTOList)
      console.log(filterArr)
      loadingOptions.loading = false
    })
  }
}

function visibleChange(v) {
  if (!v) {
    showAddMember.value = false
    reset()
  }
}

function visibleChange1(v) {
  showGroupModal.value = false
  groups.group = tempSaveGroup.value?.length === 0 ? [{ text: '' }] : tempSaveGroup.value?.map(item => ({ text: item }))
}

function checkUserName(e) {
  /// 如果名字被选了, 那么赋值
  console.log(e)
  // memberValues.userName = e.userName
  memberValues.organName = e.organName
  memberValues.organId = e.organId
  memberValues.userId = e.id
}

function handleBodyConfirm() {
  /// 新增人员
  formEl.value.validate((valid, fields) => {
    if (valid) {
      // console.log("submit!");
      // 提交到待提交参数
      if (!isAdd.value)
        memberValues.topicId = props.ketiID

      const index = findTheUser()
      if (findHaveFzr() && memberValues.role === '课题负责人') {
        window.$message.error('团队已有负责人')
        return
      }
      if (isEditUser.value) {
        commitParams.rsTopicUserDTOList.splice(index, 1, { ...memberValues })
      }
      else {
        if (index >= 0) {
          window.$message.error('成员已在团队')
          return
        }
        commitParams.rsTopicUserDTOList.push({ ...memberValues })
      }
      showAddMember.value = false
      window.$message.success(isEditUser.value ? '编辑成功' : '新增成功')
      reset()
      /// 刷新
      handleTheMemberSplice()
    }
  })
}

function reset() {
  Object.assign(memberValues, {
    userName: '',
    organName: '',
    id: null,
    userId: null,
    type: '',
    organId: null,
    role: '',
    topicId: null,
  })
  isEditUser.value = false
}

/// 寻找是否已有这个成员
function findTheUser() {
  const index = _.findIndex(commitParams.rsTopicUserDTOList, (o) => { return o.userId == memberValues.userId })
  return index
}

/// 查询是否已有负责人
function findHaveFzr() {
  const index = _.findIndex(commitParams.rsTopicUserDTOList, (o) => { return o.role == '课题负责人' })

  /// 每次查询到负责人, 都吧负责人附上值
  if (index >= 0)
    commitParams.director = commitParams.rsTopicUserDTOList[index].userName
  else
    commitParams.director = null

  return index >= 0
}

/// / 编辑
function handleEditClick(row) {
  showAddMember.value = true
  isEditUser.value = true
  Object.assign(memberValues, row)
}

function handleDeleteClick(row, index) {
  /// 是否删除
  window.$dialog.warning({
    title: '温馨提示',
    content: '确定删除该成员吗?',
    negativeText: '取消',
    positiveText: '确定',
    positiveButtonProps: {
      type: 'primary',
      ghost: true,
    },
    negativeButtonProps: {
      type: 'primary',
      ghost: true,
    },
    onPositiveClick: () => {
      _.remove(commitParams.rsTopicUserDTOList, (n) => {
        return n?.userId === row?.userId
      })
      handleTheMemberSplice()
      window.$message.success('已删除')
    },

  })
}

function throttle(func, wait) {
  let lastCall = 0

  return function (...args) {
    const now = new Date().getTime()
    if (lastCall && now < lastCall + wait) {
      // 不调用了
    }
    else {
      lastCall = now
      func.apply(this, args)
    }
  }
}

const saveDebounce = throttle(saveClick, 2000)

function saveClick() {
  emit('save')
  /// 校验
  if (commitParams.name.trim().length === 0) {
    window.$message.warning('请填写课题名称')
    return
  }
  if (commitParams.shortName.trim().length === 0) {
    window.$message.warning('请填写课题简称')
    return
  }

  // 课题简称大写字母
  const regex = /^[A-Z]+(-[A-Z]+)*$|^[A-Z]+(-[A-Z]*)*[-]$/
  if (!regex.test(commitParams.shortName)) {
    window.$message.warning('课题简称仅限输入大写字母')
    return
  }
  if (commitParams.type.trim().length === 0) {
    window.$message.warning('请选择课题类别')
    return
  }
  if (!commitParams.approvalTime) {
    window.$message.warning('请选择立项时间')
    return
  }
  // if (rangeTime.value?.length === 0 || !rangeTime.value) {
  //   window.$message.warning('请选择试验周期')
  //   return
  // }
  if (commitParams.sampleNum <= 0 || !commitParams.sampleNum) {
    window.$message.warning('请输入样本量且大于0')
    return
  }

  if (!findHaveFzr()) {
    window.$message.warning('团队需要一个负责人')
    return
  }

  // if (tempSaveGroup.value?.length === 0) {
  //   window.$message.warning('请添加试验分组')
  //   return
  // }

  if (isAdd.value) {
    /// 新增, 那么提交参数
    // commitParams.startTime = `${rangeTime.value[0]} 00:00:00`
    // commitParams.endTime = `${rangeTime.value[1]} 23:59:59`
    addNewTopic({ ...commitParams, attachment: uploadId.value }).then((res) => {
      console.log(res)
      /// 拿到课题 id  还需要保存那个试验分组信息

      if (res.data && groupText.value?.length > 0) {
        postTopicGroups({
          topicId: res?.data,
          groups: groupText.value.split('、'),
        })
      }
      window.$message.success('保存成功')
      router.back()
    })
  }
  else {
    /// 更新
    // commitParams.startTime = `${rangeTime.value[0]}`
    // commitParams.endTime = `${rangeTime.value[1]}`
    // if (rangeTime.value[0].length <= 10)
    //   commitParams.startTime += ' 00:00:00'
    // if (rangeTime.value[1].length <= 10)
    //   commitParams.endTime += ' 23:59:59'
    updateTheTopic({ ...commitParams, id: props.ketiID, attachment: uploadId.value }).then((res) => {
      if (res.data) {
        postTopicGroups({
          topicId: props.ketiID,
          groups: groupText.value.split('、'),
        })
        window.$message.success('修改成功')
        useReseacherStore().getInfo(props.ketiID)
        emit('cancel')
      }
    })
  }
}

function cancelClick() {
  emit('cancel')
  if (!props.ketiID)
    router.back()
}

function testInput() {
  if (commitParams.shortName?.length === 0)
    return
  const regex = /^[A-Z]+(-[A-Z]+)*$|^[A-Z]+(-[A-Z]*)*[-]$/
  if (!regex.test(commitParams.shortName))
    window.$message.warning('课题简称仅限输入大写字母')
}

/// 监听这个团队信息成员
watch(commitParams.rsTopicUserDTOList, () => {
  console.log('xxx')
  pageOptions.total = commitParams.rsTopicUserDTOList.length
}, { immediate: true })

const sortBys = {
  科研人员: 1,
  课题负责人: 0,
}
///  本地分页 团队成员
function handleTheMemberSplice() {
  const oriarr = commitParams?.rsTopicUserDTOList

  const tempArr = _.sortBy(oriarr, (o) => {
    return sortBys[o.role]
  })

  const array = _.chunk(tempArr, pageOptions.size)
  tableDatas.value = array[pageOptions.page - 1]
  console.log(array)
}

watch(() => props.ketiID, () => {
  if (props.ketiID) {
    getTheTopicDetail(props.ketiID).then((res) => {
      Object.assign(commitParams, res?.data)
      rangeTime.value = [commitParams?.startTime, commitParams?.endTime]
      /// 试验分组
      tempSaveGroup.value = res?.data?.groupsInfo || []
      groupText.value = tempSaveGroup.value?.join('、')
      if (tempSaveGroup.value?.length > 0)
        groups.group = tempSaveGroup.value?.map(item => ({ text: item }))

      pageOptions.total = res?.data?.rsTopicUserDTOList?.length || 0
      if (res?.data?.attachment)
        uploadId.value = res?.data?.attachment
      else
        gennerFileId()
      handleTheMemberSplice()
    })
  }
  else {
    gennerFileId()
  }
}, { immediate: true })

function gennerFileId() {
  generateFileId().then((res) => {
    uploadId.value = res?.data || ''
  })
}

function showTheGroupAdd(event) {
  /// 分组维护添加
  showGroupModal.value = true
  if (event)
    event.target.blur()
}
// 删除
function delGroupItem(index) {
  groups.group.splice(index, 1)
}
/// 添加
function addGroupItem() {
  groups.group.push({
    text: null,
  })
}
/// 分组的保存, 不是真保存, 提交课题后再保存
function handleBodyConfirm1() {
  // showGroupModal.value = false
  // 校验
  groupFromRef.value.validate((valid) => {
    if (valid) {
      showGroupModal.value = false
      tempSaveGroup.value = groups.group.map(item => item.text)
      /// 显示内容
      groupText.value = tempSaveGroup.value.join('、')
    }
  })
}

/// 使用新病史
function changeMedicalHistory() {
  /// 这个课题使用新病史, 存到课题里
  if (!commitParams.history) {
    commitParams.history = JSON.stringify(newHistory.questions)
    commitParams.patientHistory = JSON.stringify(newHistory.default)
  }
  else {
    delete commitParams.history
    delete commitParams.patientHistory
  }

  window.$message.success('切换病史')
}
</script>

<template>
  <div flex-col>
    <Breadcrumb
      :bread-list="[
        {
          title: '科研管理',
          link: null,
          key: 'researcher',
        },
        {
          title: '课题管理',
          link: '/',
          key: 'researcher_projectManage',
        },
        {
          title: '新增课题',
          link: null,
          key: 'researcher_projectManage_create',
        },
      ]"
    />
    <PageCard breadcrumb>
      <SectionTitle> 基础信息 </SectionTitle>
      <div py-15px pl-10px>
        <div flex items-center>
          <div w-80px flex items-center justify-end>
            <SvgIcon local-icon="slmc-icon-app_required1" size="14" class="text-#F36969" />
            <div mr-10px text-14px color="#666">
              课题名称
            </div>
          </div>
          <el-input v-model="commitParams.name" show-word-limit style="flex: 1" maxlength="50" placeholder="请输入" />
        </div>
        <div mt-14px flex items-center>
          <div w-80px flex items-center self-start justify-end>
            <div color="#666" mr-10px mt-10px text-14px>
              英文名称
            </div>
          </div>
          <el-input
            v-model="commitParams.englishName" :rows="5" resize="none" type="textarea" show-word-limit
            maxlength="1000" style="flex: 1" placeholder="请输入"
          />
        </div>
        <div mt-14px flex flex-wrap items-center style="gap: 14px 20px">
          <div flex items-center>
            <div w-80px flex items-center justify-end>
              <div color="#666" mr-10px text-14px>
                课题注册号
              </div>
            </div>
            <el-input
              v-model="commitParams.registrationNo" show-word-limit maxlength="50" style="width: 250px"
              placeholder="请输入"
            />
          </div>
          <div flex items-center>
            <div w-80px flex items-center justify-end>
              <SvgIcon local-icon="slmc-icon-app_required1" size="14" class="text-#F36969" />
              <div color="#666" mr-10px text-14px>
                课题类别
              </div>
            </div>
            <el-select v-model="commitParams.type" style="width: 250px" placeholder="请选择">
              <el-option v-for="(item, index) in topicType" :key="index" :label="item" :value="item" />
            </el-select>
          </div>
          <div flex items-center>
            <div w-80px flex items-center justify-end>
              <SvgIcon local-icon="slmc-icon-app_required1" size="14" class="text-#F36969" />
              <div color="#666" mr-10px text-14px>
                课题简称
              </div>
            </div>
            <el-input
              v-model="commitParams.shortName"
              show-word-limit maxlength="10" style="width: 250px" placeholder="请输入"
              @blur="testInput"
            />
          </div>
          <div flex items-center>
            <div w-80px flex items-center justify-end>
              <SvgIcon local-icon="slmc-icon-app_required1" size="14" class="text-#F36969" />
              <div color="#666" mr-10px text-14px>
                立项时间
              </div>
            </div>
            <el-date-picker
              v-model="commitParams.approvalTime" value-format="YYYY-MM-DD" type="date"
              style="width: 250px" placeholder="请输入"
            />
          </div>
          <div flex items-center>
            <div w-80px flex items-center justify-end>
              <!-- <SvgIcon local-icon="slmc-icon-app_required1" size="14" class="text-#F36969" /> -->
              <div color="#666" mr-10px text-14px>
                试验周期
              </div>
            </div>
            <el-date-picker
              v-model="rangeTime"
              disabled unlink-panels type="daterange" value-format="YYYY-MM-DD"
              style="width: 250px" range-separator="－" start-placeholder="开始日期" end-placeholder="结束日期"
            />
          </div>
          <div flex items-center>
            <div w-80px flex items-center justify-end>
              <SvgIcon local-icon="slmc-icon-app_required1" size="14" class="text-#F36969" />
              <div color="#666" mr-10px text-14px>
                样本量
              </div>
            </div>
            <n-input-number v-model:value="commitParams.sampleNum" :show-button="false" type="number" :min="1" :max="10000000" style="width: 250px" placeholder="请输入">
              <template #suffix>
                <span>例</span>
              </template>
            </n-input-number>
          </div>
        </div>
        <!-- <div mt-14px flex items-center>
          <div w-80px flex items-center justify-end>
            <SvgIcon local-icon="slmc-icon-app_required1" size="14" class="text-#F36969" />
            <div color="#666" mr-10px text-14px>
              试验分组
            </div>
          </div>
          <el-input v-model="groupText" style="flex: 1" placeholder="请添加分组" @focus="showTheGroupAdd" />
          <n-button ml-10px type="primary" @click="showTheGroupAdd">
            分组维护
          </n-button>
        </div> -->
        <div mt-14px flex items-center>
          <div w-80px flex items-center self-start justify-end>
            <div color="#666" mr-10px mt-10px text-14px>
              研究标准
            </div>
          </div>
          <el-input
            v-model="commitParams.researchStandard" :rows="5" resize="none" type="textarea" show-word-limit
            maxlength="200" style="flex: 1" placeholder="请输入"
          />
        </div>
        <div mt-14px flex items-center>
          <div w-80px flex items-center self-start justify-end>
            <div color="#666" mr-10px mt-10px text-14px>
              研究方向
            </div>
          </div>
          <el-input
            v-model="commitParams.researchDirection" :rows="5" resize="none" type="textarea" show-word-limit
            maxlength="200" style="flex: 1" placeholder="请输入"
          />
        </div>
        <UploadView :upload-id="uploadId" mt-14px />
      </div>
      <SectionTitle v-longpress="changeMedicalHistory">
        团队信息
      </SectionTitle>
      <div px-14px>
        <n-button mt-14px type="primary" class="mr-10px" @click="showAddMember = true">
          新增成员
        </n-button>
        <el-table stripe :data="tableDatas" mt-14px max-height="400">
          <el-table-column label="序号" width="150" type="index" />
          <el-table-column label="姓名" :formatter="({ userName }) => userName || '-'" width="150" />
          <el-table-column label="角色" :formatter="({ role }) => role || '-'" width="150" />
          <el-table-column label="所属机构" show-overflow-tooltip :formatter="({ organName }) => organName || '-'" />
          <el-table-column label="所属类型" :formatter="({ type }) => type || '-'" />
          <el-table-column label="操作" width="120">
            <template #default="{ row, $index }">
              <div flex items-center gap-5px>
                <span uno-link @click="handleEditClick(row)">编辑</span>
                <div v-if="row?.role !== '课题负责人'" h-14px w-1px bg="#3B8FD9" />
                <span v-if="row?.role !== '课题负责人'" uno-link @click="handleDeleteClick(row, $index)">删除</span>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div flex justify-end>
          <n-pagination
            v-model:page="pageOptions.page" v-model:page-size="pageOptions.size"
            :item-count="pageOptions.total" :page-sizes="[5, 10, 20, 30]" show-size-picker show-quick-jumper mt-14px
            @update:page-size="handleTheMemberSplice" @update:page="handleTheMemberSplice"
          />
        </div>
      </div>
    </PageCard>
    <div h-40px />
    <PageFooter>
      <template #default>
        <n-button type="primary" @click="saveDebounce">
          保存
        </n-button>
        <n-button ml-20px @click="cancelClick">
          取消
        </n-button>
      </template>
    </PageFooter>
    <BasicModal
      :visible="showAddMember" :title="isEditUser ? '编辑成员' : '新增成员'" width="560" :footer-offset="0"
      @ok="handleBodyConfirm" @visible-change="visibleChange"
    >
      <div>
        <el-form
          ref="formEl"
          pr-20px label-width="90" :rules="addRules" label-placement="left" :model="memberValues"
          class="mb-4px mt-24px" require-mark-placement="left"
        >
          <el-form-item prop="userName" label="姓名" ml-20px>
            <el-select
              v-model="memberValues.userName" :disabled="isEditUser" :teleported="false"
              placeholder="请输入" :remote-method="getReseacher" filterable
              remote remote-show-suffix w-380px :loading="loadingOptions.loading" @focus="() => { getReseacher('') }"
            >
              <el-option
                v-for="(item, index) in loadingOptions.data" :key="index" :label="item.userName"
                :value="item.userName" @click="checkUserName(item)"
              />
            </el-select>
          </el-form-item>
          <el-form-item prop="role" label="角色" ml-20px>
            <el-select v-model="memberValues.role" :teleported="false" w-380px placeholder="请选择">
              <el-option v-for="(rol, kindex) in role" :key="kindex" :label="rol.role" :value="rol.role" />
            </el-select>
          </el-form-item>
          <el-form-item label="所属机构" ml-20px>
            <el-select v-model="memberValues.organName" disabled w-380px placeholder="请选择" />
          </el-form-item>
          <el-form-item prop="type" label="机构类型" ml-20px>
            <el-select v-model="memberValues.type" :teleported="false" w-380px placeholder="请选择">
              <el-option
                v-for="(item, index) in [
                  '主研究单位',
                  '协助研究单位',
                ]" :key="index" :label="item" :value="item"
              />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </BasicModal>
    <BasicModal
      :visible="showGroupModal" title="试验分组维护" width="560" :footer-offset="0"
      @ok="handleBodyConfirm1" @cancel="visibleChange1"
    >
      <el-form ref="groupFromRef" class="group-cont" :model="groups" flex-col px-20px py-20px style="max-height:460px;overflow-y: auto;">
        <el-form-item
          v-for="(item, index) in groups.group" :key="index" :rules="{
            required: true,
            message: `请输入分组名称`,
            trigger: ['input', 'blur'],
          }"
          :prop="`group[${index}].text`"
        >
          <div :style="{ visibility: index === 0 ? 'visible' : 'hidden' }">
            试验分组
          </div>
          <el-input v-model="item.text" clearable style="width: 360px;margin-left:10px;" placeholder="请输入分组名称" />
          <div ml-10px flex items-center style="gap:0 10px">
            <img v-if="groups.group.length > 1" h-16px w-16px src="@/assets/images/keyan_group_del.png" @click="delGroupItem(index)">
            <img v-if="index === groups.group?.length - 1" h-16px w-16px src="@/assets/images/keyan_group_add.png" @click="addGroupItem">
          </div>
        </el-form-item>
      </el-form>
    </BasicModal>
  </div>
</template>

<style lang="scss" scoped>
.pageCard {
  flex: 1;
}

.group-cont{
  :deep(.el-form-item__error){
    margin-left: 65px;
    margin-top: 2px;
  }
}
</style>
