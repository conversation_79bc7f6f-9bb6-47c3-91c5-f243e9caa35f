<script setup lang='ts'>
import _ from 'lodash'
import dayjs from 'dayjs'
import { storeToRefs } from 'pinia'
import EditInfo from '../create.vue'
import { ProjectApi, getFilesListWithScaleId, getTheTopicDetail } from '~/src/api/research'
import { useReseacherStore } from '~/src/store'
import fileImage from '@/assets/svg/file_jpg_png.svg'
import fileWord from '@/assets/svg/word.svg'
import filePdf from '@/assets/svg/pdf.svg'
import fileExcel from '@/assets/svg/excel.svg'
import filePpt from '@/assets/svg/ppt.svg'
import fileUnKnow from '@/assets/svg/normal_file.svg'

const props = defineProps({
  topicId: {
    type: String,
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
})

const research = useReseacherStore()
const { topicInfo } = storeToRefs(research)
const route = useRoute()

const isEdit = ref(props.isEdit)
const uploadList = ref([])
const topicGroup = ref()

// const topicInfo = ref()

const tableDatas = ref()

const pageOptions = reactive({
  page: 1,
  size: 10,
  total: 0,
})

function jumpToEdit() {
  /// 编辑课题, 直接跳转到 create
  isEdit.value = true
}
function cancelEditClick() {
  isEdit.value = false
  getDataFromNet()
}

function getFilesList() {
  getFilesListWithScaleId(topicInfo.value?.attachment, 'RS_TOPIC_FILE').then((res) => {
    uploadList.value = res?.data || []
  })
}

function getDataFromNet() {
  if (props.topicId || route.query.topicId) {
    getTheTopicDetail(props.topicId || route.query.topicId).then((res) => {
      topicInfo.value = res?.data
      pageOptions.total = topicInfo.value?.rsTopicUserDTOList?.length || 0
      tableDatas.value = topicInfo.value?.rsTopicUserDTOList || []
      handleTheMemberSplice()
      research.updateTopicInfo(res?.data)
      getFilesList()
    })
  }
  else {
    handleTheMemberSplice()
    getFilesList()
  }
}

const sortBys = {
  科研人员: 1,
  课题负责人: 0,
}

///  本地分页 团队成员
function handleTheMemberSplice() {
  const oriarr = topicInfo.value?.rsTopicUserDTOList

  const tempArr = _.sortBy(oriarr, (o) => {
    return sortBys[o.role]
  })
  const array = _.chunk(tempArr, pageOptions.size)
  tableDatas.value = array[pageOptions.page - 1]
  console.log(array)
}

// function refresh() {
//   getTheTopicDetail(props.topicId || route.query.topicId).then((res) => {
//     topicInfo.value = res?.data
//     pageOptions.total = topicInfo.value?.rsTopicUserDTOList?.length || 0
//     tableDatas.value = topicInfo.value?.rsTopicUserDTOList || []
//     handleTheMemberSplice()
//     research.updateTopicInfo(res?.data)
//   })
// }

function fileIcon(type: string) {
  if (type?.includes('image'))
    return fileImage
  else if (type?.includes('wordprocessingml'))
    return fileWord
  else if (type?.includes('spreadsheetml') || type?.includes('excel'))
    return fileExcel
  else if (type?.includes('pdf'))
    return filePdf
  else if (type?.includes('presentationml'))
    return filePpt
  else return fileUnKnow
}

function downLoad(item) {
  /// 直接预览
  window.open(ProjectApi.previewFile + item.id, '_blank')
}

function getGroupText() {
  return topicInfo.value?.groupsInfo?.length > 0 ? topicInfo.value?.groupsInfo.join('、') : '-'
}

onMounted(() => {
  getDataFromNet()
})
</script>

<template>
  <EditInfo v-if="isEdit" :keti-i-d="topicId" @cancel="cancelEditClick" />
  <div v-else class="page" p-14px>
    <n-button v-if="research.getPower(topicInfo?.director)" type="primary" ghost mb-14px mt-2px @click="jumpToEdit">
      编辑
    </n-button>
    <SectionTitle>
      基础信息
    </SectionTitle>
    <div mt-14px pl-14px text-14px style="line-height: 20px;">
      <div flex items-center>
        <span w-60px text-right color="#666">课题名称:</span>
        <span ml-5px flex-1 color="#333">{{ topicInfo?.name || '-' }}</span>
      </div>
      <div mt-14px flex items-center style="word-break: break-all;">
        <div w-60px self-start text-right color="#666">
          英文名称:
        </div>
        <div ml-5px flex-1 color="#333">
          {{ topicInfo?.englishName || '-' }}
        </div>
      </div>
      <div mt-14px flex items-center>
        <span w-60px text-right color="#666">注册号:</span>
        <span ml-5px flex-1 color="#333">{{ topicInfo?.registrationNo || '-' }}</span>
      </div>
      <div mt-14px style="display: grid;grid-template-columns: 33.33% 33.33% 33.33%;row-gap: 14px;">
        <div w-300px flex items-center>
          <span w-60px text-right color="#666">课题类别:</span>
          <span ml-5px flex-1 color="#333">{{ topicInfo?.type || '-' }}</span>
        </div>
        <div w-300px flex items-center>
          <span w-60px text-right color="#666">课题简称:</span>
          <span ml-5px flex-1 color="#333">{{ topicInfo?.shortName || '-' }}</span>
        </div>
        <div w-300px flex items-center>
          <span w-60px text-right color="#666">立项时间:</span>
          <span ml-5px flex-1 color="#333">{{ topicInfo?.approvalTime || '-' }}</span>
        </div>
        <div w-300px flex items-center>
          <span w-60px text-right color="#666">试验周期:</span>
          <span ml-5px flex-1 color="#333">{{ topicInfo?.startTime ? `${dayjs(topicInfo?.startTime).format("YYYY-MM-DD")}～${dayjs(topicInfo?.endTime).format("YYYY-MM-DD")}` : '-' }}</span>
        </div>
        <div flex items-center>
          <span w-60px text-right color="#666">样本量:</span>
          <span ml-5px flex-1 color="#333">{{ `${topicInfo?.sampleNum || 0}例` }}</span>
        </div>
      </div>
      <div mt-14px flex items-center>
        <span w-60px self-start text-right color="#666">试验分组:</span>
        <span ml-5px flex-1 color="#333">{{ getGroupText() }}</span>
      </div>
      <div mt-14px flex items-center>
        <span w-60px self-start text-right color="#666">研究标准:</span>
        <span ml-5px flex-1 color="#333">{{ topicInfo?.researchStandard || '-' }}</span>
      </div>
      <div mt-14px flex items-center>
        <span w-60px self-start text-right color="#666">研究方向:</span>
        <span ml-5px flex-1 color="#333">{{ topicInfo?.researchDirection || '-' }}</span>
      </div>
      <div mt-14px flex items-center>
        <div w-60px self-start text-right color="#666">
          附件:
        </div>
        <div
          v-if="uploadList?.length > 0"
          class="bg-#f5f5f5"
          style="gap:14px 100px"
          ml-4px flex flex-1 flex-wrap p-14px
        >
          <div
            v-for="(item, index) in uploadList"
            :key="index"
            class="upload-item"
          >
            <img w-30px :src="fileIcon(item.contentType)">
            <div
              ml-10px style="overflow: hidden;
    text-overflow: ellipsis;
    line-height: 22px;
    max-height: 44px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;"
            >
              {{ item.fileName || item.name || "未知名称" }}
            </div>
            <img
              ml-10px
              w-16px
              src="@/assets/svg-icon/slmc_preview_file.svg"
              @click="downLoad(item)"
            >
          </div>
        </div>
        <div v-else ml-5px>
          -
        </div>
      </div>
    </div>
    <SectionTitle mt-14px>
      团队信息
    </SectionTitle>
    <div px-14px>
      <el-table stripe :data="tableDatas" mt-14px max-height="400">
        <el-table-column label="序号" width="150" type="index" />
        <el-table-column label="姓名" :formatter="({ userName }) => userName || '-'" width="150" />
        <el-table-column label="角色" :formatter="({ role }) => role || '-'" width="150" />
        <el-table-column label="所属机构" show-overflow-tooltip :formatter="({ organName }) => organName || '-'" />
        <el-table-column label="所属类型" :formatter="({ type }) => type || '-'" width="200" />
      </el-table>
      <div flex justify-end>
        <n-pagination
          v-model:page="pageOptions.page" v-model:page-size="pageOptions.size"
          :item-count="pageOptions.total" :page-sizes="[5, 10, 20, 30]" show-size-picker show-quick-jumper mt-14px
          @update:page-size="handleTheMemberSplice" @update:page="handleTheMemberSplice"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
:deep(.pageCard) {
  padding: 0;
}

:deep(.breadcrumb) {
  display: none;
}
.upload-item {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #333333;
    line-height: 20px;
}
</style>
