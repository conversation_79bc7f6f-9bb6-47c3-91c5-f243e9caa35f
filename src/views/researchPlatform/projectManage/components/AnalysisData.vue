<script lang="ts" setup>
import * as echarts from 'echarts/core'
import { storeToRefs } from 'pinia'
import ChartEventAndSex from './ChartEventAndSex.vue'
import { exportThePatientData, getExamGroup, getExaminees, getQuitCount, getSubjectList } from '@/api/research'
import { useAuthStore } from '~/src/store'

const route = useRoute()
const { userInfo } = storeToRefs(useAuthStore())
const fullscreenLoading = ref(false)
const topicId = route.query?.topicId as string
const colors = ['#5B96FD', '#68D0D4', '#FFC569', '#9D96F5', '#24BEE8', '#9EC97F', '#FF9B54', '#3AC9A8', '#E690D1', '#A5B8D1']
const pageForm = ref({
  page: 1,
  size: 10,
  total: 0,
})
const tableData = ref()
const groupData = ref()
const sourceData = ref()
const quitCount = ref()
// 获取受试者人数
function getExamineesData() {
  getExaminees(topicId).then((res) => {
    sourceData.value = res.data || []
    nextTick(() => {
      initSourceCharts()
    })
  })

  getExamGroup(topicId).then((res) => {
    groupData.value = res.data
    groupData.value.sort((a, b) => Number(b.total) - Number(a.total))
    let max = 12
    for (let index = 0; index < groupData.value.length; index++) {
      const element = groupData.value[index]

      if (index > 0) {
        const last = groupData.value[index - 1]
        if (last.total > element.total)
          max = max - 1
      }
      element.list = Array.from({ length: max }).fill(1)
    }
  })

  getQuitCount(topicId).then((res) => {
    quitCount.value = res.data
  })
}

function getSubjectListData() {
  const params = {
    topicId, // 课题 id,
    start: pageForm.value.page,
    size: pageForm.value.size,
  }
  getSubjectList(params).then((res: any) => {
    tableData.value = res.data?.records
    pageForm.value.total = Number(res.data?.total)
  })
}

function statusChange(status: number) {
  switch (status) {
    case 0:
      return ['待开始', '#45A8E6']
    case 1:
      return ['进行中', '#FF9B54']
    case 2:
      return ['已完成', '#4ACFB1']
    case 3:
      return ['已中止', '#CCCCCC']
    default:
      return '#45A8E6'
  }
}

function initSourceCharts() {
  type EChartsOption = echarts.EChartsCoreOption
  const chartDom = document.getElementById('sourceCharts')!
  const myChart = echarts.init(chartDom)

  /// 组装 data
  const data = sourceData.value.map((item) => {
    return { value: item.total, name: item.name, scale: item.scale }
  })

  const option: EChartsOption = {
    tooltip: {
      backgroundColor: 'rgba(0,0,0,0.7)',
      textStyle: {
        fontSize: '12px',
        color: 'white',
      },
      trigger: 'item',
      padding: 0,
      axisPointer: {
        type: 'line',
        show: false,
      },
      formatter(params) {
        console.log('显示====', params.data)
        return `<div p-14px>
            <div >来源：${params.data.name}</div>
            <div mt-8px>人数：${params.data.value}</div>
            <div mt-8px>占比：${params.data.scale}</div>
          </div>`
      },
      borderColor: 'rgba(0,0,0,0.7)',
    },
    color: colors,
    series: [
      {
        type: 'pie',
        radius: ['50%', '80%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 2,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        labelLine: {
          show: false,
        },
        data,
      },
    ],
  }

  option && myChart.setOption(option)
}

function exportTheData() {
  fullscreenLoading.value = true
  exportThePatientData(route.query?.topicId, userInfo.value?.id).then((res) => {
    // console.log(res)
    fullscreenLoading.value = false
    const url = window.URL.createObjectURL(res?.data)
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', res?.data?.fileName)
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    window.$message.success('导出成功,请查看下载的文件')
  }).catch((_) => {
    fullscreenLoading.value = false
  })
}

onMounted(() => {
  getExamineesData()
  getSubjectListData()
})
</script>

<template>
  <div class="bg-content">
    <div>
      <div flex>
        <SectionTitle flex-1>
          数据概览
        </SectionTitle>
        <div flex>
          <img src="@/assets/images/icon_export.png" alt="" mr-10px h-16px w-16px>
          <span color="#3B8FD9">导出分析报告</span>
        </div>
      </div>
      <div mt-14px flex>
        <div mr-14px>
          <div style="background-color: #F6F8FA;" mb-14px h-200px w-400px px-14px py-10px>
            <div class="slotName">
              受试者数（人）
            </div>
            <div class="line" mb-14px />
            <div v-if="!sourceData || sourceData.length === 0" ml-130px mt-70px flex>
              <img w-16px src="@/assets/images/information.svg" alt="">
              <div color="#999" ml-10px>
                暂无受试者
              </div>
            </div>
            <div v-else flex>
              <div w-172px>
                <div color="#455F75" mb-14px text-24px>
                  {{ tableData ? tableData.length : 0 }}
                </div>
                <div v-for="(i, k) in sourceData" :key="k" mb-8px flex>
                  <div :style="{ background: `${colors[k]}` }" mr-5px h-12px w-12px />
                  <div color="#666" text-12px>
                    {{ i.name }}
                  </div>
                </div>
              </div>
              <div id="sourceCharts" h-150px w-200px />
            </div>
          </div>

          <div h-200px w-400px style="background-color: #F6F8FA;" px-14px py-10px>
            <div class="slotName">
              累计退出数（人）
            </div>
            <div class="line" mb-14px />
            <div color="#455F75" mt-20px w-full text-center text-24px>
              {{ quitCount }}
            </div>
            <img ml-98px mt-18px src="@/assets/images/icon_group.png" h-87px w-183px style="fill: #FF9B54;">
          </div>
        </div>

        <div felx-1 h-414px w-full style="background-color: #F6F8FA;" px-14px py-10px>
          <div class="slotName">
            分组情况（人）
          </div>
          <div class="line" />
          <div v-if="!groupData || groupData.length === 0" h-full w-full flex items-center justify-center>
            <img w-16px src="@/assets/images/information.svg" alt="">
            <div color="#999" ml-10px>
              暂无分组数据
            </div>
          </div>
          <div v-else style="max-height: 380px;overflow: auto;">
            <n-space>
              <div v-for="(item, index) in groupData" :key="index" mt-22px h-68px w-470px flex style="border-bottom: 1px dashed #CCCCCC;;">
                <div w-150px>
                  <div color="#666" text-14px>
                    {{ item.name && item.name.length > 0 ? item.name : '待分组' }}:
                  </div>
                  <div color="#455F75" mt-10px text-24px>
                    {{ item.total }}
                  </div>
                </div>
                <div v-for="i in item.list" :key="i" mr-5px class="i-custom:svg-icons-people" h-44px :style="{ color: colors[index] }" />
              </div>
            </n-space>
          </div>
        </div>
      </div>
    </div>

    <div mb-14px>
      <div mt-14px flex>
        <SectionTitle flex-1>
          详细数据
        </SectionTitle>
        <div v-loading.fullscreen.lock="fullscreenLoading" element-loading-text="正在导出..." flex @click="exportTheData">
          <img src="@/assets/images/icon_export.png" alt="" mr-10px h-16px w-16px>
          <span color="#3B8FD9">导出数据</span>
        </div>
      </div>
      <el-table :data="tableData" mt-14px>
        <el-table-column label="序号" type="index" width="55" />
        <el-table-column label="受试者" :formatter="({ name }) => name || '-'" />
        <el-table-column label="试验状态">
          <template #default="{ row }">
            <div flex>
              <div mr-5px mt-8px h-6px w-6px rounded :style="{ backgroundColor: statusChange(row.status)[1] }" />
              <span>{{ statusChange(row.status)[0] }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="入组时间" :formatter="({ testStartTime }) => testStartTime || '-'" width="100">
          <template #default="{ row }">
            <div>
              {{ row.testStartTime ? row.testStartTime.substring(0, 10) : '-' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="受试者观察号" :formatter="({ examNo }) => examNo || '-'" width="170" />
        <el-table-column label="当前试验阶段" min-width="110" :formatter="({ currentExamStageName }) => currentExamStageName || '-'" />
        <el-table-column label="数据完成率">
          <template #default="{ row }">
            <span color="#06AEA6">{{ row.dataRate ? `${(row.dataRate * 100).toFixed(0)}%` : '0%' }}</span>
          </template>
        </el-table-column>
        <el-table-column label="试验分组" :formatter="({ examGroup }) => examGroup || '-'" width="170" />
        <el-table-column label="24周疗效评估" :formatter="({ efficacyEvaluation }) => efficacyEvaluation || '-'" width="120" />
        <el-table-column label="受试者来源" min-width="210" :formatter="({ patientSource }) => patientSource || '-'" />
        <el-table-column label="研究员" :formatter="({ researcher }) => researcher || '-'" />
        <template #empty>
          <div flex justify-center>
            <DataEmpty />
          </div>
        </template>
      </el-table>
      <div v-if="pageForm.total > 0" mt-20px w-full flex justify-end>
        <n-pagination
          v-model:page="pageForm.page"
          v-model:page-size="pageForm.size"
          :item-count="pageForm.total"
          :page-sizes="[5, 10, 20, 30]"
          show-size-picker
          show-quick-jumper
          @update:page="getSubjectListData"
          @update:page-size="() => {
            pageForm.page = 1
            getSubjectListData()
          }"
        />
      </div>
    </div>
    <ChartEventAndSex mt-24px />
  </div>
</template>

<style scoped>
:deep(.n-space){
  gap: 8px 60px !important;
}

.bg-content {
  padding: 14px;

  .slotName {
    color: #333;
    font-size: 14px;
  }
  .line {
    width: 100%;
    background: #ccc;
    height: 1px;
    margin-top: 10px;
  }
  /* .svgImage{
    transform: translateX(100px);
     filter: drop-shadow(10px 0px 0px #558ABB);
  } */
}
</style>
