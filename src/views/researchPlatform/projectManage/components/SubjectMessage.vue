<script setup lang="ts">
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import { storeToRefs } from 'pinia'
import AddSubject from '../subject/addSubject.vue'
import MedicalHistory from './MedicalHistory.vue'
import MedicalCustomHistory from './MedicalCustomHistory.vue'
import { ProjectApi, getFilesListWithScaleId, getSubjectDetail, getSubjectEditLog } from '@/api/research'
import fileImage from '@/assets/svg/file_jpg_png.svg'
import fileWord from '@/assets/svg/word.svg'
import filePdf from '@/assets/svg/pdf.svg'
import fileExcel from '@/assets/svg/excel.svg'
import filePpt from '@/assets/svg/ppt.svg'
import fileUnKnow from '@/assets/svg/normal_file.svg'
import { useReseacherStore } from '~/src/store/modules/reseacher'

dayjs.extend(utc)
dayjs.extend(timezone)
const reseacherStore = useReseacherStore()
const { topicInfo } = storeToRefs(reseacherStore)
const route = useRoute()
const edit = ref(false)
const medicalType = ref('0')
const medical = reactive({
  question1: '',
  question2: '',
  question3: '',
  question4: '',
  question5: '',
  question6: '',
  question7: '',
  question8: '',
  question9: '',
  question10: '',
  question11: '',
})
const drink = {
  isDrink: '',
  type: null,
  startTime: '',
  dayDrink: '',
  weekDrink: '',
  isOver: '',
}
const smoke = {
  isSmoke: '',
  startTime: null,
  daySmoke: '',
  isOver: '',
}
const subject = ref({
  idCardType: '',
  medicalHistory: medical,
  drink,
  smoke,
})
const editLog = ref()
const logTimes = ref()
const messageTitle = [{ name: '姓名', key: 'name' },
  { name: '性别', key: 'sexName' },
  { name: '证件类型', key: 'idCardType' },
  { name: '证件号码', key: 'idCardNum' },
  { name: '出生时间', key: 'birthday' },
  { name: '年龄', key: 'age' },
  { name: '国籍', key: 'countryName' },
  { name: '民族', key: 'nationName' },
  { name: '手机号', key: 'phone' },
  { name: '紧急联系人', key: 'contactsName' },
  { name: '紧急联系人电话', key: 'contactsTel' },
  { name: '婚姻状况', key: 'marriageName' },
  { name: '家庭成员', key: 'familyMembers' },
  { name: '家庭住址', key: 'liveAddr' }]

const subjectTitle = [{ name: '受试者来源', key: 'patientSource' },
  { name: '受试者观察号', key: 'examNo' },
  { name: '研究员', key: 'researcher' },
  { name: '登记员', key: 'register' },
  { name: '入组时间', key: 'testStartTime', placeholder: '第一针干扰素时间' },
  { name: '试验分组', key: 'examGroup' },
  { name: '24周疗效评估', key: 'efficacyEvaluation' }]
const uploadList = ref()

function getSubjectDetailData(isedit) {
  getSubjectDetail(route.query?.id as string).then((res: any) => {
    const json = res.data?.personalHistory ? JSON.parse(res.data?.personalHistory) : {}
    const history = JSON.parse(res.data?.diseaseHistory || '{}')
    subject.value = {
      ...(res.data || {}),
      medicalHistory: res.data?.diseaseHistory ? history : medical,
      drink: json.drink || drink,
      smoke: json.smoke || smoke,
    }
    console.log(subject.value)
    edit.value = isedit
    if (subject.value.idCardType === '01')
      subject.value.idCardType = '身份证'
    if (subject.value?.informedConsent)
      getFileWithNodeId(subject.value?.informedConsent)

    /// 查看当前使用病史
    if (topicInfo.value?.history)
      medicalType.value = '1'
    else
      medicalType.value = '0'

    /// 病史使用
    if (history.question101 && history.question101?.length > 0)
      medicalType.value = '1'
  })

  getSubjectEditLog(route.query?.id as string).then((res: any) => {
    const timeList = [...new Set(res.data?.map(item => dayjs(item.createTime).tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss')))]
    logTimes.value = timeList
    // timeList.sort((a: any, b: any) => {
    //   const dateA = new Date(a)
    //   const dateB = new Date(b)
    //   return dateA - dateB
    // })
    const logTemp = {}
    res.data.forEach((element: any) => {
      const time = dayjs(element.createTime).tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss')
      /// 处理格式不对的数据
      if (element.beforeData === '[\"以上均无\"]')
        element.beforeData = '以上均无'
      if (element.beforeData === 'true')
        element.beforeData = '是'
      if (element.beforeData === 'false')
        element.beforeData = '否'
      if (element.afterData === '[\"以上均无\"]')
        element.afterData = '以上均无'
      if (element.afterData === 'true')
        element.afterData = '是'
      if (element.afterData === 'false')
        element.afterData = '否'

      if (logTemp[time])
        logTemp[time] = [...logTemp[time], element]
      else
        logTemp[time] = [element]
    })
    editLog.value = logTemp
    console.log('时间啊=======', logTimes.value, editLog.value)
  })
}

function fileIcon(type: string) {
  if (type?.includes('image'))
    return fileImage
  else if (type?.includes('wordprocessingml'))
    return fileWord
  else if (type?.includes('spreadsheetml') || type?.includes('excel'))
    return fileExcel
  else if (type?.includes('pdf'))
    return filePdf
  else if (type?.includes('presentationml'))
    return filePpt
  else return fileUnKnow
}

function dayjsDiffTimeSB(start, end?: any) {
  if (end == null)
    end = dayjs()
  else end = dayjs(end)
  const diffMon = end.diff(dayjs(start), 'month')
  const year = Math.trunc(diffMon / 12)
  const month = diffMon % 12

  return `${year === 0 ? '' : `${year}年`}${
        month === 0 ? '' : `${month}个月`
    }`
}

function personalString() {
  let str = ''
  const drink = subject.value.drink
  const smoke = subject.value.smoke
  if (drink) {
    str = drink.isDrink === '是'
      ? `饮${
      drink.type ? drink.type : '酒'
                }${dayjsDiffTimeSB(drink.startTime)}${
                  Number(drink?.dayDrink || 0) > 0
                        ? `，平均每日乙醇摄入${drink.dayDrink}g，平均每周乙醇摄入量${drink.weekDrink}g，`
                        : ''
                }${
                 drink.isOver ? drink.isOver === 'true' ? '已戒' : '未戒' : ''
                };\n`
      : '不饮酒\n'
  }
  if (smoke) {
    str += smoke.isSmoke === '是'
      ? `吸烟${dayjsDiffTimeSB(smoke.startTime)}年,${smoke.daySmoke ? `平均每日${smoke.daySmoke}支` : ''}${
        smoke.isOver ? smoke.isOver === 'true' ? '已戒' : '未戒' : ''
                };`
      : '不吸烟'
  }

  return str.length > 0 ? str : '-'
}

function updateSubject() {
  edit.value = false
  getSubjectDetailData(false)
}

function caculatorWeekDrinkWaring() {
  if (subject.value.sexName === '男' && Number(subject.value.drink.weekDrink) >= 210)
    return 'warning'
  else if (subject.value.sexName === '女' && Number(subject.value.drink.weekDrink) >= 140)
    return 'warning'

  return 'success'
}

function downLoad(item) {
  /// 直接预览
  window.open(ProjectApi.previewFile + item.id, '_blank')
  /// 如果是 office 用在线文档预览
  /// https://view.officeapps.live.com/op/view.aspx?src=${url}
}

function getFileWithNodeId(id) {
  getFilesListWithScaleId(id, 'RS_INFORMED_CONSENT').then((res) => {
    console.log('上传的文件=====', res)
    uploadList.value = res?.data?.reverse()
  })
}

onMounted(() => {
  const str = route.query?.type as string
  getSubjectDetailData(str === 'edit')
})
</script>

<template>
  <div v-if="edit">
    <AddSubject :add-status="false" :subject="subject" @cancle-edit="updateSubject" />
  </div>

  <div v-else class="bg-content">
    <div>
      <n-button v-if="subject.status === 0 || subject.status === 1" color="#06AEA6" ghost mb-20px @click="edit = true">
        编辑
      </n-button>

      <div>
        <SectionTitle>
          基础信息
        </SectionTitle>

        <div mt-20px>
          <n-grid :x-gap="12" :y-gap="20" :cols="3">
            <n-grid-item v-for="item in messageTitle" :key="item.name">
              <div flex>
                <div w-120px text-right>
                  {{ item.name }}:
                </div>
                <div ml-10px>
                  {{ subject[item.key] || '-' }}
                </div>
              </div>
            </n-grid-item>
          </n-grid>
        </div>
      </div>

      <div mt-20px>
        <SectionTitle>
          试验信息
        </SectionTitle>

        <div mt-20px>
          <n-grid :x-gap="12" :y-gap="20" :cols="3">
            <n-grid-item v-for="item in subjectTitle" :key="item.name">
              <div flex>
                <div w-120px text-right>
                  {{ item.name }}:
                </div>
                <div ml-10px>
                  {{ item.name === '入组时间' ? (subject[item.key] ? subject[item.key].split(' ')[0] : '-') : subject[item.key] || '-' }}
                </div>
              </div>
            </n-grid-item>
          </n-grid>
          <div ml-10px mt-20px flex>
            <div w-120px text-right>
              知情同意书:
            </div>
            <div
              v-if="uploadList?.length > 0"
              class="bg-#f5f5f5"
              style="gap:14px 100px"
              ml-4px flex flex-1 flex-wrap p-14px
            >
              <div
                v-for="(item, index) in uploadList"
                :key="index"
                class="upload-item"
              >
                <img w-30px :src="fileIcon(item.contentType)">
                <div
                  ml-10px style="overflow: hidden;
                  text-overflow: ellipsis;
                  line-height: 22px;
                  max-height: 44px;
                  display: -webkit-box;
                  -webkit-line-clamp: 2;
                  -webkit-box-orient: vertical;"
                >
                  {{ item.fileName || item.name || "未知名称" }}
                </div>
                <img
                  ml-10px
                  w-16px
                  src="@/assets/svg-icon/slmc_preview_file.svg"
                  @click="downLoad(item)"
                >
              </div>
            </div>
            <div v-else ml-5px>
              -
            </div>
          </div>
        </div>

        <div v-if="subject.medicalHistory" mt-20px>
          <SectionTitle>
            病史信息
          </SectionTitle>
          <MedicalHistory v-if="medicalType === '0'" :form-value="subject" :edit="false" />
          <MedicalCustomHistory v-if="medicalType === '1'" v-model="subject.medicalHistory" :edit="false" />
        </div>

        <div mt-20px>
          <div class="pageHeader">
            <span class="pageHeader-section bg-primary" />
            <div mr-10px flex class="pl-10px">
              个人史
            </div>
            <div
              v-if="caculatorWeekDrinkWaring() === 'warning'" h-32px w-320px flex line-height-32px
            >
              <img class="add-img-hover" src="@/assets/images/icon-hint.png" mr-8px mt-8px h-16px w-16px>
              <span color="#666">该受试者有酗酒习惯，不符合试验纳入标准</span>
            </div>
          </div>

          <div mt-20px flex-1 style="background-color: #F5F5F5;white-space: pre-wrap;" p-10px>
            {{ personalString() }}
          </div>
        </div>

        <div mr-28px mt-20px>
          <SectionTitle mb-14px>
            修改日志
          </SectionTitle>

          <n-steps size="small" vertical mr-14px w-full flex-1>
            <n-step v-for="(item, index) in logTimes" :key="index" w-full flex-1>
              <template #icon>
                <div h-10px w-10px style="background: #06AEA6;border-radius: 5px" />
              </template>
              <div style="line-height: 22px; font-size: 14px; font-weight: 500;" color="#333" w-full text-left>
                {{ item ? dayjs(item).tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss') : '-' }}
              </div>
              <div style="background-color: #F5F5F5;" w-full flex-1 p-14px>
                <div v-for="(log, i) in editLog[item] || []" :key="i" text-left line-height-20px color="#333">
                  {{ `【${log.userName}】将【${log.modifyField}】从【${log.beforeData}】修改为【${log.afterData}】` }}
                </div>
              </div>
            </n-step>
          </n-steps>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
/* .n-form-item .n-form-item-feedback-wrapper {
  min-height: 0 !important
} */
:deep(.n-steps .n-step-description) {
  width: 100% !important;
}
:deep(.n-steps .n-step-indicator .n-step-indicator-slot .n-step-indicator-slot__index) {
  background: #06AEA6 !important;
  border-color: #06AEA6 !important;
  box-shadow: 0 0 0 1px #06AEA6 !important;
  border-radius: 50%;
}
:deep(.n-steps .n-step-indicator) {
  background: #06AEA6 !important;
  box-shadow: 0 0 0 1px #06AEA6 !important;
  border-radius: 50% !important;
}
:deep(.n-steps.n-steps--vertical > .n-step > .n-step-head > .n-step-splitor) {
  background: #06AEA6 !important;
  border-radius: 50% !important;

}

.bg-content{
  padding: 0 14px 14px;
  max-height: calc(100vh - 250px);
  overflow: auto;

  .pageHeader {
    display: flex;
    align-items: center;
    justify-content: flex-start;

    &-section {
        width: 4px;
        height: 16px;
    }
  }
  .upload-item {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #333333;
    line-height: 20px;
}
}
</style>
