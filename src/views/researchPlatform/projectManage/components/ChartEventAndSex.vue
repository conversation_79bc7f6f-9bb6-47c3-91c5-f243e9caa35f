<script setup lang="ts">
import * as echarts from 'echarts/core'
import { storeToRefs } from 'pinia'
import {
  adverseEventAnalysis,
  efficiencyEvaluationApi,
  patientSexAgeApi,
  patientSourceFromApi,
} from '~/src/api/research'
import { useReseacherStore } from '~/src/store'
import Empty from '~/src/views/dataScreen/wane/Empty.vue'

const { topicInfo } = storeToRefs(useReseacherStore())
const route = useRoute()
const topicId = route.query?.topicId as string
const currentAgeGroup = reactive({
  target: null,
  groups: [],
  label: '',
})
const currentAdverseEvent = ref()
const currentAdverseTab = ref('pie')
const currentAdveseEventTotal = ref(0)
const currentAdverseTitle = ref()
const allZero = ref(false)
const adverseData = ref()
const ageGroup = reactive({
  age1: {
    label: '0-12岁',
  },
  age2: {
    label: '13-18岁',
  },
  age3: {
    label: '19-28岁',
  },
  age4: {
    label: '29-38岁',
  },
  age5: {
    label: '39-48岁',
  },
  age6: {
    label: '49-58岁',
  },
  age7: {
    label: '59-68岁',
  },
  age8: {
    label: '69-78岁',
  },
  age9: {
    label: '79-88岁',
  },
})

let lxChart: any = null
let lyChart: any = null
let blsjChart: any = null

function nameTitle(name) {
  switch (name) {
    case '1级':
      return '1级：轻度不良事件           '
    case '2级':
      return '2级：中度不良事件     '
    case '3级':
      return '3级：严重不良事件     '
    case '4级':
      return '4级：危及生命的不良事件 '
    case '5级':
      return '5级：死亡'
    default:
      break
  }
}

const blsjOption = reactive({
  grid: {
    top: 0,
    bottom: 0,
    containLabel: true,
  },
  tooltip: {
    trigger: 'item',
    formatter: '点击查看详情分布',
  },
  color: ['#5B96FD', '#9EC97F', '#9D96F5', '#FFC569', '#FA8383'],
  legend: {
    orient: 'horizontal',
    left: 'left',
    align: 'left',
    itemWidth: 12,
    itemGap: 14,
    itemHeight: 12,
    formatter(name) {
      return nameTitle(name)
    },
  },
  series: [
    {
      type: 'pie',
      radius: ['35%', '65%'],
      center: ['50%', '58%'],
      itemStyle: {
        borderColor: '#fff',
        borderWidth: 2,
      },
      labelLine: {
        show: true,
      },
      label: {
        formatter: '{b}: {c}例',
        // {b} -> 数据项名称
        // {c} -> 数据项值
        // {d} -> 数据项所占比例
      },
    },
  ],
})

const lyOption = reactive({
  grid: {
    top: 36,
    left: 14,
    right: 20,
    bottom: 0,
    containLabel: true,
  },
  legend: {
    show: true,
    right: 14,
    top: -3,
    itemWidth: 14,
    itemHeight: 14,
  },
  color: ['#5B96FD', '#9EC97F', '#9D96F5', '#FFC569', '#FA8383'],
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
  },
  xAxis: {
    type: 'category',
    data: [],
    axisPointer: {
      type: 'shadow',
    },
    axisLabel: {
      color: '#999',
      rotate: 45,
    },
    axisLine: {
      lineStyle: {
        color: '#ccc', // 设置Y轴轴线的颜色
      },
    },
  },
  yAxis: {
    max(value) {
      return Math.ceil(value.max * 1.1) // 设置 Y 轴的最大值为数据最大值的 110%
    },
    axisLine: {
      lineStyle: {
        color: '#ccc', // 设置Y轴轴线的颜色
      },
    },
    splitLine: {
      lineStyle: {
        color: '#E8E8E8', // 设置Y轴分割线的颜色
      },
    },
    type: 'value',
    min: 0,
    interval: 1,
    axisLabel: {
      formatter: '{value} 人',
      color: '#999',
    },
  },
  series: [],
})

const lxOption = reactive({
  grid: {
    top: 36,
    left: 14,
    right: 20,
    bottom: 14,
    containLabel: true,
  },
  legend: {
    show: true,
    right: 14,
    top: -3,
    itemWidth: 14,
    itemHeight: 14,
  },
  color: ['#5B96FD', '#9EC97F', '#9D96F5', '#FFC569', '#FA8383'],
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
  },
  xAxis: {
    type: 'category',
    data: ['有效', '部分有效', '无效'],
    axisPointer: {
      type: 'shadow',
    },
    axisLabel: {
      color: '#999',
    },
    axisLine: {
      lineStyle: {
        color: '#ccc', // 设置Y轴轴线的颜色
      },
    },
  },
  yAxis: {
    max(value) {
      return Math.ceil(value.max * 1.1) // 设置 Y 轴的最大值为数据最大值的 110%
    },
    axisLine: {
      lineStyle: {
        color: '#ccc', // 设置Y轴轴线的颜色
      },
    },
    splitLine: {
      lineStyle: {
        color: '#E8E8E8', // 设置Y轴分割线的颜色
      },
    },
    type: 'value',
    min: 0,
    interval: 1,
    axisLabel: {
      formatter: '{value} 人',
      color: '#999',
    },
  },
  series: [],
})

function getEfficiencyData() {
  efficiencyEvaluationApi(topicId || '').then((res) => {
    console.log(res)
    const zz = {}
    /// 有效
    res?.data?.effect1.forEach((el) => {
      if (zz[el.name])
        zz[el.name].push(el.total)
      else zz[el.name] = [el.total]
    })
    /// 部分有效
    res?.data?.effect2.forEach((el) => {
      if (zz[el.name])
        zz[el.name].push(el.total)
      else zz[el.name] = [null, el.total]
    })
    /// 无效
    res?.data?.effect3.forEach((el) => {
      if (zz[el.name])
        zz[el.name].push(el.total)
      else zz[el.name] = [null, null, el.total]
    })

    /// 在处理下
    for (const key in zz) {
      const element = zz[key]
      if (element.length < Object.keys(zz).length) {
        /// 补null
        for (let index = 0; index <= Object.keys(zz).length - element.length; index++)
          element.push(null)
      }
    }

    /// 赋值
    const tempSeries: any = []
    for (const key in zz) {
      if (Object.prototype.hasOwnProperty.call(zz, key)) {
        const element = zz[key]
        tempSeries.push({
          name: key,
          type: 'bar',
          barWidth: 30,
          label: {
            show: true, // 显示数值
            position: 'top', // 数值显示在柱子内部
            formatter: '{c}', // 显示数值本身
            color: '#333', // 数值颜色
          },
          tooltip: {
            valueFormatter(value) {
              return `${value ?? 0}人`
            },
          },
          data: element,
        })
      }
    }
    lxOption.series = tempSeries
    lxChart.setOption(lxOption)
  })
}

/// 受试者来源分布
function getPatientSourceFrom() {
  patientSourceFromApi(topicId || '').then((res) => {
    console.log(res)
    const xData: any = []
    const tempSeries: any = []
    for (const key in res?.data) {
      const element = res?.data[key]
      xData.push(key)
      element.forEach((item) => {
        tempSeries.push({
          name: item.name,
          type: 'bar',
          barWidth: 16,
          label: {
            show: true, // 显示数值
            position: 'top', // 数值显示在柱子内部
            formatter: '{c}', // 显示数值本身
            color: '#333', // 数值颜色
          },
          tooltip: {
            valueFormatter(value) {
              return `${value}人`
            },
          },
          data: item.total,
        })
      })
    }
    lyOption.xAxis.data = xData
    lyOption.series = tempSeries
    lyChart.setOption(lyOption)
  })
}

/// 男女性别分布
function getSexAndAgeDataFrom() {
  patientSexAgeApi(topicId || '').then((res) => {
    console.log(res)
    for (const key in res?.data) {
      const element = res?.data[key]
      ageGroup[key].examGroups = element.examGroups
      ageGroup[key].mas = element.mas
      ageGroup[key].ms = element.ms
      ageGroup[key].total = Number(element.ms) + Number(element.mas)
    }

    /// 默认选中第一个
    nextTick(() => {
      const elements = document.getElementsByClassName('age-sex-item')
      selectTheAgeItem(ageGroup.age1, { currentTarget: elements[0] })
    })
  })
}

/// 不良事件分析
function getAdverseEventDataFrom() {
  adverseEventAnalysis(topicId || '').then((res) => {
    console.log(res)
    adverseData.value = res?.data
    const tempSeries: any = []
    const adTemp = ['one', 'two', 'three', 'four', 'five']
    let tempAdd = 0
    adTemp.forEach((item, index) => {
      tempSeries.push({
        value: res?.data[item] || '0',
        labelLine: { show: !!res?.data[item] },
        label: { show: !!res?.data[item] },
        name: `${index + 1}级`,
        other: `${item}_info`,
      })
      tempAdd += (Number(res?.data[item]) || 0)
    })

    if (tempAdd === 0)
      allZero.value = true

    blsjOption.series[0].data = tempSeries
    blsjChart.setOption(blsjOption)
    blsjChart.on('click', (params) => {
    // 控制台输出点击的数据的信息
      console.log(params)
      currentAdverseEvent.value = adverseData.value[params?.data?.other]
      /// 计算一下总数
      let currentAdverse = 0
      currentAdverseEvent.value?.forEach((item) => {
        currentAdverse += Number(item?.total)
      })
      currentAdverseTab.value = 'detail'
      currentAdverseTitle.value = nameTitle(params.name)
      currentAdveseEventTotal.value = currentAdverse
    })
  })
}

/// 选中年龄段性别
function selectTheAgeItem(item, e) {
  console.log(item)

  if (currentAgeGroup.target?.innerText !== e.currentTarget?.innerText)
    currentAgeGroup.target?.classList?.remove('age-sex-item-active')

  e.currentTarget?.classList.toggle('age-sex-item-active')
  currentAgeGroup.groups = item.examGroups
  if (currentAgeGroup.target?.innerText === e.currentTarget?.innerText && !e.currentTarget?.classList?.contains('age-sex-item-active'))
    currentAgeGroup.groups = []

  currentAgeGroup.target = e.currentTarget
  currentAgeGroup.label = item.label
}

onMounted(() => {
  nextTick(() => {
    const chartDom = document.getElementById('lxChart')
    lxChart = echarts.init(chartDom)
    const chartLy = document.getElementById('lyChart')
    lyChart = echarts.init(chartLy)
    const chartBlsj = document.getElementById('blsjChart')
    blsjChart = echarts.init(chartBlsj)
  })
  getEfficiencyData()
  getPatientSourceFrom()
  getSexAndAgeDataFrom()
  getAdverseEventDataFrom()
})

window.onresize = function () {
  lxChart.resize()
  lyChart.resize()
  blsjChart.resize()
}
</script>

<template>
  <div flex>
    <div flex-col flex-1 style="border-right: 1px solid #ccc">
      <div relative h-324px>
        <div flex items-center text-14px>
          <SectionTitle> 疗效分析 </SectionTitle>
        </div>
        <div id="lxChart" absolute h-324px w-full />
      </div>
      <div mb-18px mt-10px h-1px w-full class="bg-#ccc" />
      <div relative h-324px>
        <div flex items-center text-14px>
          <SectionTitle> 受试者来源分布 </SectionTitle>
        </div>
        <div id="lyChart" absolute h-324px w-full />
      </div>
    </div>
    <div flex-col flex-1>
      <div ml-14px>
        <div flex items-center text-14px>
          <SectionTitle> 男女性别分布 </SectionTitle>
          <img
            mx-10px
            src="@/assets/svg-icon/icon_info.svg"
            style="width: 17px; height: 17px"
          >
          <div text-14px color="#666">
            点击年龄段可查看详细分组数据
          </div>
        </div>
        <div ml-14px mt-18px flex items-center>
          <div>
            <div
              v-for="(item, index) in ageGroup"
              :key="index"
              class="age-sex-item"
              mb-10px
              flex
              items-center
              @click="
                (e) => {
                  selectTheAgeItem(item, e);
                }
              "
            >
              <div relative>
                <el-progress
                  color="#5B96FD"
                  class="left-male my-progress"
                  :show-text="false"
                  :percentage="
                    item.total === 0
                      ? 0
                      : (item.mas * 100) / item.total
                  "
                  :stroke-width="20"
                />
                <div class="left-male-label">
                  {{ item.mas }}
                </div>
              </div>
              <div
                class="age-label"
                color="#333"
                mx-10px
                min-w-60px
                py-4px
                text-center
                text-14px
              >
                {{ item.label }}
              </div>
              <div relative>
                <el-progress
                  color="#FFA8B4"
                  class="my-progress right-female"
                  :show-text="false"
                  :percentage="
                    item.total === 0
                      ? 0
                      : (item.ms * 100) / item.total
                  "
                  :stroke-width="20"
                />
                <div class="right-female-label">
                  {{ item.ms }}
                </div>
              </div>
            </div>
          </div>
          <div ml-44px flex-col style="gap: 20px 0;flex: 1;">
            <div v-if="currentAgeGroup.groups?.length > 0" style="align-self: center;font-size: 15px;color: #333;font-weight: 600;">
              {{ currentAgeGroup.label }}
            </div>
            <div
              v-for="(item, index) in currentAgeGroup.groups"
              :key="index"
              flex-col
            >
              <div text-16px font-500>
                {{ item.name }}
              </div>
              <div
                my-12px
                flex
                items-center
                text-14px
                color="#333"
              >
                <img
                  h-17px
                  src="@/assets/svg-icon/analysis_male.svg"
                >
                <div ml-10px>
                  {{ `男: ${item.mas}` }}
                </div>
              </div>
              <div flex items-center text-14px color="#333">
                <img
                  h-17px
                  src="@/assets/svg-icon/analysis_female.svg"
                >
                <div ml-10px>
                  {{ `女: ${item.ms}` }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div mb-18px mt-11px h-1px w-full class="bg-#ccc" />
      <div v-show="currentAdverseTab === 'pie'" ml-14px h-324px flex-col>
        <div flex items-center text-14px>
          <SectionTitle> 不良事件分布 </SectionTitle>
        </div>
        <div h-14px />
        <Empty v-if="allZero" />
        <div v-else id="blsjChart" flex-1 />
      </div>
      <div v-show="currentAdverseTab === 'detail'" ml-14px flex-col>
        <SectionTitle> 不良事件分布 </SectionTitle>
        <div mt-20px flex items-center>
          <div flex items-center px-10px text-14px color="#3B8FD9" @click="currentAdverseTab = 'pie'">
            <img src="@/assets/svg-icon/analysis_back.svg" w-16px>
            <div ml-10px>
              返回
            </div>
          </div>
          <div color="#333" flex-1 pr-54px text-center text-14px>
            {{ currentAdverseTitle }}
          </div>
        </div>
        <div mt-20px flex-col px-10px style="gap: 10px 0;">
          <div v-for="(item, index) in currentAdverseEvent" :key="index" color="#333" flex items-center text-14px>
            <n-ellipsis style="max-width:130px;min-width: 130px;">
              {{ item.name }}
            </n-ellipsis>
            <el-progress
              color="#5B96FD"
              class="my-progress"
              style="width:280px;padding: 0 10px 0 10px;"
              :show-text="false"
              :percentage="item.total * 100 / currentAdveseEventTotal"
              :stroke-width="20"
            />
            <div>{{ `${item.total}例` }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
#lxChart {
    top: 0;
}
#lyChart {
    top: 0;
}

.my-progress {
    width: 130px;
    :deep(.el-progress-bar__inner) {
        border-radius: 3px;
    }
    :deep(.el-progress-bar__outer) {
        border-radius: 3px;
        background-color: #e8e8e8;
    }
}

.left-male {
    :deep(.el-progress-bar__inner) {
        left: auto;
        right: 0;
    }
}

.left-male-label {
    position: absolute;
    left: 6px;
    top: 0;
    line-height: 20px;
    font-size: 14px;
    color: #333;
}

.right-female-label {
    position: absolute;
    right: 6px;
    top: 0;
    line-height: 20px;
    font-size: 14px;
    color: #333;
}

.age-sex-item {
  transition: transform 0.2s ease;
    &:hover {
      transform: scaleY(1.3);
        .age-label {
            background: #e5f1fa;
            border-radius: 3px;
        }

    }
}

.age-sex-item-active {
    .age-label {
        background: #e5f1fa;
        border-radius: 3px;
        color: #198eeb;
    }
}
</style>
