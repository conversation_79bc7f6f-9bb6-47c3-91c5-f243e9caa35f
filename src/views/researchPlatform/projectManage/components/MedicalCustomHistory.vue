<script setup lang='ts'>
import _ from 'lodash'
import { newHistory } from '~/src/constants/researcherHistorys'

const props = defineProps({
  edit: {
    type: Boolean,
    default: true,
  },
  modelValue: {
    type: Object as PropType<any>,
    default: () => {},
  },
})

const emits = defineEmits(['update:modelValue', 'validateSuccess'])

const history = reactive(_.clone(newHistory.questions))

/// 抛出去的已选项目
const questions = reactive(_.clone(newHistory.default))

// onMounted(() => {
//   /// 因为以前都是 question1 开始的病史信息. 所以
//   // if (props.modelValue?.hasOwnProperty('question101'))
//   Object.assign(questions, props.modelValue)
// })

watch(() => props.modelValue, () => {
  Object.assign(questions, props.modelValue)
  /// 得处理一下数据, 显示是否否和纳入标准
  if (questions?.question101) {
    history.forEach((item, index) => {
      const val = questions[`question${index + 101}`]
      if (item.type === 'radio' && val)
        radioSel(item, val, true)
      else if (item.type === 'checkbox' && val)
        checkboxSel(item, val, index, true)
    })
  }
}, {
  immediate: true,
})

function validate() {
  /// 验证数据填写了没
  let check = true
  history.forEach((item, index) => {
    if (!questions.hasOwnProperty(`question${index + 101}`)) {
      questions[`question${index + 101}`] = ''
      check = false
    }
    else {
      if (questions[`question${index + 101}`]?.length === 0)
        check = false
    }
  })
  if (check)
    emits('validateSuccess')
  else
    window.$message.error('请完善病史信息')
}

defineExpose({
  validate,
})

/// 单选逻辑
function radioSel(item, val, dontEmit: boolean = false) {
  /// 条件符合. 应该显示
  item.showTip = item.shouldTip === val
  if (!dontEmit)
    emits('update:modelValue', questions)
}

/// 多选逻辑
function checkboxSel(item, val, index, dontEmit: boolean = false) {
  let obj = questions[`question${index + 101}`]
  if (item.shouldTip === 'anyone') {
    if (val[val?.length - 1] === '以上均无') {
      obj = ['以上均无']
      item.showTip = false
    }
    else {
      if (val.includes('以上均无')) {
        const index = obj.indexOf('以上均无')
        if (index !== -1)
          obj.splice(index, 1)
      }
      item.showTip = obj?.length > 0
    }
  }
  questions[`question${index + 101}`] = obj
  if (!dontEmit)
    emits('update:modelValue', questions)
}
</script>

<template>
  <div mt-20px flex-col style="gap: 14px 0;">
    <div v-for="(item, index) in history" :key="index" class="question">
      <SvgIcon v-if="item.required && edit" local-icon="slmc-icon-app_required1" size="14" class="text-#F36969" />
      <div min-h-30px flex items-center>
        {{ item.title }}
      </div>
      <div v-if="item.type === 'radio'" flex flex-wrap items-center pl-16px>
        <n-radio-group
          v-model:value="questions[`question${index + 101}`]"
          :disabled="!edit" @update:value="(val) => {
            radioSel(item, val)
          }"
        >
          <n-space item-style="display: flex;padding-bottom:5px" :size="[40, 0]">
            <n-radio v-for="option in item.options" :key="option.value" :value="option.value" mt-5px>
              {{ option.label }}
            </n-radio>
          </n-space>
        </n-radio-group>
        <div
          v-if="item?.showTip"
          ml-20px
          h-32px w-230px flex line-height-32px
        >
          <img class="add-img-hover" src="@/assets/images/icon-hint.png" mr-8px mt-8px h-16px w-16px>
          <span color="#333">该受试者不符合试验纳入标准</span>
        </div>
        <div v-if="questions[`question${index + 101}`]?.length === 0" w-full flex items-center>
          <img h-16px w-16px src="@/assets/images/reset_password_wrong.png">
          <div ml-6px text-14px color="#F36969">
            请选择病史信息
          </div>
        </div>
      </div>
      <div v-if="item.type === 'checkbox'" w-full pl-16px>
        <n-checkbox-group
          v-model:value="questions[`question${index + 101}`]"
          :disabled="!edit" @update:value="(val) => {
            checkboxSel(item, val, index)
          }"
        >
          <n-space item-style="display: flex;" :size="[40, 10]">
            <n-checkbox v-for="option in item.options" :key="option" :value="option.value">
              <div ml-10px>
                {{ option.label }}
              </div>
            </n-checkbox>
            <div
              v-if="item?.showTip"
              ml--20px
              h-32px w-230px flex line-height-32px
            >
              <img class="add-img-hover" src="@/assets/images/icon-hint.png" mr-8px mt-8px h-16px w-16px>
              <span color="#333">该受试者不符合试验纳入标准</span>
            </div>
          </n-space>
        </n-checkbox-group>
        <div v-if="questions[`question${index + 101}`]?.length === 0" mt-6px w-full flex items-center>
          <img h-16px w-16px src="@/assets/images/reset_password_wrong.png">
          <div ml-6px text-14px color="#F36969">
            请选择病史信息
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.question{
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    font-size: 14px;
    color: #666;
    line-height: 14px;
}
</style>
