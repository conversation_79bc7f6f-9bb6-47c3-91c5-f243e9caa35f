<script setup lang="ts">
import { storeToRefs } from 'pinia'
import dayjs from 'dayjs'
import { addNewInterveneRecord, deleteInterveneRecord, getInterveneRecordList, getOrganAllList, updateInterveneRecord } from '~/src/api/research'
import { useAuthStore, useReseacherStore } from '~/src/store'

const { subjectInfo } = storeToRefs(useReseacherStore())
const { userInfo } = storeToRefs(useAuthStore())
const hospitals = ref()
const dangerOptions = [{
  label: '1级：轻微，对患者功能无明显影响',
  value: 'ONE',
}, {
  label: '2级：中度，对患者功能有轻度至中度影响，但不威胁生命',
  value: 'TWO',
}, {
  label: '3级：严重，对患者功能有明显影响，可能需要治疗干预',
  value: 'THREE',
}, {
  label: '4级：危及生命，对患者功能严重影响，需要紧急治疗干预',
  value: 'FOUR',
}, {
  label: '5级：死亡与不良事件相关',
  value: 'FIVE',
}]

const options = {
  ONE: '1级：轻微，对患者功能无明显影响',
  TWO: '2级：中度，对患者功能有轻度至中度影响，但不威胁生命',
  THREE: '3级：严重，对患者功能有明显影响，可能需要治疗干预',
  FOUR: '4级：危及生命，对患者功能严重影响，需要紧急治疗干预',
  FIVE: '5级：死亡与不良事件相关',
}

const formRef = ref()
const showModalOptions: any = reactive({
  type: 'add',
  showAdd: false,
  rules: {
    type: {
      required: true,
      message: '事件类型不能为空',
      trigger: ['blur', 'change'],
    },
    eventLevel: {
      required: true,
      message: '严重程度分级不能为空',
      trigger: ['blur', 'change'],
    },
    hospital: {
      required: true,
      message: '机构不能为空',
      trigger: ['blur', 'change'],
    },
  },
  values: {
    type: 'ADVERSE_EVENT',
  },
})
const statusType = {
  SMS_ALERT: {
    label: '短信提醒',
    cells: [
      {
        label: '类型：',
        content: () => '短信提醒',
      },
      {
        label: '短信内容：',
        content: (item) => {
          // return ''
          const kk = JSON.parse(item?.interveneContent || '[]')
          let tempStr = '-'
          kk.forEach((element) => {
            if (element.name === '短信内容')
              tempStr = element.value
          })
          return tempStr
        },
      },
      {
        label: '短信发送时间：',
        content: (item) => {
          const kk = JSON.parse(item?.interveneContent || '[]')
          let tempStr = '-'
          kk.forEach((element) => {
            if (element.name === '短信发送时间')
              tempStr = element.value
          })
          return tempStr
        },
      },
      {
        label: '短线状态：',
        type: 'tag',
        content: (item) => {
          const kk = JSON.parse(item?.interveneContent || '[]')
          let tempStr = '-'
          kk.forEach((element) => {
            if (element.name === '短信状态')
              tempStr = element.value
          })
          return tempStr === '发送成功' ? { label: '发送成功', color: '#4ACFB1' } : { label: '发送失败', color: '#F36969' }
        },
      },
    ],
  },
  ADVERSE_EVENT: {
    label: '不良事件',
    cells: [
      {
        label: '类型：',
        content: () => '不良事件',
      },
      {
        label: '严重程度分级：',
        content: (item) => {
          const kk = JSON.parse(item?.interveneContent || '{}')
          return options[kk.eventLevel]
        },
      },
      {
        label: '发生机构：',
        content: (item) => {
          const kk = JSON.parse(item?.interveneContent || '{}')
          return kk.hospital || '-'
        },
      },
      {
        label: '备注：',
        content: (item) => {
          const kk = JSON.parse(item?.interveneContent || '{}')
          return kk.remark || '-'
        },
      },
    ],
  },
}
const searchParams = reactive({
  interveneUser: null,
  startTime: '',
  endTime: '',
})

const searchTimes = ref()
const listDatas = ref()

/// 新增
function addBtnClick() {
  showModalOptions.showAdd = true
}

/// 获取机构列表
function getOrganList() {
  getOrganAllList().then((res) => {
    console.log(res)
    hospitals.value = res?.data
  })
}

/// 保存
function handleBodyConfirm() {
  formRef.value.validate((valid, fields) => {
    if (valid) {
      /// 提交
      // const tempArr = [{
      //   name: '严重程度分级',
      //   value: showModalOptions.values.danger,
      // }, {
      //   name: '发生机构',
      //   value: showModalOptions.values.hospital,
      // },
      // {
      //   name: '备注',
      //   value: showModalOptions.values.remark,
      // }]

      if (showModalOptions.type === 'edit') {
        updateInterveneRecord({
          interveneContent: JSON.stringify(showModalOptions.values),
          interveneId: showModalOptions.values?.interveneId,
          updateId: userInfo.value?.id,
          updateName: userInfo.value?.userName,
        }).then((res) => {
          visibleChange(false)
          if (res?.data)
            getListFromNet()
        })
      }
      else {
        addNewInterveneRecord({
          examNo: subjectInfo.value?.examNo,
          interveneType: 'ADVERSE_EVENT',
          topicId: subjectInfo.value?.topicId,
          createId: userInfo.value?.id,
          createName: userInfo.value?.userName,
          submitOrganizationId: userInfo.value?.organId,
          submitOrganizationName: userInfo.value?.organName,
          interveneContent: JSON.stringify(showModalOptions.values),
        }).then((res) => {
          visibleChange(false)
          if (res?.data)
            getListFromNet()
        })
      }
    }
  })
}

/// 取消
function visibleChange(v) {
  if (!v) {
    showModalOptions.showAdd = false
    showModalOptions.type = 'add'
    showModalOptions.values = { type: 'ADVERSE_EVENT' }
  }
}

/// 获取列表
function getListFromNet() {
  if (searchTimes.value?.length > 0) {
    searchParams.startTime = searchTimes.value[0]
    searchParams.endTime = searchTimes.value[1]
  }
  else {
    searchParams.startTime = null
    searchParams.endTime = null
  }

  getInterveneRecordList({
    examNo: subjectInfo.value?.examNo,
    topicId: subjectInfo.value?.topicId,
    ...searchParams,
  }).then((res) => {
    console.log(res)
    listDatas.value = res?.data || []
  })
}

function deleteTheItem(item) {
  window.$dialog.warning({
    title: '确定删除该记录吗？',
    negativeText: '取消',
    positiveText: '确定',
    btnGhost: true,
    onPositiveClick: () => {
      deleteInterveneRecord(item.interveneId).then((res) => {
        if (res?.data) {
          window.$message.success('成功删除')
          getListFromNet()
        }
      })
    },
  })
}

function editTheItem(item) {
  showModalOptions.type = 'edit'
  showModalOptions.showAdd = true
  showModalOptions.values.interveneId = item.interveneId
  console.log(item)

  const sj = JSON.parse(item?.interveneContent || '{}')
  showModalOptions.values.eventLevel = sj.eventLevel
  showModalOptions.values.hospital = sj.hospital
  showModalOptions.values.remark = sj.remark
  showModalOptions.values.type = item.interveneType
}

function searchTheData() {
  getListFromNet()
}

function disabledDate(time: Date) {
  return time.getTime() > Date.now()
}

onMounted(() => {
  console.log(subjectInfo)
  getListFromNet()

  getOrganList()
})
</script>

<template>
  <div class="page" h-full p-14px>
    <div flex justify-between>
      <n-button type="primary" @click="addBtnClick">
        新 增
      </n-button>
      <div flex gap-20px>
        <div flex items-center text-14px color="#666">
          <div mr-10px>
            干预人
          </div>
          <el-input
            v-model="searchParams.interveneUser"
            style="width: 200px"
            placeholder="请输入"
            @input="searchTheData"
          />
        </div>
        <div flex items-center text-14px color="#666">
          <div mr-10px>
            干预日期
          </div>
          <el-date-picker
            v-model="searchTimes"
            :disabled-date="disabledDate"
            :teleported="false"
            unlink-panels
            style="width: 260px"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            @change="searchTheData"
          />
        </div>
      </div>
    </div>
    <div v-if="listDatas?.length > 0" mt-20px>
      <div v-for="(item, index) in listDatas" :key="index" class="gy-item">
        <div class="gy-top">
          <div class="gy-circle" />
          <div>{{ dayjs(item.updateTime).format('YYYY-MM-DD HH:MM:ss') }}</div>
          <div v-if="item.interveneType !== 'SMS_ALERT'" ml-10px p-4px @click="editTheItem(item)">
            <SvgIcon local-icon="slmc-icon-edit1" size="16" />
          </div>
          <div v-if="item.interveneType !== 'SMS_ALERT'" ml-2px p-4px @click="deleteTheItem(item)">
            <SvgIcon local-icon="slmc-icon-delete2" size="16" />
          </div>
        </div>
        <div class="gy-bottom">
          <div class="gy-left-line" />
          <div class="gy-right-content">
            <div>{{ `【${item?.updateName || item?.createName}】提交了干预信息` }}</div>
            <div class="gy-detail">
              <div v-for="(ditem, dindex) in statusType[item?.interveneType].cells" :key="dindex" class="gy-detail-item">
                <div style="align-self: flex-start;">
                  {{ ditem.label }}
                </div>
                <div v-if="ditem.type === 'tag'" :style="{ background: ditem.content(item).color }" color="#fff" rounded-12px px-8px text-12px>
                  {{ ditem.content(item).label }}
                </div>
                <div v-else flex-1>
                  {{ ditem.content(item) }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div v-else mt-20px h-400px flex items-center justify-center>
      <DataEmpty />
    </div>
    <BasicModal
      :visible="showModalOptions.showAdd" :title="`${showModalOptions.type === 'add' ? '新增' : '编辑'}干预措施`" width="560" :footer-offset="84"
      @ok="handleBodyConfirm" @visible-change="visibleChange"
    >
      <el-form ref="formRef" mt-14px label-width="130" :rules="showModalOptions.rules" label-placement="right" :model="showModalOptions.values">
        <el-form-item prop="type" label="事件类型">
          <el-select v-model="showModalOptions.values.type" style="width: 360px;">
            <el-option label="不良事件" value="ADVERSE_EVENT" />
          </el-select>
        </el-form-item>
        <el-form-item prop="eventLevel" label="严重程度分级">
          <el-select v-model="showModalOptions.values.eventLevel" style="width: 360px;">
            <el-option v-for="(item, index) in dangerOptions" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item prop="hospital" label="发生机构">
          <el-select v-model="showModalOptions.values.hospital" filterable style="width: 360px;">
            <el-option v-for="(hitem, hindex) in hospitals" :key="hindex" :label="hitem" :value="hitem" />
          </el-select>
        </el-form-item>
        <el-form-item prop="remark" label="备注">
          <el-input
            v-model="showModalOptions.values.remark" placeholder="请输入"
            style="width: 360px"
            :rows="3"
            type="textarea"
          />
        </el-form-item>
      </el-form>
    </BasicModal>
  </div>
</template>

<style lang="scss" scoped>
.page{
  display: flex;
  flex-direction: column;
  max-height: calc(100vh - 250px);
  overflow: auto;
}

.gy-item {
    display: flex;
    flex-direction: column;

    .gy-top {
        display: flex;
        align-items: center;
        color: "#333";
        font-size: 14px;

        .gy-circle {
            width: 10px;
            height: 10px;
            background: #06aea6;
            border-radius: 50%;
            margin-right: 10px;
        }
    }

    .gy-bottom {
        display: flex;
        align-items: center;
        position: relative;
        margin-top: 8px;
        padding: 4px 0 14px 0;

        .gy-left-line {
            position: absolute;
            width: 2px;
            background: #06aea6;
            top: 0;
            bottom: 5px;
            left: 4px;
        }

        .gy-right-content {
            display: flex;
            margin-left: 20px;
            flex-direction: column;
            font-size: 14px;
            color: "#333";
            width: 100%;

            .gy-detail {
                margin-top: 10px;
                width: 100%;
                background: #f5f5f5;
                border-radius: 3px;
                padding: 14px 14px 0 14px;
                gap: 14px;
            }
            .gy-detail-item{
                display: flex;
                align-items: center;
                line-height: 24px;
                margin-bottom: 14px;
            }
        }
    }
}
</style>
