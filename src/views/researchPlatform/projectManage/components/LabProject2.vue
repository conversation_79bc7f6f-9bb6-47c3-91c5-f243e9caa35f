<script lang="ts" setup>
import _ from 'lodash'
import dayjs from 'dayjs'
import { storeToRefs } from 'pinia'
import { type ECOption, useEcharts } from '@/hooks'
import { getCheckDataValue, isOnlyYAxis } from '@/utils/common'
import { commitScaleDataRecord, editScaleAnswer, getScaleAnswers, getScaleChartData, getScaleDetail } from '~/src/api/research'
import { useAuthStore, useReseacherStore } from '~/src/store'

const props = defineProps({
  showAdd: {
    type: Boolean,
    default: false,
  },
  topicId: {
    type: String,
  },
  scaleId: {
    type: String,
  },
  examNo: {
    type: String,
  },
  taskId: {
    type: String,
  },
  examineeNodeId: {
    type: String,
  },
})
const emits = defineEmits(['addSuccess'])
const { userInfo } = storeToRefs(useAuthStore())
const { topicInfo, subjectInfo } = storeToRefs(useReseacherStore())
const statusOptions = {
  3: {
    label: '已中止',
    color: 'bg-#CCCCCC',
    key: 3,
  },
  2: {
    label: '已完成',
    color: 'bg-#4ACFB1',
    key: 2,
  },
  1: {
    label: '进行中',
    color: 'bg-#FF9B54',
    key: 1,
  },
  0: {
    label: '待开始',
    color: 'bg-#45A8E6',
    key: 0,
  },
}

interface Props {
  showAdd: boolean
  scaleId: string
  topicId: number
  examNo: string
  taskId: string
  examineeNodeId: string
}

const manualData = ref()

const formEl = ref()
const oriQuestions = ref()
const questionMap = ref()
const showModalOptions = reactive({
  type: 'add',
  showAdd: props.showAdd,
  answers: [],
})
const questionInfo = ref()
const newScaleData: any = reactive({
  reportDate: null,
  answers: [],
})

const addRules = reactive({
  reportDate: {
    required: true,
    message: '日期不能为空',
    trigger: ['blur', 'change'],
  },
  content: {
    required: true,
    message: '不能为空',
    trigger: ['blur', 'change'],
  },
})

const currentScaleItem = ref()
const tableRef = ref<any>(null)
/// 列的表头数据
const columnsData = ref<any>([])
/// 实验室具体数据
const tableData = ref<any>([])
/// chartData
const chartDatas = ref()

/// y 轴数据(真实数据,hover 要显示的数据啊,脑壳疼哟!!!)
const yAixTrueList = ref<any>([])
/// y 轴是否全无数据
const yAixIsNoData = ref(false)

/// 拖拽item
const dragItem = ref()
const showDragItem = ref(false)
const offsetX = ref()
const offsetY = ref()
const zoomImage = ref()
const zoomOriWH = reactive({
  w: 0,
  h: 0,
})

/// 趋势图
const lineOptions = ref<any>({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      label: {
        backgroundColor: '#6a7985',
      },
    },
    padding: 0,
    backgroundColor: 'rgba(0,0,0,0.70)',
    textStyle: {
      color: '#fff',
      fontSize: 12,
    },
    formatter(params: any) {
      // console.log(params)
      const data = params[0]
      const index = data.dataIndex
      const trueData = yAixTrueList.value[index]
      return `<div px-8px py-5px >
                <div>${data.axisValue}</div>
                <div flex items-center mt-6px >
                      <div style=background-color:#5B96FD;width:10px;height:10px;display:inline-block;border-radius:50%;margin-right:6px;" ></div>
                      <div>
                        <span>${data.seriesName}:</span>
                        <span>${trueData}</span>
                      </div>
                </div>
              </div>
            `
    },
  },
  grid: {
    left: '2%',
    right: '4%',
    bottom: '0%',
    containLabel: true,
  },
  xAxis: [
    {
      type: 'category',
      boundaryGap: false,
      axisLine: {
        lineStyle: {
          color: '#ccc',
        },
      },
      axisLabel: {
        color: '#999',
        showMinLabel: true, // 显示最小值标签
        showMaxLabel: true, // 显示最大值标签
        // rotate: 45, // 逆时针旋转 45 度
        formatter(val) {
          // 当字符串长度超过2时
          if (val.length > 2) {
            // 把字符串分割成字符串数组
            const array = val.split('-')
            // 在下标2处删除0个，加上回车
            array.splice(1, 0, '\n')
            array.splice(3, 0, '-')
            return array.join('')
          }
          else {
            return val
          }
        },
      },
      data: [],
    },
  ],
  yAxis: [
    {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#ccc',
        },
      },
      axisLabel: {
        color: '#999',
      },
    },
  ],
  series: [
    {
      color: '#5B96FD',
      name: '',
      type: 'line',
      connectNulls: true, // 连接空值
      emphasis: {
        focus: 'series',
      },
      data: [],
    },
  ],
}) as Ref<ECOption>
const { domRef: lineRef } = useEcharts(lineOptions)

// onMounted(() => {
//   getScaleQuestions()
// })

watch(() => props.scaleId, () => {
  getScaleQuestions()
}, { immediate: true })

watch(() => props.examineeNodeId, () => {
  getTheAnswerFromNet()
}, { immediate: true })

function visibleChange(v) {
  if (!v) {
    showModalOptions.showAdd = false
    // showModalOptions.type = 'add'
    newScaleData.reportDate = null
    newScaleData.answers = _.cloneDeep(oriQuestions.value)
  }
}

function handleBodyConfirm() {
  if (questionInfo.value?.scaleName !== '干扰素' && questionInfo.value?.scaleName !== '24周疗效评估') {
    if (!newScaleData.reportDate)
      return
  }

  if (questionInfo.value?.scaleName === '干扰素')
    newScaleData.reportDate = newScaleData.answers[0]?.content

  if (questionInfo.value?.scaleName === '24周疗效评估')
    newScaleData.reportDate = dayjs().format('YYYY-MM-DD')

  if (showModalOptions.type === 'add') {
  // 提交数据
    commitScaleDataRecord({
      examNo: props.examNo,
      scaleId: props.scaleId,
      topicId: props.topicId,
      taskId: props.taskId,
      writeId: userInfo.value?.id,
      writeName: userInfo.value?.userName,
      ...newScaleData,
    }).then((res) => {
      if (res?.data) {
        getTheAnswerFromNet()
        window.$message.success('新增成功')
        showModalOptions.showAdd = false
        showModalOptions.type = 'edit'
        newScaleData.reportDate = null
        newScaleData.answers = _.cloneDeep(oriQuestions.value)
        emits('addSuccess')
      }
    })
  }
  else {
  /// 修改
    editScaleAnswer({
      examNo: props.examNo,
      scaleId: props.scaleId,
      topicId: props.topicId,
      taskId: props.taskId,
      writeId: userInfo.value?.id,
      writeName: userInfo.value?.userName,
      ...newScaleData,
    }).then((res) => {
      if (res?.data) {
        getTheAnswerFromNet()
        window.$message.success('修改成功')
        showModalOptions.showAdd = false
        showModalOptions.type = 'edit'
        newScaleData.reportDate = null
        newScaleData.answers = _.cloneDeep(oriQuestions.value)
        emits('addSuccess')
      }
    })
  }
  getScaleQuestions()
}

/// 编辑
function editTheScaleData(item, index) {
  /// 查看是否能编辑, 能编辑, 则进入编辑
  console.log(item)

  if (topicInfo.value?.status === 2 || topicInfo.value?.status === 3) {
    // 不能新增
    window.$message.warning(`课题${statusOptions[topicInfo.value?.status].label},不可编辑数据`)
    return
  }
  /// 手动可以修改
  showModalOptions.type = 'edit'
  showModalOptions.showAdd = true
  // 赋值
  // newScaleData.answers = item.answers
  const sbTemp: any = []
  item?.answers?.forEach((element) => {
    if (questionMap.value[element.questionId]?.questionType === 'checkbox') {
      /// 多选
      element.contentKeys = element.contentKey?.split(',')
    }
    const bb = {
      ...element,
      ...questionMap.value[element.questionId],
    }

    /// / sb 需求
    if (item.scaleName === '生物样本留取' && bb.questionTitle === '留取类型') {
      const first = item.answers[0]
      element.hidden = first.content === '否'
    }
    sbTemp.push(bb)
  })
  newScaleData.answers = sbTemp
  newScaleData.reportDate = dayjs(item?.reportTime).format('YYYY-MM-DD')
  console.log(sbTemp)
}

async function getScaleQuestions() {
  // const res = await getPatientInfoApi(patientId)
  // patientInfo.value = res.data
  if (props.scaleId) {
    getScaleDetail(props.scaleId).then((res) => {
      ///
      console.log(res)
      questionInfo.value = res?.data
      const sb: any = []
      /// 行数据
      const tempQuestion = {}
      res?.data?.questions?.forEach((ques) => {
        const ittem = {
          questionId: ques.questionId,
          questionType: ques.questionType,
          questionTitle: ques.questionTitle,
          medicineUnit: ques.medicineUnit,
          medicineType: ques.medicineType,
          scaleName: res?.data?.scaleName,
          options: ques.options,
        }
        sb.push(ittem)
        tempQuestion[ques?.questionId] = ittem
      })
      newScaleData.answers = sb
      oriQuestions.value = _.cloneDeep(sb)
      columnsData.value = _.cloneDeep(sb)
      questionMap.value = tempQuestion
      if (columnsData.value?.length > 0)
        tableRef.value?.setCurrentRow(columnsData.value[0])
    })
    getTheAnswerFromNet()
  }
}

function getTheAnswerFromNet() {
  getScaleAnswers({
    examNo: props.examNo,
    topicId: props.topicId,
    scaleId: props.scaleId,
  }).then((res) => {
    // console.log(res)
    tableData.value = res?.data
    // tableRef.value?.setCurrentRow(columnsData.value[0])
    /// 默认选中第一行
    // headerEvent(0)
    // handleCurrentRowSelect(columnsData.value[0])
    /// 刷新 需要继续刷新当前行
    /// 需要先取消选中一下, 不然不刷新
    tableRef.value?.setCurrentRow()
    tableRef.value?.setCurrentRow(currentScaleItem.value || columnsData.value[0])
    if (res?.data === null || res?.data?.length === 0) {
      showModalOptions.type = 'add'
      manualData.value = null
    }

    /// 服了, 手动添加只有一次, 发现手动了 变编辑
    for (let index = 0; index < res?.data?.length; index++) {
      const element = res?.data[index]
      if (element.examineeNodeId === props.examineeNodeId) {
        showModalOptions.type = 'edit'
        manualData.value = element
        break
      }
      else {
        showModalOptions.type = 'add'
        manualData.value = null
      }
    }
  })
}

/// 获取指标趋势数据
function getChartDataFromNet(questionId) {
  getScaleChartData({
    examNo: props.examNo,
    topicId: props.topicId,
    scaleId: props.scaleId,
    questionId,
  }).then((res) => {
    // console.log(res)
    chartDatas.value = res?.data || []
    headerEvent()
  })
}

/// 处理趋势图的数据呀呀呀呀
function handleOptionsData() {
  /**
   * 处理趋势图的数据
   * 1.最正常的情况,嘿嘿,只有数字,跟着数字显示就好了啊
   * 2.所有值有大于小于号,阴性阳性等符号,直接正常取出数字即可
   * 3.若只有阴性,阳性汉字情况下,则纵轴分阴阳俩块,阴阳取值
   * 4.低于检测值时,取值refValue,是否包含10,是则取值 9,否则取值 29(取值范围最低,不是 10 就是 30,低于检测值则取最低值-1)
   */

  lineOptions.value.yAxis[0].name = currentScaleItem.value?.medicineUnit
  lineOptions.value.series[0].name = currentScaleItem.value?.questionTitle

  const xDatas: any = []
  const yList: any[] = []
  // /// 先判断是不是只有y 轴阴阳
  if (isOnlyYAxis(chartDatas.value, 'value')) {
    chartDatas.value.forEach((element) => {
      yAixTrueList.value.push(element.value || '-')
      if (element.value === '阴性')
        yList.push('阴性')
      else if (element.value === '阳性' || element.value?.includes('+1:'))
        yList.push('阳性')
      else
        yList.push('-')
    })
    lineOptions.value.yAxis[0].type = 'category'
    lineOptions.value.yAxis[0].data = ['阴性', '阳性']
  }
  else {
    chartDatas.value.forEach((element) => {
      xDatas.push(dayjs(element.reportDate).format('YYYY-MM-DD'))
      yAixTrueList.value.push(element.value || '-')
      if (element.value) {
        const num = getCheckDataValue(element.value, '')
        yList.push(num)
      }
      else { yList.push('-') }
    })
    lineOptions.value.yAxis[0].type = 'value'
    lineOptions.value.yAxis[0].data = []
  }

  lineOptions.value.xAxis[0].data = xDatas
  // /// 是否全部为空值
  yAixIsNoData.value = yList.every((item) => {
    return item === '-' || item === ''
  })

  lineOptions.value.series[0].data = yList

  // yAixTrueList.value = yAixTrueList.value
  lineOptions.value.legend = {
    data: [currentScaleItem.value.questionTitle],
    right: 10,
    selectedMode: false,
  }
  // /// 识别长数组中有值的一段
  // const valueRes = getEmptyIndex(yList)
  // console.log(lineOptions.value)
}

/// 点击表头
function headerEvent() {
  // selectTabIndex.value = index
  yAixTrueList.value = []
  handleOptionsData()
}

/// 选中当前行, 处理那个指标趋势图
function handleCurrentRowSelect(row: any) {
  /// 选中
  // headerEvent(row.index)
  // console.log(row)
  if (row) {
    currentScaleItem.value = row
    getChartDataFromNet(row.questionId)
  }
}

/// 时间禁用
function disabledDate(time: Date) {
  return time.getTime() > Date.now()
}
/**
 *
 *  拖动相关
 */
function checkReportLook() {
  showDragItem.value = true
  nextTick(() => {
    dragItem.value = document.getElementById('dragItem')
    dragItem.value.style.bottom = '20px'
    dragItem.value.style.right = '16px'
    dragItem.value.addEventListener('wheel', zoom)
    zoomImage.value = document.getElementById('zoomImage')
    zoomImage.value.addEventListener('wheel', zoom)
    setTimeout(() => {
      const react = zoomImage.value.getBoundingClientRect()
      zoomOriWH.w = react.width
      zoomOriWH.h = react.height
    }, 100)
  })
}
/// 拖动的报告单
function startDrag(event) {
  // dragItem.value.style.opacity = 0.7
  offsetX.value = event.offsetX
  offsetY.value = event.offsetY
  event.dataTransfer.effectAllowed = 'move'
  // console.log(e)
}

function stopDrag(event) {
  // console.log(e)
  /// 设置原div 显示, 并设定好位置
  // dragItem.value.style.opacity = 1
}

function zoom(event) {
  event.preventDefault()
  const scaleFactor = 1 + event.deltaY * 0.01
  const oriW = zoomImage.value.clientWidth
  const oriH = zoomImage.value.clientHeight
  console.log(oriW)
  // 等比例 最小 0.3  最大 2倍
  const newWidth = Math.min(Math.max(event.target.offsetWidth * 0.3, oriW * scaleFactor), event.target.offsetWidth * 2)
  const newHeight = Math.min(Math.max(event.target.offsetHeight * 0.3, oriH * scaleFactor), event.target.offsetHeight * 2)

  zoomImage.value.style.width = `${newWidth}px`
  zoomImage.value.style.height = `${newHeight}px`
}
/// 放大
function zoomUpClick() {
  /// 点击放大
  const oriW = zoomImage.value.clientWidth
  /// 获取当前 大小
  const upDeta = 0.3
  let delta = oriW / zoomOriWH.w
  delta += upDeta
  if (delta >= 2) {
    zoomImage.value.style.width = `${zoomOriWH.w * 2}px`
    zoomImage.value.style.height = `${zoomOriWH.h * 2}px`
  }
  else {
    zoomImage.value.style.width = `${zoomOriWH.w * delta}px`
    zoomImage.value.style.height = `${zoomOriWH.h * delta}px`
  }
}

function zoomDownClick() {
  const oriW = zoomImage.value.clientWidth
  /// 获取当前 大小
  const upDeta = 0.3
  let delta = oriW / zoomOriWH.w
  delta -= upDeta
  if (delta <= 0.3) {
    zoomImage.value.style.width = `${zoomOriWH.w * 0.3}px`
    zoomImage.value.style.height = `${zoomOriWH.h * 0.3}px`
  }
  else {
    zoomImage.value.style.width = `${zoomOriWH.w * delta}px`
    zoomImage.value.style.height = `${zoomOriWH.h * delta}px`
  }
}

function dragging(event) {
  // dragItem.value.style.opacity = 1
  const newLeft = event.clientX - offsetX.value
  const newTop = event.clientY - offsetY.value
  // console.log(event)
  // 更新拖动元素的位置
  event.target.style.left = `${newLeft}px`
  event.target.style.top = `${newTop}px`
}

function addBtnClick() {
  console.log(subjectInfo)
  if (subjectInfo.value?.status === 3) {
    window.$message.warning('试验已中止，不可新增数据')
    return
  }
  if (subjectInfo.value?.status === 0) {
    window.$message.warning('试验未开始，不可新增数据')
    return
  }
  if (topicInfo.value?.status === 2 || topicInfo.value?.status === 3) {
    // 不能新增
    window.$message.warning(`课题${statusOptions[topicInfo.value?.status].label},不可新增数据`)
    return
  }

  if (showModalOptions.type === 'edit') {
    editTheScaleData(manualData.value, null)
  }
  else {
    showModalOptions.showAdd = true
    /// 有些选项 要默认值
    newScaleData.answers?.forEach((item) => {
      // console.log(item)
      switch (item?.scaleName) {
        case '眼科检查':
          item.content = '无长效干扰素使用禁忌'
          break
        case '精神评估':
          item.content = '无长效干扰素使用禁忌'
          break
        case '心电图':
          item.content = '正常'
          break
        case '生物样本留取':
          if (item.questionTitle === '留取类型')
            item.hidden = true

          break
        default:
          break
      }
    })
  }
}
const cantEditColor = computed(() => {
  if (topicInfo.value?.status === 2 || topicInfo.value?.status === 3 || subjectInfo.value.status === 3 || subjectInfo.value.status === 0)
    return '#ccc'
  return undefined
})

///  modal输入框 监听
function modalInputChange(item) {
  /// 如果是 生命体征 需要计算BMI
  console.log(item)
  /// 当前是 生命体征
  if (item?.scaleName === '生命体征' && (item?.questionTitle === '体重' || item?.questionTitle === '身高')) {
    /// 监听身高体重, 计算bmi
    let bmiItem
    let tzItem
    let sgItem
    newScaleData.answers?.forEach((anitem) => {
      if (anitem.questionTitle === 'BMI')
        bmiItem = anitem
      if (anitem.questionTitle === '体重')
        tzItem = anitem
      if (anitem.questionTitle === '身高')
        sgItem = anitem
    })
    if (tzItem.content && sgItem.content)
      bmiItem.content = (Number(tzItem.content) / (Number(sgItem.content) / 100) ** 2).toFixed(2)
    else
      bmiItem.content = ''
  }
}

function modalSelectClick(item, opt) {
  item.content = opt.textContent
  console.log(item)
  if (item.scaleName === '生物样本留取' && item.questionTitle === '是否留取') {
    const last = newScaleData.answers[newScaleData.answers?.length - 1]
    last.hidden = item.content === '否'
    if (item.content === '否') {
      last.content = null
      last.contentKey = null
    }
  }
}
/// 处理多选
function checkBoxSelects(value, item) {
  // console.log(value)

  item.contentKey = value?.join(',')
  const labels = []
  item.options?.forEach((element) => {
    if (value.includes(element.optionId))
      labels.push(element.textContent)
  })
  item.content = labels.join(',')
  console.log(item)
  // value?.forEach((optItem)=>{

  // })
}
</script>

<template>
  <div class="labWrapper" h-full overflow-auto pb-14px>
    <n-button :color="cantEditColor" mb-14px type="primary" @click="addBtnClick">
      {{ showModalOptions.type === 'add' ? '新 增' : '编 辑' }}
    </n-button>
    <el-table
      ref="tableRef" max-height="360px" stripe flexible :data="columnsData" table-layout="auto"
      highlight-current-row scrollbar-always-on
      @current-change="handleCurrentRowSelect"
    >
      <el-table-column show-overflow-tooltip fixed label="项目名称">
        <template #default="{ row }">
          {{ `${row.questionTitle}${row.medicineUnit ? (`(${row.medicineUnit})`) : ''}` }}
        </template>
      </el-table-column>
      <el-table-column v-if="tableData == null || tableData?.length === 0" />
      <el-table-column v-for="(item, index) in tableData" :key="item.reportTime" show-overflow-tooltip>
        <template #header>
          <div
            style="text-align: center;
              display: flex;
              justify-content: center;
              align-items: center;
              white-space: break-spaces;line-height: normal;font-weight: 500;"
          >
            <span>{{ `${item?.reportTime ? dayjs(item?.reportTime).format('YYYY-MM-DD') : ''}(${item?.viewPoint})` }}</span>
            <img
              v-if="item?.reportFile" h-30px w-30px src="@/assets/images/subject_report.png"
              @click="checkReportLook"
            >
            <Teleport v-if="showDragItem" to="body">
              <div
                id="dragItem" class="drag-preview" draggable="true" @dragover.prevent @dragstart="startDrag"
                @dragend="stopDrag" @drag="dragging"
              >
                <div flex cursor-pointer items-center justify-between>
                  <div flex items-center>
                    <img src="@/assets/svg/dragable.svg" h-16px>
                    <span ml-5px>报告单</span>
                  </div>
                  <div flex items-center>
                    <div flex items-center color="#3B8FD9" @click="zoomUpClick">
                      <img h-16px src="@/assets/svg/zoom_up.svg">
                      <span ml-5px>放大</span>
                    </div>
                    <div color="#3B8FD9" ml-20px flex items-center @click="zoomDownClick">
                      <img h-16px src="@/assets/svg/zoom_down.svg">
                      <span ml-5px>缩小</span>
                    </div>
                    <div ml-20px p-5px @click="showDragItem = false">
                      <img src="@/assets/svg/drag_close.svg" h-12px>
                    </div>
                  </div>
                </div>
                <div class="preview-image" bg="#f5f5f5" mt-14px h-full flex-1 overflow-auto>
                  <img id="zoomImage" src="@/assets/images/home.png">
                </div>
              </div>
            </Teleport>
          </div>
        </template>
        <template #default="{ $index }">
          <div cursor-pointer :color="item?.answers[$index]?.abnormalFlag ? '#f36969' : '#333'" text-center @dblclick="editTheScaleData(item, $index)">
            <span>
              {{ item?.answers[$index]?.content || '-' }}
            </span>
            <span v-if="item?.answers[$index]?.abnormalFlag" color="#F36969">
              {{ item?.answers[$index]?.abnormalFlag }}
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="tableData?.length === 0">
        <template #header>
          <div
            style="text-align: center;
              white-space: break-spaces;line-height: normal;font-weight: 500;"
          >
            日期
          </div>
        </template>
        <template #default="scope">
          <div text-center>
            -
          </div>
        </template>
      </el-table-column>
      <template #empty>
        <div h-240px flex items-center justify-center>
          <DataEmpty />
        </div>
      </template>
    </el-table>
    <div v-if="currentScaleItem?.medicineType === 'INSPECT'">
      <SectionTitle class="mt-20px -mb-20px">
        {{ `${currentScaleItem?.questionTitle}趋势图` + `${currentScaleItem?.medicineUnit ? (` (单位: ${currentScaleItem?.medicineUnit})`) : ''}` }}
      </SectionTitle>
      <div v-if="yAixIsNoData" h-260px flex justify-center pt-80px>
        <DataEmpty />
      </div>
      <div v-else v-once ref="lineRef" class="h-320px" />
    </div>
    <BasicModal
      :min-height="60"
      :visible="showModalOptions.showAdd" :title="`${showModalOptions.type === 'add' ? '新增' : '编辑'}${questionInfo?.scaleName}记录`" width="560" :footer-offset="120"
      @ok="handleBodyConfirm" @visible-change="visibleChange"
    >
      <el-form ref="formEl" mt-14px label-width="110" :rules="addRules" label-placement="right" :model="newScaleData">
        <el-form-item v-if="questionInfo?.scaleName !== '干扰素' && questionInfo?.scaleName !== '24周疗效评估'" prop="reportDate" label="报告日期" ml-20px>
          <el-date-picker
            v-model="newScaleData.reportDate" style="width:360px;" type="date" placeholder="请选择"
            value-format="YYYY-MM-DD"
            :disabled-date="disabledDate"
            :teleported="false"
            :autofocus="false"
          />
        </el-form-item>
        <el-form-item v-for="(item, index) in newScaleData.answers" :key="index" :style="{ visibility: item.hidden ? 'hidden' : 'visible' }" :label="item.questionTitle" ml-20px>
          <el-input v-if="item.questionType === 'text'" v-model="item.content" style="width:360px;" placeholder="请输入" @input="() => { modalInputChange(item) }">
            <template #suffix>
              {{ item.medicineUnit || '' }}
            </template>
          </el-input>
          <el-date-picker
            v-if="item.questionType === 'dateTime'"
            v-model="item.content" style="width:360px;" type="date" placeholder="请选择"
            value-format="YYYY-MM-DD"
            :teleported="false"
          />
          <el-select
            v-if="item.questionType === 'radio'"
            v-model="item.contentKey"
            placeholder="请选择"
            style="width: 360px"
            :teleported="false"
            clearable
          >
            <el-option v-for="(opt, opindex) in item.options" :key="opindex" :label="opt.textContent" :value="opt.optionId" @click="modalSelectClick(item, opt)" />
          </el-select>
          <el-select
            v-if="item.questionType === 'checkbox'"
            v-model="item.contentKeys"
            multiple
            placeholder="请选择"
            style="width: 360px"
            :teleported="false"
            clearable
            @change="(value) => {
              checkBoxSelects(value, item)
            }"
          >
            <el-option v-for="(opt, opindex) in item.options" :key="opindex" :label="opt.textContent" :value="opt.optionId" />
          </el-select>
        </el-form-item>
      </el-form>
    </BasicModal>
  </div>
</template>

<style scoped lang="scss">
.hint_svg:hover {
  opacity: 50%;
}

:deep(.current-row) {
  td.el-table__cell {
    background-color: #FFE1CB !important;
  }
}

:deep(.el-table__body) {
  padding-bottom: 10px;
}

.drag-preview {
  position: absolute;
  padding: 14px;
  padding-top: 10px;
  display: flex;
  flex-direction: column;
  z-index: 9999;
  width: 560px;
  height: 300px;
  background: #ffffff;
  box-shadow: 0px 0px 4px 0px rgba(0, 0, 0, 0.30);

  .preview-image {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  #zoomImage {
    user-select: none;
    -webkit-user-drag: none;
    pointer-events: none;
  }
}

:deep(.el-form-item__label) {
  line-height: 16px;
  align-items: center;
}
</style>
