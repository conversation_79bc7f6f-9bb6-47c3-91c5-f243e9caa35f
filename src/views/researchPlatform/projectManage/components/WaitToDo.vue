<script setup lang='ts'>
import dayjs from 'dayjs'
import { storeToRefs } from 'pinia'
import { tipTheWaitToDo } from '~/src/api/research'
import { useAuthStore, useReseacherStore } from '~/src/store'

const emits = defineEmits(['goFinishJob', 'goGanYu'])

const { waitToDo } = storeToRefs(useReseacherStore())
const { userInfo } = storeToRefs(useAuthStore())
const { subjectInfo } = storeToRefs(useReseacherStore())

const statusIcon = reactive({
  START: {
    color: '#FF9B54',
    label: '未完成',
  },
  UNFINISHED: {
    color: '#FF9B54',
    label: '逾期未完成',
  },
})

const btns = reactive({
  ONE: [
    {
      label: '去完成',
      click: (item) => {
        goToDoTheJob(item)
      },
    },
    {
      label: '提醒受试者',
      click: (item) => {
        handleTheTip(item)
      },
    },
  ],
  TWO: [
    {
      label: '去完成',
      click: (item) => {
        goToDoTheJob(item)
      },
    },
    {
      label: '查看',
      click: (item) => {
        /// 去干预措施
        goToGyCs(item)
      },
    },
    {
      label: '再次提醒',
      click: (item) => {
        handleTheTip(item)
      },
    },
  ],
})

/// 去完成
function goToDoTheJob(item) {
  emits('goFinishJob', item)
}

/// 去查看
function goToGyCs(item) {
  emits('goGanYu', item)
}
/// 提醒
function handleTheTip(item) {
  //
  if (item?.reminderTimes >= 3) {
    window.$message.warning('每天最多提醒三次')
    return
  }

  tipTheWaitToDo({
    taskId: item.taskId,
    handlersId: userInfo.value?.id,
    handlersName: userInfo.value?.userName,
  }).then((res) => {
    console.log(res)
    window.$message.success('已提醒受试者')
    if (res?.data) {
      useReseacherStore().getWaitToDoList({
        examNo: item?.examNo,
        topicId: item?.topicId,
      })
    }
  })
}

onMounted(() => {
  console.log(subjectInfo.value)
})
</script>

<template>
  <div class="page">
    <el-table stripe :data="waitToDo" mt-14px>
      <el-table-column label="序号" width="100" type="index" header-align="center" align="center" />
      <el-table-column label="任务名称" :formatter="({ scaleName }) => scaleName || '-'" />
      <el-table-column label="任务节点" width="300">
        <template #default="{ row }">
          {{ `${row?.viewPoint}(${row?.titleType}): ${dayjs(row?.startTime).format('YYYY-MM-DD')} ~ ${dayjs(row?.endTime).format("YYYY-MM-DD")}` }}
        </template>
      </el-table-column>
      <el-table-column label="任务状态">
        <template #default="{ row }">
          <div flex items-center>
            <div h-6px w-6px rounded-3px :style="{ background: statusIcon[row.taskStatus]?.color }" />
            <div ml-5px>
              {{ statusIcon[row.taskStatus]?.label }}
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作" fixed="right">
        <template #default="{ row }">
          <div v-if="subjectInfo?.status !== 3" flex items-center gap-5px>
            <div v-for="(item, index) in btns[row?.reminderTimes === 0 ? 'ONE' : 'TWO']" :key="index" flex cursor-pointer items-center color="#3B8FD9" @click="item.click(row)">
              <span>{{ item.label }}</span>
              <div v-if="index !== btns[row?.reminderTimes === 0 ? 'ONE' : 'TWO']?.length - 1" ml-6px h-14px w-1px class="bg-#3b8fd9" />
            </div>
          </div>
          <div v-else>
            -
          </div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<style lang="scss" scoped>
.page{
  max-height: calc(100vh - 280px);
  overflow: auto;
}
</style>
