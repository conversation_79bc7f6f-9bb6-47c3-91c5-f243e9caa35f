<script setup lang='ts'>
import _ from 'lodash'

import { storeToRefs } from 'pinia'
import { getProcedureInfo, getScaleSimpleList, saveProcedure } from '@/api/research/index'
import { useReseacherStore } from '~/src/store'

const props = defineProps({
  topicId: {
    type: String,
  },
  edit: {
    type: Boolean,
    default: false,
  },
})

const emits = defineEmits(['updateDesign'])

const reseacher = useReseacherStore()
const { topicInfo } = storeToRefs(reseacher)

const gdOptions = [
  {
    scaleName: '访视点',
    text: (item) => {
      return item?.viewPoint || '-'
    },
    type: 'input',
  },
  {
    scaleName: '访视时间(周)',
    text: (item) => {
      return `${item?.visitTime}周`
    },
    type: 'input',
    suffix: '周',
  },
  {
    scaleName: '时间窗口(天)',
    text: (item) => {
      return `${item?.timeWindowScope + item?.timeWindowNumber}天`
    },
    type: 'addDown',
  },
]

const tableData = ref(gdOptions)
const topicId = ref(props.topicId || '')
const netData: any = reactive({})
const isEdit = ref(props.edit)

const fixedTableData = ref()

const groupTopColumns = ref()

/// 用于排序的规则
const orderNumber = {
  筛选期: 1,
  治疗期: 2,
  随访期: 3,
}
const formEl = ref()
const modalOptions: any = reactive({
  time: {
    title: '添加访视时间',
    forms: [
      {
        label: '评估步骤',
        path: 'pgbz',
        require: true,
        type: 'select',
        op_label: null,
        op_value: null,
        options: ['筛选期', '治疗期', '随访期'],
      },
      {
        label: '访视点',
        require: true,
        type: 'input',
        path: 'fsd',
        maxLength: 10,
      },
      {
        label: '访视时间',
        require: true,
        path: 'fssj',
        type: 'number',
        suffix: '周',
      },
      {
        label: '时间窗口',
        path: 'sjck',
        require: true,
        type: 'select-input',
        suffix: '天',
      },
    ],
  },
  crf: {
    title: '添加CRF量表',
    forms: [{
      label: 'CRF量表名',
      path: 'crf',
      require: true,
      type: 'select',
      op_label: 'scaleName',
      op_value: 'scaleId',
      options: [],
    }],
  },
})

const addRules = {
  pgbz: {
    required: true,
    message: '内容不能为空',
    trigger: ['blur', 'change'],
  },
  fsd: {
    required: true,
    message: '内容不能为空',
    trigger: ['blur', 'change'],
  },
  fssj: {
    required: true,
    message: '内容不能为空',
    trigger: ['blur'],
  },
  sjck: {
    required: true,
    message: '内容不能为空',
    trigger: ['blur'],
  },
  crf: {
    required: true,
    message: '内容不能为空',
    trigger: ['blur'],
  },

}

const currentModal = reactive({
  options: modalOptions.time,
  show: false,
  values: {
    crf: '',
    pgbz: '',
    fsd: '',
    fssj: '',
    sjck: '',
    sjcz: '±',
    crfName: '',
  },
})

function handleTheRowDats() {
  const tableDatas = []
  /// 选中数据
  const xz = {}
  netData.selects.forEach((item) => {
    const sz = xz[item.leftId]
    if (!sz)
      xz[item.leftId] = [item.topId]
    else
      sz.push(item.topId)
  })
  /// 行数据
  netData.lefts.forEach((item) => {
    tableDatas.push({
      scaleName: item.scaleName,
      leftId: item.id,
      topicId: item.topicId,
      selectColumn: xz[item.id],
      text: () => {
        return ''
      },
      type: 'checkbox',
    })
  })

  // console.log(tableDatas)
  tableData.value = gdOptions
  tableData.value = tableData.value.concat(tableDatas)
}

function changeRowSelect(check, row, item) {
  console.log(check)
  /// 去变更 数据
  if (check && !row.selectColumn?.includes(item.id)) {
    if (row.selectColumn)
      row.selectColumn.push(item.id)
    else
      row.selectColumn = [item.id]
  }
  else if (row.selectColumn?.includes(item.id) && !check) {
    // 删除掉
    row.selectColumn.splice(row.selectColumn.indexOf(item.id), 1)
  }
  // handleTheSelectLefts()   可以提交的时候再遍历
}
/// 处理选中个数据, 映射到 网络数据, 方便提交
function handleTheSelectLefts() {
  const selectsTemp: any = []
  for (let index = 3; index < tableData.value.length; index++) {
    const row = tableData.value[index]
    if (row.selectColumn) {
      row.selectColumn.forEach((col) => {
        selectsTemp.push({
          topicId: row.topicId,
          topId: col,
          leftId: row.leftId,
        })
      })
    }
  }
  console.log(selectsTemp)
  return selectsTemp
}

/// 添加访视时间
function addFollowTimeClick() {
  currentModal.options = modalOptions.time
  currentModal.show = true
}

/// 添加CRF量表
function addCRFClick() {
  currentModal.options = modalOptions.crf
  currentModal.show = true
}

function visibleChange(v) {
  if (!v) {
    currentModal.show = false
    currentModal.values = { sjcz: '±' }
  }
}

/// modal 提交保存
function handleBodyConfirm() {
  // const mid = _.uniqueId()
  // 时间戳id
  formEl.value.validate((valid, fields) => {
    if (valid) {
      const mid = `${Date.now().toString()}000000`
      if (currentModal.options.title === '添加访视时间') {
        const topItem = {
          id: mid,
          topicId: topicId.value,
          titleType: currentModal.values.pgbz,
          viewPoint: currentModal.values.fsd,
          visitTime: currentModal.values.fssj,
          timeWindowScope: currentModal.values.sjcz,
          timeWindowNumber: currentModal.values.sjck,
        }
        if (netData?.tops) {
          /// 先插入 在排序
          netData.tops.push(topItem)
          netData.tops = _.sortBy(netData.tops, (t) => {
            return orderNumber[t.titleType]
          })
          /// 把他们分好
          // groupTopColumns.value = _.groupBy(netData.tops, 'titleType')
          // console.log(groupTopColumns.value)
        }
        else {
          netData.tops = [topItem]
        }
      }
      else {
        const leftItem = {
          id: mid,
          scaleId: currentModal.values.crf,
          topicId: topicId.value,
          scaleName: currentModal.values.crfName,
        }

        /// 查看是否已有此量表
        const tindex = _.findIndex(netData.lefts, (o) => { return o.scaleName === currentModal.values.crfName })
        if (tindex >= 0) {
          window.$message.error('已有此CRF量表')
          return
        }

        if (netData?.lefts)
          netData.lefts.push(leftItem)
        else
          netData.lefts = [leftItem]
        tableData.value.push({
          scaleName: currentModal.values.crfName,
          leftId: mid,
          topicId: topicId.value,
          selectColumn: [],
          type: 'checkbox',
        })
      }
      /// dismiss
      visibleChange(false)
    }
  })
}

/// 获取试验设计信息
function getDesignInfo() {
  getProcedureInfo(topicId.value).then((res) => {
    console.log(res)

    Object.assign(netData, res?.data || {})
    groupTopColumns.value = _.groupBy(netData.tops, 'titleType')
    console.log(groupTopColumns.value)
    handleTheRowDats()
  })
}

/// 取消编辑
function cancelEditClick() {
  isEdit.value = false
  /// 编辑后的 东西洞不要 重新, 刷新
  getDesignInfo()
}

/// 删除项目
function deleteTheRowOrColumnItem(isRow, item, index) {
  window.$dialog.warning({
    title: '确定删除该项吗？',
    btnGhost: true,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      /// 删除 row
      if (isRow) {
        netData.lefts.splice(index - 3, 1) /// 索引减去固定的3个
        tableData.value.splice(index, 1)
      }
      else {
        netData.tops.splice(index, 1)
        // /// 先删除 在排序
        // netData.tops = _.sortBy(netData.tops, (t) => {
        //   return orderNumber[t.titleType]
        // })
      }
    },
  })
}

/// 提交实验流程
function commitTheDesignClick(designStatus) {
  /// 处理一下那个 选中的项
  const selects = handleTheSelectLefts()
  netData.selects = selects
  console.log(netData)
  saveProcedure({ ...netData, topicId: topicId.value, designStatus }).then((res) => {
    console.log(res)
    if (res?.data) {
      /// 退出编辑
      isEdit.value = false
      window.$message.success(designStatus === 1 ? '暂存成功' : '提交成功')

      /// 再请求下好了
      getDesignInfo()
      emits('updateDesign')
    }
    else {
      window.$message.error('保存失败')
    }
  })
}

/// 获取量表名字
function getScaleNameList(query?: any) {
  getScaleSimpleList().then((res) => {
    modalOptions.crf.forms[0].options = res?.data || []
  })
}

function sbCRFClick(item) {
  ///  什么玩意儿
  if (currentModal.options.title === '添加CRF量表')
    currentModal.values.crfName = item.scaleName
}

onMounted(() => {
  getDesignInfo()
  /// 量表
  getScaleNameList()
})
</script>

<template>
  <div p-14px>
    <div v-if="netData?.designStatus" mb-14px flex items-center>
      <span color="#666" mr-5px>试验流程状态:</span>
      <div v-if="netData?.designStatus === 2" flex items-center>
        <div class="bg-#4ACFB1" h-8px w-8px rounded-4px />
        <div ml-6px>
          已提交
        </div>
      </div>
      <div v-else flex items-center>
        <div class="bg-#FF9B54" h-8px w-8px rounded-4px />
        <div ml-6px>
          已暂存
        </div>
      </div>
    </div>
    <div flex>
      <el-table v-if="!isEdit" max-height="530" scrollbar-always-on :data="tableData" border style="width: 100%">
        <el-table-column fixed header-align="center" prop="scaleName" label="评估步骤" width="200" />
        <el-table-column
          v-for="(items, index) in groupTopColumns"
          :key="index" header-align="center" align="center"
          :label="items[0].titleType"
        >
          <el-table-column v-for="(top, tindex) in items" :key="tindex" align="center" :label="top.titleType" width="200">
            <template #default="scope">
              <div v-if="scope.$index < 3">
                {{ scope.row.text(top) }}
              </div>
              <div v-else flex justify-center>
                <img
                  v-if="scope.row?.selectColumn?.includes(top.id)" h-16px w-16px
                  src="@/assets/svg-icon/project-design-check.svg"
                >
                <span v-else>-</span>
              </div>
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
      <el-table v-else :data="tableData" border style="width: 100%">
        <el-table-column fixed header-align="center" label="评估步骤" width="200">
          <template #default="{ row, $index }">
            <div flex items-center justify-between px-5px>
              <div>{{ row.scaleName }}</div>
              <div v-if="$index > 2" p-4px @click="deleteTheRowOrColumnItem(true, row, $index)">
                <img h-14px w-14px src="@/assets/svg-icon/project-delete.svg">
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          v-for="(item, index) in netData.tops" :key="index" header-align="center" align="center"
          width="200" :label="item.titleType"
        >
          <template #header>
            <div flex items-center justify-between>
              <el-select v-model="item.titleType">
                <el-option
                  v-for="(mitem, mindex) in modalOptions.time.forms[0].options" :key="mindex" :label="mitem"
                  :value="mitem"
                />
              </el-select>
              <div ml-8px p-4px @click="deleteTheRowOrColumnItem(false, item, index)">
                <img h-14px w-14px src="@/assets/svg-icon/project-delete.svg">
              </div>
            </div>
          </template>
          <template #default="{ row, $index }">
            <div flex justify-center>
              <el-input v-if="$index === 0" v-model="item.viewPoint" placeholder="请输入" />
              <!-- <el-input-number v-if="$index === 1" v-model="item.visitTime" type="number" placeholder="请输入">
                <template #suffix>
                  周
                </template>
              </el-input-number> -->
              <n-input-number
                v-if="$index === 1"
                v-model:value="item.visitTime" :show-button="false" placeholder="请输入"
                style="text-align: left"
                clearable :max="1000"
              >
                <template #suffix>
                  <span color="#999">周</span>
                </template>
              </n-input-number>
              <!-- <el-input v-if="$index === 2" v-model="item.timeWindowNumber" class="input-with-select" placeholder="请输入">
                <template #prepend>
                  <el-select v-model="item.timeWindowScope" placeholder="选择" style="width: 60px">
                    <el-option label="+" value="+" />
                    <el-option label="-" value="-" />
                    <el-option label="±" value="±" />
                  </el-select>
                </template>
                <template #suffix>
                  天
                </template>
              </el-input> -->
              <n-input-number
                v-if="$index === 2" v-model:value="item.timeWindowNumber"
                class="input-number" :show-button="false" placeholder="请输入"
                clearable :max="1000"
              >
                <template #prefix>
                  <el-select v-model="item.timeWindowScope" placeholder="请选择" style="width: 60px">
                    <el-option label="+" value="+" />
                    <el-option label="-" value="-" />
                    <el-option label="±" value="±" />
                  </el-select>
                </template>
                <template #suffix>
                  <span color="#999">天</span>
                </template>
              </n-input-number>
              <n-checkbox
                v-if="$index > 2" :checked="row?.selectColumn?.includes(item.id)" @update:checked="(check) => {
                  changeRowSelect(check, row, item)
                }"
              />
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div
        v-if="isEdit"
        flex-col cursor-pointer justify-center p-13px style="background: #ecf6ff;
border: 1px dashed #198eeb;" @click="addFollowTimeClick"
      >
        <img h-14px w-14px src="@/assets/svg-icon/project-design-add.svg">
        <div mt-10px style="line-height: 16px;width: min-content;" text-14px color="#3B8FD9">
          访视时间设置
        </div>
      </div>
    </div>
    <div
      v-if="isEdit"
      class="addCrf" flex items-center justify-center py-13px style="background: #ecf6ff;
border: 1px dashed #198eeb;" @click="addCRFClick"
    >
      <img h-14px w-14px src="@/assets/svg-icon/project-design-add.svg">
      <div ml-10px text-14px color="#3B8FD9">
        添加CRF量单
      </div>
    </div>
    <div v-if="(topicInfo?.status === 0 || topicInfo.status === -1) && reseacher.getPower(topicInfo?.director)" flex items-center justify-center py-24px>
      <n-button v-if="isEdit" type="primary" @click="commitTheDesignClick(2)">
        提 交
      </n-button>
      <n-button v-if="isEdit" type="primary" ghost ml-20px @click="commitTheDesignClick(1)">
        暂 存
      </n-button>
      <n-button v-if="isEdit" ml-20px @click="cancelEditClick">
        取消编辑
      </n-button>
      <n-button v-else type="primary" ghost ml-20px @click="isEdit = true">
        编 辑
      </n-button>
    </div>
    <BasicModal
      :visible="currentModal.show" :title="currentModal.options.title" width="560" :footer-offset="120"
      @ok="handleBodyConfirm" @visible-change="visibleChange"
    >
      <el-form
        ref="formEl"
        label-width="100" :rules="addRules" label-placement="left" class="mb-4px mt-24px"
        :model="currentModal.values"
      >
        <el-form-item
          v-for="(item, index) in currentModal.options.forms" :key="index" :prop="item.path"
          :label="item.label" ml-20px
        >
          <el-select
            v-if="item.type === 'select'" v-model="currentModal.values[item.path]" placeholder="请选择"
            filterable
            style="width: 380px"
          >
            <el-option
              v-for="opt in item.options" :label="item.op_label ? opt[item.op_label] : opt"
              :value="item.op_value ? opt[item.op_value] : opt" @click="sbCRFClick(opt)"
            />
          </el-select>
          <el-input
            v-if="item.type === 'input'" v-model="currentModal.values[item.path]" placeholder="请输入"
            style="width: 380px" clearable :maxlength="item.maxLength" show-word-limit
          >
            <template #suffix>
              {{ item.suffix }}
            </template>
          </el-input>
          <n-input-number
            v-if="item.type === 'number'"
            v-model:value="currentModal.values[item.path]" :show-button="false" placeholder="请输入"
            style="width: 380px" clearable :max="1000"
          >
            <template #suffix>
              <span color="#999">{{ item.suffix }}</span>
            </template>
          </n-input-number>
          <n-input-number
            v-if="item.type === 'select-input'"
            v-model:value="currentModal.values[item.path]"
            class="input-number" :show-button="false" placeholder="请输入"
            style="width: 380px" clearable :max="1000"
          >
            <template #prefix>
              <el-select v-model="currentModal.values.sjcz" placeholder="请选择" style="width: 88px">
                <el-option label="+" value="+" />
                <el-option label="-" value="-" />
                <el-option label="±" value="±" />
              </el-select>
            </template>
            <template #suffix>
              <span color="#999">{{ item.suffix }}</span>
            </template>
          </n-input-number>
        </el-form-item>
      </el-form>
    </BasicModal>
  </div>
</template>

<style lang="scss" scoped>
.addCrf {
  width: calc(100% - 42px);
  cursor: pointer;
}

:deep(.is-group) tr:last-of-type {
  display: none
}

:deep(.el-input-group__prepend) {
  background-color: #fff;

  .el-select .el-input .el-input__wrapper {
    box-shadow: 1px 0 0 0 var(--el-input-border-color) inset, 0 1px 0 0 var#fff inset, 0 -1px 0 0 #fff inset
  }
}

.input-number{
  :deep(.n-input-wrapper){
    padding-left: 0;
  }
}
</style>
