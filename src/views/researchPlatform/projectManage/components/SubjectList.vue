<script setup lang='ts'>
import { NPopover, NTooltip } from 'wowjoy-vui'
import dayjs from 'dayjs'
import utc from 'dayjs/plugin/utc'
import timezone from 'dayjs/plugin/timezone'
import { storeToRefs } from 'pinia'
import { formartPhoneNumberMiss } from '~/src/utils/common/format'
import { SvgIcon } from '~/src/components/Icon'
import { sexIcon } from '@/utils/common'
import { useAuthStore, useReseacherStore } from '~/src/store'

import {
  deleteSubject,
  editSubject,
  exportPatientList,
  exportPatients,
  getSubjectGroupList,
  getSubjectList,
  getSubjectResourceList,
  stopSubject,
} from '@/api/research'

const props = defineProps({
  topicId: {
    type: String,
  },
})

dayjs.extend(utc)
dayjs.extend(timezone)
const router = useRouter()
const route = useRoute()

const resourceOptions = ref([])
const reseacherStore = useReseacherStore()
const { topicInfo } = storeToRefs(reseacherStore)

const groupOptions = ref([])
// const weeks24Options = [{ label: '有效', value: '有效' }, { label: '部分有效', value: '部分有效' }, { label: '无效', value: '无效' }]
const { userInfo } = useAuthStore()
const formFolder = reactive({
  subjectName: '',
  subjectfrom: null,
  subjectResearch: '',
})
const tableData = ref([])
const showModel = ref({
  showFinishModel: false,
  finishRow: null,
  showDeleteModel: false,
  deleteRow: null,
})
const pageForm = ref({
  page: 1,
  size: 10,
  total: 0,
})

// 导入模块
const showImport = ref(false)
const importTable = ref()
const countLabel = ref()
const importPatientKeys = ref([])
const patientTotal = ref(0)
const paginationImport = reactive({
  start: 1,
  pageSize: 10,
  pageSizes: [10, 20, 50, 100],
})
/// 导入患者loading
const loadingImport = ref(false)
const importSearch = reactive({
  name: '',
  phone: '',
})

function statusChange(status: number) {
  switch (status) {
    case 0:
      return ['待开始', '#45A8E6']
    case 1:
      return ['进行中', '#FF9B54']
    case 2:
      return ['已完成', '#4ACFB1']
    case 3:
      return ['已中止', '#CCCCCC']
    default:
      return '#45A8E6'
  }
}

const statusTip = {
  '2': '已完成',
  '-1': '未发布',
  '3': '已中止',
}

function addNewSubject() {
  if (getTopicEditStatus()) {
    router.push({
      path: '/researcher/projectManage/subject/addSubject',
      query: {
        topicId: route.query?.topicId as string,
      },
    })
  }
  else { window.$message.error(`课题${statusTip[`${topicInfo.value?.status}`]}，不可新增受试者`) }
}

function finishHandler() {
  console.log(showModel.value.finishRow)
  stopSubject(showModel.value.finishRow?.id).then((res) => {
    console.log('删除====', res)
    if (res.data) {
      window.$message.success('中止成功')
      showModel.value.finishRow = null
      getSubjectListData()
    }
    else { window.$message.error('中止失败') }
  })
}

function deleteHandler() {
  console.log(showModel.value.deleteRow)
  deleteSubject(showModel.value.deleteRow?.id).then((res) => {
    console.log('删除====', res)
    if (res.data) {
      window.$message.success('删除成功')
      showModel.value.deleteRow = null
      getSubjectListData()
    }
    else { window.$message.error('删除失败') }
  })
}

function getSubjectListData() {
  const params = {
    name: formFolder.subjectName,
    patientSource: formFolder.subjectfrom,
    researcher: formFolder.subjectResearch,
    topicId: route.query?.topicId as string, // 课题 id,
    start: pageForm.value.page,
    size: pageForm.value.size,
  }
  getSubjectList(params).then((res: any) => {
    console.log('list===', res)
    tableData.value = res.data?.records
    pageForm.value.total = Number(res.data?.total)
    handleTheDefaultHistoryInfo()
  })
  reseacherStore.getInfo(props.topicId)
}

function resetSelection(type: string) {
  if (type === 'list') {
    formFolder.subjectName = ''
    formFolder.subjectfrom = null
    formFolder.subjectResearch = ''
    getSubjectListData()
  }
  else {
    importSearch.name = ''
    importSearch.phone = ''
    searchChange()
  }
}

function detailHandler(row: any) {
  console.log('detailrow====', row)
  if (getSubjectEditStatus(row)) {
    router.push({
      path: '/researcher/projectManage/subjectDetail',
      query: {
        id: row.id,
        topicId: route.query?.topicId as string,
        examNo: row?.examNo,
        type: row.status === 0 ? 'detail' : 'scale',
      },
    })
  }
}

function handleEditBtnClick(row: any) {
  console.log('editrow====', row)
  router.push({
    path: '/researcher/projectManage/subjectDetail',
    query: {
      id: row.id,
      topicId: route.query?.topicId as string,
      examNo: row?.examNo,
      type: 'edit',
    },
  })
}

function getSubjectResourceListData() {
  getSubjectResourceList().then((res) => {
    console.log('resource====', res)
    resourceOptions.value = res.data?.map((item) => {
      return { label: item, value: item }
    })
  })

  getSubjectGroupList(route.query?.topicId as string).then((res) => {
    console.log('resource====', res)
    groupOptions.value = res.data?.map((item) => {
      return { label: item.name, value: item.id }
    })
  })
}

function renderOption({ node, option }: { node: VNode; option: SelectOption }) {
  return h(NTooltip, null, {
    trigger: () => node,
    default: () => `${option.label}`,
  })
}

///  确定导入
async function handleConfirmImport() {
  /// 导入患者到随访计划
  /// 菊花旋转
  loadingImport.value = true
  const res = await exportPatients(
    userInfo?.rsUserId || userInfo.id,
    route.query?.topicId as string,
    importPatientKeys.value,
  )
  loadingImport.value = false
  if (res.data) {
    window.$message.success('导入成功')
    getSubjectListData()
    showImport.value = false
  }
}

/// 导入患者
function createPatientColumns() {
  return [
    {
      type: 'selection',
      key: 'id',
      width: 30,
    },
    {
      title: '患者信息',
      key: 'index',
      width: 115,
      render(row: any) {
        return h('div', { class: 'flex items-center' }, [
          h('div', null, `${row.patientName} `),
          h(SvgIcon, {
            class: 'mx-3px',
            localIcon: sexIcon(row.sexName),
            size: 16,
            minWidth: 16,
          }),
          h('div', null, ` ${row.age ?? '-'}`),
        ])
      },
    },
    {
      title: '手机号',
      key: 'phone',
      width: 80,
      render(row: any) {
        if (!row.phone)
          return h('div', {}, '-')
        return h(
          NPopover,
          {
            trigger: 'hover',
          },
          {
            trigger: () => [
              h(
                'div',
                {
                  style: {
                    cursor: 'pointer',
                  },
                  onClick: () => {
                    row.checkPhone = !row.checkPhone
                  },
                },
                row.checkPhone
                  ? row.phone
                  : formartPhoneNumberMiss(row.phone) ?? '-',
              ),
            ],
            default: () => [
              h(
                'div',
                null,
                row.checkPhone
                  ? '点击隐藏手机号'
                  : '点击显示完整手机号',
              ),
            ],
          },
        )
      },
    },
    {
      title: '当前诊断',
      key: 'diagnose',
      width: 100,
      render(row: any) {
        return h('div', {}, row.diagnose ?? '-')
      },
    },
    {
      title: '就诊科室',
      key: 'treatDeptName',
      width: 90,
      render(row: any) {
        return h('div', {}, row.treatDeptName ?? '-')
      },
    },
    {
      title: '最近一次就诊',
      key: 'treatTime',
      width: 100,
      render(row: any) {
        return h('div', {}, row.treatTime ?? '-')
      },
    },
  ]
}

/// 导入搜索触发
function searchChange() {
  /// 导入
  importTable.value.fetch({ page: 1, size: 10, ...importSearch, start: 1 })
}

/// 查询导入患者列表
async function loadPatients(res: { size: number; start: number }) {
  //  queryFollowPlanPatients().then({
  console.log(importSearch)
  const response = await exportPatientList({
    topicId: route.query?.topicId as string,
    name: importSearch.name,
    phone: importSearch.phone,
    ...{
      size: res.size,
      start: res.start,
    },
  })

  patientTotal.value = Number(response.data?.total)

  return response
}

/// 更新患者
function handleUpdateValue(data: any) {
  // 编辑保存
  editSubject(data).then((res) => {
    console.log(res)
    if (res.data)
      window.$message.success('更新成功!')
    else
      window.$message.success('更新失败!')
  })
}

function exportData() {
  if (getTopicEditStatus())
    showImport.value = !showImport.value
  else
    window.$message.error(`课题${statusTip[`${topicInfo.value?.status}`]}，不可新增受试者`)
}

/// 获取课题状态
function getTopicEditStatus() {
  return topicInfo.value?.status === 0 || topicInfo.value?.status === 1
}

function otherHospital() {
  return userInfo.organName === '树兰（杭州）医院'
}

/// 获取课题当前研究员可否编辑
function getSubjectEditStatus(row: any) {
  // 逻辑: 负责人可以编辑所有受试者, 研究员只能编辑自己新增的受试者
  const topicManagers = topicInfo.value?.director
  const currentManagers = userInfo?.userName
  if ((topicInfo.value?.isAdmin && topicInfo.value?.isAdmin === true) || topicManagers === currentManagers)
    return true
  else if (row.researcher === currentManagers)
    return true
  else
    return false
}

onMounted(() => {
  getSubjectResourceListData()
  getSubjectListData()
})

onActivated(() => {
  console.log('------重新加载了')
  getSubjectListData()
})

/// 处理一下课题应该默认的病史信息
function handleTheDefaultHistoryInfo() {
  /// 取第一个受试者的 病史为默认病史
  // if (pageForm.value.page === 1 && tableData.value?.length > 0) {
  //   const first = tableData.value[0]
  //   const history = JSON.parse(first?.diseaseHistory || '{}')
  //   if (history.question101 && history.question101?.length > 0)
  //     reseacherStore.changeSubjectMhistory('1')
  //   else
  //     reseacherStore.changeSubjectMhistory('0')
  // }
  // else if (pageForm.value.page === 1 && tableData.value?.length === 0) {
  //   reseacherStore.changeSubjectMhistory('0')
  // }
}
</script>

<template>
  <div class="bg-content">
    <div mt-10px flex>
      <n-button v-if="otherHospital()" ghost :color="getTopicEditStatus() ? '#06AEA6' : '#bbb'" mr-10px @click="exportData">
        数据库同步
      </n-button>
      <n-button ghost :color="getTopicEditStatus() ? '#06AEA6' : '#bbb'" mr-20px @click="addNewSubject">
        手动添加
      </n-button>

      <div flex-1 />

      <div mr-20px flex>
        <div w-60px line-height-32px>
          受试者
        </div>
        <n-input v-model:value="formFolder.subjectName" w-120px type="text" placeholder="请输入" />
      </div>

      <div mr-20px flex>
        <div mr-10px line-height-32px>
          受试者来源
        </div>
        <n-select v-model:value="formFolder.subjectfrom" :render-option="renderOption" placeholder="请选择" :options="resourceOptions" w-160px>
          <template #arrow>
            <div>
              <img w-16px src="@/assets/svg-icon/normal_arrow_down.svg">
            </div>
          </template>
        </n-select>
      </div>

      <div mr-20px flex>
        <div w-60px line-height-32px>
          研究员
        </div>
        <n-input v-model:value="formFolder.subjectResearch" w-120px type="text" placeholder="请输入" />
      </div>

      <n-button color="#06AEA6" mr-10px @click="getSubjectListData">
        查询
      </n-button>
      <n-button ghost @click="resetSelection('list')">
        重置
      </n-button>
    </div>

    <el-table :data="tableData" mt-14px @row-dblclick="detailHandler">
      <el-table-column label="序号" type="index" width="55" />
      <el-table-column label="受试者" :formatter="({ name }) => name || '-'">
        <template #default="{ row }">
          <div flex>
            <span>{{ row.name }}</span>
            <NTooltip v-if="row.dataStatus === 1" trigger="hover">
              <template #trigger>
                <img src="@/assets/images/icon-hint.png" ml-5px mt-3px h-16px w-16px>
              </template>
              请完善该受试者信息！
            </NTooltip>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="试验状态">
        <template #default="{ row }">
          <div flex>
            <div mr-5px mt-8px h-6px w-6px rounded :style="{ backgroundColor: statusChange(row.status)[1] }" />
            <span>{{ statusChange(row.status)[0] }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column label="入组时间" :formatter="({ testStartTime }) => testStartTime || '-'" width="100">
        <template #default="{ row }">
          <div>
            <!-- row.createTime ? dayjs(row.createTime).tz('Asia/Shanghai').format('YYYY-MM-DD') : '-'  -->
            {{ row.testStartTime ? row.testStartTime.substring(0, 10) : '-' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="受试者观察号" :formatter="({ examNo }) => examNo || '-'" width="170" />
      <el-table-column label="当前试验阶段" min-width="110" :formatter="({ currentExamStageName }) => currentExamStageName || '-'" />
      <el-table-column label="数据完成率" width="100">
        <template #default="{ row }">
          <span color="#06AEA6">{{ row.dataRate ? `${(row.dataRate * 100).toFixed(0)}%` : '0%' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="试验分组" min-width="170">
        <template #default="{ row }">
          <!-- <div v-if="row.status === 1 && getSubjectEditStatus(row)">
            <n-select v-model:value="row.examGroup" placeholder="请选择" :options="groupOptions" @update:value="handleUpdateValue(row)" />
          </div> -->
          <div>
            {{ row.examGroup || '-' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="24周疗效评估" :formatter="({ efficacyEvaluation }) => efficacyEvaluation || '-'" width="120" />

      <!-- <el-table-column label="24周疗效评估" min-width="120">
        <template #default="{ row }">
          <div v-if="row.status === 1 && getSubjectEditStatus(row)">
            <n-select v-model:value="row.efficacyEvaluation" placeholder="请选择" :options="weeks24Options" @update:value="handleUpdateValue(row)" />
          </div>
          <div v-else>
            {{ row.efficacyEvaluation || '-' }}
          </div>
        </template>
      </el-table-column> -->
      <el-table-column label="受试者来源" min-width="210" :formatter="({ patientSource }) => patientSource || '-'" />
      <el-table-column label="研究员" :formatter="({ researcher }) => researcher || '-'" />
      <el-table-column label="操作" fixed="right" width="95">
        <template #default="{ row }">
          <div v-if="!getSubjectEditStatus(row)" uno-link>
            -
          </div>
          <div v-else-if="row.status === 0" flex items-center gap-6px>
            <div
              uno-link @click="handleEditBtnClick(row)"
            >
              编辑
            </div>
            <div h-14px w-1px bg="#3B8FD9" />
            <div
              uno-link @click="() => {
                showModel.showDeleteModel = true
                showModel.deleteRow = row
              }"
            >
              删除
            </div>
          </div>
          <div v-else-if="row.status === 1" flex items-center gap-6px>
            <div
              uno-link @click="detailHandler(row)"
            >
              查看
            </div>
            <div h-14px w-1px bg="#3B8FD9" />
            <div
              uno-link @click="() => {
                showModel.showFinishModel = true
                showModel.finishRow = row
              }"
            >
              中止
            </div>
          </div>
          <div v-else>
            <div uno-link @click="detailHandler(row)">
              查看
            </div>
          </div>
        </template>
      </el-table-column>
      <template #empty>
        <div flex justify-center>
          <DataEmpty />
        </div>
      </template>
    </el-table>
    <div v-if="pageForm.total > 0" mt-20px w-full flex justify-end>
      <n-pagination
        v-model:page="pageForm.page"
        v-model:page-size="pageForm.size"
        :item-count="pageForm.total"
        :page-sizes="[5, 10, 20, 30]"
        show-size-picker
        show-quick-jumper
        @update:page="getSubjectListData"
        @update:page-size="() => {
          pageForm.page = 1
          getSubjectListData()
        }"
      />
    </div>

    <n-modal
      v-model:show="showModel.showFinishModel" :mask-closable="false" preset="dialog" title="温馨提示!"
      content="一旦中止，不可恢复，请谨慎操作！" positive-text="继续中止" negative-text="取 消" @positive-click="finishHandler"
      @negative-click="() => {
        showModel.showFinishModel = false
        showModel.finishRow = null
      }"
    />

    <n-modal
      v-model:show="showModel.showDeleteModel" :mask-closable="false" preset="dialog" title="温馨提示!"
      content="确认删除受试者？" positive-text="继续删除" negative-text="取 消" @positive-click="deleteHandler" @negative-click="() => {
        showModel.showDeleteModel = false
        showModel.deleteRow = null
      }"
    />

    <BasicModal
      :visible="showImport"
      title="导入患者"
      :height="480"
      :loading="loadingImport"
      width="1000"
      @ok="handleConfirmImport"
    >
      <div mb-14px mt-24px flex flex-col px-20px>
        <div flex items-center>
          <div mr-20px flex items-center>
            <div mr-10px w-75px style="color: #666">
              患者姓名
            </div>
            <n-input v-model:value="importSearch.name" />
          </div>
          <div mr-20px flex items-center>
            <div mr-10px w-55px style="color: #666">
              手机号
            </div>
            <n-input v-model:value="importSearch.phone" />
          </div>
          <n-button color="#06AEA6" mr-10px @click="searchChange">
            查询
          </n-button>
          <n-button ghost @click="resetSelection('import')">
            重置
          </n-button>
        </div>
        <BasicTable
          ref="importTable"
          v-model:checked-row-keys="importPatientKeys"
          class="table"
          max-height="330"
          :columns="createPatientColumns()"
          :request="loadPatients"
          :row-key="(row:any) => row.id"
          :pagination="paginationImport"
          striped
          mt-14px
        />
        <div ref="countLabel" class="import-count" :class="patientTotal > 0 ? 'bottom-20px' : ' top-10px'" relative w-100px>
          已选 {{ importPatientKeys.length }} 人
        </div>
      </div>
    </BasicModal>
  </div>
</template>

<style lang="scss" scoped>
.bg-content {
  padding: 14px;
}
:deep(.n-base-selection .n-base-suffix .n-base-suffix__arrow){
  width: 14px !important;
}
</style>
