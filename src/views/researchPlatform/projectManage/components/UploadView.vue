<script setup lang="ts">
import { ProjectApi, deleteTheFileWithId, getFilesListWithScaleId } from '~/src/api/research'
import fileImage from '@/assets/svg/file_jpg_png.svg'
import fileWord from '@/assets/svg/word.svg'
import filePdf from '@/assets/svg/pdf.svg'
import fileExcel from '@/assets/svg/excel.svg'
import filePpt from '@/assets/svg/ppt.svg'
import fileUnKnow from '@/assets/svg/normal_file.svg'

const props = defineProps({
  uploadId: {
    type: String,
    default: '',
  },
  uploadType: {
    type: String,
    default: 'RS_TOPIC_FILE',
  },
})

const emit = defineEmits(['loadData'])
const uploadList: any = ref([])
watch(() => props.uploadId, () => {
  if (props.uploadId.length > 0)
    getFileWithNodeId(props.uploadId)
}, { immediate: true })

function getFileWithNodeId(id) {
  getFilesListWithScaleId(id, props.uploadType).then((res) => {
    console.log(res)
    uploadList.value = res?.data?.reverse()
    emit('loadData', uploadList.value)
  })
}

function fileIcon(type: string) {
  if (type?.includes('image'))
    return fileImage
  else if (type?.includes('wordprocessingml'))
    return fileWord
  else if (type?.includes('spreadsheetml') || type?.includes('excel'))
    return fileExcel
  else if (type?.includes('pdf'))
    return filePdf
  else if (type?.includes('presentationml'))
    return filePpt
  else return fileUnKnow
}

function deleteTheFile(item) {
  /// 删除
  console.log(item)

  deleteTheFileWithId(item.id).then((res) => {
    if (res?.data) {
      window.$message.success('删除成功')
      getFileWithNodeId(item.superId)
    }
  })
}

function handleExceed() {
  window.$message.error('超出10个数量限制')
}
</script>

<template>
  <div w-full flex-col :style="{ marginTop: uploadType === 'RS_TOPIC_FILE' ? '14px' : '0px' }">
    <div flex items-center>
      <div v-if="uploadType === 'RS_TOPIC_FILE'" w-80px flex items-center self-start justify-end>
        <div color="#666" mr-10px mt-10px text-14px>
          附件
        </div>
      </div>
      <el-upload
        v-model:file-list="uploadList"
        :show-file-list="false"
        :action="`${ProjectApi.uploadTheFileWithNodeId + uploadId}/${uploadType}`"
        multiple
        :on-success="() => { getFileWithNodeId(uploadId) }"
        :on-exceed="handleExceed"
        :limit="10"
      >
        <n-button type="primary" ghost class="mr-10px">
          点击上传
        </n-button>
      </el-upload>
      <div flex items-center>
        <img src="@/assets/svg-icon/warning-tip.svg" h-16px w-16px>
        <span ml-10px text-14px color="#666">请上传相关附件，支持.word、.ppt、.pptx、.xlsx、.xls、.jpg、.pdf等，最多上传10个</span>
      </div>
    </div>
    <div
      v-if="uploadList?.length > 0"
      class="bg-#f5f5f5"
      mt-8px
      w-full
      style="
                display: grid;
                grid-template-columns: repeat(2, 1fr);
                row-gap: 14px;
                column-gap: 30px;
            "
      :style="{ marginLeft: uploadType === 'RS_TOPIC_FILE' ? '80px' : '0' }"
      p-14px
    >
      <div
        v-for="(item, index) in uploadList"
        :key="index"
        class="upload-item"
      >
        <img w-30px :src="fileIcon(item.contentType)">
        <div
          ml-10px style="overflow: hidden;
    text-overflow: ellipsis;
    line-height: 22px;
    max-height: 44px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;"
        >
          {{ item.fileName || item.name || "未知名称" }}
        </div>
        <img
          ml-10px
          w-16px
          src="@/assets/svg-icon/project-delete.svg"
          @click="deleteTheFile(item)"
        >
      </div>
    </div>
  </div>
</template>

<style scoped>
.upload-item {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #333333;
    line-height: 20px;
}
</style>
