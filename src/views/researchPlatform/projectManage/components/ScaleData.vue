<script setup lang="ts">
import dayjs from 'dayjs'
import { storeToRefs } from 'pinia'
import LabProject2 from './LabProject2.vue'
import finish from '@/assets/svg/subject_finish.svg'
import wait from '@/assets/svg/subject_wait.svg'
import progress from '@/assets/svg/subject_doing.svg'
import inComplete from '@/assets/svg/subject_error.svg'
import completeOt from '@/assets/svg/subject_complete_out.svg'
import fileImage from '@/assets/svg/file_jpg_png.svg'
import fileWord from '@/assets/svg/word.svg'
import filePdf from '@/assets/svg/pdf.svg'
import fileExcel from '@/assets/svg/excel.svg'
import filePpt from '@/assets/svg/ppt.svg'
import fileUnKnow from '@/assets/svg/normal_file.svg'

import { useReseacherStore } from '~/src/store'
import { ProjectApi, deleteTheFileWithId, getFilesListWithScaleId, getVisitPlan, handelNodeTaskDelete, uploadTheFilesWithScaleData } from '~/src/api/research'

const props = defineProps({
  topicId: {
    type: String,
    default: null,
  },
  examNo: {
    type: String,
    default: null,
  },
  nodeId: { /// 默认选中的上面的 访视点
    type: String,
    default: null,
  },
  taskId: { /// / 默然选中的某一项检查
    type: String,
    default: null,
  },
})
const sbAdd = ref(false)
const fileInput = ref()
const route = useRoute()
const subjectId = (route.query?.id || '') as string
const { subjectInfo } = storeToRefs(useReseacherStore())
const loadingUpload = ref(false)
const currentNode = ref()
const currentExamineeNodeId = ref()
const selectRef = ref()
const activeScaleIndex = ref('upload')
const statusIcon = reactive({
  FINISHED: {
    icon: finish,
    label: '已完成',
  },
  WAIT: {
    icon: wait,
    label: '待开始',
  },
  START: {
    icon: progress,
    label: '未完成',
  },
  UNFINISHED: {
    icon: inComplete,
    label: '逾期未完成',
  },
  OVERTIME_FINISHED: {
    icon: completeOt,
    label: '逾期完成',
  },
})

const nodeStatus = reactive({
  OFF: 'status-999',
  NOW: 'status-6ea',
  ON: 'status-333',
  已逾期: 'status-999',
})

const visitDatas = ref()
const visitNodeTasks = ref([])
// const tableColumns = ref()

function getVisitData(nodeId?: any, taskId?: any) {
  if (props.topicId != null && props.examNo != null) {
    getVisitPlan({
      topicId: props?.topicId?.toString(),
      examNo: props?.examNo,
    }).then((res) => {
      // console.log(res)
      visitDatas.value = res?.data || []

      /// / 特殊处理一下 V7 V8 访视点

      specialHandleTheNode()
      /// 默认选中第一个
      nextTick(() => {
        if (nodeId === null)
          selectTheFirstNode()
        else
          selectTheIdNode(nodeId, taskId)
      })
    })
  }
}

/// 选中
function selectTheNode(item, e) {
  /// 未开始的节点不能选择
  if (item?.nodeStatus === 'OFF')
    return
  if (item?.tag === 'special')
    return

  if (selectRef.value)
    selectRef.value?.classList.toggle('scale-node-item-active')

  selectRef.value = e.currentTarget
  selectRef.value?.classList.add('scale-node-item-active')
  // console.log(e)

  // console.log(activeScale)
  /// 默认第一个
  visitNodeTasks.value = item?.nodeTasks || []
  nextTick(() => {
    if (visitNodeTasks.value?.length > 0) {
      activeScaleSelect(0)
      currentExamineeNodeId.value = item.examineeNodeId
      getFileWithNodeId(item.examineeNodeId)
      activeScaleIndex.value = 'upload'
    }
  })
}

function selectTheFirstNode() {
  const elements = document.getElementsByClassName('scale-node-item')
  selectTheNode(visitDatas.value[0], { currentTarget: elements[0] })
}

/// 选中指定id 的node
function selectTheIdNode(nodeId, taskId) {
  let map = {}
  let ind
  visitDatas.value?.forEach((item, index) => {
    if (item?.examineeNodeId === nodeId) {
      map = item
      ind = index
    }
  })
  const elements = document.getElementsByClassName('scale-node-item')
  selectTheNode(map, { currentTarget: elements[ind] })
  let knd
  visitNodeTasks.value?.forEach((item, index) => {
    if (item?.taskId === taskId)
      knd = index
  })
  nextTick(() => {
    activeScaleSelect(knd ?? 0)
    /// 然后点击新增
    sbAdd.value = true
  })
}

function activeScaleSelect(index) {
  /// 查询获取 量表数据
  activeScaleIndex.value = index
  // activeScale.value = visitNodeTasks.value[index]?.scaleId
  console.log(index)
  if (visitNodeTasks.value?.length > 0)
    currentNode.value = visitNodeTasks.value[index]
}

onMounted(() => {
  // document.getElementsByClassName()
  // el-upload__input
  // getVisitData()
  document.getElementById('fileInput')?.addEventListener('change', (event) => {
    console.log(event.target.files)
  })
  setTimeout(() => {
    if (visitDatas.value?.length > 0)
      window.$message.warning('请及时上传节点下原始报告', { duration: 600 })
  }, 800)
})

const uploadUrl = computed(() => {
  return ProjectApi.uploadTheFileWithNodeId + currentExamineeNodeId.value
})

function fileIcon(type: string) {
  if (type?.includes('image'))
    return fileImage

  else if (type?.includes('wordprocessingml'))
    return fileWord

  else if (type?.includes('spreadsheetml') || type?.includes('excel'))
    return fileExcel

  else if (type?.includes('pdf'))
    return filePdf
  else if (type?.includes('presentationml'))
    return filePpt
  else
    return fileUnKnow
}

watch(
  () => props.examNo,
  () => {
    getVisitData(null)
  },
  { immediate: true },
)

/**
 * 上传文件(可批量)
 */
// const uploadingFilesArray = ref([])
// function uploadFiles(options) {
//   console.log(options)
//   uploadingFilesArray.value.push(options.file)
//   options.files = uploadingFilesArray.value
//   uploadTheFilesWithScaleData(options)
// }

const uploadFileListMap = reactive({})

function handleExceed() {
  window.$message.error('最多同时上传五个')
}
/// 成功上传
function successUpLoad(response, uploadFile, UploadFiles) {
  console.log(UploadFiles)
  /// 上传结果,
  // uploadFile?.forEach((element) => {
  // })
  getFileWithNodeId(currentExamineeNodeId.value)
}

/// 开始上传
// function beforeUpload(rawFile) {
//   // console.log(rawFile)
//   // if (uploadFileListMap[currentExamineeNodeId.value]) {
//   //   uploadFileListMap[currentExamineeNodeId.value].push({
//   //     fileName: rawFile.name,
//   //     contentType: rawFile.type,
//   //   })
//   // }
//   // else {
//   //   const temp: any = []
//   //   temp.push({
//   //     fileName: rawFile.name,
//   //     contentType: rawFile.type,
//   //   })
//   //   uploadFileListMap[currentExamineeNodeId.value] = temp
//   // }
//   loadingUpload.value = true
//   return true
// }

/// 获取已经上传的数据
function getFileWithNodeId(id) {
  getFilesListWithScaleId(id, 'RS_PATIENT_DATA').then((res) => {
    uploadFileListMap[currentExamineeNodeId.value] = res?.data
  })
}

function fileLook(item) {
  /// 查看
  // downloadFileWithId(item.id).then((res) => {

  // })
  window.open(ProjectApi.previewFile + item.id, '_blank')
}

/// 上传失败
function uploadFalied(error, uploadFile, uploadFiles) {
  window.$message.error(`${uploadFile?.name}上传失败${error}`)
}

function deleteTheFile(item) {
  /// 删除
  console.log(item)

  deleteTheFileWithId(item.id).then((res) => {
    if (res?.data) {
      window.$message.success('删除成功')
      getFileWithNodeId(item.superId)
    }
  })
}

function refreshNodeData() {
  /// 节点可能有变化, 需要刷新数据
  getVisitData(currentExamineeNodeId.value, currentNode.value.taskId)
}

/// 点击上传
function startUpLoad() {
  fileInput.value.click()
}
function uploadFileChange(e) {
  // console.log(e)
  if (e.target?.files?.length > 0) {
    /// 开始上传
    loadingUpload.value = true
    console.log(loadingUpload.value)
    uploadTheFilesWithScaleData({
      nodeId: currentExamineeNodeId.value,
      data: e.target?.files,
    }, 'RS_PATIENT_DATA').then((res) => {
      console.log(res)
      loadingUpload.value = false
      getFileWithNodeId(currentExamineeNodeId.value)
    }).catch((err) => {
      loadingUpload.value = false
      getFileWithNodeId(currentExamineeNodeId.value)
      window.$message.error(`上传失败${err}`)
    })
  }
}

defineExpose({
  selectTheIdNode,
})

/**
 * 特别处理, V7和V8
 */
function specialHandleTheNode() {
  /// 查看患者分组
  if (subjectInfo.value?.examGroup === '空白对照组' || subjectInfo.value?.examGroup === '益生菌治疗组') {
    const tempNodes = []
    visitDatas.value?.forEach((item) => {
      if (item.viewPoint === 'V7' || item.viewPoint === 'V8' || item.viewPoint === 'V9') {
        tempNodes.push(item?.examineeNodeId ?? '')
        item.nodeTasks = []
        item.tag = 'special'
      }
    })

    /// 那么 V7 V8 不需要检查
    if (tempNodes?.length > 0) {
      handelNodeTaskDelete({
        examNo: props?.examNo,
        nodeIds: tempNodes,
        topicId: props.topicId,
      }).then((res) => {
      // getVisitData()
      })
    }
  }
}

// watch(uploadFileListMap, () => {
//   if (activeScaleIndex.value === 'upload') {
//     /// 原始报告
//     if (uploadFileListMap[currentExamineeNodeId.value]?.length === 0)
//       window.$message.warning('请及时上传该节点下原始报告')
//   }
// }, { immediate: true })
</script>

<template>
  <div class="page">
    <div v-if="visitDatas?.length > 0">
      <div flex items-center style="gap: 0 10px; overflow-x: auto">
        <div
          v-for="(item, index) in visitDatas" :key="index" class="scale-node-item"
          :class="nodeStatus[item?.nodeStatus]" flex-col @click="(e) => {
            selectTheNode(item, e);
          }
          "
        >
          <div ml-5px flex items-center text-16px>
            <span font-500>{{ item?.viewPoint || "" }}</span>
            <div ml-10px flex items-center>
              <span v-if="!item?.finishedTime">～</span>
              <div ml-4px>
                <div v-if="item?.startTime">
                  {{
                    dayjs(item?.startTime).format(
                      "YYYY-MM-DD",
                    )
                  }}
                </div>
                <div v-if="item?.endTime">
                  {{
                    dayjs(item?.endTime).format(
                      "YYYY-MM-DD",
                    )
                  }}
                </div>
              </div>
            </div>
          </div>
          <div mt-8px h-20px flex items-center>
            <img v-if="item?.nodeStatus === 'ON'" w-10px src="@/assets/svg-icon/scale_item_success.svg">
            <img v-if="item?.nodeStatus === 'NOW'" w-10px src="@/assets/svg-icon/scale_item_now.svg">
            <div v-if="item?.nodeStatus === 'OFF'" h-10px w-10px rounded-5px bg="#CCCCCC" />
            <div ml-5px h-2px w-full class="bg-#ccc" />
            <img
              v-if="index === visitDatas?.length - 1" ml-2px h-20px
              src="@/assets/svg-icon/project-progress-arrow.svg"
            >
          </div>
          <div v-if="item.tag === 'special'" style="line-height: 20px;" class="scale-progress-item">
            {{ `受试者试验分组为${subjectInfo.examGroup}，无需进行该访视点。` }}
          </div>
          <div v-else class="scale-progress-item" mt-8px px-2px py-5px>
            <div v-for="(citem, cindex) in item?.nodeTasks" :key="cindex" my-5px flex items-center>
              <span mr-6px>○</span>
              <span text-14px>{{
                citem?.scaleName || "-"
              }}</span>
              <el-tooltip effect="dark" :content="statusIcon[citem?.taskStatus].label" placement="top">
                <img :src="statusIcon[citem?.taskStatus].icon" ml-6px h-14px>
              </el-tooltip>
            </div>
          </div>
        </div>
      </div>
      <div>
        <el-tabs :model-value="activeScaleIndex" @tab-change="activeScaleSelect">
          <el-tab-pane name="upload" label="原始报告">
            <div>
              <!-- <el-upload
                :show-file-list="false" class="upload-demo" :action="uploadUrl" multiple
                :on-success="successUpLoad" :on-exceed="handleExceed" :before-upload="beforeUpload"
                :on-error="uploadFalied"
                :on-change="uploadFileChange"
              >
                <n-button type="primary" ghost @click="startUpLoad">
                  上传
                </n-button>
              </el-upload> -->
              <n-button type="primary" ghost @click="startUpLoad">
                上传
                <input
                  ref="fileInput" style="display: none;" name="file" type="file" multiple
                  @change="uploadFileChange"
                >
              </n-button>
              <div
                v-loading="loadingUpload"
                element-loading-text="上传中..." mt-14px flex-wrap style="gap:14px;display: flex"
              >
                <div v-for="(item, index) in uploadFileListMap[currentExamineeNodeId]" :key="index" class="file-item">
                  <div h-144px w-130px flex items-center justify-center bg="#fff">
                    <img
                      v-if="item.contentType?.includes('image')" style="max-width: 110px;max-height: 126px;"
                      object-cover :src="ProjectApi.previewFile + item.id"
                    >
                    <img v-else w-56px :src="fileIcon(item.contentType)">
                  </div>
                  <div color="#333" mt-11px px-14px text-14px>
                    {{ item.fileName || '未知名称' }}
                  </div>
                  <div class="file-item-mask">
                    <img w-24px src="@/assets/svg-icon/file-look.svg" @click="fileLook(item)">
                    <img ml-20px w-24px src="@/assets/svg-icon/file-delete.svg" @click="deleteTheFile(item)">
                  </div>
                </div>
                <div v-if="uploadFileListMap[currentExamineeNodeId]?.length === 0" h-300px w-full flex items-center justify-center>
                  <DataEmpty show-text="暂无文件, 请及时上传原始报告" />
                </div>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane v-for="(item, index) in visitNodeTasks" :key="index" :name="index" :label="item?.scaleName" />
        </el-tabs>
        <LabProject2
          v-if="activeScaleIndex !== 'upload'"
          :show-add="sbAdd" :exam-no="subjectInfo?.examNo"
          :topic-id="subjectInfo?.topicId" :scale-id="currentNode?.scaleId || ''" :task-id="currentNode?.taskId || ''"
          :examinee-node-id="currentExamineeNodeId"
          @add-success="refreshNodeData"
        />
      </div>
    </div>
    <DataEmpty v-else show-text="暂无数据" h-400px flex-col items-center justify-center />
  </div>
</template>

<style lang="scss" scoped>
.page {
  padding: 14px;
  max-height: calc(100vh - 270px);
  overflow: auto;
}

.scale-node-item {
  padding: 8px 5px;
  border: 1px solid #fff;

  &:hover {
    background: #edf1f6;
    border: 1px solid #c7cfd9;
    border-radius: 3px;
  }

  &:active {
    background: #edf1f6;
    border: 1px solid #c7cfd9;
    border-radius: 3px;
  }
}

.scale-node-item-active {
  padding: 8px 5px;
  background: #edf1f6;
  border: 1px solid #c7cfd9;
  border-radius: 3px;
}

.scale-progress-item {
  // background: #f6f8fa;
  border-radius: 5px;
  overflow: hidden;
  min-width: 190px;
  height: 82px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;

  // opacity: 0.7;
  // color:#999;
  // cursor: pointer;
}

.status-999 {
  color: "#999";

  .scale-progress-item {
    color: #999 !important;
  }
}

.status-333 {
  color: #333;

  .scale-progress-item {
    color: #666 !important;
  }
}

.status-6ea {
  color: #06aea6;

  .scale-progress-item {
    color: #666 !important;
  }
}

:deep(.el-tabs__item.is-active) {
  color: #06aea6;
}

:deep(.el-tabs__item) {
  min-width: 82px;

  &:hover {
    color: #06aea6;
  }
}

:deep(.el-tabs__active-bar) {
  background-color: #06aea6;
  // min-width: 82px !important;
}

.file-item {
  width: 180px;
  height: 240px;
  // opacity: 0.2;
  background: rgba(165, 184, 209, 0.2);
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 14px;
  position: relative;
  line-height: 22px;
}

.file-item-mask {
  background: #000000;
  border-radius: 6px;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;

  &:hover {
    opacity: 0.4;
  }

}

:deep(.el-tabs__nav-prev){
  font-size: 23px;
  line-height: 50px;
}
:deep(.el-tabs__nav-next){
  font-size: 23px;
  line-height: 50px;
}

/* 移除自定义滚动条样式 */

/* 或者调整滚动条伪元素的样式 */
::-webkit-scrollbar-track {
    background: #ccc; /* 或其他背景色 */
    border-radius: 5px; /* 或其他圆角 */
}

::-webkit-scrollbar-thumb {
    background: #ccc; /* 或其他颜色 */
    border-radius: 10px; /* 或其他圆角 */
    // display: none;
}
</style>
