<!-- eslint-disable vue/no-mutating-props -->
<script setup lang='ts'>
import type { FormInst } from 'wowjoy-vui'

const props = defineProps({
  formValue: {
    type: Object,
    default: () => {
      return {}
    },
  },
  rules: {
    type: Object,
    default: () => {
      return {}
    },
  },
  edit: {
    type: Boolean,
    default: true,
  },
})
const emit = defineEmits(['validateSuccess'])
const formRef = ref<FormInst | null>(null)
const medicalOptions = [
  {
    title: '是否持续口服抗病毒治疗至少1年，且至今未停药？',
    options: [
      { value: '否', label: '否' },
      { value: '是', label: '是' },
    ],
    type: 'radio',
  },
  {
    title: '既往干扰素治疗结束至今已超过2年？',
    options: [
      { value: '否', label: '否' },
      { value: '是', label: '是' },
      { value: '从未使用干扰素', label: '从未使用干扰素' },
    ],
    type: 'radio',
  },
  {
    title: '近3月内有无急性感染或者使用抗生素治疗的病史？',
    options: [
      '有急性感染，如发烧，肺炎',
      '有抗生素治疗',
      '以上均无',
    ],
    type: 'check',
  },
  {
    title: '有无合并感染以下病毒？',
    options: [
      '甲肝病毒',
      '丙肝病毒',
      '戊肝病毒',
      'EB病毒',
      '巨细胞病毒',
      '以上均无',
    ],
    type: 'check',
  },
  {
    title: '是否患者遗传或者代谢性肝病，如威尔逊氏病或血色沉着病？',
    options: [
      { value: '否', label: '否' },
      { value: '是', label: '是' },
    ],
    type: 'radio',
  },
  {
    title: '自生免疫性疾病',
    options: [
      { value: '无', label: '无' },
      { value: '有', label: '有' },
    ],
    type: 'radio',
  },
  {
    title: '糖尿病',
    options: [
      { value: '无', label: '无' },
      { value: '有', label: '有' },
    ],
    type: 'radio',
  },
  {
    title: '高血压',
    options: [
      { value: '无', label: '无' },
      { value: '有', label: '有' },
    ],
    type: 'radio',
  },
  {
    title: '严重的心脑血管疾病',
    options: [
      { value: '无', label: '无' },
      { value: '有', label: '有' },
    ],
    type: 'radio',
  },
  {
    title: '吸毒',
    options: [
      { value: '无', label: '无' },
      { value: '有', label: '有' },
    ],
    type: 'radio',
  },
  {
    title: '精神病史(如抑郁症)',
    options: [
      { value: '无', label: '无' },
      { value: '有', label: '有' },
    ],
    type: 'radio',
  },
]

function showInconformHint(index, answer) {
  switch (index) {
    case 0:
    case 1:
      return answer === '否'
    case 3:
      return answer && !answer.includes('以上均无')
    case 4:
      return answer === '是'
    case 5:
    case 8:
    case 9:
    case 10:
      return answer === '有'
    default:
      return false
  }
}

function validate() {
  formRef.value?.validate((errors) => {
    if (!errors) {
      // window.$message.success('valid')
      emit('validateSuccess')
    }
    else {
      console.log(errors)
      window.$message.error('请完善病史信息')
    }
  })
}

function handleUpdateValue(key: string) {
  // 反选
  if (key && props.formValue) {
    const value = props.formValue.medicalHistory[key] || undefined
    if (value && value.length > 1 && value.includes('以上均无')) {
    // 判断表格是否包含以上均无,若有,则直接吧最后一个选项替换之前的数组即可
      const last = value[value.length - 1]
      props.formValue!.medicalHistory[key] = [last]
    }
  }
}

defineExpose({
  validate,
})
</script>

<template>
  <div mt-20px>
    <n-form
      ref="formRef"
      :model="formValue"
      label-placement="left"
      label-width="auto"
      label-align="left"
      :rules="rules"
      :disabled="!edit"
      require-mark-placement="left"
    >
      <n-form-item v-for="(item, index) in medicalOptions" :key="item.title" :label="item.title" :path="`medicalHistory.question${index + 1}`">
        <n-radio-group v-if="item.type === 'radio'" v-model:value="formValue.medicalHistory[`question${index + 1}`]" name="radiogroup">
          <n-space item-style="display: flex;" :size="[40, 0]">
            <n-radio v-for="option in item.options" :key="option.value" :value="option.value" mt-5px>
              {{ option.label }}
            </n-radio>
            <div
              v-if="showInconformHint(index, formValue.medicalHistory[`question${index + 1}`])" h-32px flex line-height-32px
            >
              <img class="add-img-hover" src="@/assets/images/icon-hint.png" mr-8px mt-8px h-16px w-16px>
              <span color="#333">该受试者不符合试验纳入标准</span>
            </div>
          </n-space>
        </n-radio-group>
        <n-checkbox-group v-else v-model:value="formValue.medicalHistory[`question${index + 1}`]" @update:value="handleUpdateValue(`question${index + 1}`)">
          <n-space item-style="display: flex;" :size="[40, 0]">
            <n-checkbox v-for="option in item.options" :key="option" :value="option">
              <div ml-10px>
                {{ option }}
              </div>
            </n-checkbox>
            <div
              v-if="showInconformHint(index, formValue.medicalHistory[`question${index + 1}`])"
              h-32px w-230px flex line-height-32px
            >
              <img class="add-img-hover" src="@/assets/images/icon-hint.png" mr-8px mt-8px h-16px w-16px>
              <span color="#333">该受试者不符合试验纳入标准</span>
            </div>
          </n-space>
        </n-checkbox-group>
      </n-form-item>
    </n-form>
  </div>
</template>

<style></style>
