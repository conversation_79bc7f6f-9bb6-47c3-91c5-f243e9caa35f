<script setup lang='ts'>
import { storeToRefs } from 'pinia'
import dayjs from 'dayjs'
import ScaleData from './components/ScaleData.vue'
import SubjectMessage from './components/SubjectMessage.vue'
import WaitToDo from './components/WaitToDo.vue'
import Intervention from './components/Intervention.vue'
import { sexIcon } from '@/utils/common'
import { Breadcrumb } from '@/layouts/common'
import { useReseacherStore } from '~/src/store'

const route = useRoute()

const currentPage = ref('scale')
const { subjectInfo, waitToDo } = storeToRefs(useReseacherStore())
const subjectId = (route.query?.id || '') as string
const topicId = (route.query?.topicId || '') as string
const examNo = (route.query?.examNo || '') as string
const scaleComponet = ref()

function statusChange(status: number) {
  switch (status) {
    case 0:
      return ['待开始', '#45A8E6']
    case 1:
      return ['进行中', '#FF9B54']
    case 2:
      return ['已完成', '#4ACFB1']
    case 3:
      return ['已中止', '#CCCCCC']
    default:
      return '#45A8E6'
  }
}

onMounted(() => {
  console.log(subjectId)
  breadListChange()
  useReseacherStore().getSubjectInfo(subjectId)
  useReseacherStore().getWaitToDoList({
    examNo,
    topicId,
  })
  const str = route.query?.type as string
  currentPage.value = str === 'detail' || str === 'edit' ? 'info' : 'scale'
  if (str === 'toDo')
    currentPage.value = 'toDo'
})

function jumpFinishScale(item) {
  console.log(item)
  currentPage.value = 'scale'
  setTimeout(() => {
    console.log(scaleComponet)
    scaleComponet.value?.selectTheIdNode(item.examineeNodeId, item.taskId)
  }, 700)
}

const breadList = ref([
  {
    title: '科研管理',
    link: null,
    key: 'researcher',
  },
  {
    title: '课题管理',
    link: '/',
    key: 'researcher_projectManage',
  },
  {
    title: '课题详情',
    link: `/?topicId=${route.query?.topicId}&currentTab=manage`,
    key: 'researcher_projectManage_detail',
  },
  {
    title: '受试者详情',
    link: null,
    key: 'researcher_projectManage_subjectDetail',
  },
])

function breadListChange() {
  /// 检测 href
  if (window.location.href.includes('subjectManage')) {
    breadList.value = [
      {
        title: '科研管理',
        link: null,
        key: 'researcher',
      },
      {
        title: '受试者管理',
        link: '/researcher/subjectManage',
        key: 'researcher_subjectManage',
      },
      {
        title: '受试者详情',
        link: null,
        key: 'researcher_subjectManage_subjectDetail',
      },
    ]
  }
}
</script>

<template>
  <div class="page" style="overflow: hidden;">
    <Breadcrumb
      :bread-list="breadList"
    />
    <div class="bg-#fff" m-14px flex-col flex-1>
      <div class="subject-info bg-#fff" rounded-6px p-14px>
        <div flex items-center style="min-width: 130px;">
          <span text-20px font-500 color="#333">{{ subjectInfo?.name || '-' }}</span>
          <SvgIcon
            class="mx-10px cursor-pointer" :local-icon="sexIcon(subjectInfo?.sexName)" size="16"
          />
          <div
            style="border: 1px solid #dcdcdc;border-radius: 2px;padding:4px 8px;color:#666;text-align: center;font-size: 12px;"
          >
            {{ subjectInfo?.age ? (`${subjectInfo?.age}岁`) : '-' }}
          </div>
        </div>
        <div mx-10px h-42px w-1px class="bg-#ccc" />
        <div>
          <div flex items-center gap-col-40px>
            <div w-200px text-14px>
              <span color="#666">试验状态: </span>
              <span>{{ statusChange(subjectInfo?.status)[0] || '-' }}</span>
            </div>
            <div w-240px text-14px>
              <span color="#666">试验分组: </span>
              <span>{{ subjectInfo?.examGroup || '-' }}</span>
            </div>
            <div text-14px>
              <span color="#666">24周疗效评估: </span>
              <span>{{ subjectInfo?.efficacyEvaluation || '-' }}</span>
            </div>
          </div>
          <div mt-14px flex items-center gap-col-40px>
            <div w-200px text-14px>
              <span color="#666">试验开始时间: </span>
              <span>{{ subjectInfo?.testStartTime ? dayjs(subjectInfo?.testStartTime).format('YYYY-MM-DD') || '-' : '-' }}</span>
            </div>
            <div w-240px text-14px>
              <span color="#666">研究员: </span>
              <span>{{ subjectInfo?.researcher || '-' }}</span>
            </div>
            <div flex items-center style="line-height: 24px;" text-14px>
              <div style="align-self: flex-start;" color="#666">
                关联项目:
              </div>
              <div flex-1>
                {{ subjectInfo?.topicName || '-' }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div h-14px w-full class="bg-#f6f9f8" />
      <n-tabs v-model:value="currentPage" type="card" tab-style="width: 124px;">
        <n-tab-pane name="toDo" :tab="`待办任务${waitToDo?.length > 0 ? `(${waitToDo?.length})` : ''}`">
          <WaitToDo @go-finish-job="jumpFinishScale" @go-gan-yu="currentPage = 'inter'" />
        </n-tab-pane>
        <n-tab-pane name="inter" tab="干预措施">
          <Intervention />
        </n-tab-pane>
        <n-tab-pane name="scale" tab="试验数据">
          <ScaleData ref="scaleComponet" :topic-id="subjectInfo?.topicId" :exam-no="subjectInfo?.examNo" />
        </n-tab-pane>
        <n-tab-pane name="info" tab="基础信息">
          <SubjectMessage />
        </n-tab-pane>
      </n-tabs>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.subject-info{
  display: flex;
  align-items: center;
}
</style>
