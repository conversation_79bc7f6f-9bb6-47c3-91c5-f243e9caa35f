<script setup lang="ts">
import type { FormInst } from 'wowjoy-vui'
import { storeToRefs } from 'pinia'
import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'
import _ from 'lodash'
import MedicalCustomHistory from '../components/MedicalCustomHistory.vue'
import MedicalHistory from '../components/MedicalHistory.vue'
import UploadView from '../components/UploadView.vue'
import { Breadcrumb } from '@/layouts/common'
import { onlyAllowNumber } from '@/utils/common/utils'
import { IDCardTypeOptions, countryOptions, marryOptions, nationOptions, wineOptions } from '@/utils/common'
import { addSubject, editSubject, generateFileId, getReseacherList, getSubjectDetail, getSubjectGroupList, getSubjectResourceList } from '@/api/research'
import { calculateAge, formatDate } from '@/utils/common/format'
import { useAuthStore, useReseacherStore } from '~/src/store'

const props = defineProps({
  addStatus: {
    type: Boolean,
    default: true,
  },
  subject: {
    type: Object,
    default: null,
  },
})
const emits = defineEmits(['cancleEdit'])
dayjs.extend(isBetween)
const reseacherStore = useReseacherStore()
const { topicInfo } = storeToRefs(reseacherStore)

const router = useRouter()
const route = useRoute()
const { userInfo } = useAuthStore()
const groupOptions = ref([])
const weeks24Options = [{ label: '有效', value: '有效' }, { label: '部分有效', value: '部分有效' }, { label: '无效', value: '无效' }]
const resourceOptions = ref([])
const researchOptions = ref([])
const showHintModal = ref(false)
const formRef = ref<FormInst | null>(null)
const medicalRef = ref(null)
const fileId = ref() /// 上传的文件 id
const formValue = reactive({
  name: '',
  sexName: '',
  idCardType: '身份证',
  idCardNum: '',
  birthday: undefined,
  age: '',
  countryName: '中国',
  nationName: '汉',
  phone: '',
  contactsName: '',
  contactsTel: '',
  marriageName: undefined,
  familyMembers: '',
  liveAddr: '',
  patientSource: userInfo?.organName,
  examNo: '',
  researcher: userInfo?.userName,
  researcherId: userInfo?.rsUserId || userInfo.id,
  register: userInfo?.userName, // 登记人员
  testStartTime: undefined,
  efficacyEvaluation: null,
  groupId: undefined,
  fileIds: undefined,
  medicalHistory: {
    // question1: '是',
    // question2: '是',
    // question3: ['以上均无'],
    // question4: ['以上均无'],
    // question5: '否',
    // question6: '无',
    // question7: '无',
    // question8: '无',
    // question9: '无',
    // question10: '无',
    // question11: '无',
  },
  drink: {
    isDrink: '否',
    type: null,
    startTime: undefined,
    dayDrink: '',
    weekDrink: '',
    isOver: '',
  },
  smoke: {
    isSmoke: '否',
    startTime: undefined,
    daySmoke: '',
    isOver: '',
  },
})
const rules = reactive({
  name: {
    required: true,
    message: '请输入姓名',
    trigger: ['blur', 'input'],
  },
  sexName: {
    required: true,
    message: '请选择性别',
    trigger: 'change',
  },
  idCardType: {
    required: true,
    message: '请选择证件类型',
    trigger: ['blur', 'change'],
  },
  idCardNum: {
    required: true,
    message: '请输入证件号',
    trigger: 'blur',
  },
  birthday: {
    required: true,
    trigger: ['blur', 'change'],
    message: '请选择出生日期',
  },
  countryName: {
    required: true,
    message: '请选择国籍',
    trigger: ['blur', 'change'],
  },
  nationName: {
    required: true,
    message: '请选择国籍',
    trigger: ['blur', 'change'],
  },
  phone: {
    required: true,
    message: '请输入手机号',
    trigger: 'blur',
  },
  contactsName: {
    required: true,
    message: '请选择紧急联系人',
    trigger: ['blur', 'change'],
  },
  contactsTel: {
    required: true,
    message: '请输入紧急联系人电话',
    trigger: 'blur',
  },
  patientSource: {
    required: true,
    trigger: ['blur', 'change'],
    message: '请选择受试者来源',
  },
  researcher: {
    required: true,
    trigger: ['blur', 'change'],
    message: '请选择研究员',
  },
  testStartTime: {
    required: true,
    trigger: ['blur', 'change'],
    message: '请选择入组时间',
  },
  medicalHistory: {
    question1: {
      required: true,
      message: '请选择病史信息',
      trigger: 'change',
    },
    question2: {
      required: true,
      message: '请选择病史信息',
      trigger: 'change',
    },
    question3: {
      type: 'array',
      required: true,
      message: '请选择病史信息',
      trigger: 'change',
    },
    question4: {
      type: 'array',
      required: true,
      message: '请选择病史信息',
      trigger: 'change',
    },
    question5: {
      required: true,
      message: '请选择病史信息',
      trigger: 'change',
    },
    question6: {
      required: true,
      message: '请选择病史信息',
      trigger: 'change',
    },
    question7: {
      required: true,
      message: '请选择病史信息',
      trigger: 'change',
    },
    question8: {
      required: true,
      message: '请选择病史信息',
      trigger: 'change',
    },
    question9: {
      required: true,
      message: '请选择病史信息',
      trigger: 'change',
    },
    question10: {
      required: true,
      message: '请选择病史信息',
      trigger: 'change',
    },
    question11: {
      required: true,
      message: '请选择病史信息',
      trigger: 'change',
    },
  },
  drink: {
    isDrink: {
      required: true,
      message: '请选择是否饮酒',
      trigger: 'change',
    },
  },
  smoke: {
    isSmoke: {
      required: true,
      message: '请选择是否吸烟',
      trigger: 'change',
    },
  },
  groupId: {
    type: 'number',
    required: true,
    message: '请选择分组',
    trigger: 'change',
  },
  fileIds: {
    required: true,
    message: '请上传知情同意书',
    trigger: 'change',
  },
})

function yjsbchange() {
  const temp = {
    白酒: 0.4,
    洋烈酒: 0.344,
    啤酒: 0.04,
    红酒: 0.12,
    黄酒: 0.096,
  }
  formValue.drink.weekDrink = formValue.drink.dayDrink ? ((temp[formValue.drink.type || ''] ?? 0) * Number(formValue.drink.dayDrink) * 7).toFixed(2) : ''
}

function saveSubject() {
  formRef.value?.validate((errors) => {
    if (!errors) {
      medicalRef.value?.validate()
      /// 直接校验数据
      // if (formValue.medicalHistory)
      //   for (const key in formValue.medicalHistory) {
      //     const element = formValue.medicalHistory[key];
      //     if (element == null || element == undefined || element?.length === 0) {
      //       window.$message.error('请完善病史信息')
      //       return
      //     }
      //   }
      // console.log('结束, 可以提交',formValue)
    }
    else {
      console.log(errors)
      window.$message.error('请完善受试者信息')
    }
  })
}

function cancleSubject() {
  if (props.addStatus)
    router.back()
  else
    emits('cancleEdit')
}

function changeBirthday(v: number) {
  console.log('=======', v, formValue.birthday)
  // formValue.birthday = formatDate(v)
  formValue.age = calculateAge(formatDate(v)).toString()
}

function validateSuccess() {
  // console.log('验证成果啦===', researchOptions.value, formValue.researcher, researchOptions.value.filter((item: any) => item.value === formValue.researcher || item.label === formValue.researcher))
  console.log('通过,', formValue)
  const params = {
    name: formValue.name,
    sexName: formValue.sexName,
    idCardNum: formValue.idCardNum,
    idCardType: formValue.idCardType,
    birthday: formValue.birthday,
    age: formValue.age,
    countryName: formValue.countryName,
    nationName: formValue.nationName || '',
    phone: formValue.phone,
    contactsName: formValue.contactsName,
    contactsTel: formValue.contactsTel,
    marriageName: formValue.marriageName,
    familyMembers: formValue.familyMembers,
    liveAddr: formValue.liveAddr,
    examGroup: groupOptions.value.find((item: any) => item.value === formValue.groupId)?.label,
    groupId: formValue.groupId,
    efficacyEvaluation: formValue.efficacyEvaluation,
    patientSource: formValue.patientSource,
    researcher: formValue.researcher,
    researcherId: formValue.researcherId,
    register: props.subject?.register || userInfo?.userName,
    registerId: props.subject?.registerId || (userInfo?.rsUserId || userInfo.id),
    rsUserId: userInfo?.rsUserId || userInfo.id,
    testStartTime: formValue.testStartTime?.includes('00:00:00') ? formValue.testStartTime : `${formValue.testStartTime} 00:00:00`,
    informedConsent: formValue.fileIds, // 知情同意书
    diseaseHistory: JSON.stringify(formValue.medicalHistory),
    personalHistory: JSON.stringify({ drink: formValue.drink, smoke: formValue.smoke }),
    topicId: route.query?.topicId as string, // 课题 id
  }
  if (props.addStatus) {
    _.throttle(() => {
      addSubject(params).then((res) => {
        console.log(res)
        if (res.data) {
          window.$message.success('受试者信息保存成功!')

          getSubjectDetail(res.data as string).then((results: any) => {
            const json = results.data
            if (json.id && json.examNo) {
              router.replace({
                path: '/researcher/projectManage/subjectDetail',
                query: {
                  id: json.id,
                  topicId: route.query?.topicId as string,
                  examNo: json.examNo,
                },
              })
            }
            else {
              router.back()
            }
            /// 更新下受试者信息
            reseacherStore.getSubjectInfo(json.id)
          })
        }
        else {
          window.$message.success('受试者信息保存失败!')
        }
      })
    }, 400)()
  }
  else {
    // 编辑保存
    editSubject({ ...params, id: route.query?.id as string }).then((res) => {
      console.log(res)
      if (res.data) {
        window.$message.success('受试者信息编辑成功!')
        emits('cancleEdit')
      }
      else {
        window.$message.success('受试者信息编辑失败!')
      }
      /// 更新下受试者信息
      reseacherStore.getSubjectInfo(route.query?.id)
    })
  }
}

function getSubjectResourceListData() {
  getSubjectResourceList().then((res) => {
    console.log('resource====', res)
    resourceOptions.value = res.data?.map((item) => {
      return { label: item, value: item }
    })
  })

  getSubjectGroupList(route.query?.topicId as string).then((res) => {
    console.log('resource====', res)
    groupOptions.value = res.data?.map((item) => {
      return { label: item.name, value: item.id }
    })
  })
}

function getReseacherListData() {
  getReseacherList({
    size: 10000,
    start: 1,
  }).then((res: any) => {
    researchOptions.value = res.data?.records.map((item) => {
      return { label: item.userName, value: item.id.toString() }
    })
  })
}

function caculatorWeekDrinkWaring() {
  if (formValue.sexName === '男' && Number(formValue.drink.weekDrink) >= 210)
    return 'warning'
  else if (formValue.sexName === '女' && Number(formValue.drink.weekDrink) >= 140)
    return 'warning'

  return 'success'
}

// function updateReseacher(value: string, option: SelectOption) {
//   console.log('11111======',JSON.stringify(value), JSON.stringify(option), option.value, option.label)
//   formValue.researcher = option.label?.toString() || ''
//   formValue.researcherId = option.value?.toString() || ''
// }

/// / 时间限制 结束时间大于开始时间
function isDateDisable(current) {
  const currentStr = dayjs(current).format('YYYY-MM-DD')
  return !dayjs(currentStr).isBetween(dayjs(topicInfo.value?.startTime), dayjs(topicInfo.value?.endTime), 'day', '[]')
}

function createFileId() {
  generateFileId().then((res) => {
    fileId.value = res.data
  })
}

function loadDataChange(data) {
  if (data && data.length > 0)
    formValue.fileIds = fileId.value
  else
    formValue.fileIds = undefined
}

onMounted(() => {
  getSubjectResourceListData()
  reseacherStore.getInfo(route.query?.topicId as string)
  getReseacherListData()
  if (props.subject) {
    for (const key in formValue)
      formValue[key] = props.subject[key] || ''

    if (!props.addStatus && props.subject.dataStatus === 1)
      showHintModal.value = true

    if (props.subject.informedConsent)
      fileId.value = props.subject.informedConsent
    else
      createFileId()
  }
  if (props.addStatus) {
    showHintModal.value = true
    createFileId()
  }
})

/// 监听
watch(() => formValue.groupId, () => {
  console.log(formValue.groupId)
  // 44
  if (formValue.groupId === 44) {
    // 临床资料组, 此时知情同意书不是必填
    rules.fileIds.required = false
  }
  else {
    rules.fileIds.required = true
  }
})
</script>

<template>
  <div class="page" style="overflow: hidden">
    <Breadcrumb
      v-if="addStatus" :bread-list="[
        {
          title: '科研管理',
          link: null,
          key: 'researcher',
        },
        {
          title: '课题管理',
          link: '/',
          key: 'researcher_projectManage',
        },
        {
          title: '课题详情',
          link: `/?currentTab=manage&topicId=${route.query?.topicId as string}`,
          key: 'researcher_projectManage_detail',
        },
        {
          title: addStatus ? '新增受试者' : '编辑受试者',
          link: null,
          key: 'researcher_projectManage_subject_create',
        },
      ]"
    />

    <div
      class="bg-content" :class="addStatus ? 'h-[calc(100vh-165px)]' : 'h-[calc(100vh-300px)]'"
      :style="{ margin: addStatus ? '14px' : '0', overflow: 'auto' }"
    >
      <div :style="{ padding: addStatus ? '14px' : '0 14px' }">
        <n-form
          ref="formRef" :item-margin="20" :model="formValue" label-placement="left" label-align="left"
          label-width="125" :rules="rules" require-mark-placement="left"
        >
          <div class="model-content">
            <SectionTitle mb-14px>
              基础信息
            </SectionTitle>

            <n-grid :x-gap="12" :y-gap="0" :cols="3">
              <n-grid-item>
                <n-form-item label="姓名" path="name">
                  <n-input v-model:value="formValue.name" placeholder="请输入" />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="性别" path="sexName">
                  <n-radio-group v-model:value="formValue.sexName" w-full name="sexName">
                    <n-space flex-1 justify="space-around">
                      <n-radio value="男">
                        男
                      </n-radio>
                      <n-radio value="女">
                        女
                      </n-radio>
                      <n-radio value="未知">
                        未知
                      </n-radio>
                    </n-space>
                  </n-radio-group>
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="证件类型" path="idCardType">
                  <n-select v-model:value="formValue.idCardType" placeholder="请选择" :options="IDCardTypeOptions" />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="证件号码" path="idCardNum">
                  <n-input v-model:value="formValue.idCardNum" placeholder="请输入" />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="出生时间" path="birthday">
                  <el-date-picker
                    v-model="formValue.birthday" type="date" value-format="YYYY-MM-DD" placeholder="请选择"
                    style="width: 100%;" @change="changeBirthday"
                  />
                  <!-- <n-date-picker v-model:value="formValue.birthday" type="date" w-full  @update:value="changeBirthday" /> -->
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="年龄:" pl-18px path="age">
                  <div style="margin-left: -18px;">
                    {{ formValue.age ? formValue.age : '系统自动计算' }}
                  </div>
                  <!-- <n-input v-model:value="formValue.age" placeholder="系统自动计算" disabled /> -->
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="国籍" path="countryName">
                  <n-select v-model:value="formValue.countryName" placeholder="Select" :options="countryOptions" />
                </n-form-item>
              </n-grid-item>
              <n-grid-item v-if="formValue.countryName === '中国'">
                <n-form-item label="民族" path="nationName">
                  <n-select v-model:value="formValue.nationName" placeholder="Select" :options="nationOptions" />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="手机号" path="phone">
                  <n-input v-model:value="formValue.phone" placeholder="请输入" />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="紧急联系人" path="contactsName">
                  <n-input v-model:value="formValue.contactsName" placeholder="请输入" />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="紧急联系人电话" path="contactsTel">
                  <n-input v-model:value="formValue.contactsTel" placeholder="请输入" />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="婚姻状况" pl-18px path="marriageName">
                  <n-select
                    v-model:value="formValue.marriageName" style="margin-left: -18px;" placeholder="请选择"
                    :options="marryOptions"
                  />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="家庭成员" pl-18px path="familyMembers">
                  <n-input v-model:value="formValue.familyMembers" style="margin-left: -18px;" placeholder="请输入" />
                </n-form-item>
              </n-grid-item>
              <n-grid-item>
                <n-form-item label="家庭地址" pl-18px path="liveAddr" span="2">
                  <n-input v-model:value="formValue.liveAddr" style="margin-left: -18px;" placeholder="请输入" />
                </n-form-item>
              </n-grid-item>
            </n-grid>
          </div>

          <div class="model-content">
            <SectionTitle>
              试验信息
            </SectionTitle>

            <div mt-20px>
              <n-grid :x-gap="12" :y-gap="0" :cols="3">
                <n-grid-item>
                  <n-form-item label="受试者来源:" path="patientSource">
                    <div>
                      {{ formValue.patientSource }}
                    </div>
                    <!-- <n-input v-model:value="formValue.patientSource" disabled /> -->
                  </n-form-item>
                </n-grid-item>
                <n-grid-item v-if="!addStatus">
                  <n-form-item label="受试者观察号:" path="examNo">
                    <div>
                      {{ formValue.examNo ? formValue.examNo : "系统自动生成" }}
                    </div>
                    <!-- <n-input v-model:value="formValue.examNo" placeholder="系统自动生成" disabled /> -->
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="研究员:" path="researcher">
                    <div>
                      {{ formValue.researcher }}
                    </div>
                    <!-- <n-input v-model:value="formValue.researcher" disabled /> -->
                    <!-- <n-select
                        v-model:value="formValue.researcher"
                        placeholder="请选择"
                        :options="researchOptions"
                        @update:value="updateReseacher"
                      /> -->
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="登记人员:" pl-18px path="register">
                    <div style="margin-left: -18px;">
                      {{ formValue.register }}
                    </div>
                    <!-- <n-input v-model:value="formValue.register" disabled /> -->
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="入组时间" path="testStartTime">
                    <el-date-picker
                      v-model="formValue.testStartTime" type="date" value-format="YYYY-MM-DD"
                      placeholder="第一针干扰素时间" style="width: 100%;" :disabled="formValue.status === 1"
                      :disabled-date="(current) => { return isDateDisable(current) }"
                    />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="试验分组" path="groupId">
                    <n-select v-model:value="formValue.groupId" placeholder="请选择" :options="groupOptions" clearable />
                  </n-form-item>
                </n-grid-item>
                <n-grid-item>
                  <n-form-item label="24周疗效评估" pl-18px path="efficacyEvaluation">
                    <n-select
                      v-model:value="formValue.efficacyEvaluation" style="margin-left: -18px;" placeholder="请选择"
                      :options="weeks24Options"
                    />
                  </n-form-item>
                </n-grid-item>
              </n-grid>
              <n-form-item label="知情同意书:" path="fileIds">
                <UploadView :upload-id="fileId" upload-type="RS_INFORMED_CONSENT" mt-14px @loadData="loadDataChange" />

                <!-- <n-button type="primary" ghost @click="startUpLoad" mr-20px>
                    点击上传
                    <input
                      ref="fileInput" style="display: none;" name="file" type="file" multiple
                      @change="uploadFileChange"
                    >
                  </n-button>
                  <img src="@/assets/images/icon_hint.png" w-16px alt="">
                  <span>&nbsp;&nbsp;请上传相关附件，支持.word、.ppt、.pptx、.xlsx、.xls、.jpg、.pdf等，最多上传10个</span> -->
              </n-form-item>
            </div>
          </div>

          <div class="model-content">
            <SectionTitle>
              病史信息
            </SectionTitle>
            <MedicalCustomHistory v-if="topicInfo?.history" ref="medicalRef" v-model="formValue.medicalHistory" @validate-success="validateSuccess" />
            <MedicalHistory v-else ref="medicalRef" :form-value="formValue" :rules="rules" @validate-success="validateSuccess" />
          </div>
          <div class="model-content">
            <SectionTitle>
              个人史
            </SectionTitle>

            <n-form-item label="是否饮酒" path="drink.isDrink">
              <div mt-5px flex-1>
                <n-radio-group v-model:value="formValue.drink.isDrink">
                  <n-space item-style="display: flex;" :size="[40, 0]">
                    <n-radio value="否">
                      否
                    </n-radio>
                    <n-radio value="是">
                      是
                    </n-radio>
                    <div
                      v-if="caculatorWeekDrinkWaring() === 'warning'" style="margin-top: -5px" ml-10px h-32px flex
                      line-height-32px
                    >
                      <img class="add-img-hover" src="@/assets/images/icon-hint.png" mr-8px mt-8px h-16px w-16px>
                      <span color="#666">该受试者有酗酒习惯，不符合试验纳入标准</span>
                    </div>
                  </n-space>
                </n-radio-group>
              </div>
            </n-form-item>
            <div v-if="formValue.drink.isDrink === '是'" class="grsBack">
              <div flex items-center pt-14px>
                <span color="#666" ml-20px>饮酒种类</span>
                <n-select
                  v-model:value="formValue.drink.type" :options="wineOptions"
                  style="width: 120px;margin-left: 10px;" @update:value="() => {
                    yjsbchange();
                  }
                  "
                />
                <span color="#666" ml-20px>开始饮酒时间</span>
                <el-date-picker
                  v-model="formValue.drink.startTime" type="month" placeholder="请选择"
                  style="width: 120px;margin-left: 10px;"
                />
                <span color="#666" ml-20px>平均每日饮酒量</span>
                <n-input
                  v-model:value="formValue.drink.dayDrink" placeholder="请输入" :allow-input="onlyAllowNumber"
                  style="width: 100px;margin-left: 10px;" @input="() => {
                    yjsbchange();
                  }
                  "
                >
                  <template #suffix>
                    <span color="#999">ml</span>
                  </template>
                </n-input>
              </div>
              <div flex items-center pt-14px>
                <span color="#666" ml-20px>平均每周乙醇摄入量</span>
                <n-tooltip trigger="hover">
                  <template #trigger>
                    <n-input
                      v-model:value="formValue.drink.weekDrink" mr-20px placeholder="自动计算"
                      :status="caculatorWeekDrinkWaring()" :allow-input="onlyAllowNumber"
                      style=" width: 100px; margin-left: 10px; "
                    >
                      <template #suffix>
                        <span color="#999">g</span>
                      </template>
                    </n-input>
                  </template>
                  成人每周乙醇最多摄取量，超过视为有酗酒习惯：女性：≥140g/周，男性：≥210g/周
                </n-tooltip>

                <n-radio-group v-model:value="formValue.drink.isOver" name="radiogroup">
                  <n-radio :value="true">
                    已戒
                  </n-radio>
                  <div w-40px />
                  <n-radio :value="false">
                    未戒
                  </n-radio>
                </n-radio-group>
              </div>
            </div>
            <n-form-item label="是否抽烟" path="smoke.isSmoke">
              <div mt-5px flex-1>
                <n-radio-group v-model:value="formValue.smoke.isSmoke">
                  <n-space item-style="display: flex;" :size="[40, 0]">
                    <n-radio value="否">
                      否
                    </n-radio>
                    <n-radio value="是">
                      是
                    </n-radio>
                  </n-space>
                </n-radio-group>
              </div>
            </n-form-item>
            <div v-if="formValue.smoke.isSmoke === '是'" class="grsBack">
              <div flex items-center pt-14px>
                <span color="#666" ml-20px>开始吸烟时间</span>
                <el-date-picker
                  v-model="formValue.smoke.startTime" type="month" placeholder="请选择" clearable
                  style="width: 120px; margin-left: 10px"
                />
                <span color="#666" ml-20px>平均每日吸烟量</span>
                <n-input
                  v-model:value="formValue.smoke.daySmoke" mr-20px placeholder="请输入"
                  :allow-input="onlyAllowNumber" style="width: 100px; margin-left: 10px"
                >
                  <template #suffix>
                    <span color="#999">支</span>
                  </template>
                </n-input>
                <n-radio-group v-model:value="formValue.smoke.isOver" name="radiogroup">
                  <n-radio :value="true">
                    已戒
                  </n-radio>
                  <div w-40px />
                  <n-radio :value="false">
                    未戒
                  </n-radio>
                </n-radio-group>
              </div>
            </div>
          </div>
        </n-form>
      </div>
    </div>

    <div
      style="background-color: #f5f5f5;justify-content: center;border:1px solid #E8E8E8;"
      h-60px w-full flex items-center
    >
      <n-button mr-20px w-100px color="#06AEA6" @click="saveSubject">
        保存
      </n-button>
      <n-button glost w-100px @click="cancleSubject">
        取消
      </n-button>
    </div>

    <n-modal
      v-model:show="showHintModal" :mask-closable="false" preset="dialog" title="请核对该受试者病史及个人史信息！"
      positive-text="我知道了"
    >
      <template #icon>
        <img src="@/assets/images/icon-hint.png" mr-8px h-16px w-16px>
      </template>
    </n-modal>
  </div>
</template>

<style scoped>
/* :deep(.n-dialog .n-modal) {
  width: 310px !important;
  max-width: 310px !important;
} */

/* .bg-content ::v-deep .n-dialog .n-modal {
  width: 310px !important;
  max-width: 310px !important;
} */

.bg-content {
  background: #fff;
  overflow: auto;

  .model-content {
    margin-bottom: 14px;
  }

  .grsBack {
    display: flex;
    align-items: center;
    background: #f5f5f5;
    margin-top: -10px;
    margin-left: 120px;
    padding-bottom: 14px;
    flex: 1;
  }
}
</style>
