<script setup lang='ts'>
import dayjs from 'dayjs'
import { Breadcrumb } from '@/layouts/common'
import { getScale<PERSON>reaterList, getScaleList, getScaleSimpleList, getTopicNameList } from '~/src/api/research'
import { useAuthStore } from '~/src/store'

const router = useRouter()
const pageOptions = reactive({
  page: 1,
  size: 10,
  total: 0,
})

const searchParams = reactive({
  scaleName: null,
  topicId: null,
  createId: null,
})

const tableData = ref()
const { userInfo } = useAuthStore()

function getDataFromNet() {
  getScaleList({
    page: pageOptions.page,
    size: pageOptions.size,
    ...searchParams,
  }).then((res) => {
    tableData.value = res?.data?.records || []
    pageOptions.total = res?.data.total || 0
  })
}

const selectOptions: any = reactive({
  topicNames: [],
  creators: [],
  scaleNames: [],
})

function getCreator() {
  getScaleCreaterList().then((res) => {
    selectOptions.creators = res?.data || []
  })
}

function getTopicNameListWithQuery(query) {
  if (query != null) {
    getTopicNameList({
      start: 1,
      size: 100,
      name: query,
      rsUserId: userInfo.rsUserId || userInfo.id || '',
    }).then((res) => {
      selectOptions.topicNames = res?.data || []
    })
  }
}

function getScaleNameListWithQuery() {
  getScaleSimpleList().then((res) => {
    selectOptions.scaleNames = res?.data || []
  })
}

function computeRelationTopics(relationTopics) {
  let str = ''
  relationTopics?.forEach((item) => {
    if (item)
      str += `${item},`
  })
  return str?.length === 0 ? '-' : str.substring(0, str.length - 1)
}

function handleSearchClick() {
  getDataFromNet()
}

function handleResetClick() {
  searchParams.createId = null
  searchParams.scaleName = null
  searchParams.topicId = null
  getDataFromNet()
}

function jumpToDetail(row) {
  router.push({
    path: '/researcher/scaleDetail',
    query: {
      scaleId: row.scaleId,
    },
  })
}

onMounted(() => {
  getDataFromNet()
  getTopicNameListWithQuery('')
  getCreator()
  getScaleNameListWithQuery()
})
</script>

<template>
  <div class="page">
    <Breadcrumb
      :bread-list="[
        {
          title: '科研管理',
          link: null,
          key: 'researcher',
        },
        {
          title: '量表管理',
          link: null,
          key: 'researcher_scaleManage',
        },
      ]"
    />
    <PageCard breadcrumb style="display: flex;flex-direction: column;">
      <PageTitle class="mb-2px">
        量表管理
      </PageTitle>
      <div mt-14px flex flex-wrap items-center style="gap: 14px 20px;">
        <div flex items-center>
          <span color="#666" mr-10px text-14px>量表名称</span>
          <el-select-v2
            v-model="searchParams.scaleName" filterable clearable
            placeholder="请选择" style="width: 200px" :options="selectOptions.scaleNames"
            :props="{
              label: 'scaleName',
              value: 'scaleName',
            }"
          />
        </div>
        <div flex items-center>
          <span color="#666" mr-10px text-14px>关联课题</span>
          <el-select-v2
            v-model="searchParams.topicId" remote filterable clearable
            :remote-method="getTopicNameListWithQuery" placeholder="请选择" style="width: 200px" :options="selectOptions.topicNames"
            :props="{
              label: 'name',
              value: 'id',
            }"
          />
        </div>
        <div flex items-center>
          <span color="#666" mr-10px text-14px>创建人</span>
          <el-select-v2
            v-model="searchParams.createId" clearable :options="selectOptions.creators"
            :props="{ label: 'name', value: 'id' }" placeholder="请选择" style="width: 200px"
          />
        </div>
        <div flex items-center>
          <n-button type="primary" class="mr-10px" @click="handleSearchClick">
            查询
          </n-button>
          <n-button @click="handleResetClick">
            重置
          </n-button>
        </div>
      </div>
      <el-table :data="tableData" stripe mt-14px overflow-y-auto>
        <el-table-column label="序号" width="80" type="index" header-align="center" align="center" />
        <el-table-column show-overflow-tooltip label="量表名称" :formatter="({ scaleName }) => scaleName || '-'" width="238" />
        <el-table-column
          label="关联课题"
          :formatter="({ relationTopics }) => { return computeRelationTopics(relationTopics) }"
        />
        <el-table-column label="创建人" width="90" :formatter="({ createName }) => createName || '-'" />
        <el-table-column
          label="创建时间" width="120"
          :formatter="({ createTime }) => dayjs(createTime).format('YYYY-MM-DD')"
        />
        <el-table-column label="操作" width="100">
          <template #default="{ row }">
            <div text-14px color="#3B8FD9" @click="jumpToDetail(row)">
              查看
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div flex justify-end>
        <n-pagination
          v-model:page="pageOptions.page" v-model:page-size="pageOptions.size"
          :item-count="pageOptions.total" :page-sizes="[5, 10, 20, 30]" show-size-picker show-quick-jumper mt-14px
          @update:page-size="getDataFromNet"
          @update:page="getDataFromNet"
        />
      </div>
    </PageCard>
  </div>
</template>

<style lang="scss" scoped></style>
