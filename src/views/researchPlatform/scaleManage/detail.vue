<script setup lang='ts'>
import { Breadcrumb } from '@/layouts/common'
import { getScaleDetail } from '~/src/api/research'

const route = useRoute()
const router = useRouter()
const scaleId = route.query?.scaleId as string

const scaleData = ref()

function getTheDetailQestionsFromNet() {
  getScaleDetail(scaleId).then((res) => {
    scaleData.value = res?.data
  })
}

function handleGoBack() {
  router.back()
}

onMounted(() => {
  getTheDetailQestionsFromNet()
})
</script>

<template>
  <div class="page flex-col">
    <Breadcrumb
      :bread-list="[
        {
          title: '科研管理',
          link: null,
          key: 'researcher',
        },
        {
          title: '量表管理',
          link: '/',
          key: 'researcher_scaleManage',
        },
        {
          title: '量表查看',
          link: null,
          key: 'researcher_scaleDetail',
        },
      ]"
    />
    <div m-14px flex-col flex-1 p-14px class="card bg-#fff">
      <div text-center text-20px font-500>
        {{ scaleData?.scaleName || '-' }}
      </div>
      <div mt-14px h-2px w-full bg="#06AEA6" />
      <div bg="#f5f5f5" flex-col flex-1 items-center overflow-y-auto>
        <div bg="#fff" mt-16px w-600px flex-1 overflow-y-auto pl-14px pt-14px>
          <div v-for="(item, index) in scaleData?.questions" :key="index" flex-col items-start pb-30px>
            <div mb-10px text-14px font-500>
              {{ `${index + 1}、${item?.questionTitle}` }}
            </div>
            <el-input
              v-if="item.questionType === 'text'" disabled style="width:480px; margin-left: 20px;"
              placeholder="请输入"
            >
              <template #suffix>
                {{ item.medicineUnit || '' }}
              </template>
            </el-input>
            <el-date-picker
              v-if="item.questionType === 'dateTime'"
              disabled style="width:360px;" type="date" placeholder="请选择时间"
              value-format="YYYY-MM-DD"
              :teleported="false"
            />
            <el-radio-group
              v-if="item.questionType === 'radio'"
              disabled
              flex-col
              style="align-items: flex-start;"
            >
              <el-radio v-for="(opt, opindex) in item.options" :key="opindex" :label="opt.textContent" :value="opt.optionId" />
            </el-radio-group>
            <el-checkbox-group
              v-if="item.questionType === 'checkbox'"
              disabled
              flex-col
              style="align-items: flex-start;"
            >
              <el-checkbox v-for="(opt, opindex) in item.options" :key="opindex" :label="opt.textContent" :value="opt.optionId" />
            </el-checkbox-group>
          </div>
        </div>
      </div>
    </div>
    <div h-40px />
    <PageFooter>
      <template #default>
        <n-button secondary w-100px @click="router.go(-1)">
          返 回
        </n-button>
      </template>
    </PageFooter>
  </div>
</template>

<style lang="scss" scoped>
.page{
  position: relative;
}
.bottom-btn {
  width: 100%;
  height: 60px;
  position: fixed;
  box-shadow: 0px -1px 0px 0px #e8e8e8 inset, 0px 1px 0px 0px #e8e8e8 inset;
  position: fixed;
  bottom: 0;
  left: 0px;
  right: 0;
}
</style>
