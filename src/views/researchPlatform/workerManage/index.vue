<script setup lang="ts">
import dayjs from 'dayjs'
import { pinyin } from 'pinyin-pro'
import { Breadcrumb } from '@/layouts/common'
import {
  addThe<PERSON><PERSON><PERSON>,
  getOrgan<PERSON>ll<PERSON>ist,
  getR<PERSON><PERSON><PERSON>ist,
  getR<PERSON><PERSON><PERSON><PERSON><PERSON>ist,
  stop<PERSON><PERSON><PERSON>,
  updateThe<PERSON><PERSON>acher,
} from '~/src/api/research'
import { useAuthStore } from '~/src/store'

const userInfo = useAuthStore().userInfo

const ruleFormRef = ref()
const showUseModal = ref()

const searchParams = reactive({
  userNames: [],
  status: '',
})

const organOptions: any = reactive({
  data: [],
})

const isAdd = ref(true)

const statusOptions = {
  2: {
    label: '全部',
    color: '',
    key: '',
  },
  0: {
    label: '启用中',
    color: 'bg-#4ACFB1',
    key: 0,
  },
  1: {
    label: '已禁用',
    color: 'bg-#CCCCCC',
    key: 1,
  },
}

const defaultTime = ref<[Date, Date]>([
  new Date(),
  new Date(2055, 12, 30, 23, 59, 59),
])

const pageOptions = reactive({
  page: 1,
  size: 10,
  total: 10,
})

const loadingOptions: any = reactive({
  loding: false,
  data: [],
})

const tableData = ref()
const showAddMember = ref(false)
const addRules = {
  userName: {
    required: true,
    message: '姓名不能为空',
    trigger: ['blur', 'change'],
  },
  organName: {
    required: true,
    message: '所属机构不能为空',
    trigger: ['blur', 'change'],
  },
  phone: {
    required: true,
    message: '手机号不能为空',
    trigger: ['blur', 'change'],
  },
  password: [
    {
      required: true,
      message: '密码不能为空',
      trigger: ['blur', 'change'],
    },
    {
      min: 8,
      max: 12,
      trigger: ['blur', 'change'],
      validator: (rule: any, value: any, callback: any) => {
        if (value?.length < 8 || value.length > 12)
          callback(new Error('请设置8-12位密码，需包含字母大小写、数字、特殊符号'))
        const pass = isStrongPassword(value)
        if (pass)
          callback()
        else
          callback(new Error('请设置8-12位密码，需包含字母大小写、数字、特殊符号'))
      },
    },
  ],
  allowTimes: {
    required: true,
    message: '时间段不能为空',
    // trigger: ['blur', 'change'],
  },
}
const formValue = reactive({
  name: '',
  phone: '',
  createBy: userInfo.userName,
  organName: '',
  allowTimes: [],
  password: '',
  status: 0,
  userName: '',
})

function visibleChange(v) {
  if (!v) {
    isAdd.value = true
    showAddMember.value = false
    Object.assign(formValue, {
      name: '',
      phone: '',
      createBy: userInfo.userName,
      organName: '',
      allowTimes: [],
      password: '',
      status: 0,
      userName: '',
      id: null,
    })
  }
}

function getDataFromNet() {
  getReseacherList({
    status: searchParams.status,
    userName: searchParams.userNames.join(','),
    size: pageOptions.size,
    start: pageOptions.page,
  }).then((res) => {
    console.log(res)
    tableData.value = res?.data?.records || []
    pageOptions.total = Number(res?.data?.total || 0)
    pageOptions.page = Number(res?.data?.current || 0)
    pageOptions.size = Number(res?.data?.size || 10)
  })
}

function getUserNameList(query) {
  if (query) {
    loadingOptions.loding = true
    getReseacherNameList(query)
      .then((res) => {
        console.log(res)
        loadingOptions.loding = false
        loadingOptions.data = res?.data || []
      })
      .catch((e) => {
        loadingOptions.loding = false
      })
  }
}

function handleBodyConfirm() {
  /// 新增研究员
  if (!ruleFormRef.value)
    return
  ruleFormRef.value.validate((valid, fields) => {
    if (valid) {
      /// 赋值
      console.log(formValue)
      formValue.allowStartTime = formValue.allowTimes[0]
      formValue.allowEndTime = formValue.allowTimes[1]
      if (isAdd.value) {
        formValue.name = formValue.phone/// 登录名
        addTheReseacher(formValue).then((res) => {
          if (res.data) {
            window.$message.success('新增成功')
            showAddMember.value = false
            reset()
          }
        })
      }
      else {
        updateTheReseacher(formValue).then((res) => {
          if (res.data) {
            window.$message.success('更新成功')
            showAddMember.value = false
            reset()
          }
        })
      }
    }
  })
}

function reset() {
  Object.assign(formValue, {
    name: '',
    phone: '',
    createBy: userInfo.userName,
    organName: '',
    allowTimes: [],
    password: '',
    status: 0,
    userName: '',
    id: null,
  })
  getDataFromNet()
}

/// 编辑
function handleEditClick(row) {
  ///
  // console.log(row)
  Object.assign(formValue, row)
  formValue.allowTimes = [row?.allowStartTime, row?.allowEndTime]
  isAdd.value = false
  showAddMember.value = true
  if (formValue.status === 1)
    formValue.allowTimes = []
}

/// 禁用
function handleDeleteClick(row) {
  if (row.status === 0) {
    window.$dialog.warning({
      title: '温馨提示',
      content: '禁用后，该用户将不可登本系统。请确认是否继续！',
      negativeText: '取消',
      positiveText: '禁用',
      btnGhost: true,
      onPositiveClick: () => {
        stopReseacher(row?.id).then((res) => {
          if (res.data) {
            window.$message.success('禁用成功')
            getDataFromNet()
          }
        })
      },
    })
  }
  else {
    /// 启用
    showUseModal.value = true
    formValue.id = row.id
    // updateTheReseacher({
    //   id: row.id,
    //   status: 0,
    // }).then((res) => {
    //   window.$message.success('启用成功')
    //   getDataFromNet()
    // })
  }
}

/// 机构列表
function getOrganList() {
  getOrganAllList().then((res) => {
    const sb: any = []
    const sx: any = res?.data || []
    sx.forEach((item) => {
      sb.push({
        label: item,
        value: item,
      })
    })
    organOptions.data = sb
  })
}

/// 随机一个8位强密码
function generateRandomSixDigitString() {
  const pinyinFirstLetters = getPinyinFirstLetter(formValue.userName)
  const phoneLastFourDigits = formValue.phone.slice(-4)
  /// 根据姓名和手机号, 生成一个固定规则密码
  const pass = `${pinyinFirstLetters + phoneLastFourDigits}@`
  return pass?.length < 8 ? (`${pass}@`) : pass
  // return generateStrongPassword(8)
}

// 将汉字转换为拼音首字母的函数
function getPinyinFirstLetter(str) {
  const pys = pinyin(str, {
    toneType: 'none',
    type: 'array',
  })
  let result = ''
  for (let i = 0; i < pys.length; i++) {
    const item = pys[i]
    result += item.charAt(0)
  }

  /// 首字母大写
  return result.charAt(0).toUpperCase() + result.slice(1)
}

function isStrongPassword(password: string): boolean {
  // 检查长度是否在8到12位之间
  const lengthRegex = /^.{8,12}$/
  if (!lengthRegex.test(password))
    return false

  // 检查是否包含小写字母、大写字母、数字和特殊符号
  const lowerCaseRegex = /[a-z]/
  const upperCaseRegex = /[A-Z]/
  const digitRegex = /\d/
  const specialCharRegex = /[!@#$%^&*()_+~`|}{[\]:;?><,./\-=\]]/

  return lowerCaseRegex.test(password)
           && upperCaseRegex.test(password)
           && digitRegex.test(password)
           && specialCharRegex.test(password)
}

/// 随机一个强密码
function generateStrongPassword(length: number): string {
  const lowerChars = 'abcdefghijklmnopqrstuvwxyz'
  const upperChars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
  const digitChars = '0123456789'
  const specialChars = '!@#$%^&*()_+~`|}{[]:;?><,./-='

  // 确保每种字符类型至少有一个
  const password = [
    lowerChars[Math.floor(Math.random() * lowerChars.length)],
    upperChars[Math.floor(Math.random() * upperChars.length)],
    digitChars[Math.floor(Math.random() * digitChars.length)],
    specialChars[Math.floor(Math.random() * specialChars.length)],
  ]

  // 填充剩余的字符
  const allChars = lowerChars + upperChars + digitChars + specialChars
  for (let i = password.length; i < length; i++)
    password.push(allChars[Math.floor(Math.random() * allChars.length)])

  // 打乱密码的顺序
  for (let i = password.length - 1; i > 0; i--) {
    const j = Math.floor(Math.random() * (i + 1));
    [password[i], password[j]] = [password[j], password[i]]
  }

  return password.join('')
}

function addMemberClick() {
  showAddMember.value = true
}

function disabledDate(time: Date) {
  return time.getTime() <= dayjs().subtract(1, 'day').valueOf()
}

function dateHaveSelect() {
  /// 时间选择后
  if (formValue.allowTimes.length > 0)
    formValue.status = Date.now() >= dayjs(formValue.allowTimes[0]).valueOf() ? 0 : 1
}

function setTheNameinput() {
  // 新增就 给 登录名 赋值
  if (isAdd.value)
    formValue.name = formValue.phone
    /// 尝试生成密码
  if (formValue.userName?.length > 0 && formValue.phone?.length >= 11)
    formValue.password = generateRandomSixDigitString()
}

function setThePasswordinput() {
  if (formValue.userName?.length > 0 && formValue.phone?.length >= 11)
    formValue.password = generateRandomSixDigitString()
}

function handleUseConfirm() {
  /// 去启用
  if (formValue.allowTimes?.length === 0)
    return
  updateTheReseacher({
    id: formValue.id,
    allowStartTime: formValue.allowTimes[0],
    allowEndTime: formValue.allowTimes[1],
  }).then((res) => {
    window.$message.success('启用成功')
    visibleUseChange(false)
    getDataFromNet()
  })
}
function visibleUseChange(v) {
  if (!v) {
    showUseModal.value = false
    formValue.allowTimes = []
    delete formValue.id
  }
}

onMounted(() => {
  getDataFromNet()
  getOrganList()
  getUserNameList(' ')
})
</script>

<template>
  <div class="page">
    <Breadcrumb
      :bread-list="[
        {
          title: '科研管理',
          link: null,
          key: 'researcher',
        },
        {
          title: '研究者管理',
          link: null,
          key: 'researcher_workerManage',
        },
      ]"
    />
    <PageCard breadcrumb style="display:flex;flex-direction: column;">
      <PageTitle class="mb-2px">
        研究者管理
      </PageTitle>
      <div mt-14px flex flex-wrap items-center style="gap: 14px 20px">
        <div flex items-center>
          <span color="#666" mr-10px text-14px>姓名</span>
          <el-select
            v-model="searchParams.userNames" multiple filterable remote placeholder="请输入"
            :remote-method="getUserNameList" remote-show-suffix :loading="loadingOptions.loding" style="width: 200px"
            @change="getDataFromNet"
          >
            <el-option v-for="(nitem, index) in loadingOptions.data" :key="index" :label="nitem" :value="nitem" />
          </el-select>
        </div>
        <div flex items-center>
          <span color="#666" mr-10px text-14px>状态</span>
          <el-select v-model="searchParams.status" placeholder="请选择" style="width: 200px" @change="getDataFromNet" @blur="getDataFromNet">
            <el-option v-for="stem in [2, 0, 1]" :key="stem" :label="statusOptions[stem].label" :value="statusOptions[stem].key" />
          </el-select>
        </div>
      </div>
      <div mt-14px>
        <n-button type="primary" class="mr-10px" @click="addMemberClick">
          新增
        </n-button>
      </div>
      <el-table :data="tableData" stripe mt-14px flex-1 overflow-y-auto>
        <el-table-column label="序号" width="80" type="index" header-align="center" align="center" />
        <el-table-column label="姓名" width="90" :formatter="({ userName }) => userName || '-'" />
        <el-table-column label="手机号" width="120" :formatter="({ phone }) => phone || '-'" />
        <el-table-column label="登录名" width="120" :formatter="({ name }) => name || '-'" />
        <el-table-column label="密码" width="140" :formatter="({ password }) => password || '-'" />
        <el-table-column label="所属机构" :formatter="({ organName }) => organName || '-'" />
        <el-table-column label="账号状态">
          <template #default="{ row }">
            <div flex items-center>
              <div h-6px w-6px rounded-3px :class="statusOptions[row.status].color" />
              <div ml-6px>
                {{ statusOptions[row.status].label }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="创建人" :formatter="({ createBy }) => createBy || '-'" />
        <el-table-column
          label="创建时间" :formatter="({ createTime }) =>
            dayjs(createTime).format('YYYY-MM-DD')
          "
        />
        <el-table-column label="操作" width="110" fixed="right">
          <template #default="{ row }">
            <div flex items-center gap-5px>
              <span uno-link @click="handleEditClick(row)">编辑</span>
              <div h-14px w-1px bg="#3B8FD9" />
              <span uno-link @click="handleDeleteClick(row)">{{
                row.status === 1 ? "启用" : "禁用"
              }}</span>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div flex justify-end>
        <n-pagination
          v-model:page="pageOptions.page" v-model:page-size="pageOptions.size"
          :item-count="pageOptions.total" :page-sizes="[5, 10, 20, 30]" show-size-picker show-quick-jumper mt-14px
          @update:page-size="getDataFromNet"
          @update:page="getDataFromNet"
        />
      </div>
    </PageCard>
    <BasicModal
      :visible="showAddMember" :title="isAdd ? '新增成员' : '编辑成员'" width="820"
      @ok="handleBodyConfirm" @visible-change="visibleChange"
    >
      <div pb-5px pl-24px pt-20px>
        <el-form ref="ruleFormRef" :label-width="120" inline :model="formValue" :rules="addRules">
          <el-form-item prop="userName" required label="姓名">
            <el-input v-model="formValue.userName" style="width: 240px" @input="setThePasswordinput" />
          </el-form-item>
          <el-form-item prop="organName" required label="所属机构">
            <el-select-v2
              v-model="formValue.organName" filterable style="width: 240px" :options="organOptions.data"
              placeholder="请选择"
            />
          </el-form-item>
          <el-form-item prop="phone" required label="手机号码">
            <el-input v-model="formValue.phone" maxlength="11" style="width: 240px" @input="setTheNameinput" />
          </el-form-item>
          <el-form-item label="登录名">
            <el-input v-model="formValue.name" disabled style="width: 240px" />
          </el-form-item>
          <el-form-item prop="password" required label="密码">
            <el-input v-model="formValue.password" minlength="8" maxlength="12" style="width: 240px" />
          </el-form-item>
          <el-form-item prop="allowTimes" class="ts-form-item" required label="允许登录时间段">
            <el-date-picker
              v-model="formValue.allowTimes"
              :disabled-date="disabledDate"
              :teleported="false" unlink-panels style="width: 240px" type="daterange"
              range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD"
              @change="dateHaveSelect"
            />
          </el-form-item>
          <el-form-item label="账号状态">
            <el-select v-model="formValue.status" disabled style="width: 240px">
              <el-option
                v-for="(item, index) in [
                  {
                    label: '启用中',
                    value: 0,
                  },
                  {
                    label: '已禁用',
                    value: 1,
                  },
                ]" :key="index" :label="item.label" :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="创建人">
            <el-input v-model="formValue.createBy" disabled style="width: 240px" />
          </el-form-item>
        </el-form>
      </div>
    </BasicModal>
    <BasicModal
      :visible="showUseModal" title="启用账号" width="560"
      ok-text="启 用" :footer-offset="60"
      @ok="handleUseConfirm"
      @visible-change="visibleUseChange"
    >
      <div>
        <div
          flex items-center style="
height: 36px;
padding: 0 12px;
background: #f2fcff;
border-bottom: 1px solid #ceeaf2;"
        >
          <SvgIcon
            local-icon="slmc-icon-information" size="16" color="#1296db
"
          />
          <span ml-8px text-12px color="#666">请完善该用户可登录的时间段</span>
        </div>
        <el-form :model="formValue" :rules="addRules" :label-width="120" mb-10px mt-24px inline px-20px>
          <el-form-item prop="allowTimes" class="ts-form-item" required label="允许登录时间段">
            <el-date-picker
              v-model="formValue.allowTimes"
              :disabled-date="disabledDate"
              :teleported="false" unlink-panels style="width: 290px" type="daterange"
              range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期" value-format="YYYY-MM-DD"
              @change="dateHaveSelect"
            />
          </el-form-item>
        </el-form>
      </div>
    </BasicModal>
  </div>
</template>

<style lang="scss" scoped>
  .page{
    display: flex;
    flex-direction: column;
  }
  :deep(.is-error){
    margin-bottom: 30px;
  }
</style>
