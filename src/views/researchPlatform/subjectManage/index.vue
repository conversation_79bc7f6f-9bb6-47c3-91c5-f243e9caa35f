<script setup lang='ts'>
import { Breadcrumb } from '@/layouts/common'
import { useAuthStore } from '~/src/store'
import { deleteSubject, getSubjectList2, stopSubject } from '@/api/research/index'

const router = useRouter()
const pageOptions = reactive({
  start: 1,
  size: 10,
  total: 0,
})
const { userInfo } = useAuthStore()
const searchParams = reactive({
  name: '',
  userId: userInfo.id,
  status: '',
  topicName: '',
  inTimeStart: '',
  inTimeEnd: '',
})
const showModel = ref({
  showFinishModel: false,
  finishRow: null,
  showDeleteModel: false,
  deleteRow: null,
})
const dateRange = ref()
const tableData = ref()
const statusOptions = ref([{
  label: '全部',
  value: '',
}, {
  label: '待开始',
  value: '0',
},
{
  label: '进行中',
  value: '1',
},
{
  label: '已完成',
  value: '2',
},
{
  label: '已中止',
  value: '3',
}])
function statusChange(status: number) {
  switch (status) {
    case 0:
      return ['待开始', '#45A8E6']
    case 1:
      return ['进行中', '#FF9B54']
    case 2:
      return ['已完成', '#4ACFB1']
    case 3:
      return ['已中止', '#CCCCCC']
    default:
      return '#45A8E6'
  }
}

function getDataFromNet() {
  console.log(userInfo)
  if (dateRange.value?.length > 0) {
    searchParams.inTimeStart = dateRange.value[0]
    searchParams.inTimeEnd = dateRange.value[1]
  }
  getSubjectList2({ ...searchParams, start: pageOptions.start, size: pageOptions.size, loginUserId: userInfo.id }).then((res) => {
    // console.log(res)
    tableData.value = res?.data?.records || []
    pageOptions.total = res?.data?.total || 0
  })
}

function handleSearchClick() {
  getDataFromNet()
}

function handleResetClick() {
  searchParams.topicName = null
  searchParams.name = null
  searchParams.status = null
  searchParams.inTimeStart = null
  searchParams.inTimeEnd = null
  dateRange.value = null
  getDataFromNet()
}

function detailHandler(row: any) {
  console.log('detailrow====', row)
  router.push({
    path: '/researcher/subjectManage/subjectDetail',
    query: {
      id: row.id,
      topicId: row.topicId,
      examNo: row?.examNo,
      type: row.status === 0 ? 'detail' : 'scale',
    },
  })
}

function handleEditBtnClick(row: any) {
  console.log('editrow====', row)
  router.push({
    path: '/researcher/projectManage/subjectDetail',
    query: {
      id: row.id,
      topicId: row.topicId,
      examNo: row?.examNo,
      type: 'edit',
    },
  })
}

function disabledDate(time: Date) {
  return time.getTime() > Date.now()
}

function finishHandler() {
  console.log(showModel.value.finishRow)
  stopSubject(showModel.value.finishRow?.id).then((res) => {
    console.log('删除====', res)
    if (res.data) {
      window.$message.success('中止成功')
      showModel.value.finishRow = null
      getDataFromNet()
    }
    else { window.$message.error('中止失败') }
  })
}

function deleteHandler() {
  console.log(showModel.value.deleteRow)
  deleteSubject(showModel.value.deleteRow?.id).then((res) => {
    console.log('删除====', res)
    if (res.data) {
      window.$message.success('删除成功')
      showModel.value.deleteRow = null
      getSubjectListData()
    }
    else { window.$message.error('删除失败') }
  })
}

function jumpToUnfinishTask(row) {
  /// 跳转到待办任务
  router.push({
    path: '/researcher/subjectManage/subjectDetail',
    query: {
      id: row.id,
      topicId: row.topicId,
      examNo: row?.examNo,
      type: 'toDo',
    },
  })
}

// /// 获取课题当前研究员可否编辑
// function getSubjectEditStatus(row: any) {
//   // 逻辑: 负责人可以编辑所有受试者, 研究员只能编辑自己新增的受试者
//   const topicManagers = row?.director
//   const currentManagers = userInfo?.userName
//   if ((topicInfo.value?.isAdmin && topicInfo.value?.isAdmin === true) || topicManagers === currentManagers)
//     return true
//   else if (row.researcher === currentManagers)
//     return true
//   else
//     return false
// }

onMounted(() => {
  getDataFromNet()
})
</script>

<template>
  <div class="page">
    <Breadcrumb
      :bread-list="[
        {
          title: '科研管理',
          link: null,
          key: 'researcher',
        },
        {
          title: '受试者管理',
          link: null,
          key: 'researcher_subjectManage',
        },
      ]"
    />
    <PageCard breadcrumb style="display: flex;flex-direction: column;">
      <PageTitle class="mb-2px">
        受试者管理
      </PageTitle>
      <div mt-14px flex flex-wrap items-center style="gap: 14px 20px;">
        <div flex items-center>
          <span color="#666" mr-10px text-14px>课题名称</span>
          <el-input v-model="searchParams.topicName" style="width: 230px;" placeholder="请输入" clearable />
        </div>
        <div flex items-center>
          <span color="#666" mr-10px text-14px>受试者姓名</span>
          <el-input v-model="searchParams.name" style="width: 230px;" placeholder="请输入" clearable />
        </div>
        <div flex items-center>
          <span color="#666" mr-10px text-14px>试验状态</span>
          <el-select-v2
            v-model="searchParams.status"
            :empty-values="[null, undefined]"
            :value-on-clear="null"
            :options="statusOptions"
            placeholder="请选择试验状态"
            style="width: 230px"
          />
        </div>
        <div flex items-center>
          <span color="#666" mr-10px text-14px>入组时间</span>
          <el-date-picker
            v-model="dateRange"
            unlink-panels
            value-format="YYYY-MM-DD"
            type="daterange"
            clearable
            range-separator="~"
            start-placeholder="入组日期"
            end-placeholder="入组截止"
            :disabled-date="disabledDate"
            style="width: 230px"
          />
        </div>
        <div flex items-center>
          <n-button type="primary" class="mr-10px" @click="handleSearchClick">
            查询
          </n-button>
          <n-button @click="handleResetClick">
            重置
          </n-button>
        </div>
      </div>
      <el-table scrollbar-always-on :data="tableData" stripe mt-14px overflow-y-auto>
        <el-table-column label="序号" type="index" width="55" />
        <el-table-column label="受试者姓名" :formatter="({ name }) => name || '-'" width="100">
          <template #default="{ row }">
            <div flex>
              <span>{{ row.name }}</span>
              <NTooltip v-if="row.dataStatus === 1" trigger="hover">
                <template #trigger>
                  <img src="@/assets/images/icon-hint.png" ml-5px mt-3px h-16px w-16px>
                </template>
                请完善该受试者信息！
              </NTooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="课题名称" prop="topicName" width="280" />
        <el-table-column label="试验状态" width="90">
          <template #default="{ row }">
            <div flex>
              <div mr-5px mt-8px h-6px w-6px rounded :style="{ backgroundColor: statusChange(row.status)[1] }" />
              <span>{{ statusChange(row.status)[0] }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="入组时间" :formatter="({ testStartTime }) => testStartTime || '-'" width="100">
          <template #default="{ row }">
            <div>
              <!-- row.createTime ? dayjs(row.createTime).tz('Asia/Shanghai').format('YYYY-MM-DD') : '-'  -->
              {{ row.testStartTime ? row.testStartTime.substring(0, 10) : '-' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="受试者观察号" :formatter="({ examNo }) => examNo || '-'" width="170" />
        <el-table-column label="当前试验阶段" min-width="120" :formatter="({ currentExamStageName }) => currentExamStageName || '-'" />
        <el-table-column label="待办任务数" width="100">
          <template #default="{ row }">
            <div cursor-pointer color="#3B8FD9" @click="jumpToUnfinishTask(row)">
              {{ row.unfinishedTask }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="试验分组" min-width="170">
          <template #default="{ row }">
            <!-- <div v-if="row.status === 1 && getSubjectEditStatus(row)">
            <n-select v-model:value="row.examGroup" placeholder="请选择" :options="groupOptions" @update:value="handleUpdateValue(row)" />
          </div> -->
            <div>
              {{ row.examGroup || '-' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="受试者来源" min-width="210" :formatter="({ patientSource }) => patientSource || '-'" />
        <el-table-column label="操作" fixed="right" width="95">
          <template #default="{ row }">
            <div v-if="row.status === 0" flex items-center gap-6px>
              <div
                uno-link @click="handleEditBtnClick(row)"
              >
                编辑
              </div>
              <div h-14px w-1px bg="#3B8FD9" />
              <div
                uno-link @click="() => {
                  showModel.showDeleteModel = true
                  showModel.deleteRow = row
                }"
              >
                删除
              </div>
            </div>
            <div v-else-if="row.status === 1" flex items-center gap-6px>
              <div
                uno-link @click="detailHandler(row)"
              >
                查看
              </div>
              <div h-14px w-1px bg="#3B8FD9" />
              <div
                uno-link @click="() => {
                  showModel.showFinishModel = true
                  showModel.finishRow = row
                }"
              >
                中止
              </div>
            </div>
            <div v-else>
              <div uno-link @click="detailHandler(row)">
                查看
              </div>
            </div>
          </template>
        </el-table-column>
        <template #empty>
          <div flex justify-center>
            <DataEmpty />
          </div>
        </template>
      </el-table>
      <div flex justify-end>
        <n-pagination
          v-model:page="pageOptions.start" v-model:page-size="pageOptions.size"
          :item-count="pageOptions.total" :page-sizes="[5, 10, 20, 30]" show-size-picker show-quick-jumper mt-14px
          @update:page-size="getDataFromNet"
          @update:page="getDataFromNet"
        />
      </div>
    </PageCard>
    <n-modal
      v-model:show="showModel.showFinishModel" :mask-closable="false" preset="dialog" title="温馨提示!"
      content="一旦中止，不可恢复，请谨慎操作！" positive-text="继续中止" negative-text="取 消" @positive-click="finishHandler"
      @negative-click="() => {
        showModel.showFinishModel = false
        showModel.finishRow = null
      }"
    />

    <n-modal
      v-model:show="showModel.showDeleteModel" :mask-closable="false" preset="dialog" title="温馨提示!"
      content="确认删除受试者？" positive-text="继续删除" negative-text="取 消" @positive-click="deleteHandler" @negative-click="() => {
        showModel.showDeleteModel = false
        showModel.deleteRow = null
      }"
    />
  </div>
</template>

<style lang="scss" scoped></style>
