<script setup lang='ts'>
import dayjs from 'dayjs'
import { Breadcrumb } from '@/layouts/common'
import { useAuthStore } from '~/src/store'
import { postTopicGroups, queryTopicGroups } from '@/api/research/index'

const router = useRouter()
const pageOptions = reactive({
  start: 1,
  size: 10,
  total: 0,
})

const searchParams = reactive({
  topicName: null,
  shortName: null,
  createName: null,
})

const tableData = ref()
const { userInfo } = useAuthStore()

function getDataFromNet() {
  console.log(userInfo)
  queryTopicGroups({ ...searchParams, start: pageOptions.start, size: pageOptions.size, loginUserId: userInfo.id }).then((res) => {
    // console.log(res)
    tableData.value = res?.data?.records || []
    pageOptions.total = res?.data?.total || 0
  })
}

function handleSearchClick() {
  getDataFromNet()
}

function handleResetClick() {
  searchParams.topicName = null
  searchParams.shortName = null
  searchParams.createName = null
  getDataFromNet()
}

function jumpToDetail(row) {
//   router.push({
//     path: '/researcher/scaleDetail',
//     query: {
//       scaleId: row.scaleId,
//     },
//   })
}

const showGroupModal = ref(false)
const groupFromRef = ref()
const groups = reactive({
  edit: true, /// 能否编辑
  row: {}, /// 拿到当前ref数据
  topicId: '',
  group: [{
    text: '',
  }],
})

function addGroupItem() {
  groups.group.push({
    text: null,
  })
}
function delGroupItem(index) {
  groups.group.splice(index, 1)
}

function handleBodyConfirm1() {
  /// 校验一下, 然后
  groupFromRef.value.validate((valid) => {
    if (valid) {
      showGroupModal.value = false
      /// 保存
      postTopicGroups({
        topicId: groups.topicId,
        groups: groups.group.map(item => item.text),
      }).then((res) => {
        //
        // console.log(res)
        if (res?.data) {
          window.$message.success('修改成功')
          /// 此处建议 直接修改这一项目
          groups.row.examGroups = groups.group.map((item) => {
            return { name: item.text }
          })
        }
      })
    }
  })
}
function visibleChange1() {
  showGroupModal.value = false
}

function onlyShowTheGroup(row) {
  showGroupModal.value = true
  groups.edit = false
  /// 赋值
  if (row?.examGroups?.length > 0) {
    groups.group = row?.examGroups?.map((item) => {
      return { text: item.name }
    })
  }
  else {
    groups.group = [{ text: '' }]
  }
}

function ShowTheGroupEidt(row) {
  showGroupModal.value = true
  groups.edit = true
  groups.topicId = row.topicId
  groups.row = row
  /// 赋值
  if (row?.examGroups?.length > 0) {
    groups.group = row?.examGroups?.map((item) => {
      return { text: item.name }
    })
  }
  else {
    groups.group = [{ text: '' }]
  }
}

onMounted(() => {
  getDataFromNet()
})
</script>

<template>
  <div class="page">
    <Breadcrumb
      :bread-list="[
        {
          title: '科研管理',
          link: null,
          key: 'researcher',
        },
        {
          title: '试验分组管理',
          link: null,
          key: 'researcher_groupManage',
        },
      ]"
    />
    <PageCard breadcrumb style="display: flex;flex-direction: column;">
      <PageTitle class="mb-2px">
        试验分组管理
      </PageTitle>
      <div mt-14px flex flex-wrap items-center style="gap: 14px 20px;">
        <div flex items-center>
          <span color="#666" mr-10px text-14px>课题名称</span>
          <el-input v-model="searchParams.topicName" style="width: 210px;" placeholder="请输入" />
        </div>
        <div flex items-center>
          <span color="#666" mr-10px text-14px>课题简称</span>
          <el-input v-model="searchParams.shortName" style="width: 210px;" placeholder="请输入" />
        </div>
        <div flex items-center>
          <span color="#666" mr-10px text-14px>创建人</span>
          <el-input v-model="searchParams.createName" style="width: 210px;" placeholder="请输入" />
        </div>
        <div flex items-center>
          <n-button type="primary" class="mr-10px" @click="handleSearchClick">
            查询
          </n-button>
          <n-button @click="handleResetClick">
            重置
          </n-button>
        </div>
      </div>
      <el-table scrollbar-always-on :data="tableData" stripe mt-14px overflow-y-auto>
        <el-table-column label="序号" width="80" type="index" header-align="center" align="center" />
        <el-table-column width="280" label="课题名称" prop="topicName" />
        <el-table-column
          label="课题类型"
          prop="type"
          width="130"
        />
        <el-table-column label="课题简称" prop="shortName" width="140" />
        <el-table-column label="课题负责人" prop="principal" width="100" />
        <el-table-column
          show-overflow-tooltip
          width="280"
          label="试验分组" :formatter="({ examGroups }) => {
            return examGroups?.map(item => item.name).join('、');
          }"
        />
        <el-table-column
          label="创建人" :formatter="({ createName }) => {
            return createName || '-'
          }"
        />
        <el-table-column
          label="创建时间" width="120"
          :formatter="({ createTime }) => dayjs(createTime).format('YYYY-MM-DD')"
        />
        <el-table-column fixed="right" label="操作" width="90">
          <template #default="{ row }">
            <div v-if="row.principal === userInfo.userName" text-14px color="#3B8FD9" @click="ShowTheGroupEidt(row)">
              分组维护
            </div>
            <div v-else text-14px color="#3B8FD9" @click="onlyShowTheGroup(row)">
              查看
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div flex justify-end>
        <n-pagination
          v-model:page="pageOptions.start" v-model:page-size="pageOptions.size"
          :item-count="pageOptions.total" :page-sizes="[5, 10, 20, 30]" show-size-picker show-quick-jumper mt-14px
          @update:page-size="getDataFromNet"
          @update:page="getDataFromNet"
        />
      </div>
    </PageCard>
    <BasicModal
      :show-ok-btn="groups.edit"
      :show-cancel-btn="groups.edit"
      :visible="showGroupModal" title="试验分组维护" width="560" :footer-offset="0"
      @ok="handleBodyConfirm1" @cancel="visibleChange1"
    >
      <el-form ref="groupFromRef" :disabled="!groups.edit" class="group-cont" :model="groups" flex-col px-20px py-20px style="max-height:460px;overflow-y: auto;">
        <el-form-item
          v-for="(item, index) in groups.group" :key="index" :rules="{
            required: true,
            message: `请输入分组名称`,
            trigger: ['input', 'blur'],
          }"
          :prop="`group[${index}].text`"
        >
          <div :style="{ visibility: index === 0 ? 'visible' : 'hidden' }">
            试验分组
          </div>
          <el-input v-model="item.text" clearable style="width: 360px;margin-left:10px;" placeholder="请输入分组名称" />
          <div v-if="groups.edit" ml-10px flex items-center style="gap:0 10px">
            <img v-if="groups.group.length > 1" h-16px w-16px src="@/assets/images/keyan_group_del.png" @click="delGroupItem(index)">
            <img v-if="index === groups.group?.length - 1" h-16px w-16px src="@/assets/images/keyan_group_add.png" @click="addGroupItem">
          </div>
        </el-form-item>
      </el-form>
    </BasicModal>
  </div>
</template>

<style lang="scss" scoped></style>
