<script setup lang="ts">
import * as echarts from 'echarts/core'
import { onMounted, ref } from 'vue'
import Empty from './Empty.vue'
import { ageAndSexCount } from '~/src/api/screen/screen'
import { localStg } from '~/src/utils/storage'

const props = defineProps({
  currentSelect: {
    type: String,
    required: true,
  },
})

const userInfo = localStg.get('userInfo')

const isEmpty = ref(true)
const dataList = ref<any>([])
const dataListSex = ref<any>([])

watch(() => props.currentSelect, (newVal) => {
  // 监听
  // const organList = flattenChildren(userInfo?.organList)
  // const findObj = organList.find((item: any) => item.organName === newVal)
  // console.log(findObj)
  // if (findObj)
  //   initData(findObj.parentId)
  // else
  //   initData()
  initData()
},
{ deep: true },
)

function initData(val?: any) {
  const params = {
    endTime: '',
    organId: props.currentSelect,
    searchOrganId: val || '0',
    startTime: '',
    status: '',
  }
  ageAndSexCount(params).then((res: any) => {
    const total = res.data['患者总数']
    const formatData = Object.entries(res.data).map(([name, value]) => ({ name, value })).filter(item => item.name !== '女性' && item.name !== '男性' && item.name !== '患者总数')
    const formatData2 = Object.keys(res.data)
      .filter(key => (key === '男性' || key === '女性' || key === '患者总数'))
      .reduce((obj: any, key: any) => {
        obj[key] = res.data[key]
        return obj
      }, {})
    dataListSex.value = formatData2
    dataList.value = formatData.sort((a, b) => a.name.localeCompare(b.name))
    if (sumArray(dataList.value) === 0) {
      isEmpty.value = false
      return
    }
    else {
      isEmpty.value = true
    }
    dataList.value = dataList.value.map((item: any) => {
      if (item.name === '30岁以下') {
        return {
          ...item,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [
              { offset: 0, color: '#61D2FF' },
              { offset: 1, color: '#3274FF' },
            ]),
          },
        }
      }
      if (item.name === '31-45岁') {
        return {
          ...item,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [
              { offset: 0, color: '#FFB3CB' },
              { offset: 1, color: '#FF7292' },
            ]),
          },
        }
      }
      if (item.name === '46-60岁') {
        return {
          ...item,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [
              { offset: 0, color: '#46FEAF' },
              { offset: 1, color: '#7BFCE8' },
            ]),
          },
        }
      }
      if (item.name === '60岁以上') {
        return {
          ...item,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [
              { offset: 0, color: '#FF905D' },
              { offset: 1, color: '#FFF582' },
            ]),
          },
        }
      }
    })
    nextTick(() => {
      initCharts()
    })
  })
}

function initCharts() {
    type EChartsOption = echarts.EChartsCoreOption
    const chartDom = document.getElementById('age')!
    const myChart = echarts.init(chartDom)
    const option: EChartsOption = {
      tooltip: {
        show: false,
        trigger: 'item',
      },
      // legend: {

      //   orient: 'vertical',
      //   itemGap: 20,
      //   fontSize: '20',
      //   itemWidth: 15,
      //   itemHeight: 15,
      //   textStyle: {
      //     color: 'white', // 设置图例文字颜色为白色
      //   },
      //   top: '10%',
      //   left: '60%', // 图例距离右侧的距离,
      //   // formatter(params: any) {
      //   //   return `${params} ${dataList.value}`
      //   // },
      // },

      series: [
        {
          name: 'Access From',
          type: 'pie',
          radius: ['65%', '80%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderWidth: 0,
          },
          label: {
            show: false,
            position: 'center',
          },
          emphasis: {
            label: {
              show: false,
              fontSize: '20px',
              color: '#fffff',
              background: 'rgba(18,88,162,0.30)',
              fontWeight: 'bold',
              border: '1px solid rgba(255,255,255,0.17)',
            },
          },
          labelLine: {
            show: false,
          },
          data: dataList.value,
          center: ['30%', '55%'],
        },
        // {
        //   itemStyle: {
        //     normal: {
        //       color: '#ffffff',
        //       shadowBlur: 6,
        //       shadowColor: 'rgba(63, 127, 255, 1)',
        //       shadowOffsetX: 0,
        //       shadowOffsetY: 0,
        //     },
        //   },

        //   type: 'pie',
        //   // silent: true, // 取消高亮
        //   radius: ['88%', '88%'],
        //   center: ['30%', '50%'],
        //   showEmptyCircle: true,
        //   hoverAnimation: false, // 取消动画效果
        //   emptyCircleStyle: {
        //     // color: '#FFFFFF',
        //     borderColor: '#FFFFFF',
        //     borderType: [3, 6],
        //     borderDashOffset: 3,
        //   },
        //   z: -1,
        // },
      ],
    }
    option && myChart.setOption(option)
}

function sumArray(data: any) {
  let sum = 0
  for (let i = 0; i < data.length; i++)
    sum += Number.parseInt(data[i].value)
  return sum
}
onMounted(() => {
  initData()
})

// option && myChart.setOption(option);
</script>

<template>
  <div v-if="isEmpty" relative>
    <div id="age" relative h-200px w-430px />
    <div absolute class="circleText">
      <div h-30px color="#8CCEFF" text-16px>
        总数
      </div>
      <div color="#ffffff">
        {{ sumArray(dataList) }}
      </div>
    </div>
    <div absolute class="icon_position">
      <div color="#ffffff" my-20px>
        <n-grid x-gap="12" :cols="1">
          <n-gi v-for="(item, index) in dataList" :key="item.name" my-10px>
            <div flex items-center>
              <div :class="`icon_${index + 1}`" />
              <span mx-5px color="#8CCEFF">{{ item.name }}:</span>
              <span mx-15px>{{ item.value }}人</span>
              <span inline-block>{{ (item.value / sumArray(dataList) * 100).toFixed(2) }}%</span>
              <!-- <span>55.5%</span> -->
            </div>
          </n-gi>
        </n-grid>
      </div>
    </div>
    <div ml-40px style="border: 1px dashed #055382;" mt-5px />
    <div my-15px ml-40px flex justify-between>
      <div color="#fff">
        <span color="#8cceff">男性</span>
        <span mx-10px>{{ dataListSex['男性'] }}</span>
        <span>{{ (dataListSex['男性'] / dataListSex['患者总数'] * 100).toFixed(2) }}%</span>
      </div>
      <div color="#fff">
        <span color="#8cceff">女性</span>
        <span mx-10px>{{ dataListSex['女性'] }}</span>
        <span>{{ (dataListSex['女性'] / dataListSex['患者总数'] * 100).toFixed(2) }}%</span>
      </div>
    </div>
    <div ml-40px w-430px flex>
      <div />
      <div style="background: #0091ff;width: 50%;border-radius: 10px 0 0 10px;">
        <span color="#0091ff"> {{ 2 }}</span>
      </div>
      <div style="background: #FF769D;width: 50%;border-radius: 0 10px 10px 0px;">
        <span color="#FF769D"> {{ 2 }}</span>
      </div>
    </div>
  </div>
  <div v-else w-full>
    <Empty />
  </div>
</template>

<style lang="scss" scoped>
.circleText{
  top: 60px;
  width: 96px;
  height: 96px;
  left: 81px;
  line-height: 70px;
  border-radius: 50%;
  text-align: center;
  background: rgba(18,88,162,0.30);
  color: #ffffff;
}
.icon_position{
  left: 250px;
  top: 30px;
}
.icon_1{
  width: 11px;
  height: 11px;
  background: linear-gradient(90deg,#61d2ff, #3274ff);
}
.icon_2{
  width: 11px;
  height: 11px;
  background: linear-gradient(90deg,#ffb3cb, #ff7292);
}
.icon_3{
  width: 11px;
  height: 11px;
  background: linear-gradient(270deg,#46feaf 0%, #7bfce8);
}
.icon_4{
  width: 11px;
  height: 11px;
  background: linear-gradient(270deg,#ff905d 0%, #fff582);
}
</style>
