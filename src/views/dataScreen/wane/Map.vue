<script setup lang="ts">
import * as echarts from 'echarts'
import { useMouse } from '@vueuse/core'
import arcode from '@/utils/arcode'

const props = defineProps({
  data: {
    type: Array,
    default: () => [],
  },
})

let myChart: any = null
const mapContainerRef = ref<any>(null)

let currentDataIndex = -1
const currentMapData = ref<any>(null)

const cityTemp = computed(() => {
  let temp = ''
  // const currentCityCode = currentMapData.value?.code
  const currentCityName = currentMapData.value?.name
  // let currentCitys = []
  // const citys: any = props.data.find((item: any) => item.adcode === currentCityCode)

  // currentCitys = citys?.child || arcode[currentCityCode].child
  temp = `
          <div flex gap-10px>
            <div max-w-90px text="#8CCEFF 14px" truncate>${currentCityName}:</div>
          </div>
          `
  // for (const city of currentCitys) {
  //   temp += `
  //         <div flex gap-10px>
  //           <div max-w-90px text="#8CCEFF 14px" truncate>${city.name.replaceAll('市', '')}:</div>
  //           <div font="700" text="18px #fff">${city?.num || 0}</div>
  //         </div>
  //         `
  // }

  return temp
})

const { x, y } = useMouse()

let mouseActionTimer: any = null
let hightlightTimer: any = null
let stopFlag = false

watch([x, y], () => {
  mouseActionTimer && clearTimeout(mouseActionTimer)
  hightlightTimer && clearInterval(hightlightTimer)
  stopHightlight()
  mouseActionTimer = setTimeout(() => {
    stopFlag = false
    startHighlight()
  }, 3000)
})

function startHighlight() {
  function hightList() {
    myChart.dispatchAction({
      type: 'downplay',
      seriesIndex: 0,
      dataIndex: currentDataIndex,
    })
    currentDataIndex++

    myChart.dispatchAction({
      type: 'highlight',
      seriesIndex: 0,
      dataIndex: currentDataIndex,
    })

    myChart.dispatchAction({
      type: 'showTip',
      seriesIndex: 0,
      dataIndex: currentDataIndex,
    })

    if (currentDataIndex >= 33)
      currentDataIndex = -1
  }

  hightList()
  hightlightTimer = setInterval(() => {
    hightList()
  }, 8000)
}

function stopHightlight() {
  if (stopFlag)
    return

  stopFlag = true
  myChart && myChart.dispatchAction({
    type: 'downplay',
    seriesIndex: 0,
  })
}

async function render(map: any, data: any) {
  try {
    stopFlag = false
    currentDataIndex = -1
    currentMapData.value = null
    myChart && myChart.dispose()
    myChart = null
    myChart = echarts.init(mapContainerRef.value)
    const rawData = await fetch(`/geo/${map}.json`)
    const geoJSON = await rawData.json()
    echarts.registerMap(map, geoJSON)

    const option = {
      tooltip: {
        trigger: 'item',
        padding: 0,
        enterable: true, // 鼠标是否可进入提示框浮层中
        hideDelay: 200, // 浮层隐藏的延迟
        backgroundColor: 'rgba(235,91,0,0.39);',
        position: 'left',
        formatter(params: any) {
          currentDataIndex = params.dataIndex
          currentMapData.value = params.data
          return `
          <div class="map-tooltip">
            <div class="tooltip-body" w-239px h-114px grid-gap-y-20px flex justify-center>
              <div>
               <div  class='patient_color' mb-10px>${currentMapData.value?.name}</div>
                <div class='patient_total'>肝病患者总数:${params.data.value ? params.data.value : '0'}</div>
              </div>
            </div>
          </div>
        `
        },
      },
      geo: {
        silent: true,
        map,
        roam: false, // 开启鼠标缩放和漫游
        zoom: 1, // 地图缩放级别
        layoutCenter: ['50%', '50%'],
        layoutSize: '100%', // 保持地图宽高比
        scaleLimit: {
          min: 1,
          max: 5,
        },
        itemStyle: {
          // borderColor: '#78DEF8',
          // borderWidth: 10,
          // shadowColor: '#78DEF8',
          // shadowOffsetX: 0,
          shadowOffsetY: 0,
          // shadowBlur: 10,
        },

        label: {
          show: false,
          fontSize: 10,
          color: '#ceac09',
        },

      },
      series: [
        {
          roam: false,
          zoom: 1, // 地图缩放级别
          layoutCenter: ['50%', '50%'],
          layoutSize: '100%', // 保持地图宽高比
          scaleLimit: {
            min: 1,
            max: 5,
          },
          name: '地域分布',
          type: 'map',
          map,
          geoIndex: 1,
          data,
          itemStyle: {
            borderColor: '#78DEF8',
            borderWidth: 2,
            shadowBlur: 1,
            areaColor: '#003e6f',
          },
          emphasis: {
            label: {
              show: false,
            },
            itemStyle: {
              areaColor: '#5E4D48',
            },
          },

        },
      ],
    }

    myChart.setOption(option)

    startHighlight()
  }
  catch (error) {
    console.log(error)
  }
}

watch(() => props.data, async (datas) => {
  const data = Object?.entries(arcode)?.map(([key, value]) => {
    const findOne: any = datas.find((item: any) => item.adcode === key)
    const num = findOne?.num || 0
    return {
      code: key,
      name: value?.name,
      value: num,
    }
  })

  await nextTick()

  render('100000', data)
},
{
  immediate: true,
},
)

// onMounted(async () => {
//   const data = getFakeData('100000')
//   render('100000', data)
// })

onBeforeUnmount(() => {
  mouseActionTimer && clearTimeout(mouseActionTimer)
  hightlightTimer && clearInterval(hightlightTimer)
})
</script>

<template>
  <div id="map-container" ref="mapContainerRef" relative bottom-0 style="width: 850px;height: 795px;" />
</template>

<style lang="scss">
.map-tooltip {
  // width: 400px;
  box-sizing: border-box;

  .tooltip-header {
    padding-left: 20px;
    background-image: url('@/assets/images/dataScreen/tooltip-header.png');
    background-repeat: no-repeat;
    box-sizing: border-box;
    height: 47px;

    .header-title {
      font-size: 18px;
      line-height: 47px;
      color: #fff;
      font-weight: 600;
    }
  }

  .tooltip-body {
    overflow: auto;
    max-height: 370px;
    padding: 20px;
    // border-image: linear-gradient(180deg, rgba(28,143,233,0.50), rgba(28,143,233,0.00) 55%, rgba(28,143,233,0.50)) 2 2;
    // // box-shadow: 0px 0px 30px 0px rgba(27,112,255,0.50) inset;
    // // box-sizing: border-box;
    // border: 2px solid;
    // border-image: linear-gradient(180deg, rgba(28,143,233,0.50), rgba(28,143,233,0.00) 55%, rgba(28,143,233,0.50)) 2 2;
    // box-shadow: 0px 0px 30px 0px rgba(27,112,255,0.50) inset;
    // background: rgba(0,21,51,0.95);
    background-image: url('@/assets/images/dataScreen/map_bg.svg');
    background-repeat: no-repeat;
    .patient_total{
      font-size: 16px;
      color: #ffffff;
    }
    .patient_color{
      font-size: 16px;
      color: #8CCEFF;
    }

    &::-webkit-scrollbar-track-piece {
      background-color: #082351 !important;
      border-radius: 3px 3px 0 0;
      border: 1px solid;
      box-shadow: none;

      &:vertical {
        border-top: 0px;
        border-bottom: 0px;
      }

      &:horizontal {
        border-left: 0px;
        border-right: 0px;
      }
    }

    &::-webkit-scrollbar-thumb:vertical {
      background-color: #0971BA;

      &:hover {
        background-color: #0971BA !important;
      }
    }
  }

}
</style>
