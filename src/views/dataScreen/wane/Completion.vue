<script setup lang="ts">
import * as echarts from 'echarts/core'
import { onMounted, watch } from 'vue'
import Empty from './Empty.vue'
import { newPlanPatientCount } from '~/src/api/screen/screen'
import { localStg } from '~/src/utils/storage'

const props = defineProps({
  currentSelect: {
    type: String,
    required: true,
  },
})

const userInfo = localStg.get('userInfo')

const isEmpty = ref(true)

const completeData = ref<any>([])

watch(() => props.currentSelect, (newVal) => {
  // 监听
  // const organList = flattenChildren(userInfo?.organList)
  // const findObj = organList.find((item: any) => item.organName === newVal)

  // isEmpty.value = true
  // if (findObj)
  //   getDataInit(findObj.id)
  // else
  getDataInit()
},
{ deep: true },
)

function getDataInit(val?: any) {
  const params = {
    endTime: '',
    organId: props.currentSelect,
    searchOrganId: val || '0',
    startTime: '',
    status: '',
  }
  newPlanPatientCount(params).then((res: any) => {
    if (res.data.length === 0) {
      isEmpty.value = false
      return
    }
    else {
      isEmpty.value = true
    }
    completeData.value = res.data
    nextTick(() => {
      initCharts()
    })
  })
}

function initCharts() {
    type EChartsOption = echarts.EChartsCoreOption
    const chartDom = document.getElementById('complete')!
    const myChart = echarts.init(chartDom)

    const option: EChartsOption = {
      grid: {
        show: true, // 显示绘图区域网格线
        borderWidth: 0, // 设置绘图区域网格线的边框宽度为0，即去掉边框
        borderColor: '#055382', // 设置绘图区域网格线的颜色
        // backgroundColor: '#055382', // 设置绘图区域的背景颜色为透明
        left: '10%', // 左边距
        right: '10%', // 右边距
        top: '15%', // 上边距
        bottom: '10%', // 下边距
        containLabel: true,
        cursor: 'default',
      },
      cursor: 'default',
      // tooltip: {
      //   trigger: 'axis', // 触发类型, axis: 坐标轴触发
      //   axisPointer: {
      //     type: 'line', // 指示器类型
      //   },
      //   textStyle: {
      //     // color: '#d5dbff', // 文字颜色
      //   },
      //   // 提示框浮层内容格式器，支持字符串模板和回调函数两种形式
      //   // 折线（区域）图、柱状（条形）图、K线图 : {a}（系列名称），{b}（类目值），{c}（数值）, {d}（无）
      //   // formatter: '{b}<br />{a0}: {c0}万<br />{a3}: {c3}%',
      // },
      xAxis: {
        type: 'category',
        cursor: 'default',
        boundaryGap: false,
        axisLine: {
          lineStyle: {
            color: '#8CCEFF', // 设置 y 坐标轴的颜色
            type: 'dashed',

          },
        },
        axisLabel: {
          rotate: 45, // 设置横坐标标签逆时针倾斜45度
        },
        data: Object.keys(completeData.value),
      },
      yAxis: {
        name: '人数',
        type: 'value',
        cursor: 'default',
        min: 0, // 设置 Y 轴最小值为 0
        // max: 0, // 设置 Y 轴最大值为 1
        axisLabel: {
          formatter(value: number) {
            // return (value === 0) ? value : ''
            if (value === 0)
              return value

            if (value > 0 && value < 1)
              return null

            if (value > 1 && value < 2)
              return null
            if (value > 2 && value < 3)
              return null
            if (value > 3)
              return value

            // else
            //   return value
          },
        },
        splitLine: {
          show: true, // 显示x轴的分隔线
          lineStyle: {
            type: 'dashed', // 设置分隔线的样式为虚线
            color: '#055382',
          },
        },
        axisLine: {
          lineStyle: {
            color: '#8CCEFF', // 设置 y 坐标轴的颜色
          },
        },
      },
      series: [
        {
          silent: true,
          data: Object.values(completeData.value),
          type: 'line',
          lineStyle: {
            color: '#45F8CD',
          },
          itemStyle: {
            color: '#45F8CD',
            borderWidth: 1,
            borderColor: 'black',
          },
          areaStyle: {
            cursor: 'default',
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#45F8CD' },
              // { offset: 0.2, color: 'rgba(69,248,205,0.10)' },
              { offset: 1, color: 'rgba(69,248,205,0.10)' },
            ]),
          },
        },
      ],
    }

    option && myChart.setOption(option)
}

onMounted(() => {
  getDataInit()
})

// option && myChart.setOption(option);
</script>

<template>
  <!-- <div color="#fff">
    {{ props.currentSelect }}
  </div> -->
  <div v-if="isEmpty">
    <div id="complete" h-280px />
  </div>
  <div v-else>
    <Empty />
  </div>
</template>
