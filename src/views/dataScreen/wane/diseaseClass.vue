<script setup lang="ts">
import * as echarts from 'echarts/core'
import { onMounted } from 'vue'
import Empty from './Empty.vue'
import { localStg } from '~/src/utils/storage'
import { diseasesCount } from '~/src/api/screen/screen'

const props = defineProps({
  currentSelect: {
    type: String,
    required: true,
  },
})

const userInfo = localStg.get('userInfo')

const data = ref<any>([{
  name: '',
  value: 0,
}])

const isEmpty = ref(true)

watch(() => props.currentSelect, (newVal) => {
  // 监听

  // const organList = flattenChildren(userInfo?.organList)
  // const findObj = organList.find((item: any) => item.organName === newVal)
  // console.log(findObj)
  // isEmpty.value = true
  // if (findObj)
  //   getDataInit(findObj.id)
  // else
  //   getDataInit()
  // if (newVal && newVal !== '全部')
  //   getDataInit('1')

  // else
  getDataInit()
},
{ deep: true },
)

function getDataInit(val?: any) {
  const params = {
    endTime: '',
    organId: props.currentSelect,
    searchOrganId: '',
    startTime: '',
    status: val || '',
  }
  diseasesCount(params).then((res: any) => {
    const formatData = Object.entries(res.data).map(([name, value]) => ({ name, value })).filter(item => item.name !== '患者总数')
    data.value = formatData

    const sortOrder = ['乙肝', '丙肝', '脂肪肝', '其他肝病']
    data.value = data.value.sort((a: any, b: any) => {
      return sortOrder.indexOf(a.name) - sortOrder.indexOf(b.name)
    })
    if (sumArray(data.value) === 0) {
      isEmpty.value = false
      return
    }
    else {
      isEmpty.value = true
    }

    data.value = data.value.map((item: any) => {
      if (item.name === '脂肪肝') {
        return {
          ...item,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [
              { offset: 0, color: '#FFB3CB' },
              { offset: 1, color: '#FF7292' },
            ]),
          },
        }
      }
      if (item.name === '其他肝病') {
        return {
          ...item,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [
              { offset: 0, color: '#FFF582' },
              { offset: 1, color: '#FF905D' },
            ]),
          },
        }
      }
      if (item.name === '乙肝') {
        return {
          ...item,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [
              { offset: 0, color: '#46FEAF' },
              { offset: 1, color: '#7BFCE8' },
            ]),
          },
        }
      }
      if (item.name === '丙肝') {
        return {
          ...item,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [
              { offset: 0, color: '#61D2FF' },
              { offset: 1, color: '#3274FF' },
            ]),
          },
        }
      }
    })
    nextTick(() => {
      initCharts()
    })
  })
}

function initCharts() {
    type EChartsOption = echarts.EChartsCoreOption
    const chartDom = document.getElementById('main')!
    const myChart = echarts.init(chartDom, null, { height: '240px' })
    const option: EChartsOption = {
      tooltip: {
        show: false,
        // trigger: 'item',
      },
      // legend: {
      //   orient: 'vertical',
      //   textStyle: {
      //     color: 'white', // 设置图例文字颜色为白色
      //   },
      //   top: '30%',
      //   left: '60%', // 图例距离右侧的距离
      // },
      series: [
        {
          name: 'Access From',
          type: 'pie',
          radius: ['40%', '60%'],
          avoidLabelOverlap: false,
          itemStyle: {
            borderWidth: 0,
          },
          label: {
            show: false,
            position: 'center',
          },
          emphasis: {
            label: {
              show: false,
              fontSize: '20px',
              color: '#fffff',
              fontWeight: 'bold',
            },
          },
          labelLine: {
            show: false,
          },
          data: data.value || [],
          center: ['30%', '52%'],
        },
      ],
    }
    option && myChart.setOption(option)
}

function sumArray(data: any) {
  let sum = 0
  for (let i = 0; i < data.length; i++)
    sum += Number.parseInt(data[i].value)
  return sum
}

onMounted(() => {
  nextTick(() => {
    // initCharts()
    getDataInit()
  })
})
// option && myChart.setOption(option);
</script>

<template>
  <div v-if="isEmpty" relative class="bg_image" h-240px>
    <div id="main" h-240px w-516px />
    <div absolute class="icon_position" h-240px>
      <div v-for="(item, index) in data" :key="item.name" color="#ffffff" my-20px>
        <n-grid x-gap="10" :cols="1">
          <n-gi>
            <div flex>
              <div :class="`icon_${index + 1}`" mr-20px />
              <span color="#8CCEFF">{{ item.name }}</span>
              <span ml-10px>{{ item.value }}人</span>
              <span ml-10px inline-block>{{ (item.value / sumArray(data) * 100).toFixed(2) }}%</span>
            </div>
          </n-gi>
        </n-grid>
      </div>
      <!-- <div color="#ffffff" my-10px>
        <n-grid x-gap="12" :cols="1">
          <n-gi>
            <div flex>
              <div class="icon_2" mr-20px />
              <span color="#8CCEFF">30岁以下：</span>
              <span mr-10px inline-block>362</span>
              <span>55.5%</span>
            </div>
          </n-gi>
        </n-grid>
      </div>
      <div color="#ffffff" my-20px>
        <n-grid x-gap="12" :cols="1">
          <n-gi>
            <div flex>
              <div class="icon_3" mr-20px />
              <span color="#8CCEFF">30岁以下：</span>
              <span mr-10px inline-block>362</span>
              <span>55.5%</span>
            </div>
          </n-gi>
        </n-grid>
      </div>
      <div color="#ffffff" my-10px>
        <n-grid x-gap="12" :cols="1">
          <n-gi>
            <div flex>
              <div class="icon_4" mr-20px />
              <span color="#8CCEFF">30岁以下：</span>
              <span mr-10px inline-block>362</span>
              <span>55.5%</span>
            </div>
          </n-gi>
        </n-grid>
      </div> -->
    </div>
  </div>
  <div v-else>
    <Empty />
  </div>
</template>

<style lang="scss" scoped>
.icon_position{
  left: 285px;
  top: 50px;
}
.icon_1{
  width: 11px;
  height: 11px;
  background: linear-gradient(270deg,#46feaf, #7bfce8 100%);
}
.icon_2{
  width: 11px;
  height: 11px;
  background: linear-gradient(90deg,#61d2ff, #3274ff);

}
.icon_3{
  width: 11px;
  height: 11px;
  background: linear-gradient(90deg,#ffb3cb, #ff7292);
}
.icon_4{
  width: 11px;
  height: 11px;
  background: linear-gradient(90deg,#fff582, #ff905d);
}
.bg_image{
   background: url('@/assets/images/dataScreen/circle.svg')  no-repeat 26px 22px ;
   background-size: 50% 85%;
  // background-size: 50% auto;
  }
</style>
