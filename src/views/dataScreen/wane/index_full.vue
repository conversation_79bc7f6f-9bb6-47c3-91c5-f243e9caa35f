<script setup lang='ts'>
import VScaleScreen from 'v-scale-screen'
import { useFullscreen } from '@vueuse/core'
import DataScreen from './data-screen.vue'

const scaleSCreenRef = ref(null)

const { isFullscreen, toggle } = useFullscreen(scaleSCreenRef)
</script>

<template>
  <VScaleScreen ref="scaleSCreenRef" width="1920" height="1080">
    <DataScreen :is-fullscreen="isFullscreen" @fullScreenClick="toggle" />
  </VScaleScreen>
</template>
