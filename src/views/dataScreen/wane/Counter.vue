<script setup lang="ts">
const props = defineProps({
  count: {
    type: String,
    default: '0',
  },
  title: {
    type: String,
    default: '',
  },
})

const countString = computed(() => {
  return String(props.count)
})
</script>

<template>
  <div class="count_bg">
    <div color="#8CCEFF" w-full pb-6px text-center text-18px>
      {{ title }}
    </div>
    <div w-full flex justify-center gap-4px>
      <template v-for="(item, index) in countString" :key="index">
        <div
          :class="item !== ',' ? 'number-bg' : ''"
          text="#fff 36px"
          flex items-center
          justify-center
        >
          {{ item }}
        </div>
        <div v-if="countString.length > 3 && index === countString.length - 4" relative>
          <img absolute class="bottom-0 -right-3px" src="@/assets/images/dataScreen/split.svg" alt="" srcset="">
        </div>
      </template>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.count_bg {
  background-image: url('@/assets/images/dataScreen/count_bg.svg');
  background-repeat: round;
  height: 120px;
  width: 240px;
  line-height: 30px;
  box-sizing: border-box;
  padding: 18px;
}

.number-bg {
  background-image: url('@/assets/images/dataScreen/number_bg.png');
  height: 44px;
  background-repeat: round;
  width: 34px;
  box-sizing: border-box;
  font-family: DIN Alternate, DIN Alternate-Bold;
}
</style>
