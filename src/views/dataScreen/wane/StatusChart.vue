<script setup lang="ts">
import dayjs from 'dayjs'
import * as echarts from 'echarts/core'
import { onMounted } from 'vue'
import Empty from './Empty.vue'
import { rateCount } from '~/src/api/screen/screen'
import { localStg } from '~/src/utils/storage/local'

const props = defineProps({
  currentSelect: {
    type: String,
    required: true,
  },
})

const userInfo = localStg.get('userInfo')

const isEmpty = ref(true)

const chartData = ref<any>([])

watch(() => props.currentSelect, (newVal) => {
  // 监听

  // const organList = flattenChildren(userInfo?.organList)
  // const findObj = organList.find((item: any) => item.organName === newVal)

  // isEmpty.value = true

  // if (findObj)
  //   getDataInit(findObj.id, newVal === '全部')
  // else
  getDataInit(null, true)
},
{ deep: true },
)

function getDataInit(id?: any, isAll = false) {
  const params = {
    startTime: dayjs().subtract(6, 'month').startOf('day').format('YYYY-MM-DD'),
    endTime: dayjs().endOf('day').startOf('day').format('YYYY-MM-DD'),
    organId: props.currentSelect || '',
    isAll,
  }
  rateCount(params).then((res: any) => {
    if (res.data.length === 0) {
      isEmpty.value = false
      return
    }
    else {
      isEmpty.value = true
    }
    chartData.value = res.data
    nextTick(() => {
      initCharts()
    })
  })
}

function initCharts() {
    type EChartsOption = echarts.EChartsCoreOption
    const chartDom = document.getElementById('tender')!
    const myChart = echarts.init(chartDom)

    const option: EChartsOption = {
      // 设置图表的位置
      grid: {
        x: 40, // 左间距
        y: 30, // 上间距
        x2: 50, // 右间距
        y2: 30, // 下间距
        containLabel: true, // grid 区域是否包含坐标轴的刻度标签, 常用于『防止标签溢出』的场景
      },
      // dataZoom 组件 用于区域缩放
      dataZoom: [
        {
          type: 'inside',
          xAxisIndex: [0], // 设置 dataZoom-inside 组件控制的 x轴
          // 数据窗口范围的起始和结束百分比  范围: 0 ~ 100
          start: 0,
          end: 100,
        },
      ],
      // 图表主标题
      // title: {
      //   text: '订单', // 主标题文本，支持使用 \n 换行
      //   top: 10, // 定位 值: 'top', 'middle', 'bottom' 也可以是具体的值或者百分比
      //   left: 'center', // 值: 'left', 'center', 'right' 同上
      //   textStyle: { // 文本样式
      //     fontSize: 24,
      //     fontWeight: 600,
      //     color: '#fff',
      //   },
      // },
      // 设置自定义文字
      graphic: [
        {
          type: 'text', // 图形元素类型
          left: 70, // 进行定位
          bottom: 32,
          style: { // 文本样式
            fill: '#cdd3ee',
            // text: '(月份)',
            font: 'normal 13px Microsoft', // style | weight | size | family
          },
        },
      ],
      // 提示框组件
      // tooltip: {
      //   trigger: 'axis', // 触发类型, axis: 坐标轴触发
      //   axisPointer: {
      //     type: 'line', // 指示器类型
      //   },
      //   textStyle: {
      //     // color: '#d5dbff', // 文字颜色
      //   },
      //   // 提示框浮层内容格式器，支持字符串模板和回调函数两种形式
      //   // 折线（区域）图、柱状（条形）图、K线图 : {a}（系列名称），{b}（类目值），{c}（数值）, {d}（无）
      //   // formatter: '{b}<br />{a0}: {c0}万<br />{a3}: {c3}%',
      // },
      // 图例组件
      legend: {
        show: false,
        // 图例的数据数组
        // data: ['订单1', '转化率'], // 图例项的名称 与 series 里的 name 相对应
        top: 13, // 定位
        right: 30,
        textStyle: { // 文本样式
          fontSize: 16,
          color: '#cdd3ee',
        },
      },
      // X轴
      xAxis: {
        show: true, // 不设置默认值为 true
        type: 'category', // 坐标轴类型,  'category' 类目轴，适用于离散的类目数据，为该类型时必须通过 data 设置类目数据
        // 坐标轴轴线
        axisLine: {
          lineStyle: {
            type: 'solid', // 坐标轴线线的类型 'solid', 'dashed', 'dotted'
            width: 1, // 坐标轴线线宽, 不设置默认值为 1
            color: '#204756', // 坐标轴线线的颜色
          },
        },
        // 坐标轴刻度
        axisTick: {
          show: false,
        },
        // 分隔线
        splitLine: {
          show: false,
        },
        // 坐标轴刻度标签
        axisLabel: {
          fontSize: 16, // 文字的字体大小
          color: '#cdd3ee', // 刻度标签文字的颜色
          rotate: 45, // 设置横坐标标签逆时针倾斜45度
          // 使用函数模板   传入的数据值 -> value: number|Array,
          // formatter(value) {
          //   return value.replace(/[\u4E00-\u9FA5]/g, '')
          // },
        },
        // 类目数据，在类目轴（type: 'category'）中有效
        data: chartData.value.map((item: any) => item.time),
      },
      yAxis: [
        // 左侧Y轴
        {
          type: 'value', // 坐标轴类型,   'value' 数值轴，适用于连续数据
          // min: 0, // 坐标轴刻度最小值
          // max: 100, // 坐标轴刻度最大值
          // 坐标轴刻度
          axisTick: {
            show: false, // 是否显示坐标轴刻度 默认显示
          },
          // 坐标轴轴线
          axisLine: { // 是否显示坐标轴轴线 默认显示
            show: false, // 是否显示坐标轴轴线 默认显示
            lineStyle: { // 坐标轴线线的颜色
              color: '#204756',
            },
          },
          // 坐标轴在 grid 区域中的分隔线
          splitLine: {
            show: true, // 是否显示分隔线，默认数值轴显示
            lineStyle: {
              color: '#204756', // 分隔线颜色
              opacity: 0.5, // 分隔线透明度
              type: 'dashed',
            },
          },
          // 坐标轴刻度标签
          axisLabel: {
            show: true, // 是否显示刻度标签 默认显示
            fontSize: 16, // 文字的字体大小
            color: '#cdd3ee', // 刻度标签文字的颜色
            min: `${0}%`,
            formatter(value: number) {
              if (value === 0)
                return `${value}%`

              if (value > 0 && value < 1)
                return null

              if (value > 1 && value < 2)
                return null
              if (value > 2 && value < 3)
                return null
              if (value > 3)
                return `${Math.floor(value)}%`
            },
            // 使用字符串模板，模板变量为刻度默认标签 {value}

          },
        },
      ],
      // 系列列表
      series: [
        {
          name: '1', // 系列名称，用于tooltip的显示，legend 的图例筛选
          type: 'bar', // 系列类型
          barWidth: 15, // 指定柱宽度，可以使用绝对数值或百分比，默认自适应
          // 图例的图形样式
          itemStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [{
                offset: 0,
                color: 'rgba(55,234,242,0.80)', // 0% 处的颜色
              }, {
                offset: 1,
                color: 'rgba(55,234,242,0.00)', // 100% 处的颜色
              }],
            },
          },
          data: chartData.value.map((item: any) => item.rate),
        },
        // 最上面的圆片
        // {
        //   type: 'null', // 系列类型
        //   symbolSize: [15, 10], // 标记的大小
        //   symbolOffset: [0, -5], // 标记相对于原本位置的偏移
        //   symbolPosition: 'end', // 图形的定位位置。可取值：start、end、center
        //   // 图例的图形样式
        //   itemStyle: {
        //     color: {
        //       type: 'linear',
        //       x: 0,
        //       y: 0,
        //       x2: 0,
        //       y2: 1,
        //       colorStops: [{
        //         offset: 0,
        //         color: '#37EAF2', // 0% 处的颜色
        //       }, {
        //         offset: 1,
        //         color: '#37EAF2', // 100% 处的颜色
        //       }],
        //     },
        //   },
        //   z: 10, // 组件的所有图形的z值。控制图形的前后顺序。z值小的图形会被z值大的图形覆盖
        //   data: chartData.value.map((item: any) => item.rate),
        // },
      ],
    }

    option && myChart.setOption(option)
}

onMounted(() => {
  getDataInit(null, true)
})

// option && myChart.setOption(option);
</script>

<template>
  <div v-if="isEmpty" id="tender" h-270px w-516px />
  <div v-else>
    <Empty />
  </div>
</template>
