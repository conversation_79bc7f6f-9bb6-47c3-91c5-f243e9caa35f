<script setup lang='ts'>
import DataScreen from './data-screen.vue'
import { router } from '~/src/router'

function onFullScreenClick() {
  const { href } = router.resolve({
    name: 'fullScreen_data',
  })
  window.open(href, '_blank')
}

function handleScreenAuto() {
  // 根据屏幕的变化适配的比例
  const designDraftWidth = 2560
  const designDraftHeight = 1080
  const scale = document.documentElement.clientWidth / document.documentElement.clientHeight < designDraftWidth / designDraftHeight
    ? document.documentElement.clientWidth / designDraftWidth
    : document.documentElement.clientHeight / designDraftHeight
  // 缩放比例
  const element = document.querySelector('#screen')
  if (element instanceof HTMLElement)
    element.style.transform = `scale(${scale}) translate(-49%, -40%)`
}

onMounted(() => {
  handleScreenAuto()
  window.onresize = () => handleScreenAuto()
})

onUnmounted(() => {
  window.onresize = null
})
</script>

<template>
  <div class="screen-card">
    <DataScreen id="screen" v-permission="'lddp:dataScreen:show'" class="screen" :is-fullscreen="false" @fullScreenClick="onFullScreenClick" />
  </div>
</template>

<style>
.screen-card {
  height: calc(100vh - 100px);
  /* height: calc(100vh); */
  overflow: auto;
  background-color: #00152c;
  /* padding: 10px; */
  position: relative;
  box-sizing: border-box;
}

.screen {
  display: inline-block;
  width: 1920px !important;
  height: 1280px !important;
  transform-origin: 0 0;
  position: absolute;
  left: 50%;
  top: 50%;
  background: green;
}
</style>
