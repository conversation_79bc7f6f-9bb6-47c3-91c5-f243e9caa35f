<script setup lang="ts">
import * as echarts from 'echarts/core'
import { onMounted } from 'vue'
import Empty from './Empty.vue'
import { planPatientCount } from '~/src/api/screen/screen'

import { localStg } from '~/src/utils/storage'

const props = defineProps({
  currentSelect: {
    type: String,
    required: true,
  },
})

const userInfo = localStg.get('userInfo')
const isEmpty = ref(true)

watch(() => props.currentSelect, (newVal) => {
  // 监听

  // const organList = flattenChildren(userInfo?.organList)
  // const findObj = organList.find((item: any) => item.organName === newVal)
  // isEmpty.value = true
  // if (findObj)
  //   getDataInit(findObj.id)
  // else
  getDataInit()
},
{ deep: true },
)

const trendedData = ref<any>([])
function getDataInit(id?: any) {
  const params = {
    endTime: '',
    organId: props.currentSelect,
    searchOrganId: id || '0',
    startTime: '',
    status: '',
  }
  planPatientCount(params).then((res: any) => {
    if (res.data.length === 0) {
      isEmpty.value = false
      return
    }
    else {
      isEmpty.value = true
    }

    trendedData.value = res.data

    nextTick(() => {
      initCharts()
    })
  })
}

function initCharts() {
    type EChartsOption = echarts.EChartsCoreOption
    const chartDom = document.getElementById('trend')!
    const myChart = echarts.init(chartDom)

    const option: EChartsOption = {
      // tooltip: {
      //   trigger: 'axis', // 触发类型, axis: 坐标轴触发
      //   axisPointer: {
      //     type: 'line', // 指示器类型
      //   },
      //   textStyle: {
      //     // color: '#d5dbff', // 文字颜色
      //   },
      //   // 提示框浮层内容格式器，支持字符串模板和回调函数两种形式
      //   // 折线（区域）图、柱状（条形）图、K线图 : {a}（系列名称），{b}（类目值），{c}（数值）, {d}（无）
      //   // formatter: '{b}<br />{a0}: {c0}万<br />{a3}: {c3}%',
      // },
      xAxis: {
        type: 'category',
        axisLine: {
          lineStyle: {
            color: '#8cceff', // 设置 y 坐标轴的颜色
          },
        },
        axisLabel: {
          rotate: 45, // 设置横坐标标签逆时针倾斜45度
        },
        data: Object.keys(trendedData.value),
      },

      yAxis: {
        name: '人数',
        splitLine: {
          show: true, // 显示x轴的分隔线
          lineStyle: {
            type: 'dashed', // 设置分隔线的样式为虚线
            color: '#055382',
          },
        },

        axisLabel: {
          min: 0,
          formatter(value: number) {
            if (value === 0)
              return value

            if (value > 0 && value < 1)
              return null

            if (value > 1 && value < 2)
              return null
            if (value > 2 && value < 3)
              return null
            if (value > 3)
              return value
          },
        },
        axisLine: {
          lineStyle: {
            color: '#8cceff', // 设置 y 坐标轴的颜色
          },
        },
        type: 'value',
      },
      series: [
        {
          data: Object.values(trendedData.value),
          type: 'bar',
          barWidth: '40%',
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#0091FF' },
              { offset: 1, color: '#00DCFF' },
            ]),
          },
        },
      ],
    }
    option && myChart.setOption(option)
}

onMounted(() => {
  getDataInit()
})

// option && myChart.setOption(option);
</script>

<template>
  <div v-if="isEmpty">
    <div id="trend" h-270px w-516px />
  </div>
  <div v-else>
    <Empty />
  </div>
</template>
