<script setup lang="ts">
defineProps(['title'])
</script>

<template>
  <div class="title_bg" relative text-18px>
    <div color="#fff" absolute left-32px top-20px ml-10px mt-2px flex class="textContent">
      <div w-450px flex justify-between>
        <div>
          {{ title }}
        </div>
        <div>
          <slot />
        </div>
      </div>
    </div>
  </div>
</template>

<style>
.title_bg {
  height: 48px;
  background: url('@/assets/images/dataScreen/title-border.png') ;
  /* border: 2px solid; */
  line-height: 1px;
  border-radius: 6px 6px 6px 6px;
}
.textContent{
  color: #bcf5ff;
  font-size: 20px;
}
</style>
