<script setup lang='ts'>
import { onMounted, ref } from 'vue'
import Map from './Map.vue'
import Title from './Title.vue'
import Disease from './diseaseClass.vue'
import Trend from './Trender.vue'
import Follow from './FollowUp.vue'
import Completion from './Completion.vue'
import AgeDistr from './AgeDistr.vue'
import StatusChart from './StatusChart.vue'
import Counter from './Counter.vue'
import { SvgIcon } from '@/components/Icon'

// import Empty from './Empty.vue'
import { getMapData, newPatientCount } from '~/src/api/screen'
import { localStg } from '~/src/utils/storage'
import { useRouterPush } from '~/src/hooks/userRouterPush'
import { usePermission } from '~/src/hooks/usePermission'

defineProps(['isFullscreen'])

const emits = defineEmits(['fullScreenClick'])
const { hasPermission } = usePermission()
const userInfo = localStg.get('userInfo')
const { routerPush } = useRouterPush()

// const timeSelect = ref('day')
const mapData = ref([])
const selectRef = ref<any>(null)
const isScreen = ref(false)
const selectShow = ref(false)
const selectShow2 = ref(false)
const selectShow3 = ref(false)
const selectShow4 = ref(false)
const selectShow5 = ref(false)
const selectShow6 = ref(false)
const currentSelect6 = ref({
  id: userInfo?.organId,
})
const currentSelect = ref({ id: userInfo?.organId })
const currentSelect2 = ref({ id: userInfo?.organId })
const currentSelect3 = ref({ id: userInfo?.organId })
const currentSelect4 = ref({ id: userInfo?.organId })
const currentSelect5 = ref({ id: userInfo?.organId })

const counterList = ref<any>([])

// const activeElPosition = computed(() => {
//   return timeSelect.value === 'day' ? 1 : '2'
// })

function handleScreenAuto() {
  // 根据屏幕的变化适配的比例
  const designDraftWidth = 1280
  const designDraftHeight = 1080
  const scale = document.documentElement.clientWidth / document.documentElement.clientHeight < designDraftWidth / designDraftHeight
    ? document.documentElement.clientWidth / designDraftWidth
    : document.documentElement.clientHeight / designDraftHeight
  // 缩放比例
  const element = document.querySelector('#screen')
  // if (element instanceof HTMLElement)
  //   element.style.transform = `scale(${scale}) translate(-50%, -45%)`

  // document.querySelector(
  //   '#screen',
  // )!.style.transform = `scale(${scale}) translate(-50%, -45%)`
}

function getCountPatient() {
  const params = {
    endTime: '',
    organId: userInfo?.organId,
    searchOrganId: '0',
    startTime: '',
    status: '',
  }
  newPatientCount(params).then((res: any) => {
    counterList.value = Object.entries(res.data).map(([key, value]) => ({ key, value }))
  })
}

function getMapDatas() {
  const params = {
    endTime: '',
    organId: userInfo?.organId,
    searchOrganId: '0',
    startTime: '',
    status: '',
  }
  getMapData(params).then((res: any) => {
    mapData.value = res.data
  })
}

function handleSelectClick() {

}

function screenHandle() {
  emits('fullScreenClick')
  isScreen.value = !isScreen.value
}

const canJumpAll = hasPermission('lddp:dataScreen:jump') && hasPermission('lddp:statistics:main:all')
const canJumpDisease = hasPermission('lddp:dataScreen:jump') && hasPermission('lddp:statistics:main:disease')
const canJumpFollow = hasPermission('lddp:dataScreen:jump') && hasPermission('lddp:statistics:main:follow')
const canJumpAge = hasPermission('lddp:dataScreen:jump') && hasPermission('lddp:statistics:main:age_sex')

/// 跳转逻辑 统计
function jumpToAnalysis(indexType: string, organId: string) {
  const qx = {
    'SLMC患者总数': 'lddp:statistics:main:all',
    '疾病分类占比': 'lddp:statistics:main:disease',
    '随访率': 'lddp:statistics:main:follow',
    '年龄(性别)分布': 'lddp:statistics:main:age_sex',
  }
  if (hasPermission('lddp:dataScreen:jump')) {
    if (hasPermission(qx[indexType])) {
      routerPush({
        name: 'analysis_statement',
        query: {
          indexType,
          organId,
        },
      })
    }
  }
}

onMounted(() => {
  // handleScreenAuto()
  // handleScreenAuto()
  getCountPatient()
  getMapDatas()
})
onUnmounted(() => {
  window.onresize = null
})

function sbcursorPointer(title) {
  if (title === '患者总数')
    return canJumpAll ? 'cursor-pointer' : ''

  return hasPermission('lddp:dataScreen:jump') ? 'cursor-pointer' : ''
}

function jumpToPage(item: any) {
  if (hasPermission('lddp:dataScreen:jump')) {
    switch (item.key) {
      case '患者总数':
        if (hasPermission('lddp:statistics:main:all'))
          routerPush({ name: 'patient_list' })
        break
      case '今日纳入随访患者数':
        routerPush({ name: 'follow_manage', query: { tab: 'a', isToday: true } })
        break
      case '累计随访患者数':
        routerPush({ name: 'follow_manage', query: { tab: 'a' } })
        break
      default:
        break
    }
  }
}
</script>

<template>
  <div id="data-screen" w-full class="background">
    <div class="header" flex justify-between>
      <!-- <div color="#BDE3FF" ml-40px pt-18px text-18px font-700 class="flex-1/3">
        总路径数：
      </div> -->
      <span class="big_title flex-1/3" pt-10px text-center />
      <div mr-40px flex flex-grow-0 justify-end gap-60px pt-18px class="flex-1/3">
        <div ref="selectRef" relative flex gap-10px>
          <div color="#BDE3FF" w-400px cursor-pointer truncate text-right text-18px font-700 @click="handleSelectClick" />
          <i relative top-5px color="#BDE3FF" text-12px class="iconfont icon-xuanze1" />
          <!-- <DSelect v-model:show="selectShow" v-model="currentSelect" /> -->
        </div>
        <div mb-5px flex cursor-pointer gap-10px @click="emits('fullScreenClick')">
          <!-- <i :class="isFullscreen ? 'icon-tuichuquanping' : 'icon-quanping'" class="iconfont -top-2px text-20px color-#BDE3FF" relative /> -->
          <div color="#bde3ff" mt-30px flex items-center text-18px font-700 @click="screenHandle">
            <SvgIcon :local-icon="isFullscreen ? 'slmc-icon-tuichuquanping' : 'slmc-icon-quanping1'" size="20" color="#ffffff" />
            <span ml-10px>
              {{ isFullscreen ? '退出全屏' : '全屏' }}
            </span>
          </div>
        </div>
      </div>
    </div>
    <div flex>
      <div flex="~ col" ml-20px>
        <div class="pop_select">
          <Title title=" 患者年龄(性别)分布">
            <div flex justify-around>
              <!-- <div inline-block flex cursor-pointer @click="selectShow6 = !selectShow6">
                {{ currentSelect6.organName || '' }}
                <SvgIcon v-if="selectShow6" local-icon="slmc-icon-drop_down" color="#BDE3FF" size="16" relative top--9px ml-10px rotate-180 transform />
                <SvgIcon v-if="!selectShow6" local-icon="slmc-icon-drop_down" color="#BDE3FF" size="16" relative top--9px ml-10px />
              </div> -->
            </div>
            <!-- <DSelect v-model:show="selectShow6" v-model="currentSelect6" ml-20px /> -->
          </Title>
          <div class="content" h-280px w-516px>
            <div flex>
              <AgeDistr :current-select="currentSelect6.id" />
            </div>
            <div :class="canJumpAge ? 'cursor-pointer sb' : 'sb'" @click="jumpToAnalysis('年龄(性别)分布', currentSelect6.id)" />
          </div>
        </div>
        <div mt-20px>
          <Title title="疾病分类占比">
            <div flex justify-around>
              <!-- <div inline-block flex cursor-pointer @click="selectShow2 = !selectShow2">
                {{ currentSelect2.organName || '' }}
                <SvgIcon v-if="selectShow2" local-icon="slmc-icon-drop_down" color="#BDE3FF" size="16" relative top--9px ml-10px rotate-180 transform />
                <SvgIcon v-if="!selectShow2" local-icon="slmc-icon-drop_down" color="#BDE3FF" size="16" relative top--9px ml-10px />
              </div> -->
            </div>
            <!-- <DSelect v-model:show="selectShow2" v-model="currentSelect2" ml-20px :disease-type="true" /> -->
          </Title>
          <div class="content" h-240px w-516px>
            <Disease class="disease-canvans" :current-select="currentSelect2.id" />
            <div :class="canJumpDisease ? 'cursor-pointer sb' : 'sb'" @click="jumpToAnalysis('疾病分类占比', currentSelect2.id)" />
          </div>
        </div>
        <div mt-20px>
          <Title title="新增患者趋势图">
            <div flex justify-around>
              <!-- <div inline-block flex cursor-pointer @click="selectShow3 = !selectShow3">
                {{ currentSelect3.organName || '' }}
                <SvgIcon v-if="selectShow3" local-icon="slmc-icon-drop_down" color="#BDE3FF" size="16" relative top--9px ml-10px rotate-180 transform />
                <SvgIcon v-if="!selectShow3" local-icon="slmc-icon-drop_down" color="#BDE3FF" size="16" relative top--9px ml-10px />
              </div> -->
            </div>
            <!-- <DSelect v-model:show="selectShow3" v-model="currentSelect3" ml-20px /> -->
          </Title>
          <div class="content" w-516px>
            <Trend class="all-canvans" :current-select="currentSelect3.id" />
            <div :class="canJumpAll ? 'cursor-pointer sb' : 'sb'" @click="jumpToAnalysis('SLMC患者总数', currentSelect3.id)" />
          </div>
        </div>
      </div>
      <div w-840px>
        <div class="" mx-20px mt-40px h-100px flex justify-around>
          <div v-for="(item, index) in counterList" :key="index" mx-5px @click="jumpToPage(item)">
            <Counter :class="sbcursorPointer(item.key)" class="middle-count" :count="item.value" :title="item.key" />
          </div>
        </div>
        <div>
          <Map :data="mapData" />
        </div>
      </div>
      <div flex="~ col">
        <div>
          <Title title="纳入随访患者趋势图">
            <div flex justify-around>
              <!-- <div inline-block flex cursor-pointer @click="selectShow = true">
                <div>
                  {{ currentSelect.organName || '' }}
                </div>
                <SvgIcon v-if="selectShow" local-icon="slmc-icon-drop_down" color="#BDE3FF" size="16" relative top--9px ml-10px rotate-180 transform />
                <SvgIcon v-if="!selectShow" local-icon="slmc-icon-drop_down" color="#BDE3FF" size="16" relative top--9px ml-10px />
              </div> -->
            </div>
            <!-- <DSelect v-model:show="selectShow" v-model="currentSelect" ml-20px /> -->
          </Title>
          <div class="content" h-280px w-516px>
            <Completion :current-select="currentSelect.id" />
          </div>
        </div>
        <div mt-20px>
          <Title title="随访总人次">
            <div flex justify-around>
              <!-- <div inline-block flex cursor-pointer @click="selectShow4 = !selectShow4">
                {{ currentSelect4.organName || '' }}
                <SvgIcon v-if="selectShow4" local-icon="slmc-icon-drop_down" color="#BDE3FF" size="16" relative top--9px ml-10px rotate-180 transform />
                <SvgIcon v-if="!selectShow4" local-icon="slmc-icon-drop_down" color="#BDE3FF" size="16" relative top--9px ml-10px />
              </div> -->
            </div>
            <!-- <DSelect v-model:show="selectShow4" v-model="currentSelect4" ml-20px /> -->
          </Title>
          <div class="content" h-240px w-516px>
            <Follow :current-select="currentSelect4.id" />
          </div>
        </div>
        <div mt-20px>
          <Title title="随访完成率趋势图">
            <div flex justify-around>
              <!-- <div inline-block flex cursor-pointer @click="selectShow5 = !selectShow5">
                {{ currentSelect5.organName || '' }}
                <SvgIcon v-if="selectShow5" local-icon="slmc-icon-drop_down" color="#BDE3FF" size="16" relative top--9px ml-10px rotate-180 transform />
                <SvgIcon v-if="!selectShow5" local-icon="slmc-icon-drop_down" color="#BDE3FF" size="16" relative top--9px ml-10px />
              </div> -->
            </div>
            <!-- <DSelect v-model:show="selectShow5" v-model="currentSelect5" ml-20px /> -->
          </Title>
          <div class="content" h-270px w-516px>
            <StatusChart class="follow-canvans" :current-select="currentSelect5.id" />
            <div :class="canJumpFollow ? 'cursor-pointer sb' : 'sb'" @click="jumpToAnalysis('随访率', currentSelect5.id)" />
          </div>
          <!-- <div class="content" h-300px w-516px>
            <Empty />
          </div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
#data-screen {
  *::-webkit-scrollbar-track-piece {
    border: 1px solid;
    background: #082351;
    border-right: none;
    border-image: linear-gradient(270deg, #0091ff 0%, rgba(0, 145, 255, 0.40) 49%, rgba(0, 145, 255, 0.58) 100%) 1 1;
    border-radius: 3px 3px 0px 0px;

    &:vertical {
      border-top: 0px;
      border-bottom: 0px;
    }

    &:horizontal {
      border-left: 0px;
      border-right: 0px;
    }
  }

  *::-webkit-scrollbar-thumb:vertical {
    background-color: #0971BA;

    &:hover {
      background-color: #0971BA !important;
    }
  }
}

.sb{
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.background {
  background: url('@/assets/images/dataScreen/bg.png') 0 240px;
  background-size: 100% 100%;
}

.header {
  background-image: url('@/assets/images/dataScreen/header.svg');
  width: 100%;
  height: 75px;
}

.big_title {
  background: -webkit-linear-gradient(180deg, #ffffff, #abd9ff 82%);
  font-size: 37px;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  -webkit-background-clip: text;
  font-weight: 700;
  text-shadow: 0px 3px 3px 0px rgba(255, 255, 255, 0.50) inset;
}

.title_bg {
  // background-image: url('@/assets/images/dataScreen/title_bg.png');
  background-repeat: round;
}

.select_bg {
  z-index: 100;
  color: #fff;
  background-image: url('@/assets/images/dataScreen/select_bg.png');
  width: 207px;
  background-repeat: round;
  height: 27px;
  box-sizing: border-box;
}

.btn_bg {
  background-image: url('@/assets/images/dataScreen/button.png');
  width: 106px;
  height: 33px;
  background-repeat: round;
  box-sizing: border-box;

  &:hover {
    background-image: url('@/assets/images/dataScreen/btn_hover.png');
  }

  &:active {
    background-image: url('@/assets/images/dataScreen/btn_active.png');
  }
}
.content{
  background: rgba(0,59,124,0.20);
  border: 1px solid rgba(64,199,255,0.13);
  position: relative;
  // backdrop-filter: blur(0px);
}
.bg_image{
  //  background: url('@/assets/images/dataScreen/circle.svg')  no-repeat -25px 5px ;
  //  background-size: 70% 100%;
  }
.contentImage{
  background-image: url('@/assets/images/dataScreen/total-bg.png') ;
  background-repeat: no-repeat;
  // background-size: cover;
}
.n-base-select-menu .n-popselect-menu .n-popover-shared .n-popover-shared--show-arrow{
  border-image: linear-gradient(180deg, rgba(0,145,255,0.58) 0%, rgba(0,145,255,0.40) 51%, #0091ff) 1 1 !important;
 }
 canvas{cursor:pointer !important;}
</style>
