<script setup lang="ts">
import dayjs from 'dayjs'
import * as echarts from 'echarts/core'
import { onMounted } from 'vue'
import _ from 'lodash'
import Empty from './Empty.vue'
import { rateCount } from '~/src/api/screen/screen'
import { localStg } from '~/src/utils/storage/local'

const props = defineProps({
  currentSelect: {
    type: String,
    required: true,
  },
})
const userInfo = localStg.get('userInfo')
const data = ref<any>(null)
const isEmpty = ref(true)

watch(() => props.currentSelect, (newVal) => {
  // 监听

  // const organList = flattenChildren(userInfo?.organList)
  // const findObj = organList.find((item: any) => item.organName === newVal)
  // isEmpty.value = true
  // if (findObj)
  //   getDataInit(findObj.id, newVal === '全部')
  // else
  getDataInit(null)
},
{ deep: true },
)

function getDataInit(id?: any, isAll = false) {
  const params = {
    startTime: dayjs().subtract(6, 'month').startOf('day').format('YYYY-MM-DD'),
    endTime: dayjs().endOf('day').startOf('day').format('YYYY-MM-DD'),
    organId: props.currentSelect || '',
    isAll,
  }
  rateCount(params).then((res: any) => {
    if (res.data.length === 0)
      isEmpty.value = false
    else
      isEmpty.value = true

    data.value = res.data
    nextTick(() => {
      initCharts()
    })
  })
}

function initCharts() {
    type EChartsOption = echarts.EChartsCoreOption
    const chartDom = document.getElementById('flow')!
    const myChart = echarts.init(chartDom, null, { height: '240px' })

    const option: EChartsOption = {
      // tooltip: {
      //   trigger: 'axis',
      //   axisPointer: {
      //     type: 'none',
      //   },
      //   // formatter(params) {
      //   //   return `${params[0].name}: ${params[0].value}`
      //   // },
      // },

      xAxis: {
        data: data.value.map((item: any) => item.time),
        axisLabel: {
          color: '#8CCEFF',
          rotate: 45, // 设置横坐标标签逆时针倾斜45度
        },

      },
      yAxis: {
        minInterval: 1,
        name: '人数',
        splitLine: {
          show: true, // 显示x轴的分隔线
          lineStyle: {
            type: 'dashed', // 设置分隔线的样式为虚线
            color: '#055382',
          },
        },
        axisLine: {
          lineStyle: {
            color: '#8CCEFF', // 设置 y 坐标轴的颜色
          },
        },
        type: 'value',
        axisLabel: {
          formatter: (value: any) => {
            const maxV = _.maxBy(data.value, (o) => {
              return o.total
            })
            if (maxV.total === '0' && value === 1)
              return null
            else
              return value
          },
        },
      },
      //   yAxis: {
      //     splitLine: { show: false },
      //     axisTick: { show: false },
      //     axisLine: { show: false },
      //     axisLabel: { show: false },
      //   },
      color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
        { offset: 0, color: '#00EDFD' },
        { offset: 1, color: '#0b64c8' },
      ]),
      series: [
        {
          // name: 'hill',
          type: 'pictorialBar',
          cursor: 'default',
          // barCategoryGap: '-130%',
          // symbol: 'path://M0,10 L10,10 L5,0 L0,10 z',
          symbol: 'triangle',
          itemStyle: {
            opacity: 1,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#00EDFD' },
              { offset: 1, color: '#0b64c8' },
            ]),
          },
          lineStyle: {
            color: 'blue',
          },
          emphasis: {
            itemStyle: {

              opacity: 1,
            },
          },
          data: data.value.map((item: any) => item.total),
          // data: [0, 0, 0, 0, 3],
          z: 10,
        },

      ],
    }

    option && myChart.setOption(option)
}

onMounted(() => {
  // initCharts()
  getDataInit(null, true)
})

// option && myChart.setOption(option);
</script>

<template>
  <div v-if="isEmpty" id="flow" h-240px />
  <div v-else>
    <Empty />
  </div>
</template>
