<script setup lang="ts">
import { onClickOutside } from '@vueuse/core'
import { getSbOrganList } from '~/src/api/organization/organization'
import { localStg } from '~/src/utils/storage'

const props = defineProps({
  diseaseType: {
    type: Boolean,
    required: true,
  },
})
const userInfo: any = localStg.get('userInfo') || {}
// import { usePathStore } from '@/pinia'

const currentSelect = defineModel()
const selectShow = defineModel<boolean>('show')
const hospitalList = ref<any>([
  // {
  //   isSelect: false,
  //   organName: '全部',
  //   id: '0',
  // },
  // {
  //   isSelect: false,
  //   organName: '纳入随访',
  //   id: '1',
  // },
])

const selectRef = ref(null)

onClickOutside(selectRef, () => {
  // selectShow.value = false
  selectShow.value = false
})

// if (!props.diseaseType) {
//   hospitalList.value = flattenChildren(userInfo.organList)
//   hospitalList.value.unshift({
//     isSelect: false,
//     organName: '全部',
//     id: '0',
//   })
// }

function handleOptionClick(index: any) {
  hospitalList.value.forEach((item: any, i: string) => {
    if (i === index) {
      item.isSelect = true

      currentSelect.value = item
    }
    else {
      item.isSelect = false
    }
  })

  selectShow.value = false
}

onMounted(() => {
  ///  获取机构选择
  getSbOrganList().then((res: any) => {
    hospitalList.value = res?.data || []
    /// 默认选中第一个
    if (hospitalList.value.length > 0)
      handleOptionClick(0)
  })
})
</script>

<template>
  <div
    v-show="selectShow"
    ref="selectRef"
    bg="#031940" border="1px solid"
    absolute left-258px top-40px z-1000 box-border w-190px overflow-auto b-rd-3px py-3px
    style="max-height: 150px; border-image: linear-gradient(180deg, rgba(0,145,255,0.58) 0%, rgba(0,145,255,0.40) 51%, #0091ff) 1 1;"
  >
    <div flex="~ col">
      <div
        v-for="(item, index) in hospitalList" :key="index" font="700"
        h-40px cursor-pointer truncate px-12px py-10px leading-none hover="bg-#0DC8FD/20"
        :class="item.isSelect ? 'text-#2b95ff' : 'text-#BDE3FF' "
        @click="handleOptionClick(index)"
      >
        {{ item.organName }}
      </div>
    </div>
  </div>
</template>
