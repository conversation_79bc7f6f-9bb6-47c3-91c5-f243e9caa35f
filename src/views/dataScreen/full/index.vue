<script lang='ts' setup>
import { useFullscreen } from '@vueuse/core'
import VScaleScreen from 'v-scale-screen'
import DataScreen from '../wane/data-screen.vue'

const scaleSCreenRef = ref(null)

const { isFullscreen, toggle } = useFullscreen(scaleSCreenRef)
</script>

<template>
  <div class="">
    <VScaleScreen ref="scaleSCreenRef" v-permission="'lddp:dataScreen:show'" width="1920" height="1080">
      <DataScreen :is-fullscreen="isFullscreen" @fullScreenClick="toggle" />
    </VScaleScreen>
  </div>
</template>

<style scoped lang="scss">

</style>
