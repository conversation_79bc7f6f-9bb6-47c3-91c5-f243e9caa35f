<script lang="ts" setup>
import { useRouter } from 'vue-router'
import Exception404Img from '@/assets/images/exception_404.jpg'
import Exception404Text from '@/assets/images/exception_404Text.jpg'

const router = useRouter()
function goHome() {
//   const permissionStore = usePermissionStore()
//   const menuList = permissionStore.menuList
//   menuList[0].powerUrl && router.push(menuList[0].powerUrl)
  router.push({ path: '/home' })
}
</script>

<template>
  <div class="container">
    <div class="text-center">
      <img :src="Exception404Img" alt="" style="width: 300px">
    </div>
    <div class="text-center">
      <img :src="Exception404Text" alt="" style="width: 117px">
      <h1 class="text-base">
        服务器查询不到信息
      </h1>
      <n-button type="info" ghost @click="goHome">
        回到首页
      </n-button>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    .text-base {
        color: #999;
        margin: 16px 0;
    }
}
</style>
