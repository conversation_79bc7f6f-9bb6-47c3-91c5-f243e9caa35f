<script setup lang="ts">
import { useDialog } from 'wowjoy-vui'
import dayjs from 'dayjs'
import { vOnClickOutside } from '@vueuse/components'
import { deleteTagList } from '@/api/followCenter'
import { addTagAPI, checkTagName, deleteTagAPI, getTagPageAPI, updateTagAPI } from '@/api/tag'
import { useAuthStore } from '@/store'

defineOptions({
  name: 'TagList',
})

const tagEnum: Record<string, string> = {
  CUSTOM: '自定义标签',
  SCALE: '通用标签',
  DISEASE: '病种标签',
  TECHNIQUE: '十大技术',
  DISEASE_GRADE: '诊断标签',
}

const auth = useAuthStore()
const dialog = useDialog()
const updateRef = ref<any>(null)

const userInfo = auth.userInfo

const tableData = ref([])

const showModal = ref(false)
const isEditor = ref(false)
const deleteConfirm = ref(false)
const tagStatus = ref('ALL')
const searchTagName = ref<string | null>(null)
const tagNameList = ref<any[]>([])

const category = ref<string[]>([])

const formValue = ref({
  tagName: '',
  isEnabled: true,
  createId: userInfo.id,
  createName: userInfo.userName,
  hospitalId: userInfo.organId,
})

const page = reactive({
  page: 1,
  size: 10,
  total: 0,
})

async function getTags() {
  try {
    const params = {
      categories: category.value,
      tagName: searchTagName.value,
      organId: userInfo.organId,
      userId: userInfo.id,
      page: page.page,
      size: page.size,
      isEnabled: (tagStatus.value === 'ALL') ? '' : !!tagStatus.value,
    }

    const { data } = await getTagPageAPI(params)
    tableData.value = data.records
    tagNameList.value = data.records
    page.total = Number(data.total)
  }
  catch (error) {
    console.log(error)
  }
}

function addTags() {
  formValue.value.tagName = ''
  isEditor.value = true
  showModal.value = true
}

function deleteHandler(row: any) {
  dialog.warning({
    title: '确定删除此标签吗？',
    positiveText: '确定',
    negativeText: '取消',
    btnGhost: true,
    onPositiveClick: async () => {
      try {
        const { data } = await deleteTagAPI(row.tagId)
        if (data) {
          window.$message.success('删除成功')
          getTags()
        }
      }
      catch (error) {
        window.$message.error('删除失败')
      }
    },
  })
}

function onPositiveClick() {
  deleteTagList(updateRef.value.tagId).then((res) => {
    if (res.data)
      window.$message.success('删除成功')

    else
      window.$message.error('删除失败')
  })
  showModal.value = false

  deleteConfirm.value = false
}

async function addTagHandler() {
  try {
    const { data: canUse } = await checkTagName({
      tagId: '',
      tagName: formValue.value.tagName,
    })

    if (!canUse) {
      window.$message.warning('标签名称不能重复')
      return false
    }

    const { data } = await addTagAPI(formValue.value)
    if (data) {
      window.$message.success('新增成功')
      showModal.value = false
      getTags()
    }
  }
  catch (error) {

  }
}

async function handleEnable(row: any) {
  try {
    const params = {
      tagId: row.tagId,
      isEnabled: !row.isEnabled,
      updateId: userInfo.id,
      updateName: userInfo.userName,
    }

    const { data } = await updateTagAPI(params)
    if (data) {
      window.$message.success('操作成功')
      getTags()
    }
    else {
      window.$message.error('操作失败')
    }
  }
  catch (error) {
    window.$message.error('操作失败')
  }
}

function handleUpdatePageSize() {
  page.page = 1
  getTags()
}

const currentEditIndex = ref(-1)
let currentEditTagId = ''
const newTagName = ref('')

function handleEditBtnClick(row: any, index: number) {
  currentEditIndex.value = index
  newTagName.value = row.tagName
  currentEditTagId = row.tagId
}

async function handleConfirmRename() {
  try {
    if (!newTagName.value) {
      window.$message.error('请输入标签名称')
      return false
    }

    const { data: canUse } = await checkTagName({
      tagName: newTagName.value,
      tagId: currentEditTagId,
    })

    if (!canUse) {
      window.$message.warning('标签名称不能重复')
      return false
    }

    const { data } = await updateTagAPI({
      tagId: currentEditTagId,
      tagName: newTagName.value,
      updateId: userInfo.id,
      updateName: userInfo.userName,
    })

    if (data) {
      window.$message.success('修改成功')
      currentEditIndex.value = -1
      getTags()
    }
  }
  catch (error) {

  }
}

function handleCancelRename() {
  currentEditIndex.value = -1
}

onMounted(() => {
  getTags()
})
</script>

<template>
  <div>
    <PageCard>
      <PageTitle border class="flex">
        标签管理
      </PageTitle>
      <div my-20px flex gap-20px>
        <div flex items-center>
          <div color="#666" mr-10px flex-shrink-0>
            标签名称
          </div>
          <n-input-group>
            <n-input v-model:value="searchTagName" clearable class="!w-260px" />

            <n-button type="primary" class="!min-w-16px" @click="getTags">
              <template #icon>
                <SvgIcon local-icon="slmc-icon-search" size="16" style="color: #fff; cursor: pointer;" />
              </template>
            </n-button>
          </n-input-group>
        </div>

        <div flex items-center>
          <div color="#666" mr-10px flex-shrink-0>
            标签状态
          </div>
          <n-select
            v-model:value="tagStatus"
            class="!w-260px"
            :options="[
              {
                label: '全部',
                value: 'ALL',
              },
              {
                label: '启用中',
                value: 1,
              },
              {
                label: '已禁用',
                value: 0,
              }]"
            @update:value="() => {
              page.page = 1
              getTags()
            }"
          />
        </div>

        <div flex items-center>
          <div color="#666" mr-10px flex-shrink-0>
            标签类型
          </div>
          <n-select
            v-model:value="category"
            class="!w-260px"
            max-tag-count="responsive"
            placeholder="全部"
            multiple
            :options="[
              {
                label: '自定义标签',
                value: 'CUSTOM',
              },
              {
                label: '通用标签',
                value: 'SCALE',
              },
              {
                label: '病种标签',
                value: 'DISEASE',
              },
              {
                label: '十大技术',
                value: 'TECHNIQUE',
              },
              {
                label: '诊断标签',
                value: 'DISEASE_GRADE',
              },
            ]"
            @update:value="() => {
              page.page = 1
              getTags()
            }"
          />
        </div>
      </div>

      <n-button type="primary" @click="addTags">
        新增标签
      </n-button>

      <el-table
        stripe
        max-height="calc(100vh - 305px)"
        class="mt-[20px]" :data="tableData" style="width: 100%"
      >
        <el-table-column label="序号" type="index" width="80" />
        <el-table-column label="标签名称">
          <template #default="{ row, $index }">
            <div
              v-if="currentEditIndex === $index" v-on-click-outside="() => {
                if (currentEditIndex > -1)
                  handleConfirmRename()
              }" flex items-center gap-10px
            >
              <n-input
                v-model:value="newTagName" class="!w-110px"
              />
              <SvgIcon flex-shrink-0 cursor-pointer local-icon="slmc-icon-done_line" size="16" @click="handleConfirmRename" />
              <SvgIcon flex-shrink-0 cursor-pointer local-icon="slmc-icon-wrong_line" size="16" @click="handleCancelRename" />
            </div>
            <div v-else flex items-center gap-6px>
              <div>{{ row.tagName }}</div>
              <SvgIcon
                v-if="row.createId !== 'SYSTEM'"
                local-icon="slmc-icon-edit1"
                size="14"
                cursor-pointer
                text-primary @click="handleEditBtnClick(row, $index)"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="category" label="标签类型" width="100" :formatter="({ category }) => tagEnum[category as string] || '-'" />
        <el-table-column label="标签覆盖人数" width="150">
          <template #default="{ row }">
            <span text="#FF9B54">
              {{ row.patientNums || 0 }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="isEnableed" width="150" label="标签状态">
          <template #default="{ row }">
            <div text="12px #fff" h-18px w-56px flex-center b-rd-9px leading-12px :class="row.isEnabled ? 'bg-#3AC9A8' : 'bg-#ccc' ">
              {{ row.isEnabled ? '启用中' : '已禁用' }}
            </div>
          </template>
        </el-table-column>
        <el-table-column label="创建人" width="170">
          <template #default="{ row }">
            <div v-if="row.createId === 'SYSTEM'" flex items-center>
              <div>
                系统默认
              </div>
              <img ml-5px h-20px w-20px flex-shrink-0 src="@/assets/images/default-tag.png" alt="">
            </div>
            <div v-else>
              {{ row.createName }}
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="150" :formatter="({ createTime }) => dayjs(createTime).format('YYYY-MM-DD')" />
        <el-table-column label="操作" fixed="right" width="120">
          <template #default="{ row }">
            <div flex items-center gap-6px>
              <div uno-link @click="handleEnable(row)">
                {{ row.isEnabled ? '禁用' : '启用' }}
              </div>
              <template v-if="row.createId !== 'SYSTEM'">
                <div h-14px w-1px bg="#3B8FD9" />
                <div
                  uno-link
                  @click="deleteHandler(row)"
                >
                  删除
                </div>
              </template>
            </div>
          </template>
        </el-table-column>
        <template #empty>
          <div flex justify-center>
            <DataEmpty />
          </div>
        </template>
      </el-table>

      <div v-if="page.total > 0" mt-20px w-full flex justify-end>
        <n-pagination
          v-model:page="page.page"
          v-model:page-size="page.size"
          :item-count="page.total"
          :page-sizes="[5, 10, 20, 30]"
          show-size-picker
          show-quick-jumper
          @update:page="getTags"
          @update:page-size="handleUpdatePageSize"
        />
      </div>
    </PageCard>
    <n-modal
      v-model:show="showModal"
      preset="card"
      :style="{ width: '540px' }"
      :title="`${isEditor ? '新增' : '修改'}标签`"
      head-style="divide"
    >
      <div px-10px pt-20px>
        <div flex items-center>
          <SvgIcon local-icon="slmc-icon-app_required1" size="14" class="text-#F36969" />
          <div text="#666666" mr-10px>
            标签名称
          </div>
          <n-input v-model:value="formValue.tagName" class="!w-320px" />
        </div>
        <div mt-20px flex items-center>
          <div text="#666666" mr-10px w-70px text-right>
            标签分类
          </div>
          <div>自定义</div>
        </div>
        <div mt-20px flex items-center>
          <div text="#666666" mr-10px w-70px text-right>
            标签状态
          </div>
          <n-radio-group v-model:value="formValue.isEnabled">
            <n-radio mr-40px :value="true">
              启用
            </n-radio>
            <n-radio :value="false">
              禁用
            </n-radio>
          </n-radio-group>
        </div>
      </div>
      <template #footer>
        <div mb-8px ml-90px mt-8px flex items-center>
          <n-button class="!w-100px" type="primary" @click="addTagHandler">
            保 存
          </n-button>
          <n-button class="!w-100px" ml-20px @click="showModal = false">
            取 消
          </n-button>
        </div>
      </template>
    </n-modal>
    <n-modal
      v-model:show="deleteConfirm"
      :mask-closable="false"
      preset="dialog"
      title="确定删除此标签吗？"
      positive-text="确定"
      negative-text="取消"
      @positive-click="onPositiveClick"
      @negative-click="deleteConfirm = false"
    />
  </div>
</template>
