<script lang='ts' setup>
import { useFullscreen } from '@vueuse/core'
import VScaleScreen from 'v-scale-screen'
import zheYiData from './zheYiDataCenter.vue'

const scaleSCreenRef = ref(null)

const { isFullscreen, toggle } = useFullscreen(scaleSCreenRef)

onMounted(() => {

})
</script>

<template>
  <div class="full-screen">
    <VScaleScreen ref="scaleSCreenRef" :full-screen="isFullscreen" :wrapper-style="{ margin: 0 }" width="1920" height="1080">
      <zheYiData :full-sreen="true" @fullScreenClick="toggle" />
    </VScaleScreen>
  </div>
</template>

<style scoped lang="scss">

</style>
