<script setup lang="ts">
import { useDebounceFn } from '@vueuse/core'
import dayjs from 'dayjs'
import { NButton } from 'wowjoy-vui'
import HighSearch from './components/HighSearch.vue'
import ExportModal from './components/ExportModal.vue'
import { RenderPatientTag, RenderPhone } from '@/components/business/UiRender'
import { BasicTable } from '@/components/Table'
import { isPhone, sexIcon } from '@/utils/common'
import { SvgIcon } from '@/components/Icon'
import { getManageDoctorListApi, getPatientNameApi } from '@/api/specificDisease'
import { getPlanNameList } from '@/api/dataCenter'
import { useAuthStore } from '@/store'
import type { PatientRecord } from '@/api/specificDisease'
import { getTagListAPI } from '@/api/tag'
import { getAdvancedSearchPatientListApi } from '@/api/patient/patient'

const auth = useAuthStore()
const userInfo = auth.userInfo

/** 机构ID */
const ORGAN_ID = userInfo.organId

const tableRef = ref<InstanceType<typeof BasicTable> | null>(null)
const hightSearchRef = ref<InstanceType<typeof HighSearch>>()

const genderOptions = [
  { label: '全部', value: '' },
  { label: '男', value: '男' },
  { label: '女', value: '女' },
]

const options = ref({
  patientName: [] as { label: string; value: string }[],
  sexName: genderOptions,
  doctorName: [],
  planName: [],
  tagName: [],
})
const loading = ref({
  patientName: false,
  doctorName: false,
  planName: false,
  tagName: false,
})

/**  搜索患者姓名 */

const getPatientName = useDebounceFn(async (patientName = '') => {
  loading.value.patientName = true
  const res = await getPatientNameApi<string[]>({ patientName, organId: ORGAN_ID })

  if (res.data) {
    const array = res.data.map((item) => {
      return {
        label: item,
        value: item,
      }
    })
    options.value.patientName = array
    loading.value.patientName = false
  }
}, 300)
/**
 * 查找患者姓名
 * @param query 搜索key
 */
function handleSearchPatientName(query: string) {
  getPatientName(query)
}
/**
 * 患者名字打开/关闭回调
 * @param show 下拉打开/关闭
 */
function handleUpdateShowPatientName(show: boolean) {
  if (show)
    getPatientName('')
}
/**
 * 获取医生姓名
 * @param query 搜索关键字
 */
const getDoctorName = useDebounceFn(async (doctorName = '') => {
  loading.value.doctorName = true
  const params = {
    searchName: doctorName,
    size: 1000,
  }
  const res = await getManageDoctorListApi<any>(params)

  if (res?.data) {
    options.value.doctorName = res.data.records
    loading.value.doctorName = false
  }
}, 300)
/** 搜索医生姓名 */
function handleSearchDoctorName(query: string) {
  getDoctorName(query)
}
/**
 * 下拉列表打开/关闭回调
 * @param show 打开/关闭
 */
function handleUpdateShowDoctorName(show: boolean) {
  if (show)
    getDoctorName('')
}

/**
 * 获取随访
 *
 */
const getPlanName = useDebounceFn(async () => {
  loading.value.planName = true

  const res = await getPlanNameList<any>()

  if (res?.data) {
    options.value.planName = res.data?.map((item: any) => ({ label: item.keyValue, value: item.keyValue }))
    loading.value.planName = false
  }
}, 300)

/**
 * 搜索随访方案
 */
const handleSearchPlanName = useDebounceFn(async () => {
  try {
    getPlanName()
  }
  catch (error) {

  }
}, 500)
/**
 * 下拉列表打开/关闭回调
 * @param show 打开/关闭
 */
async function handleUpdateShowPlanName(show: boolean) {
  if (show)
    getPlanName()
}

/**
 * 获取标签列表
 */
async function getTagList() {
  try {
    loading.value.tagName = true
    const res = await getTagListAPI('')
    if (res?.data) {
      options.value.tagName = res.data
      loading.value.tagName = false
    }
  }
  catch (error) {
    console.log(error)
  }
}

/**
 * 渲染患者姓名列
 * @param row 行数据
 */
function renderPatientNameInfo(row: any) {
  const { patientName, sexName, age, ageUnit } = row

  const localIcon = sexIcon(sexName)
  return h('div', {
    class: 'flex ',
  }, [
    h('span', {
      class: 'truncate',
    }, patientName),
    sexName && h(SvgIcon, {
      size: 16,
      class: 'mx-6px ',
      localIcon,

    }),
    h('span', { class: 'truncate' }, { default: () => age ? `${age}${ageUnit || '岁'}` : '' }),
  ])
}
/**
 * 渲染手机号信息
 * @param row 行信息
 */
function renderPhoneInfo(row: PatientRecord) {
  const { phone } = row

  // 判断是否手机号 ，再脱敏
  if (isPhone(phone))
    return h(RenderPhone, { phone })

  else return phone
}
/**
 * 渲染患者标签
 * @param row 行信息
 */
function renderPatientTagInfo(row: PatientRecord) {
  const { tagName } = row
  const tag = tagName ? tagName.split(',') : []
  return tagName
    ? h(
      RenderPatientTag,
      { tag },
    )
    : '-'
}

// 表格列初始化
function createColumns() {
  return [
    {
      type: 'selection',
      width: 55,
    },
    {
      title: '序号',
      key: 'index',
      width: 70,
      render(_: PatientRecord, index: number) {
        return index + 1
      },
    },
    // {
    //   title: 'SLMC编号',
    //   key: 'slmcNo',
    //   width: 150,
    // },
    {
      title: '患者信息',
      key: 'patientName',
      width: 180,
      render(row: PatientRecord) {
        return renderPatientNameInfo(row)
      },
    },
    {
      title: '患者标签',
      key: 'tagName',
      width: 210,
      render(row: PatientRecord) {
        return renderPatientTagInfo(row)
      },
    },
    {
      title: '手机号',
      key: 'phone',
      width: 150,
      render(row: any) {
        return renderPhoneInfo(row)
      },
    },

    {
      title: '随访方案',
      key: 'planName',
      width: 150,
      ellipsis: false,
    },

    {
      title: '管理医生',
      key: 'manageDoctorName',
      width: 120,
    },
    {
      title: '加入SLMC日期',
      key: 'createTime',
      width: 120,
      render(row: PatientRecord) {
        const { createTime } = row
        return createTime.substring(0, 10)
      },
    },
  ]
}

const columns = createColumns()
/** 默认结尾时间 */
const DEFAULT_END_TIME = dayjs().valueOf()
/** 默认开始时间，往前推半年 */
const START_END_TIME = dayjs().subtract(12, 'month').valueOf()

// 分页器
const paginationReactive = reactive({
  page: 1,
  pageSize: 10,
  pageSizes: [10, 20, 50, 100],
})
// 默认从参数
const defaultTableParams = {
  size: 10,
  start: 1,
  sex: '',
  patientNames: [],
  planNames: [],
  doctorNames: [],
  tagNames: [],
  firstStartTime: START_END_TIME,
  firstEndTime: DEFAULT_END_TIME,
  lastStartTime: START_END_TIME,
  lastEndTime: DEFAULT_END_TIME,
}

const tableParams = reactive({
  size: 10,
  start: 1,
  sex: '',
  patientNames: [],
  planNames: [],
  doctorNames: [],
  tagNames: [],
  firstStartTime: START_END_TIME,
  firstEndTime: DEFAULT_END_TIME,
  lastStartTime: START_END_TIME,
  lastEndTime: DEFAULT_END_TIME,
})

const tableTotal = ref('0')
// 加载表格数据
async function loadDataTable(res: { size: number; start: number; isPageChange?: boolean }) {
  const patientName = tableParams.patientNames.join(',')
  const manageDoctorId = tableParams.doctorNames.join(',')
  const planName = tableParams.planNames.join(',')
  const tagName = tableParams.tagNames.join(',')
  if (!res.isPageChange)
    hightSearchRef?.value?.syncGroupList()

  const highSearchParams = hightSearchRef?.value?.genSearchParams()

  const params = {
    patientName,
    manageDoctorId,
    planName,
    tagName,
    sex: tableParams.sex,
    list: highSearchParams ? highSearchParams.list : [],
    firstStartTime: tableParams.firstStartTime && dayjs(tableParams.firstStartTime).format('YYYY-MM-DD'),
    firstEndTime: tableParams.firstEndTime && dayjs(tableParams.firstEndTime).format('YYYY-MM-DD'),
    lastStartTime: tableParams.lastStartTime && dayjs(tableParams.lastStartTime).format('YYYY-MM-DD'),
    lastEndTime: tableParams.lastEndTime && dayjs(tableParams.lastEndTime).format('YYYY-MM-DD'),
  }
  const result = await getAdvancedSearchPatientListApi<PatientRecord>({ ...params, ...res })
  // @ts-expect-error any
  tableTotal.value = result.data.total

  return result
}

const isPopoverShow = ref(false)

/** 重置查询参数 */
function handleResetClick() {
  Object.assign(tableParams, defaultTableParams)
  hightSearchRef?.value?.initDefaultForm()
  tableRef?.value?.fetch({ start: 1, size: 10 })
}
function init() {
  handleSearchPatientName('')
  handleSearchDoctorName('')
  handleSearchPlanName()
  getTagList()
}

onMounted(() => {
  init()
})

/** 表格选中的key */
const selectPatientKeys = ref<string[]>([])

function handleSearchClick() {
  if (tableParams.firstStartTime && tableParams.firstEndTime && tableParams.firstStartTime > tableParams.firstEndTime) {
    window.$message.warning('开始时间不能大于结束时间')
    return false
  }

  if (tableParams.lastStartTime && tableParams.lastEndTime && tableParams.lastStartTime > tableParams.lastEndTime) {
    window.$message.warning('开始时间不能大于结束时间')
    return false
  }

  selectPatientKeys.value = []
  hightSearchRef?.value?.syncGroupList()

  if (!hightSearchRef?.value?.isGroupAllEmpey && hightSearchRef?.value?.isGroupListEmpty) {
    window.$message.warning('请完善高级搜索条件')
    isPopoverShow.value = true
    return false
  }

  const payload = { start: 1 }
  tableRef?.value?.fetch(payload)
  isPopoverShow.value = false
}

function showWarning() {
  window.$message.warning('请完善高级搜索条件')
}

const dropdownOptions = computed(() => {
  return [
    {
      label: `导出选中（共${selectPatientKeys.value.length}条）`,
      key: 'selected',
      value: 'selected',
      disabled: selectPatientKeys.value.length <= 0,
    },
    {
      label: `导出所有（共${tableTotal.value}条）`,
      key: 'all',
      value: 'all',
    },
  ]
})

const showExportModal = ref(false)
const searchParamsForModal = ref('')
const exportCount = ref<'selected' | 'all'>('selected')

function handleDropdownSelect(key: 'selected' | 'all') {
  const highSearchParams = hightSearchRef?.value?.genSearchParams()
  const patientName = tableParams.patientNames.join(',')
  const manageDoctorId = tableParams.doctorNames.join(',')
  const planName = tableParams.planNames.join(',')
  const tagName = tableParams.tagNames.join(',')
  const params = {
    patientName,
    manageDoctorId,
    planName,
    tagName,
    sex: tableParams.sex,
    list: highSearchParams ? highSearchParams.list : [],
    firstStartTime: tableParams.firstStartTime && dayjs(tableParams.firstStartTime).format('YYYY-MM-DD'),
    firstEndTime: tableParams.firstEndTime && dayjs(tableParams.firstEndTime).format('YYYY-MM-DD'),
    lastStartTime: tableParams.lastStartTime && dayjs(tableParams.lastStartTime).format('YYYY-MM-DD'),
    lastEndTime: tableParams.lastEndTime && dayjs(tableParams.lastEndTime).format('YYYY-MM-DD'),
  }

  searchParamsForModal.value = JSON.stringify({
    ...params,
    ...highSearchParams,
  })
  showExportModal.value = true
  exportCount.value = key
}
</script>

<template>
  <div>
    <div class="specificDiseaseList">
      <PageCard>
        <PageTitle class="mb-14px">
          数据中心
        </PageTitle>
        <n-form
          label-placement="left"
          :model="tableParams"
          :show-feedback="false"
        >
          <div class="mb-14px flex flex-wrap justify-start gap-x-20px gap-y-14px">
            <n-form-item label="管理医生" path="doctorNames" class="basis-326px">
              <n-select
                v-model:value="tableParams.doctorNames"
                filterable placeholder="请选择(可多选)"
                :options="options.doctorName"
                :loading="loading.doctorName"
                label-field="userName"
                value-field="id"
                clearable
                remote
                multiple
                max-tag-count="responsive"
                style="width: 260px;"
                @search="handleSearchDoctorName"
                @update:show="handleUpdateShowDoctorName"
              />
            </n-form-item>
            <n-form-item label="性别" path="sexName" class="basis-298px">
              <n-select v-model:value="tableParams.sex" placeholder="请输入" :options="options.sexName" style="width: 260px;" />
            </n-form-item>
            <n-form-item label="随访方案" path="planNames" class="basis-326px">
              <n-select
                v-model:value="tableParams.planNames"
                filterable placeholder="请选择(可多选)"
                :options="options.planName"
                :loading="loading.planName"
                max-tag-count="responsive"
                clearable
                multiple
                style="width: 260px;"
                @update:show="handleUpdateShowPlanName"
              />
            </n-form-item>
            <n-form-item label="患者姓名" path="patientNames" class="basis-326px">
              <n-select
                v-model:value="tableParams.patientNames"
                filterable placeholder="请选择(可多选)"
                :options="options.patientName"
                :loading="loading.patientName"
                clearable
                remote
                style="width: 260px;"
                multiple
                max-tag-count="responsive"
                @search="handleSearchPatientName"
                @update:show="handleUpdateShowPatientName"
              />
            </n-form-item>
            <n-form-item label="初次就诊时间" class="basis-536px">
              <n-date-picker v-model:value="tableParams.firstStartTime" clearable type="date" style="width: 204px;" />
              <span class="mx-10px">—</span>
              <n-date-picker v-model:value="tableParams.firstEndTime" clearable type="date" style="width: 204px;" />
            </n-form-item>
            <n-form-item label="末次就诊时间" class="basis-536px">
              <n-date-picker v-model:value="tableParams.lastStartTime" clearable type="date" style="width: 204px;" />
              <span class="mx-10px">—</span>
              <n-date-picker v-model:value="tableParams.lastEndTime" clearable type="date" style="width: 204px;" />
            </n-form-item>
            <n-form-item label="患者标签" path="tagNames" class="basis-608px">
              <n-select
                v-model:value="tableParams.tagNames"
                placeholder="请选择(可多选)"
                :options="options.tagName"
                :loading="loading.tagName"
                label-field="tagName"
                value-field="tagId"
                filterable
                clearable
                multiple
                max-tag-count="responsive"
                style="width: 260px;"
              />

              <HighSearch ref="hightSearchRef" v-model:popoverShow="isPopoverShow" @onSearch="handleSearchClick" @onReset="handleResetClick" />
              <div v-show="!isPopoverShow">
                <NButton type="primary" class="mx-10px" @click="handleSearchClick">
                  查询
                </NButton>
                <NButton @click="handleResetClick">
                  重置
                </NButton>
              </div>
            </n-form-item>
          </div>
        </n-form>

        <div mb-14px>
          <n-dropdown :options="dropdownOptions" trigger="click" @select="handleDropdownSelect">
            <NButton :disabled="tableTotal === '0'" type="primary" ghost icon-placement="right">
              导出
              <template #icon>
                <SvgIcon local-icon="slmc-icon-drop_down" size="12" class="icon-drop_down" />
              </template>
            </NButton>
          </n-dropdown>
          <router-link to="/dataCenter/records">
            <NButton type="primary" ghost ml-16px>
              导出记录
            </NButton>
          </router-link>
        </div>

        <BasicTable
          ref="tableRef"
          v-model:checked-row-keys="selectPatientKeys"
          :columns="columns"
          :request="loadDataTable"
          :row-key="(row:any) => row.id"
          :pagination="paginationReactive"
          :scroll-x="1400"
          striped
        />
        <div relative>
          <div
            v-show="!hightSearchRef?.isGroupAllEmpey && hightSearchRef?.isGroupListEmpty"
            absolute bottom-0px right-0 z-100 h-28px w-628px cursor-pointer bg-red op-0
            @click="showWarning"
          />
        </div>
      </PageCard>
    </div>

    <ExportModal v-model:showExportModal="showExportModal" :search-param="searchParamsForModal" :export-count="exportCount" :patient-keys="selectPatientKeys" />
  </div>
</template>

<style scoped lang="scss">
.specificDiseaseList {
    .followStatus{
        width: 56px;
        height: 18px;
        color: #fff;
        border-radius: 9px;
        text-align: center;
        line-height: 18px;

    }
}

.vertifycode {
  margin-left: -2px;
}
</style>
