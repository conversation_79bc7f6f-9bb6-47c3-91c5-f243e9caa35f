<script setup lang='ts'>
import * as echarts from 'echarts'
import { getZyOrgansStatistics } from '~/src/api/statistics'

const props = defineProps({
  theme: {
    type: String,
    default: 'data-normal',
  },
})
const isEmpty = ref(false)
const oriData = ref()
const chartRef = ref()
let myChart
const colors = ['#5B96FD', '#68D0D4', '#FFC569', '#9D96F5', '#24BEE8', '#9EC97F', '#FF9B54', '#A5B8D1']
const options = reactive({
  legend: {
    show: false,
  },
  tooltip: {
    backgroundColor: 'rgba(0,0,0,0.7)',
    textStyle: {
      fontSize: '12px',
      color: 'white',
    },
    trigger: 'item',
    padding: 0,
    axisPointer: {
      type: 'line',
      show: false,
    },
    formatter(params) {
      return `<div p-14px>
            <div >来源：${params.data.name}</div>
            <div mt-8px>人数：${params.data.value}</div>
            <div mt-8px>占比：${params.data.scale}</div>
          </div>`
    },
    borderColor: 'rgba(0,0,0,0.7)',
  },
  color: colors,
  series:
  {
    minAngle: 8,
    avoidLabelOverlap: true,
    type: 'pie',
    radius: ['40%', '65%'],
    center: ['48%', '49%'],
    itemStyle: {
      borderColor: '#fff',
      borderWidth: 4,
    },
    label: {
      position: 'outer',
      alignTo: 'none',
      bleedMargin: 15,
      formatter: (params: any) => {
        return `${params.data.name}: ${params.data.scale}`
      },
    },
    labelLine: {
      show: true,
      length: 15,
      length2: 25,
      lineStyle: {},
    },
    data: [],
  },
})

function getTheData() {
  getZyOrgansStatistics().then((res) => {
    options.series.data = res.data?.map((item) => {
      return {
        name: item.name,
        value: item.number,
        scale: item.scale,
      }
    })
    isEmpty.value = !(options.series.data?.length > 0)
    oriData.value = res.data || []
    setTheTheme()
    myChart.setOption(options)
    nextTick(() => {
      myChart.resize()
    })
  })
}

function setTheTheme() {
  options.series.itemStyle.borderColor = props.theme === 'data-normal' ? '#fff' : '#062155'
  options.series.label.color = props.theme === 'data-normal' ? '#333' : '#8CCEFF'
  options.series.labelLine.lineStyle.color = props.theme === 'data-normal' ? null : '#8CCEFF'
}

function legendClick(name, e) {
  console.log(e)
  myChart.dispatchAction({
    type: 'legendToggleSelect',
    name,
  })
  e.currentTarget.classList.toggle('legend-unactive')
}

onMounted(() => {
  myChart = echarts.init(chartRef.value)
  window.addEventListener('resize', () => {
    myChart?.resize()
  })
  getTheData()
})
</script>

<template>
  <div class="organ-content">
    <div :class="theme">
      <div class="data-title">
        <div class="icon" h-16px w-4px />
        <div class="title">
          机构分布
        </div>
      </div>
      <div v-if="!isEmpty" flex-1 class="layout">
        <div class="layout-content">
          <div
            v-for="(item, index) in oriData" :key="index" class="legend-item" @click.stop="(e) => {
              legendClick(item.name, e)
            }"
          >
            <div class="squre" h-12px w-12px :style="{ background: colors[index] }" />
            <div class="name">
              {{ item.name }}
            </div>
            <div class="scale">
              {{ item.scale }}
            </div>
          </div>
        </div>
        <div ref="chartRef" class="chart-content" mt-16px flex-1 />
      </div>
      <div v-else h-full flex items-center justify-center>
        <n-empty size="large" mode="none" description="无数据" />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.organ-content {
  display: flex;
  flex-direction: column;
  border-radius: 3px;
  overflow: hidden;
  box-shadow: 0px 1px 4px 0px rgba(202, 202, 202, 0.50);

  .data-normal {
    height: 100%;
    display: flex;
    background: #ffffff;
    padding: 16px 16px 6px 16px;
    flex-direction: column;

    .data-title {
      display: flex;
      align-items: center;

      .icon {
        background: #06aea6;
      }

      .title {
        margin-left: 10px;
        font-size: 16px;
        color: #333333;
        line-height: 16px;
      }
    }

    .layout{
        display: flex;
        align-items: center;
        flex-direction: row-reverse;
        height: 200px;

        .layout-content{
          display: flex;
          flex-direction: column;
          gap: 14px;

          .legend-item{
              display: flex;
              align-items: center;

              .name{
                color: #666;
                font-size: 12px;
                margin-left: 6px;
                max-width: 136px;
                overflow: hidden;
                text-overflow: ellipsis;
                -webkit-line-clamp: 1;
                line-clamp: 1;
                white-space: nowrap;
              }

              .scale{
                margin-left: 10px;
                font-size: 12px;
                color: #333;
              }
          }

          .legend-unactive{
            .squre{
              background: #999 !important;
            }
            .name{
              color:#999;
            }
            .scale{
              color:#999;
            }
          }
        }

        .chart-content{
          flex: 1;
          width: 200px;
          height: 100%;
        }
    }
  }

  .data-big {
    height: 100%;
    display: flex;
    flex-direction: column;
    background: #062155;
    border: 1px solid #3f63b5;
    border-radius: 3px;
    padding: 16px 16px 6px 16px;

    .data-title {
      display: flex;
      align-items: center;

      .icon {
        background: #8CCEFFFF;
      }

      .title {
        margin-left: 10px;
        font-size: 16px;
        color: #8CCEFFFF;
        line-height: 16px;
      }
    }

      .layout{
        display: flex;
        flex-direction: column;
        height: 200px;

        .layout-content{
          display: grid;
          gap: 14px 0;
          margin-top: 16px;
          grid-template-columns: repeat(2, 1fr);
          .legend-item{
              display: flex;
              align-items: center;

              .name{
                color: #4B8DBE;
                font-size: 12px;
                margin-left: 6px;
                max-width: 136px;
                overflow: hidden;
                text-overflow: ellipsis;
                -webkit-line-clamp: 1;
                line-clamp: 1;
                white-space: nowrap;
              }

              .scale{
                margin-left: 10px;
                font-size: 12px;
                color: #fff;
              }
          }

          .legend-unactive{
            .squre{
              background: #999 !important;
            }
            .name{
              color:#999;
            }
            .scale{
              color:#999;
            }
          }
        }

        .chart-content{
          height: 200px;
          flex: 1;
          width: 100%;
        }
    }
  }

}
</style>
