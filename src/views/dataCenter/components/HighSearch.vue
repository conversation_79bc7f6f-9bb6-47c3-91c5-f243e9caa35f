<script lang='ts' setup>
import { v4 as uuidv4 } from 'uuid'
import { useDebounceFn } from '@vueuse/core'
import { useMessage } from 'wowjoy-vui'
import { cloneDeep } from 'lodash'
import { FormType, Options, UNIT, extentLeftOptions, extentRightOptions } from './constant'
import OutGroupLine from './OutGroupLine.vue'
import InGroupLine from './InGroupLine.vue'
import type { ConditionName, FormList, FormName, FormTypeVal, FormatValueParams, GroupList, RelationOption, Result } from './type'
import arcode from '@/utils/arcode'
import { getAdvancedSearchListApi } from '@/api/patient'
import type { ConditionType, HighConditions } from '@/api/patient'
import { isArray } from '@/utils'
import { getDoctorGroupListApi } from '@/api/doctor'
import { getDiagnoseListApi } from '@/api/dic'
import type { DoctorGroupRecord } from '@/api/doctor'
import type { DiagnoseRecord } from '@/api/dic'
import { useAuthStore } from '@/store'

const emit = defineEmits(['onSearch', 'onReset'])

const message = useMessage()
const { userInfo } = useAuthStore()
const CONDITIONS_TYPE: Record<ConditionType, ConditionName> = {
  0: 'single',
  1: 'multiple',
  2: 'number',
  3: 'date',
  4: 'text',
  5: 'address',
}

const FORM_TYPE: Record<ConditionType, FormTypeVal> = {
  0: 'single',
  1: 'multiple',
  2: 'number',
  3: 'date',
  4: 'input',
  5: 'cascader',
}
/* 逻辑关系 */
const OPTIONS_TYPE: Record<ConditionName, RelationOption[]> = {
  single: [
    { label: '完全等于', value: '=' },
    { label: '包含任意', value: 'in' },
  ],
  multiple: [
    { label: '完全等于', value: '=' },
    { label: '包含任意', value: 'in' },
  ],
  number: [
    { label: '大于', value: '>' },
    { label: '等于', value: '=' },
    { label: '小于', value: '<' },
    { label: '大于等于', value: '>=' },
    { label: '小于等于', value: '<=' },
    { label: '介于', value: 'between' },
  ],
  date: [
    { label: '大于', value: '>' },
    { label: '大于等于', value: '>=' },
    { label: '小于', value: '<' },
    { label: '小于等于', value: '<=' },
    { label: '介于', value: 'between' },
  ],
  text: [
    { label: '等于', value: '=' },
  ],
  address: [
    { label: '等于', value: 'like' },
  ],
}

const groupList = ref<GroupList[]>([])

const realGroupList = ref<GroupList[]>([])

function syncGroupList() {
  realGroupList.value = cloneDeep(groupList.value)
}

/** 初始化init */
function init() {
  getAdvancedSearchList()
  initDefaultForm()
  initAddress()
  initMainDiagnose()
}
onMounted(() => {
  init()
})

const conditions = ref<HighConditions[]>([])
/** 获取条件列表 */
async function getAdvancedSearchList() {
  const { data } = await getAdvancedSearchListApi<HighConditions[]>()
  if (data)
    conditions.value = data
}
/** 初始化默认的条件 */
function initDefaultForm() {
  groupList.value = [
    {
      formList: [],
      groupUuid: uuidv4(),
    },

  ]
  const initDefaultFromItem = createCondition()
  groupList.value[0].formList[0] = initDefaultFromItem
}
const addressOptions = ref<any[]>([])
/** 初始化地址 */
function initAddress() {
  const data = Object?.entries(arcode)?.map(([key, value]) => {
    return {
      code: key,
      name: value.name,
      children: value.child,
    }
  })
  addressOptions.value = data
}
const mainDiagnose = ref<DiagnoseRecord[]>([])
async function initMainDiagnose() {
  const res = await getDiagnoseListApi<DiagnoseRecord[]>({ name: '' })
  if (res.data) {
    const resOptions = res.data.map((item) => {
      return {
        label: item.diagnoseName,
        value: item.diagnoseIcd10,
        ...item,
      }
    })
    const defaultArray = [{ label: '全部', value: '' }] as DiagnoseRecord[]

    mainDiagnose.value = defaultArray.concat(resOptions)
  }
}

/** 新增条件组 */
function createGroup() {
  const formItem = createCondition()
  const group: GroupList = {
    formList: [
      formItem,
    ],
    groupUuid: uuidv4(),
  }
  return group
}

/** 新增条件 */
function createCondition() {
  const condition: FormList = {
    formUuid: uuidv4(),
    condition: {
      value: null,
      name: '',
      type: '0',
      status: undefined,
      code: '',
      field: '',
    },
    relation: {
      name: '',
      value: null,
      status: undefined,
    },
    result: {
      name: '',
      status: undefined,
      formType: 'input',
      value: null,
      left: '>=',
      right: '<=',
      options: [],
    },
  }

  return condition
}
/**
 * 删除条件
 * @param groupIndex 条件组索引
 * @param formIndex 条件索引
 */
function onDeleteCondition(groupIndex: number, formIndex: number) {
  if (groupIndex > -1 && formIndex > -1)
    groupList.value[groupIndex].formList.splice(formIndex, 1)
}
/**
 * 新增条件
 * @param groupIndex 条件组索引
 */
function onCreateCondition(groupIndex: number) {
  const condition = createCondition()
  groupList.value[groupIndex].formList.push(condition)
}
/**
 * 删除条件组
 * @param groupIndex 条件组索引
 */
function onDeleteGroup(groupIndex: number) {
  if (groupIndex > -1)
    groupList.value.splice(groupIndex, 1)
}
/**
 * 新增条件组
 */
function onCreateGroup() {
  const group = createGroup()
  groupList.value.push(group)
}
/**
 * 表单失焦触发回调
 * @param groupIndex 条件组索引
 * @param formIndex 条件索引
 * @param key 表单key
 */

// @ts-expect-error any
function onBlur(groupIndex: number, formIndex: number, key: FormName) {

}

// function onBlur(groupIndex: number, formIndex: number, key: FormName) {
//   const formItem = groupList.value[groupIndex].formList[formIndex]
//   const conditionBlur = () => {
//     const value = formItem.condition.value
//     formItem.condition.status = value ? undefined : 'error'
//   }

//   const relationBlur = () => {
//     const value = formItem.relation.value
//     formItem.relation.status = value ? undefined : 'error'
//   }
//   const resultBlur = () => {
//     const value = formItem.result.value

//     if (isArray(value)) {
//       // 日期类型数组
//       const isNumberArray = isNumber(value[0]) && isNumber(value[1])

//       formItem.result.status = (isNumberArray && (value[1] < value[0])) ? 'error' : undefined
//     }
//     else {
//       formItem.result.status = value ? undefined : 'error'
//     }
//   }
//   const strategyActions: Record<FormName, () => void> = {
//     CONDITION: () => conditionBlur(),
//     RELATION: () => relationBlur(),
//     RESULT: () => resultBlur(),
//   }
//   strategyActions[key]()
// }

/**
 * 表单值更新时回调函数
 * @param value 值
 * @param option 下拉中的option
 * @param groupIndex 条件组索引
 * @param formIndex 条件列表索引
 * @param key 表单key
 */
function onUpdate(value: string, option: HighConditions | RelationOption | Result, groupIndex: number, formIndex: number, key: FormName) {
  const formItem = groupList.value[groupIndex].formList[formIndex]

  /** 条件值更新 */
  const conditionUpdate = () => {
    const highConditions = option as HighConditions
    formItem.condition.type = highConditions.type
    formItem.condition.field = highConditions.field
    formItem.condition.code = highConditions.code
    formItem.condition.name = highConditions.name
    formItem.condition.value = value

    // 重置逻辑关系表单
    formItem.relation.name = ''
    formItem.relation.value = null

    // 重置值域表单的值和表单类型
    formItem.result.formType = FORM_TYPE[highConditions.type]
    formItem.result.value = null
    // 如果有枚举改变枚举
    formItem.result.options = Options[highConditions.name] ?? []
    if (formItem.condition.name === '主诊断')
      formItem.result.options = mainDiagnose.value
  }

  /** 逻辑关系值更新 */
  const relationUpdate = () => {
    const relationOption = option as RelationOption
    const formType = formItem.result.formType
    // 介于关系时，变更值为数组
    if (relationOption.value === 'between' && formType === FormType.NUMBER)
      formItem.result.value = []
    else
      formItem.result.value = null
  }
  /** 值域值更新 */
  const resultUpdate = () => {
    const resultOptions = option as Result
    const formType = formItem.result.formType

    if (formType === FormType.ADDRESS)
      formItem.result.value = resultOptions.name
  }

  const strategyActions: Record<FormName, () => void> = {
    CONDITION: () => conditionUpdate(),
    RELATION: () => relationUpdate(),
    RESULT: () => resultUpdate(),
  }
  strategyActions[key]()
}
const remoteNameArray: string[] = []
/**
 * select下拉搜索
 * @param query 搜索参数
 * @param groupIndex 条件组索引
 * @param formIndex 条件索引
 * @param key 表单的key
 */
async function onSearch(query: string, groupIndex: number, formIndex: number, key: FormName) {
  const formItem = groupList.value[groupIndex].formList[formIndex]
  const name = formItem.condition.name

  if (key === 'RESULT') {
    if (remoteNameArray.includes(name))
      formItem.result.options = await getAsyncOptions(name, query)
  }
}
/**
 * select打开/关闭回调
 * @param show 下拉列表打开/隐藏
 * @param groupIndex 条件组索引
 * @param formIndex 条件索引
 * @param key 表单key
 */
async function onUpdateShow(show: boolean, groupIndex: number, formIndex: number, key: FormName) {
  const formItem = groupList.value[groupIndex].formList[formIndex]
  const name = formItem.condition.name

  if (show && key === 'RESULT' && remoteNameArray.includes(name))
    formItem.result.options = await getAsyncOptions(name)
}
/**
 * 获取异步的下拉列表
 * @param key 表单的key
 * @param query 搜索的参数
 */
async function getAsyncOptions(name: string, query = ''): Promise<any> {
  const organId = userInfo.organId
  /** 获取所在医疗组 */
  const doctor = useDebounceFn(async (name: string, organId: string) => {
    const res = await getDoctorGroupListApi<DoctorGroupRecord[]>({ name, organId })
    if (res.data) {
      const resOptions = res.data.map((item) => {
        return {
          label: item.doctorGroupName,
          value: item.doctorGroupId,
          ...item,
        }
      })
      if (!name) {
        const defaultArray = [{ label: '全部', value: '' }] as DoctorGroupRecord[]

        return Promise.resolve(defaultArray.concat(resOptions))
      }
      else {
        return Promise.resolve(resOptions)
      }
    }
  }, 300)
  /** 获取主诊断 */
  // @ts-expect-error nouse
  const diagnose = useDebounceFn(async (name: string) => {
    const res = await getDiagnoseListApi<DiagnoseRecord[]>({ name })
    if (res.data) {
      const resOptions = res.data.map((item) => {
        return {
          label: item.diagnoseName,
          value: item.diagnoseIcd10,
          ...item,
        }
      })
      if (!name) {
        const defaultArray = [{ label: '全部', value: '' }] as DiagnoseRecord[]

        return Promise.resolve(defaultArray.concat(resOptions))
      }
      else {
        return Promise.resolve(resOptions)
      }
    }
  }, 300)

  const strategyActions: Record<string, () => void> = {
    所在医疗组: () => doctor(query, organId),
    // 主诊断: () => diagnose(query),

  }
  return await strategyActions[name]()
}
/**
 * 设置末尾按钮的间距
 * @param groupIndex 条件组索引
 * @param formIndex 条件列表索引
 */
function setEndButtonSpaceSize(_groupIndex: number, formIndex: number): [number, number] {
  if (formIndex === 0)
    return [0, 0]

  else
    return [10, 0]
}

function formatResultValue(payload: FormatValueParams) {
  const { value, left, right, formType, relation } = payload

  let valueRes: string[] = []
  if (formType === FormType.NUMBER) {
    if (isArray(value) && relation === 'between')
      valueRes = [left, `${value[0]}`, `${value[1]}`, right]
    else
      valueRes = [`${value}`]
  }
  if (formType === FormType.SINGLE) {
    if (isArray(value) && relation === 'in')
      valueRes = [...value] as string[]
    else
      valueRes = [`${value}`]
  }

  if (formType === FormType.MULTIPLE) {
    if (isArray(value))
      valueRes = [...value] as string[]
  }
  if (formType === FormType.ADDRESS)
    valueRes = [`${value}`]

  if (formType === FormType.DATE) {
    if (isArray(value) && relation === 'between')
      valueRes = [left, `${value[0]}`, `${value[1]}`, right]
    else
      valueRes = [`${value}`]
  }
  if (formType === FormType.INPUT && value)
    valueRes = [`${value}`]

  return valueRes
}
// function checkStatus(status: StatusVal[]) {
//   const isFail = status.includes('error')
//   return isFail
// }

function filterData(data: GroupList[]) {
  return data.map(group => ({
    ...group,
    formList: group.formList.filter(form =>
      form.condition.value !== null
      && form.relation.value !== null
      && form.result.value !== null,
    ),
  })).filter(group => group.formList.length > 0)
}

function checkEmptyValue(data: GroupList[]) {
  let isEmpty = true

  for (let i = 0; i < data.length; i++) {
    const formList = data[i].formList
    for (let j = 0; j < formList.length; j++) {
      const form = formList[j]
      if (form.condition.value || form.relation.value || form.result.value) {
        isEmpty = false
        break
      }
    }
    if (!isEmpty)
      break
  }

  return isEmpty
}

const isGroupListEmpty = computed(() => {
  return filterData(groupList.value)?.length === 0
})

const isGroupAllEmpey = computed(() => {
  return checkEmptyValue(groupList.value)
})

function searchPatientList(params: GroupList[]) {
  try {
    const list = filterData(params).map((group: GroupList) => {
      const formList = group.formList.map((form) => {
        const condition = form.condition
        const relation = form.relation
        const result = form.result

        // isNullOrUnDef(condition?.value) && (condition.status = 'error')
        // isNullOrUnDef(relation?.value) && (relation.status = 'error')
        // isNullOrUnDef(result?.value) && (result.status = 'error')

        // if (isArray(result?.value) && !result.value.length)
        //   result.status = 'error'

        // const formStatusFail = checkStatus([condition.status, relation.status, result.status])

        // if (formStatusFail && showError)
        //   throw new Error('请检查条件是否正确填写！')

        const params = {
          value: result.value,
          left: result?.left,
          right: result?.right,
          formType: result.formType,
          relation: relation.value,
        }
        const getValue = formatResultValue(params)

        const list = {
          code: condition.code,
          field: condition.field,
          id: condition.value,
          name: condition.name,
          relation: relation.value,
          type: condition.type,
          value: getValue,

        }
        return list
      })

      return { list: formList }
    })

    return {
      list,
    }

    // emit('onSearch', { list })
  }
  catch (error: any) {
    const errorMsg = error?.message
    message.error(errorMsg)
    return false
  }
}

function genSearchParams() {
  return searchPatientList(unref(realGroupList))
}

defineExpose({
  genSearchParams,
  syncGroupList,
  isGroupListEmpty,
  isGroupAllEmpey,
  initDefaultForm,
})

function onResetClick() {
  initDefaultForm()
  emit('onReset')
}

const isPopoverShow = defineModel<boolean>('popoverShow', { default: false })

watch(isPopoverShow, (val) => {
  if (val && groupList.value.length === 0)
    initDefaultForm()
})
</script>

<template>
  <n-popover v-model:show="isPopoverShow" trigger="click" placement="bottom" raw>
    <template #trigger>
      <div flex cursor-pointer items-center>
        <div ml-20px mr-6px :class="!isGroupAllEmpey && isGroupListEmpty ? 'text-#06AEA6' : 'text-#666'">
          高级搜索
        </div>
        <SvgIcon
          local-icon="slmc-icon-jiantou"
          size="8"
          class="color-#999 transition-all duration-300"
          :class="isPopoverShow ? 'rotate-180' : 'rotate-0'"
        />
      </div>
    </template>
    <div w-960px bg-white pb-20px>
      <div max-h-327px overflow-auto px-20px pt-20px class="2xl:max-h-527px">
        <div v-for="(group, groupIndex) in groupList" :key="group.groupUuid">
          <!-- 或 -->
          <OutGroupLine v-show="groupList?.length >= 2 && groupIndex !== 0" />
          <div class="relative w-full bg-#F8F8F8 py-10px pl-40px pr-14px">
            <n-space :size="[10, 10]" vertical>
              <div v-for="(form, formIndex) in group.formList" :key="form.formUuid">
                <section class="flex items-center gap-10px">
                  <div class="flex flex-grow-2 basis-302px items-center">
                    <SvgIcon local-icon="slmc-icon-app_required1" :size="16" class="text-#F36969" />
                    <div class="mr-10px w-55px">
                      条件{{ formIndex + 1 }}
                    </div>
                    <!-- 条件 -->
                    <n-select
                      v-model:value="form.condition.value"
                      :status="form.condition.status" placeholder="请选择匹配条件"
                      label-field="name"
                      value-field="id"
                      filterable
                      :options="conditions"
                      @blur="onBlur(groupIndex, formIndex, 'CONDITION')"
                      @update:value="(value, option) => onUpdate(value, option, groupIndex, formIndex, 'CONDITION')"
                    />
                  </div>
                  <!-- 逻辑关系 -->
                  <n-select
                    v-model:value="form.relation.value"
                    :status="form.relation.status"
                    placeholder="请选择"
                    class="flex-grow-1 basis-130px"
                    :options="OPTIONS_TYPE[CONDITIONS_TYPE[form.condition.type]]"
                    @blur="onBlur(groupIndex, formIndex, 'RELATION')"
                    @update:value="(value, option) => onUpdate(value, option, groupIndex, formIndex, 'RELATION')"
                  />

                  <!-- 值域 -->
                  <n-select
                    v-if="form.result.formType === FormType.SINGLE"
                    v-model:value="form.result.value"
                    :status="form.result.status"
                    filterable
                    :remote="remoteNameArray.includes(form.condition.name)"
                    placeholder="请选择"
                    class="flex-grow-3 basis-500px"
                    :options="form.result.options"
                    :multiple="form.relation.value === 'in' ? true : false"
                    @blur="onBlur(groupIndex, formIndex, 'RESULT')"
                    @search="(query:string) => onSearch(query, groupIndex, formIndex, 'RESULT')"
                    @update:show="(show:boolean) => onUpdateShow(show, groupIndex, formIndex, 'RESULT')"
                  />
                  <n-select
                    v-else-if="form.result.formType === FormType.MULTIPLE"
                    v-model:value="form.result.value"
                    :status="form.result.status"

                    filterable remote multiple
                    placeholder="请选择"
                    class="flex-grow-3 basis-500px"
                    :options="form.result.options"
                    :max-tag-count="4"
                    @blur="onBlur(groupIndex, formIndex, 'RESULT')"
                    @search="(query:string) => onSearch(query, groupIndex, formIndex, 'RESULT')"
                    @update:show="(show:boolean) => onUpdateShow(show, groupIndex, formIndex, 'RESULT')"
                  />
                  <!-- 日期类型 -->
                  <n-date-picker
                    v-else-if="form.result.formType === FormType.DATE"
                    v-model:formatted-value="form.result.value"
                    value-format="yyyy-MM-dd HH:mm:ss"
                    :type="form.relation.value === 'between' ? 'daterange' : 'date'"
                    class="flex-grow-3 basis-500px"
                    @blur="onBlur(groupIndex, formIndex, 'RESULT')"
                  />
                  <!-- 数值类型 -->
                  <div
                    v-else-if="form.result.formType === FormType.NUMBER && form.relation.value !== 'between'"
                    class="w-full flex flex-grow-3 basis-500px items-center gap-10px"
                  >
                    <n-input-number
                      v-model:value="form.result.value"
                      :status="form.result.status"
                      placeholder="请输入"
                      :show-button="false"
                      class="flex-1"
                      @blur="onBlur(groupIndex, formIndex, 'RESULT')"
                    />
                    <div v-if="form.result.formType === FormType.NUMBER && UNIT[form.condition.name]" class="w-20px">
                      {{ UNIT[form.condition.name] }}
                    </div>
                  </div>
                  <div
                    v-else-if="form.result.formType === FormType.NUMBER && form.relation.value === 'between'"
                    class="w-full flex flex-grow-3 basis-500px items-center gap-10px"
                  >
                    <div v-if="isArray(form.result.value)" class="flex flex-1 basis-200px items-center">
                      <n-select
                        v-model:value="form.result.left"
                        placeholder="请选择"
                        class="flex-grow-1 basis-65px"
                        :options="extentLeftOptions"
                      />
                      <n-input-number
                        v-model:value="form.result.value[0]"
                        :status="form.result.status"
                        placeholder="请输入"
                        :show-button="false"
                        class="flex-grow-3 basis-130px"
                        @blur="onBlur(groupIndex, formIndex, 'RESULT')"
                      />
                    </div>
                    <span>至</span>
                    <div v-if="isArray(form.result.value)" class="flex flex-1 basis-200px items-center">
                      <n-input-number
                        v-model:value="form.result.value[1]"
                        :status="form.result.status"
                        placeholder="请输入"
                        :show-button="false"
                        class="flex-grow-3 basis-130px"
                        @blur="onBlur(groupIndex, formIndex, 'RESULT')"
                      />
                      <n-select
                        v-model:value="form.result.right"
                        placeholder="请选择"
                        class="flex-grow-1 basis-65px"
                        :options="extentRightOptions"
                      />
                    </div>
                    <div v-if="form.result.formType === FormType.NUMBER && UNIT[form.condition.name]" class="w-20px">
                      {{ UNIT[form.condition.name] }}
                    </div>
                  </div>
                  <!-- 地址类型 -->
                  <n-cascader
                    v-else-if="form.result.formType === FormType.ADDRESS"
                    v-model:value="form.result.value"
                    class="flex-grow-3 basis-500px"
                    :options="addressOptions"
                    label-field="name"
                    value-field="code"
                    placeholder="请选择地址"
                    :status="form.result.status"
                    @blur="onBlur(groupIndex, formIndex, 'RESULT')"
                    @update:value="(value, option) => onUpdate(value, option, groupIndex, formIndex, 'RESULT')"
                  />
                  <!-- 输入框 -->
                  <n-input
                    v-else
                    v-model:value="form.result.value"
                    :status="form.result.status"
                    placeholder="请输入"
                    class="flex-grow-3 basis-500px" @blur="onBlur(groupIndex, formIndex, 'RESULT')"
                  />
                  <n-space :size="setEndButtonSpaceSize(groupIndex, formIndex)" align="center" class="basis-50px !flex-nowrap" justify="start">
                    <SvgIcon
                      v-show="group.formList.length > 1" local-icon="slmc-icon-delete1"
                      class="cursor-pointer"
                      :size="16" @click="onDeleteCondition(groupIndex, formIndex)"
                    />
                    <SvgIcon
                      v-show="group.formList.length - 1 === formIndex"
                      local-icon="slmc-icon-add1" class="cursor-pointer" :size="16" @click="onCreateCondition(groupIndex)"
                    />
                  </n-space>
                </section>
              </div>
            </n-space>
            <!-- 且 -->
            <InGroupLine v-show="group?.formList?.length >= 2" :condition-count="group?.formList?.length" />
            <SvgIcon
              v-show="!(groupList?.length === 1)"
              local-icon="slmc-icon-wrong1"
              :size="16"
              class="absolute cursor-pointer -right-8px -top-8px"
              @click="onDeleteGroup(groupIndex)"
            />
          </div>
        </div>

        <n-space justify="center" class="mt-10px pb-14px">
          <n-button type="primary" ghost round @click="onCreateGroup">
            添加条件组
          </n-button>
        </n-space>
      </div>
      <n-divider px-20px margin="0px 0px 0px 0px" />

      <n-space justify="center" mt-20px>
        <n-button type="primary" @click="emit('onSearch')">
          查询
        </n-button>
        <n-button @click="onResetClick">
          重置
        </n-button>
      </n-space>
    </div>
  </n-popover>
</template>
