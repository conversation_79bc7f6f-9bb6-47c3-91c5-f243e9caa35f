<script setup lang='ts'>
import { useTransition } from '@vueuse/core'
import * as echarts from 'echarts'
import { getZyAlsStatistics } from '~/src/api/statistics'

const props = defineProps({
  theme: {
    type: String,
    default: 'data-normal',
  },
})

const isEmpty = ref(false)
const chartRef = ref()
let myChart

const totalAniCount = ref(0)
const patientAniCount = ref(0)
const rggAniCount = ref(0)

const totalCount = reactive({
  total: {
    left: '#5B96FD',
    back: props.theme === 'data-normal' ? '#5B96FD29' : '#5B96FD4D',
    value: 7812312,
    title: '总人次数',
    titleColor: props.theme === 'data-normal' ? '#666' : '#8CCEFF',
    animateCount: useTransition(totalAniCount, { duration: 1000 }),
  },
  patient: {
    left: '#FFC569',
    back: props.theme === 'data-normal' ? '#FFC56929' : '#FFC5694D',
    value: 10029,
    title: '患者数',
    titleColor: props.theme === 'data-normal' ? '#666' : '#8CCEFF',
    animateCount: useTransition(patientAniCount, { duration: 1000 }),
  },
  rgg: {
    left: '#9EC97F',
    back: props.theme === 'data-normal' ? '#9EC97F29' : '#9EC97F4D',
    value: 2,
    title: '平均患者人工肝次数',
    titleColor: props.theme === 'data-normal' ? '#666' : '#8CCEFF',
    animateCount: useTransition(rggAniCount, { duration: 1000 }),
  },
})

const options = reactive({
  color: ['#5B96FDFF', '#FFC569FF', '#9EC97FFF'],
  grid: {
    left: '8%',
    right: 15,
  },
  legend: {
    itemHeight: 12,
    top: 10,
    icon: 'path://M 0 0 H 12 V 12 H 0 Z',
    textStyle: {
      color: '#666',
      padding: [0, 0, 0, -6],
    },
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    textStyle: {
      color: '#fff',
    },
  },
  xAxis: {
    type: 'category',
    data: [],
    splitLine: { lineStyle: {} },
    axisLabel: {
      rotate: 40,
      color: '#999',
      padding: 10,
      formatter(value) {
        if (value.length > 14)
          return `${value.substring(0, 6)}\n${value.substring(6, 14)}...`
        else if (value.length > 8)
          return `${value.substring(0, 6)}\n${value.substring(6)}`
        else
          return value
      },
    },
    axisLine: {
      lineStyle: {
        color: '#CCC',
      },
    },
  },
  yAxis: {
    type: 'value',
    splitLine: { lineStyle: {} },
    axisLabel: {
      color: '#999',
      formatter: '{value} 人', // 在Y轴标签中添加"人"单位
    },
  },
  series: [
    {
      name: '总人次数',
      type: 'bar',
      data: [],
      barWidth: 20,
      label: {
        show: true,
        position: 'top',
        color: '#fff',
      },
    },
    {
      name: '患者数',
      type: 'bar',
      data: [],
      barWidth: 20,
      label: {
        show: true,
        position: 'top',
        color: '#fff',
      },
    },
    {
      name: '平均患者人工肝次数',
      type: 'bar',
      data: [],
      barWidth: 20,
      label: {
        show: true,
        position: 'top',
        color: '#fff',
      },
    },
  ],
})

function setOptionsData() {
  setTheTheme()
  console.log(options)
  myChart.setOption(options)
  nextTick(() => {
    myChart.resize()
  })
}

function getTheData() {
  getZyAlsStatistics().then((res) => {
    console.log(res)
    totalCount.total.value = res?.data?.all?.personTime
    totalCount.patient.value = res?.data?.all?.personNumber
    totalCount.rgg.value = res?.data?.all?.average

    totalAniCount.value = Number(res?.data?.all?.personTime)
    patientAniCount.value = Number(res?.data?.all?.personNumber)
    rggAniCount.value = Number(res?.data?.all?.average)

    handleTheData(res?.data?.organs)
    if (totalAniCount.value === 0 && patientAniCount.value === 0 && rggAniCount.value === 0)
      isEmpty.value = true
    else
      isEmpty.value = false
  })
}

/// 判断是否包含小数点
function haveDot(num) {
  /// 查询
  const haveDot = num.toString().includes('.')
  return haveDot ? 2 : 0
}

/// 赋值数据
function handleTheData(data) {
  const xAxis = []
  const mapData = {
    总人次数: [],
    患者数: [],
    平均患者人工肝次数: [],
  }
  data?.forEach((element) => {
    xAxis.push(element?.name || '')
    mapData.总人次数.push(element?.personTime || '0')
    mapData.患者数.push(element?.personNumber || '0')
    mapData.平均患者人工肝次数.push(element?.average || '0')
  })

  options.xAxis.data = xAxis
  options.series = ['总人次数', '患者数', '平均患者人工肝次数'].map((item) => {
    return {
      name: item,
      type: 'bar',
      data: mapData[item],
      barWidth: 20,
      barMinHeight: 3,
      label: {
        show: true,
        position: 'top',
        color: '#fff',
      },
    }
  })

  setOptionsData()
}

function setTheTheme() {
  if (props.theme === 'data-big') {
    options.legend.textStyle.color = '#4B8DBE'
    options.xAxis.axisLabel.color = '#4B8DBE'
    options.xAxis.axisLine.lineStyle.color = '#42A1F1'
    options.yAxis.splitLine.lineStyle.color = '#FFFFFF24'
    options.yAxis.axisLabel.color = '#4B8DBEFF'
  }
}

onMounted(() => {
  myChart = echarts.init(chartRef.value)
  getTheData()
  window.addEventListener('resize', () => {
    setTimeout(() => {
      myChart?.resize()
    }, 100)
  })
})
</script>

<template>
  <div class="liver-content">
    <div :class="theme">
      <div v-if="!isEmpty" class="counts">
        <div v-for="(item, index) in totalCount" :key="index" :class="`count-item` + ` count-item-${index}` " :style="{ background: item.back }">
          <div h-full w-2px :style="{ background: item.left }" />
          <el-statistic ml-20px :precision="haveDot(item.value)" :value="item.animateCount">
            <template #title>
              <span text-14px :style="{ color: item.titleColor }">{{ item.title }}</span>
            </template>
          </el-statistic>
        </div>
      </div>
      <div v-if="!isEmpty" ref="chartRef" class="chart-content" ml-20px h-full flex-1 />
      <div v-if="isEmpty" h-full w-full flex items-center justify-center>
        <n-empty size="large" mode="none" description="无数据" />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.liver-content {
  .data-normal {
    height: 330px;
    display: flex;
    align-items: center;
    padding: 16px;
    background: #ffffff;
    box-shadow: 0px 1px 4px 0px rgba(202, 202, 202, 0.50);
    border-radius: 3px;

    .counts {
      display: flex;
      flex-direction: column;
      gap: 14px 0;

      .count-item {
        width: 200px;
        height: 90px;
        display: flex;
        align-items: center;
        border-radius: 3px;
        overflow: hidden;

        :deep(.el-statistic__number) {
          font-size: 26px;
          line-height: 26px;
          font-family: OPPOSans-H;
          font-weight: 500;
        }
      }
    }

    .chart-content{
      flex:1;
      width: 200px;
      height:100%;
    }
  }

  .data-big {
    height: 330px;
    padding: 16px;
    display: flex;
    align-items: center;
    background: #062155;
    border: 1px solid #3f63b5;
    border-radius: 3px;

    .counts {
      display: flex;
      flex-direction: column;
      gap: 14px 0;

      .count-item {
        width: 200px;
        height: 90px;
        display: flex;
        align-items: center;
        border-radius: 3px;
        overflow: hidden;
        :deep(.el-statistic__number) {
          font-size: 26px;
          line-height: 26px;
          /* 设置线性渐变背景 */
          -webkit-background-clip: text;
          /* 对 Chrome 和 Safari 应用背景剪裁 */
          background-clip: text;
          /* 对现代浏览器应用背景剪裁 */
          color: transparent;
          /* 使文本颜色透明，以显示背景渐变 */
          font-family: OPPOSans-H;
        }
      }
      .count-item-total{
        :deep(.el-statistic__number) {
          background-image: linear-gradient(180deg, #ffffff 25%, #0dcaf5);
        }
      }
      .count-item-patient{
        :deep(.el-statistic__number) {
          background-image: linear-gradient(180deg, #ffffff 25%, #ffc569);
        }
      }
      .count-item-rgg{
        :deep(.el-statistic__number) {
          background-image: linear-gradient(180deg, #ffffff 25%, #9ec97f);
        }
      }
    }

    .chart-content{
      flex:1;
      width: 200px;
      height:100%;
    }

  }

}
</style>
