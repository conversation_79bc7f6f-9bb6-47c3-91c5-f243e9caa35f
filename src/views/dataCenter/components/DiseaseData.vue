<script setup lang='ts'>
import * as echarts from 'echarts'
import { getZyDiseaseStatistics } from '~/src/api/statistics'

const props = defineProps({
  theme: {
    type: String,
    default: 'data-normal',
  },
})

const chartRef = ref()
let myChart
const isEmpty = ref(false)

const keysArray = ['one', 'eight', 'two', 'ten', 'nine', 'three']
const datas = reactive({})
const options = reactive({
  colors: ['#5B96FD', '#68D0D4', '#FFC569', '#9D96F5', '#24BEE8', '#9EC97F', '#FF9B54', '#A5B8D1'],
  tooltip: {
    trigger: 'axis',
    backgroundColor: 'rgba(0,0,0,0.7)',
    textStyle: {
      fontSize: '12px',
      color: 'white',
    },
    borderColor: 'rgba(0,0,0,0.7)',
    axisPointer: {
      type: 'shadow', // 'shadow' as default; can also be 'line' or 'shadow'
    },
  },
  legend: {
    align: 'left',
    itemGap: 20,
    itemHeight: 12,
    icon: 'path://M 0 0 H 12 V 12 H 0 Z',
    textStyle: {
      color: '#666',
      padding: [0, 0, 0, -5],
    },
  },
  grid: {
    left: '',
    right: '4%',
    bottom: '3%',
    top: '10%',
    containLabel: true,
  },
  xAxis: {
    type: 'value',
    max(value) {
      return value.max + Math.ceil(value.max)
    },
    axisLabel: {
      color: '#666',
      formatter: '{value}人',
    },
    splitLine: {
      lineStyle: {
        color: '#ccc',
      },
    },
  },
  yAxis: {
    type: 'category',
    data: ['乙肝', '肝硬化', '脂肪肝', '肝肿瘤', '肝衰竭', '丙肝'],
    axisLabel: {
      color: '#666',
    },
    axisLine: {
      lineStyle: {
        color: '#ccc',
      },
    },
  },
  series: [],
})

onMounted(() => {
  myChart = echarts.init(chartRef.value)
  window.addEventListener('resize', () => {
    handleTheLegend()
    setChartOption()
    myChart?.resize()
  })
  getTheData()
})

function getTheData() {
  getZyDiseaseStatistics().then((res) => {
    keysArray.forEach((item) => {
      handleThedata(res?.data?.[item], item)
    })

    isEmpty.value = !(Object.keys(datas).length > 0)
    /// 处理数据
    console.log(datas)
    const series: any = []
    for (const key in datas) {
      const element = datas[key]
      series.push({
        name: key,
        type: 'bar',
        stack: 'total',
        barWidth: 21,
        barMinHeight: 24,
        label: {
          show: true,
          color: '#fff',
        },
        itemStyle: {
          borderColor: '#00000000',
          borderWidth: 1,
        },
        emphasis: {
          focus: 'series',
        },
        data: element,
      })
    }
    options.series = series
    handleTheLegend()
    setTheTheme()
    setChartOption()
    myChart?.resize()
  })
}

function handleTheLegend() {
  /// 处理一下 legend

  const hNumber = Math.ceil((options.series.length * 160) / chartRef.value.clientWidth) || '1'
  options.grid.top = `${hNumber}0%`
}

function setTheTheme() {
  options.legend.textStyle.color = props.theme === 'data-normal' ? '#666' : '#4B8DBE'
  options.yAxis.axisLabel.color = props.theme === 'data-normal' ? '#666' : '#8CCEFF'
  options.yAxis.axisLine.lineStyle.color = props.theme === 'data-normal' ? '#ccc' : '#42A1F1'
  options.xAxis.splitLine.lineStyle.color = props.theme === 'data-normal' ? '#E8E8E8FF' : '#FFFFFF24'
  options.xAxis.axisLabel.color = props.theme === 'data-normal' ? '#999' : '#4B8DBE'
}

function handleThedata(obj, index) {
  for (const key in obj) {
    const num = obj[key]
    if (datas[key]) {
      datas[key][keysArray.indexOf(index)] = num
    }
    else {
      datas[key] = [null]
      datas[key][keysArray.indexOf(index)] = num
    }
  }
}

function setChartOption() {
  myChart.setOption(options)
}
</script>

<template>
  <div class="disease-content">
    <div :class="theme">
      <div class="data-title">
        <div class="icon" h-16px w-4px />
        <div class="title">
          病种分布
        </div>
      </div>
      <div v-if="!isEmpty" ref="chartRef" mt-16px flex-1 />
      <div v-else h-full flex items-center justify-center>
        <n-empty size="large" mode="none" description="无数据" />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.disease-content {
  width: 100%;
  height: 100%;
  .data-normal {
    display: flex;
    height: 100%;
    flex-direction: column;
    background: #fff;
    border-radius: 3px;
    box-shadow: 0px 1px 4px 0px rgba(202, 202, 202, 0.50);
    padding: 16px 16px 6px 16px;

    .data-title {
      display: flex;
      align-items: center;

      .icon {
        background: #06aea6;
      }

      .title {
        margin-left: 10px;
        font-size: 16px;
        color: #333333;
        line-height: 16px;
      }
    }
  }

  .data-big {
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    box-shadow: 0px 1px 4px 0px rgba(202, 202, 202, 0.50);
    padding: 16px 16px 6px 16px;
    background: #062155;
    border: 1px solid #3f63b5;
    border-radius: 3px;

    .data-title {
      display: flex;
      align-items: center;

      .icon {
        background: #8CCEFF;
      }

      .title {
        margin-left: 10px;
        font-size: 16px;
        color: #8CCEFF;
        line-height: 16px;
      }
    }

  }

}
</style>
