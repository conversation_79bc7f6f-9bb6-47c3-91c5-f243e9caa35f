<script setup lang='ts'>
import { getZyAgeSexStatistics } from '~/src/api/statistics'

const props = defineProps({
  theme: {
    type: String,
    default: 'data-normal',
  },
})

const ageGroup = reactive({
  age1: {
    label: '0-10岁',
  },
  age2: {
    label: '11-20岁',
  },
  age3: {
    label: '21-30岁',
  },
  age4: {
    label: '31-40岁',
  },
  age5: {
    label: '41-50岁',
  },
  age6: {
    label: '51-60岁',
  },
  age7: {
    label: '61-70岁',
  },
  age8: {
    label: '71-80岁',
  },
  age9: {
    label: '81-90岁',
  },
  age10: {
    label: '≥91岁',
  },
})

function getTheData() {
  getZyAgeSexStatistics().then((res) => {
    for (const key in res?.data) {
      const element = res?.data[key]
      ageGroup[key].examGroups = element.examGroups
      ageGroup[key].mas = element.mas
      ageGroup[key].ms = element.ms
      ageGroup[key].total = Number(element.ms) + Number(element.mas)
    }
  })
}

onMounted(() => {
  getTheData()
})
</script>

<template>
  <div class="content">
    <div :class="theme">
      <div class="data-title">
        <div flex items-center>
          <div class="icon" h-16px w-4px />
          <div class="title">
            年龄性别分布
          </div>
        </div>
        <div flex items-center>
          <div flex items-center>
            <div style="background: #5B96FD;" h-12px w-12px />
            <div ml-6px text-12px>
              男
            </div>
          </div>
          <div ml-20px flex items-center>
            <div style="background: #FFA8B4;" h-12px w-12px />
            <div ml-6px text-12px>
              女
            </div>
          </div>
        </div>
      </div>
      <div mt-16px flex-col :style="{ gap: theme === 'data-normal' ? '10px' : '15px' }">
        <div
          v-for="(item, index) in ageGroup" :key="index" class="age-sex-item" flex items-center
          @click="(e) => {
            selectTheAgeItem(item, e);
          }
          "
        >
          <div relative flex-1>
            <el-progress
              color="#5B96FD" class="left-male my-progress" :show-text="false" :percentage="item.total === 0
                ? 0
                : (item.mas * 100) / item.total
              " :stroke-width="20"
            />
            <div class="left-male-label">
              {{ item.mas }}
            </div>
          </div>
          <div class="age-label" color="#666" mx-10px min-w-54px py-4px text-center text-14px>
            {{ item.label }}
          </div>
          <div relative flex-1>
            <el-progress
              color="#FFA8B4" class="my-progress right-female" :show-text="false" :percentage="item.total === 0
                ? 0
                : (item.ms * 100) / item.total
              " :stroke-width="20"
            />
            <div class="right-female-label">
              {{ item.ms }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.content{
  height: 100%;
.data-normal {
    height: 100%;
    background: #ffffff;
    border-radius: 3px;
    box-shadow: 0px 1px 4px 0px rgba(202, 202, 202, 0.50);
    padding: 16px 16px 6px 16px;

    .data-title {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .icon {
            background: #06aea6;
        }

        .title {
            margin-left: 10px;
            font-size: 16px;
            color: #333333;
            line-height: 16px;
        }
    }

    .my-progress {
        // width: 130px;

        :deep(.el-progress-bar__inner) {
            border-radius: 3px;
        }

        :deep(.el-progress-bar__outer) {
            border-radius: 3px;
            background-color: #e8e8e8;
        }
    }

    .left-male {
        :deep(.el-progress-bar__inner) {
            left: auto;
            right: 0;
        }
    }

    .left-male-label {
        position: absolute;
        left: 6px;
        top: 0;
        line-height: 20px;
        font-size: 14px;
        color: #333;
    }

    .right-female-label {
        position: absolute;
        right: 6px;
        top: 0;
        line-height: 20px;
        font-size: 14px;
        color: #333;
    }

    .age-sex-item {
        transition: transform 0.2s ease;

        &:hover {
            transform: scaleY(1.3);

            .age-label {
                background: #e5f1fa;
                border-radius: 3px;
                font-size: 14px;
            }

        }
    }

    .age-sex-item-active {
        .age-label {
            background: #e5f1fa;
            border-radius: 3px;
            color: #198eeb;
        }
    }

}

.data-big {
  height: 100%;
background: #062155;
border: 1px solid #3f63b5;
border-radius: 3px;
padding: 16px 16px 6px 16px;

    .data-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        color:#8CCEFF;

        .icon {
            background: #8CCEFF;
        }

        .title {
            margin-left: 10px;
            font-size: 16px;
            color: #8CCEFF;
            line-height: 16px;
        }
    }

    .my-progress {
        // width: 130px;

        :deep(.el-progress-bar__inner) {
            border-radius: 3px;
        }

        :deep(.el-progress-bar__outer) {
            border-radius: 3px;
            background-color: rgba(0,145,255,0.15);
        }
    }

    .left-male {
        :deep(.el-progress-bar__inner) {
            left: auto;
            right: 0;
        }
    }

    .left-male-label {
        position: absolute;
        left: 6px;
        top: 0;
        line-height: 20px;
        font-size: 14px;
        color: #fff;
    }

    .right-female-label {
        position: absolute;
        right: 6px;
        top: 0;
        line-height: 20px;
        font-size: 14px;
        color: #fff;
    }

    .age-sex-item {
        transition: transform 0.2s ease;
        .age-label {
               color:#8CCEFF;
                font-size: 14px;
            }

        &:hover {
            transform: scaleY(1.3);

            .age-label {
                background: #e5f1fa;
                border-radius: 3px;
                font-size: 14px;
                color:#5B96FD;
            }

        }
    }

    .age-sex-item-active {
        .age-label {
            background: #e5f1fa;
            border-radius: 3px;
            color: #198eeb;
        }
    }

}
}
</style>
