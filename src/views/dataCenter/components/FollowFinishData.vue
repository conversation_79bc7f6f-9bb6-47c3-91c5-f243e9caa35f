<script setup lang='ts'>
import * as echarts from 'echarts'
import { getZyFollowCompleteStatistics } from '~/src/api/statistics'

const props = defineProps({
  theme: {
    type: String,
    default: 'data-normal',
  },
})

const chartRef = ref()
let myChart
const isEmpty = ref(false)

const options = reactive({
  grid: {
    top: 10,
    right: 10,
  },
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    textStyle: {
      color: '#fff',
    },
    formatter: '{b}<br /> {c} %',
  },
  xAxis: {
    type: 'category',
    data: ['桐庐县医b', '桐庐县妇保院', '浙大一院', '浙大二院', '富阳骨伤医院', '浙江省肿瘤医院', '国家老年病临床中心十', '其他'],
    axisLabel: {
      rotate: 40,
      color: '#999',
      margin: 12,
      formatter(value) {
        if (value.length > 14)
          return `${value.substring(0, 6)}\n${value.substring(6, 14)}...`
        else if (value.length > 8)
          return `${value.substring(0, 6)}\n${value.substring(6)}`
        else
          return value
      },
    },
    axisLine: {
      lineStyle: {
        color: '#CCC',
      },
    },
  },
  yAxis: {
    type: 'value',
    max: 100,
    axisLabel: {
      formatter: '{value} %',
    },
    splitLine: {
      lineStyle: {},
    },
  },
  series: {
    data: [56.23, 69.45, 48.23, 62.23, 27.23, 16.23, 60.23, 88.23],
    type: 'bar',
    barWidth: 16,
    itemStyle: {
      color: '#5B96FD',
    },
    label: {
      show: true,
      position: 'top',
      formatter: '{c}%',
    },
  },
})

function getTheData() {
  getZyFollowCompleteStatistics().then((res) => {
    const xAxis = []
    const datas = []
    res?.data?.forEach((item) => {
      xAxis.push(item.name)
      datas.push(item.scale?.split('%')[0])
    })
    isEmpty.value = !(res?.data?.length > 0)
    options.xAxis.data = xAxis
    options.series.data = datas
    setChartOption()
  })
}

onMounted(() => {
  myChart = echarts.init(chartRef.value)
  getTheData()
  window.addEventListener('resize', () => {
    setTimeout(() => {
      myChart?.resize()
    }, 100)
  })
})

function setTheTheme() {
  if (props.theme === 'data-big') {
    options.xAxis.axisLabel.color = '#4B8DBE'
    options.xAxis.axisLine.lineStyle.color = '#42A1F1'
    options.yAxis.splitLine.lineStyle.color = '#FFFFFF24'
    options.yAxis.axisLabel.color = '#4B8DBEFF'
    options.series.label.color = '#fff'
  }
}

function setChartOption() {
  setTheTheme()
  myChart.setOption(options)
  nextTick(() => {
    myChart.resize()
  })
}
</script>

<template>
  <div class="follow-content">
    <div :class="theme">
      <div class="data-title">
        <div class="icon" h-16px w-4px />
        <div class="title">
          随访完成率
        </div>
      </div>
      <div v-if="!isEmpty" ref="chartRef" mt-16px flex-1 />
      <div v-else h-full flex items-center justify-center>
        <n-empty size="large" mode="none" description="无数据" />
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.follow-content {

  .data-normal {
    height: 100%;
    background: #ffffff;
    border-radius: 3px;
    box-shadow: 0px 1px 4px 0px rgba(202, 202, 202, 0.50);
    padding: 16px 16px 6px 16px;
    display: flex;
    flex-direction: column;

    .data-title {
      display: flex;
      align-items: center;

      .icon {
        background: #06aea6;
      }

      .title {
        margin-left: 10px;
        font-size: 16px;
        color: #333333;
        line-height: 16px;
      }
    }

  }

  .data-big {
    height: 100%;
    background: #062155;
    border: 1px solid #3f63b5;
    border-radius: 3px;
    padding: 16px 16px 6px 16px;
    display: flex;
    flex-direction: column;

    .data-title {
      display: flex;
      align-items: center;

      .icon {
        background: #8CCEFF;
      }

      .title {
        margin-left: 10px;
        font-size: 16px;
        color: #8CCEFF;
        line-height: 16px;
      }
    }

  }

}
</style>
