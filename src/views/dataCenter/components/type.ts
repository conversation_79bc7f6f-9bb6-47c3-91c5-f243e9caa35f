import type { SelectOption } from 'wowjoy-vui'
import type { FormType } from './constant'
import type { ConditionType } from '@/api/patient'

type LogicLabel = '完全等于' | '包含任意' | '大于' | '等于' | '小于' | '大于等于' | '小于等于' | '介于'
export type LogicValue = '=' | 'in' | '<' | '=' | '>' | '>=' | '<=' | 'between' | 'like'

export type ConditionName = 'single' | 'multiple' | 'number' | 'date' | 'text' | 'address'

export type FormTypeVal = `${FormType}`

export type FormName = 'CONDITION' | 'RELATION' | 'RESULT'

export type StatusVal = undefined | 'error'

export interface RelationOption extends SelectOption {
  label: LogicLabel
  value: LogicValue
}

interface Condition {
  /** 条件id */
  value: null | string
  /** 条件名称 */
  name: string
  /** 条件数据类型 */
  type: ConditionType
  /** 条件表单状态 */
  status: StatusVal
  /** 条件对应标识 */
  code: string
  /** 条件对应字段 */
  field: string
}

interface Relation {
  /** 逻辑关系名字 */
  name: LogicLabel | string
  /** 逻辑关系值 */
  value: LogicValue | null
  /** 关系表单状态 */
  status: StatusVal
}

export interface Result {
  /** 值表单名字 */
  name: string
  /** 值表单值 */
  value: any
  /** 值表单状态 */
  status: StatusVal
  /** value表单类型 */
  formType: FormTypeVal
  /** 左边的开闭区间 */
  left: '>' | '>='
  /** 右边的开闭区间 */
  right: '<' | '<='
  options: SelectOption[]
}

export interface FormList {
  formUuid: string
  condition: Condition
  relation: Relation
  result: Result

}

export interface GroupList {
  formList: FormList[]
  groupUuid: string
}

export interface FormatValueParams {
  value: string | number | number[] | null | string[]
  left: LogicValue
  right: LogicValue
  formType: FormTypeVal
  relation: LogicValue | null
}
