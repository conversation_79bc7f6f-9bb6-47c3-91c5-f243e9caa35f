<script setup lang="ts">
import { type FormInst, NButton, useMessage } from 'wowjoy-vui'
import { useDebounceFn } from '@vueuse/core'
import RenderKeyString from './RenderKeyString.vue'
import type { FieldNode } from '@/api/dataCenter/type'
import { customExportApi, exportDataFieldsApi, exportExcelApi, getSmsCodeApi } from '@/api/dataCenter'
import { SvgIcon } from '@/components/Icon'
import { useAuthStore } from '@/store'

const props = defineProps<{
  patientKeys: string[]
  exportCount: 'selected' | 'all'
  searchParam: string
}>()

const DEFAULT_PHONE = window.location.origin.includes('test-mng') ? '18814819789' : '18757169475'

const showExportModal = defineModel('showExportModal', {
  default: false,
})
const formRef = ref<FormInst | null>(null)

const rules = {
  way: {
    required: true,
    message: '内容不能为空',
    trigger: ['blur', 'change'],
  },
  treat: {
    required: true,
    message: '内容不能为空',
    trigger: ['blur', 'change'],
  },
  range: {
    required: true,
    message: '内容不能为空',
    trigger: ['blur', 'change'],
  },
  verifyCode: {
    required: true,
    message: '内容不能为空',
    trigger: ['blur'],
  },

}

const selectExport = reactive({
  /** 导出方式 */
  way: 'template',
  /** 数据范围 */
  range: null,
  /** 模板治疗模式 */
  treat: 'liver',
  verifyCode: null,
  showCountDown: false,
})

function handleConfirmImport() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      if (selectExport.way === 'template')
        exportExcel()
      else
        customExport()
    }
    else {
      window.$message.warning('请填写完整信息')
      console.log(errors)
    }
  })
}

const exportEnum = {
  ways: [
    { value: 'template', label: '按模板导出' },
    { value: 'custom', label: '自定义导出' }],
  treat: [
    { value: 'liver', label: '人工肝' },
    // FEAT:功能展示隐藏
    // { value: 'coliform', label: '肠菌移植治疗', disabled: true },
  ],
  range: [
    { value: 'all', label: '全量数据' },
    { value: 'last', label: '最新一次数据' }],
}

async function sendVerifyCode() {
  try {
    const res = await getSmsCodeApi(DEFAULT_PHONE)
    if (res?.data) {
      window.$message.success('发送成功！')
      selectExport.showCountDown = true
    }
  }
  catch (error) {

  }
}

const treeData = ref<any>([])
const treeRef = ref()
const isTreeEmpey = ref(false)

const handleSearchUpdate = useDebounceFn(() => {
  isTreeEmpey.value = !treeRef.value.fNodes?.length
}, 300)

function renderIcon() {
  return h(
    SvgIcon,
    {
      localIcon: 'slmc-icon-wenjian1',
      class: 'text-06AEA6',
      size: '16',
    },

  )
}

const searchKey = ref('')

const selectedNodes = ref<FieldNode[]>([])
const checkedKeys = ref<string[]>([])

function resetFormValue() {
  selectExport.way = 'template'
  selectExport.range = null
  selectExport.treat = 'liver'
  selectExport.verifyCode = null
  selectExport.showCountDown = false
}

/**
 * 弹窗打开/关闭的callback
 * @param v true:打开，false:关闭
 */
async function visibleChange(v: boolean) {
  if (!v) {
    resetFormValue()
    handleCleanClick()
    showExportModal.value = false
  }
}

function findFirstMatchingNode(root, condition) {
  const stack = [root]

  while (stack.length > 0) {
    const node = stack.pop()

    if (condition(node))
      return node

    if (node.children && node.children.length > 0)
      stack.push(...node.children)
  }

  return null
}

const isTreeLoading = ref(false)
function updateCheckedKeys(_keys: string[]) {
  selectedNodes.value = _keys.map((_key) => {
    return findFirstMatchingNode(treeData.value[0], node => node.id === _key)
  })

  checkedKeys.value = selectedNodes.value.map(_item => _item.id)
}

function handleDelete(_item, index) {
  selectedNodes.value.splice(index, 1)
  checkedKeys.value = selectedNodes.value.map(_item => _item.id)
}

function handleCleanClick() {
  selectedNodes.value = []
  checkedKeys.value = []
}

async function getFields(name: string = '') {
  try {
    const { data } = await exportDataFieldsApi(name)

    function traversal(tree) {
      tree.forEach((item) => {
        if (item.children) {
          traversal(item.children)
        }

        else {
          item.prefix = () => {
            return h(
              SvgIcon,
              {
                localIcon: 'slmc-icon-wenjian1',
                class: 'text-06AEA6',
                size: '16',
              },

            )
          }
        }
      })
    }

    const originalTree = [{
      name: '全部',
      id: 'ALL',
      children: data,
    }]

    traversal(originalTree)
    treeData.value = originalTree
  }
  catch (error) {
    console.log(error)
  }
}

const { userInfo } = useAuthStore()
const message = useMessage()
const router = useRouter()

function createMessage() {
  const _message = message.success(() => (
    h('div', null, [
      h('span', null, { default: () => '操作成功，请在' }),
      h('span', {
        class: 'text-#1A78B8 cursor-pointer',
        onClick: () => {
          router.push('/dataCenter/records')
          _message.destroy()
        },
      }, { default: () => '导出记录' }),
      h('span', null, { default: () => '中查看详情' }),
    ])
  ),
  {
    keepAliveOnHover: true,
  })
}

async function customExport() {
  const { data } = await customExportApi<boolean>({
    code: selectExport.verifyCode!,
    dataRange: selectExport.range as unknown as string,
    fieldIds: checkedKeys.value,
    patientList: props.patientKeys,
    phone: DEFAULT_PHONE,
    userId: userInfo.id,
    userName: userInfo.userName,
    allExport: props.exportCount === 'all',
    searchParam: props.searchParam,
  })

  if (data) {
    showExportModal.value = false
    resetFormValue()
    createMessage()
  }
  else {
    message.error('导出失败')
  }
}

async function exportExcel() {
  const params = {
    dataRange: selectExport.range as unknown as string,
    exportWay: selectExport.way,
    patientList: props.patientKeys,
    code: selectExport.verifyCode,
    phone: DEFAULT_PHONE,
    technique: selectExport.treat,
    userId: userInfo.id,
    userName: userInfo.userName,
    allExport: props.exportCount === 'all',
    searchParam: props.searchParam,
  }

  const { data } = await exportExcelApi(params)
  if (data) {
    showExportModal.value = false
    resetFormValue()
    createMessage()
  }
  else {
    message.error('导出失败')
  }
}

onMounted(() => {
  getFields()
})
</script>

<template>
  <BasicModal
    :visible="showExportModal" title="导出数据" width="720" ok-text="导出" @visible-change="visibleChange"
    @ok="handleConfirmImport"
  >
    <n-form
      ref="formRef" label-placement="left" :model="selectExport" :rules="rules" require-mark-placement="left"
      :show-feedback="false"
    >
      <div mx-20px my-20px>
        <n-form-item label="导出方式" path="way">
          <n-radio-group v-model:value="selectExport.way" name="exportWay">
            <n-space align="center" :size="[40, 0]">
              <n-radio v-for="way in exportEnum.ways" :key="way.value" :value="way.value">
                {{ way.label }}
              </n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item>
        <n-form-item v-show="selectExport.way === 'template'" path="treat" :show-label="false">
          <div class="mb-15px ml-80px mr-20px mt-5px h-36px w-full bg-[#F5F5F5] px-14px pt-8px">
            <n-radio-group v-model:value="selectExport.treat" name="exportWay">
              <n-space align="center" :size="[40, 0]">
                <n-radio v-for="treat in exportEnum.treat" :key="treat.value" :value="treat.value">
                  {{ treat.label }}
                </n-radio>
              </n-space>
            </n-radio-group>
          </div>
        </n-form-item>

        <n-form-item v-show="selectExport.way === 'custom'" path="organName" :show-label="false">
          <div class="my-10px ml-80px h-320px w-full flex justify-start text-#333">
            <div class="w-326px border border-#d1d1d1 border-rd-3px">
              <div class="h-32px flex items-center bg-#E7F9F7 pl-10px">
                请选择导出字段
              </div>
              <div>
                <n-input
                  v-model:value="searchKey" placeholder="请输入字段名称搜索" style="--n-border:1px solid transparent;
                    --n-border-focus:1px solid transparent;
                    --n-border-hover:1px solid transparent;
                    --n-box-shadow-focus:none"
                  @update:value="handleSearchUpdate"
                >
                  <template #suffix>
                    <SvgIcon
                      v-show="searchKey" local-icon="slmc-icon-delete_11" color="#000"
                      class="cursor-pointer op30 hover:op50" size="16"
                      @click="() => {
                        searchKey = ''
                        isTreeEmpey = false
                      }"
                    />
                    <SvgIcon v-show="!searchKey" local-icon="slmc-icon-search" color="#ccc" class="cursor-pointer" size="16" />
                  </template>
                </n-input>
                <n-divider margin="0px 0px 0px 0px" class="px-10px" />
              </div>
              <n-spin :show="isTreeLoading" size="medium">
                <div class="h-253px overflow-auto pt-10px">
                  <n-tree
                    v-show="!isTreeEmpey"
                    ref="treeRef"
                    class="custom-tree h-240px"
                    label-field="name"
                    key-field="id"
                    check-strategy="child"
                    :show-irrelevant-nodes="false"
                    :pattern="searchKey" :data="treeData" cascade block-line
                    checkable
                    checkbox-placement="right"
                    default-expand-all
                    virtual-scroll
                    :checked-keys="checkedKeys"
                    :render-icon="renderIcon"
                    @update:checked-keys="updateCheckedKeys"
                  >
                    <template #default="{ data }">
                      <div class="custom-tree-node flex items-center justify-between">
                        <RenderKeyString :checked-keys="checkedKeys" :data="data" :max-width="200" :search-key="searchKey" :text="data?.name" />
                      </div>
                    </template>
                    <template #empty>
                      <div class="h-full flex pt-100px">
                        <EmptyList />
                      </div>
                    </template>
                  </n-tree>
                  <div v-show="isTreeEmpey" class="h-210px flex items-center">
                    <EmptyList msg="无搜索结果" />
                  </div>
                </div>
              </n-spin>
            </div>
            <div class="w-36px flex items-center justify-center">
              <SvgIcon local-icon="slmc-icon-crumbs1" color="#666" />
            </div>
            <div class="w-210px border border-#d1d1d1 border-rd-3px">
              <div class="h-32px flex items-center justify-between bg-#E7F9F7 px-10px">
                <div>
                  已选择 ({{ selectedNodes.length }})
                </div>
                <div
                  :class="selectedNodes.length ? 'text-#3B8FD9 cursor-pointer' : 'text-#3B8FD9/30 cursor-not-allowed'"
                  @click="handleCleanClick"
                >
                  清空
                </div>
              </div>
              <div v-if="selectedNodes.length" h-286px overflow-auto>
                <div
                  v-for="(item, index) in selectedNodes"
                  :key="item.id"
                  flex justify-between px-13px py-10px leading-none
                  class="select-item"
                >
                  <span text-12px>
                    {{ item.name }}
                  </span>

                  <SvgIcon
                    class="close-icon"
                    local-icon="slmc-icon-deletex2" size="10" @click="handleDelete(item, index)"
                  />
                </div>
              </div>
              <div
                v-else
                class="h-286px flex"
              >
                <EmptyList msg="无数据" />
              </div>
            </div>
          </div>
        </n-form-item>

        <n-form-item label="数据范围" path="range">
          <n-radio-group v-model:value="selectExport.range" name="exportWay">
            <n-space align="center" :size="[40, 0]">
              <n-radio v-for="range in exportEnum.range" :key="range.value" :value="range.value">
                {{ range.label }}
              </n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item>

        <n-divider dashed margin="15px 0 20px 0" />
        <!-- TODO -->
        <n-form-item label="请进行身份验证，验证码将发送至杨玲(187****9475)" path="verifyCode">
          <div mb-4px flex gap-10px>
            <NButton
              class="vertifycode" secondary strong type="success" :disabled="selectExport.showCountDown"
              @click="sendVerifyCode"
            >
              <div v-if="!selectExport.showCountDown">
                获取验证码
              </div>
              <n-countdown
                v-else :duration="1000 * 59" :active="selectExport.showCountDown"
                :on-finish="() => selectExport.showCountDown = false"
              />
            </NButton>
            <n-input v-model:value="selectExport.verifyCode" placeholder="请输入验证码" :maxlength="6" w-240px w-full />
          </div>
        </n-form-item>
      </div>
    </n-form>
  </BasicModal>
</template>

<style scoped lang="scss">
:deep(.n-tree .n-tree-node) {
  align-items: normal !important;
}

:deep(.n-tree .n-tree-node-checkbox-padding) {
  margin-right: 10px;
}

.select-item {

  .close-icon {
      cursor: pointer;

    }

  &:hover {
    background-color: #FFFBE0;

  }
}
</style>
