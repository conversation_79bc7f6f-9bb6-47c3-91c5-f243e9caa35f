<script setup lang='ts'>
import * as echarts from 'echarts'
import { useIntervalFn } from '@vueuse/core'
import chinaMap from '@/assets/map/china.json'
import { getZyRegionsStatistics } from '~/src/api/statistics'
import map_tip from '@/assets/images/zheYiData/full_map_tip.png'

const props = defineProps({
  theme: {
    type: String,
    default: 'data-normal',
  },
})

const chartRef = ref()

let myChart
const oridata = ref()
const options = reactive({
  tooltip: {
    backgroundColor: 'rgba(0,0,0,0.7)',
    textStyle: {
      fontSize: '12px',
      color: 'white',
    },
    trigger: 'item',
    formatter: (params) => {
      console.log(params)
      return `${params.name} <br /><br /> 人数: ${params.data?.value || '-'} <br /><br />占比: ${params.data?.scale || '-'}`
    },
    extraCssText: '',
  },
  //   visualMap: {
  //     min: 0,
  //     max: 1000,
  //     left: 'left',
  //     top: 'bottom',
  //     text: ['High', 'Low'],
  //     inRange: {
  //       color: ['#e0ffff', '#006edd'],
  //     },
  //     show: true,
  //   },
  series:
  {
    zoom: 1.3,
    center: [105, 36],
    aspectScale: 0.75,
    name: 'China',
    type: 'map',
    map: 'china',
    roam: false, // 启用平移和缩放
    itemStyle: {
      normal: {
        areaColor: 'rgb(235,246,255)', // 默认区域颜色
        borderColor: 'rgba(129,144,229,0.6)', // 地图线条颜色
        borderWidth: 1, // 线条宽度
      },
      emphasis: {
        label: {},
        areaColor: '#FFC569', // 鼠标悬停时的区域颜色
        borderColor: 'rgba(129,144,229,0.6)', // 鼠标悬停时的线条颜色
        borderWidth: 1, // 悬停时线条宽度
      },
    },
    data: [
      //   { name: '北京市', value: 1000 },
      //   { name: '上海市', value: 800 },
      //   { name: '广东省', value: 900 },
      //   { name: '杭州市', value: 1000 },
    ],
  },
})

onMounted(() => {
  echarts.registerMap('china', { geoJSON: chinaMap })
  myChart = echarts.init(chartRef.value)
  // setChartOption()

  window.addEventListener('resize', () => {
    /// 设置缩放的倍率
    // debugger
    const bl = chartRef.value.clientWidth / 410
    options.series.zoom = bl
    myChart?.setOption(options)
    myChart?.resize()
  })

  getTheData()
  animateShow()
})

function animateShow() {
  /// 间隔触发 tooltip
  let dataIndex = 0
  const { pause, resume, isActive } = useIntervalFn(() => {
    myChart.dispatchAction({
      type: 'showTip',
      seriesIndex: 0,
      dataIndex,
    })
    dataIndex = (dataIndex + 1) % options.series.data.length
  }, 2000)

  myChart.on('mousemove', (params) => {
    console.log('Tooltip triggered:', params)
    /// 暂停
    pause()
  })
  myChart.on('mouseout', (params) => {
    console.log('Tooltip triggered:', params)
    /// 暂停
    resume()
  })
}

function getTheData() {
  getZyRegionsStatistics().then((res) => {
    options.series.data = res?.data?.map((item) => {
      return {
        name: item.name,
        value: item.number,
        scale: item.scale,
      }
    })
    oridata.value = res?.data
    setChartOption()
  })
}

function setTheTheme() {
  options.series.itemStyle.normal.areaColor = props.theme === 'data-normal' ? 'rgb(235,246,255)' : '#1C499F'
  options.series.itemStyle.normal.borderColor = props.theme === 'data-normal' ? 'rgba(129,144,229,0.6)' : '#7DA5FF'
  options.series.itemStyle.emphasis.areaColor = props.theme === 'data-normal' ? '#FFC569' : 'rgba(0, 158, 205, 1)'
  options.series.itemStyle.emphasis.label.color = props.theme === 'data-normal' ? '#333' : '#fff'

  if (props.theme === 'data-big') {
    options.tooltip.backgroundColor = 'transparent'
    options.tooltip.borderWidth = 0
    options.tooltip.position = 'inside'
    options.tooltip.extraCssText = `background-image: url(${map_tip});background-size: cover;box-shadow:none;width:42px;height:53px;padding:0;margin:0;display:flex;align-items:center;`
    options.tooltip.formatter = function (params) {
      console.log(params)
      return `
            <div mb-12px w-full text-center text-12px>${params.data?.value || '-'}</div>
           `
    }
  }

  /// 设置缩放的倍率
  const bl = chartRef.value.clientWidth / 420
  options.series.zoom = bl
}

function setChartOption() {
  setTheTheme()
  myChart.setOption(options)
  nextTick(() => {
    myChart.resize()
  })
}
</script>

<template>
  <div class="map-content">
    <div :class="theme">
      <div class="data-title">
        <div class="icon" h-16px w-4px />
        <div class="title">
          地域分布
        </div>
      </div>
      <div flex flex-1 items-center style="height: 200px; width: 100%;">
        <div ref="chartRef" mt-16px style="flex: 1; height: calc(100% - 10px);width: 200px;" />
        <div style="gap: 10px;display: flex;flex-direction: column;">
          <div v-for="(item, index) in oridata" :key="index" class="map-item" flex items-center>
            <div
              style="overflow: hidden;line-clamp: 1;text-overflow: ellipsis;white-space: nowrap;min-width: 60px;"
              color="#666"
              class="province"
            >
              {{ `${item.name}: ` }}
            </div>
            <div class="count" ml-5px>
              {{ `${item.number} 人` }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.map-content {
  display: flex;
  flex-direction: column;
  border-radius: 3px;
  overflow: hidden;
  box-shadow: 0px 1px 4px 0px rgba(202, 202, 202, 0.50);

  .data-normal {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: #ffffff;
    padding: 16px 16px 6px 16px;

    .data-title {
      display: flex;
      align-items: center;

      .icon {
        background: #06aea6;
      }

      .title {
        margin-left: 10px;
        font-size: 16px;
        color: #333333;
        line-height: 16px;
      }
    }
  }

  .data-big {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: #062155;
    padding: 16px 16px 6px 16px;
    border: 1px solid #3f63b5;
    border-radius: 3px;

    .data-title {
      display: flex;
      align-items: center;

      .icon {
        background: #8CCEFF;
      }

      .title {
        margin-left: 10px;
        font-size: 16px;
        color: #8CCEFF;
        line-height: 16px;
      }
    }
    .map-item{
      .province{
      color: #4B8DBE !important;
    }
    .count{
      color: #fff !important;
    }
    }
  }

  .custom-tooltip {
            position: relative;
            display: inline-block;
            width: 42px;
            height: 53px;
            background-image: url('@/assets/images/zheYiData/full_map_tip.png'); /* 背景图片 */
            text-align: center;
            background-size: cover;
            line-height: 53px;
            color: white;
        }

}
</style>
