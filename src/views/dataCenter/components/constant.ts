import type { SelectOption } from 'wowjoy-vui'

interface Extent extends SelectOption {
  label: '[' | '(' | ']' | ')'
  value: '>=' | '>' | '<=' | '<'
}

const SEX_NAME: SelectOption[] = [
  { label: '男', value: '男' },
  { label: '女', value: '女' },
//   { label: '未知', value: '未知' },
]
const BLOOD_TYPE: SelectOption[] = [
  { label: 'A型', value: 'A' },
  { label: 'B型', value: 'B' },
  { label: 'O型', value: 'O' },
  { label: 'AB型', value: 'AB' },
  { label: 'RH型', value: 'RH' },
]
const BLOOD_ABO_TYPE: SelectOption[] = [
  { label: 'A型', value: 'A型' },
  { label: 'B型', value: 'B型' },
  { label: 'O型', value: 'O型' },
  { label: 'AB型', value: 'AB型' },

]
const BLOOD_RH_TYPE: SelectOption[] = [
  { label: '阴性', value: '阴性' },
  { label: '阳性', value: '阳性' },

]

export const UNIT: Record<string, string> = {
  年龄: '岁',
  身高: 'cm',
  体重: 'kg',
}

export const Options: Record<string, SelectOption[]> = {
  性别: SEX_NAME,
  血型: BLOOD_TYPE,
  ABO血型: BLOOD_ABO_TYPE,
  RH血型: BLOOD_RH_TYPE,
}

export const extentLeftOptions: Extent[] = [
  {
    label: '[',
    value: '>=',
  },
  {
    label: '(',
    value: '>',
  },

]
export const extentRightOptions: Extent[] = [
  {
    label: ']',
    value: '<=',
  },
  {
    label: ')',
    value: '<',
  },

]

export enum FormType {
  SINGLE = 'single',
  INPUT = 'input',
  DATE = 'date',
  NUMBER = 'number',
  MULTIPLE = 'multiple',
  ADDRESS = 'cascader',
}
