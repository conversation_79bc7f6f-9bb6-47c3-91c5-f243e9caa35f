<script setup lang='ts'>
import G6 from '@antv/g6'
import { debounce } from 'lodash-es'

import { getAllEntitys, queryEntitys, searchEntitys } from '@/api/dataCenter/data.ts'

const relations = ref([])
const lanTypes = ref([])
const searchLists = ref([])
const searchContent = ref('')

const route = useRoute()

const unSelect = reactive({
  0: [],
  1: [],
})

let navPush = []
const nodeDatas = ref([])

const selects1Indeter = ref(false)
const select1CheckAll = ref(true)
const select1s = ref()

const selects2Indeter = ref(false)
const select2CheckAll = ref(true)
const select2s = ref()

// const selectOne = reactive([])
// const selectTwo = reactive([])

let g6Graph

function fittingString(str, maxWidth, fontSize) {
  const ellipsis = '...'
  const ellipsisLength = G6.Util.getTextSize(ellipsis, fontSize)[0]
  let currentWidth = 0
  let res = str
  const pattern = new RegExp('[\u4E00-\u9FA5]+') // distinguish the Chinese charactors and letters
  str.split('').forEach((letter, i) => {
    if (currentWidth > maxWidth - ellipsisLength)
      return
    if (pattern.test(letter)) {
      // Chinese charactors
      currentWidth += fontSize
    }
    else {
      // get the width of single letter according to the fontSize
      currentWidth += G6.Util.getLetterWidth(letter, fontSize)
    }
    if (currentWidth > maxWidth - ellipsisLength)
      res = `${str.substr(0, i)}${ellipsis}`
  })
  return res
}

/// 获取节点数据
async function getAllData() {
  try {
    const { data } = await getAllEntitys('1070333')
    if (data) {
      const { nodes, links, relation, type } = formatData(data)
      nodeDatas.value = nodes

      relations.value = relation
      lanTypes.value = type
      //   navPushTopNodes.push(nodes[0])
      btnClick({ text: '全部' }, 0)
      btnClick({ text: '全部' }, 1)
      render(nodes, links)
    }
  }
  catch (error) {
    console.log('🚀 ~ file: App.vue:186 ~ getAllData ~ error:', error)
  }
}

/// / 搜索
async function inputChange(e) {
  const str = e
  searchContent.value = str
  handleSearch(str, str.trim().length === 0)
}

/// / 防抖
const handleSearch = debounce((query: string, resetState = false) => {
  if (resetState) {
    searchLists.value = []
    /// 切换回 肝炎
    navPush = []
    if (nodeDatas.value[0]?.label !== '急性肝炎')
      // getAllData()
      searchIdEnter()
  }
  else {
    searchEntitys(query).then((res: any) => {
      searchLists.value = res.data || []
    })
  }
}, 220)

/// 实焦点
function handleBlur(e) {
  console.log(e)
  /// 有可能是真的查询
  setTimeout(() => {
    searchLists.value = []
  }, 300)
}

/// push 推送栈里面

/// 获取节点数据
async function getNodeData(id, clickNode = false, model = null) {
  try {
    const { data } = await queryEntitys(id)
    if (data && data?.length > 0) {
      const { nodes, links, relation, type } = formatData(data)

      /// 赋值前, 先加入堆栈 push到下一个节点
      if (clickNode) {
        if (model?.isBack) {
          /// 点击了返回, 那么返回 pop 到层级 /// 出栈
          const temps = JSON.parse(JSON.stringify(navPush))
          for (let index = temps.length - 1; index >= 0; index--) {
            const element = temps[index]
            if (element.id === id)
              break

            navPush.pop()
          }
        }
        else {
          /// 有可能重复加
          const last = navPush[navPush.length - 1]

          const item = nodeDatas.value[0]
          item.isBack = true
          item.size = 70
          item.style = {
            fill: 'l(0) 0:#f9957f 1:#eebd89',
          }
          if (last?.id != item.id)
            navPush.push(item)
        }
        /// 遍历加入 节点
        navPush.forEach((nd, index) => {
          if (nodes[0].id != nd.id)
            nodes.push(nd)

          const tid = (index == navPush.length - 1) ? nodes[0].id : (navPush[index + 1]).id
          if (tid != nd.id) {
            links.push({
              target: tid,
              source: nd.id,
              label: '返回',
              isBack: true,
              fullName: nd.fullName,
              realName: '',
              semanticTag: '',
              style: {
                stroke: '#c973ff',
                endArrow: {
                  fill: '#c973ff',
                  stroke: '#c973ff',
                },
              },
              labelCfg: {
                refY: 5,
                refX: 5,
                position: 'middle',
                autoRotate: true,
                style: {
                  fill: '#c973ff',
                },
              },
            })
          }
        })
      }
      nodeDatas.value = nodes
      relations.value = relation
      lanTypes.value = type
      //   removeChart()
      /// 继续添加层级
      //   addTopBackNode(nodes, links)
      btnClick({ text: '全部' }, 0)
      btnClick({ text: '全部' }, 1)
      render(nodes, links)
    }
  }
  catch (error) {
    console.log('🚀 ~ file: App.vue:251 ~ getNodeData ~ error:', error)
  }
}

function searchItemClick(item) {
  console.log('信息')
  searchLists.value = []
  searchContent.value = item.entity
  navPush = []
  getNodeData(item.entityId)
}

/// 初始化图形
function startRender() {
  const container1 = document.getElementById('mountNode')
  const width = container1?.scrollWidth || 400
  const height = container1?.scrollHeight || 300

  const tooltip = new G6.Tooltip({
    offsetX: -70,
    offsetY: -70,
    fixToNode: [1, 0.5],
    // the types of items that allow the tooltip show up
    // 允许出现 tooltip 的 item 类型
    itemTypes: ['node'],
    // custom the tooltip's content
    // 自定义 tooltip 内容
    getContent: (e) => {
      const outDiv = document.createElement('div')
      outDiv.style.width = 'fit-content'
      outDiv.style.height = 'fit-content'
      const model = e.item?.getModel()
      if (e.item?.getType() === 'node')
        outDiv.innerHTML = `${model.fullName}`

      return outDiv
    },
  })

  g6Graph = new G6.Graph({
    container: 'mountNode',
    width,
    height,
    renderer: 'canvas',
    // fitView: true,
    layout: {
      type: 'force',
      preventOverlap: true,
      linkDistance: 170,
      nodeStrength: -1500,
      edgeStrength: 0.8,
      collideStrength: 0.8,
      // gpuEnabled: true,
    },
    plugins: [tooltip],
    animate: false,
    defaultNode: {
      size: 60,
      style: {
        fill: 'l(0) 0:rgba(67,191,244,1) 1:rgba(0,104,180,1)',
        stroke: '',
      },
      labelCfg: {
        style: {
          fill: '#fff',
        },
      },
    },
    defaultEdge: {
      style: {
        stroke: '#ccc',
        endArrow: {
          path: 'M 0,0 L 6,3 L 6,-3',
          fill: 'rgba(201, 79, 79, 1)',
          stroke: 'rgba(201, 79, 79, 1)',
        },
      },
    },
    nodeStateStyles: {
      /// 状态
      hover: {
        opacity: 0.2,
      },
    },
    edgeStateStyles: {
      hover: {
        opacity: 0.2,
      },
    },
    modes: {
      default: ['drag-canvas', 'zoom-canvas'],
    },
  })

  /// / size 变化
  if (typeof window !== 'undefined') {
    window.onresize = () => {
      console.log('嘻嘻嘻')
      if (!g6Graph || g6Graph.get('destroyed'))
        return
      // if (!container || !container.scrollWidth || !container.scrollHeight) return;
      changeCanvansSize()
    }
  }
}

function changeCanvansSize() {
  const rect = document.getElementById('mountNode')
  g6Graph.changeSize(rect?.offsetWidth, rect?.offsetHeight)
}

/// 处理节点数据
function formatData(data) {
  const relation = []
  const type = []
  const temps = []
  const temps2 = []
  const nodes = data.map((i, index) => {
    if (index !== 0 && !temps.includes(i.relation[0].name)) {
      relation.push({ text: i.relation[0].name })
      temps.push(i.relation[0].name)
    }

    if (index !== 0 && !temps2.includes(i.semanticTag)) {
      type.push({ text: i.semanticTag })
      temps2.push(i.semanticTag)
    }

    /// 第一个有可能是组织机构什么的, 可能 valId 不为空 直接把第一个当source
    if (index == 0 && i.val[0].valId.length !== 0)
      i.val[0].valId = i.entityInfo[0].eid

    const node = {
      id: i.val[0].valId || i.entityInfo[0].eid,
      label: index > 0 ? i.val[0].realName : i.entityInfo[0].realName,
      fullName: index > 0 ? i.val[0].name : i.entityInfo[0].name,
      size: index == 0 ? 90 : 60,
      relation: i.relation[0].name || '',
      semanticTag: i.semanticTag,
    }

    node.label = fittingString(node.label, node.size, 14)
    return node
  })

  const links = data.filter((j, index) => index > 0).map((i) => {
    return {
      target: i.val[0].valId || i.entityInfo[0].eid,
      source: i.entityInfo[0].eid,
      label: i.relation[0].name || '',
      fullName: i.val[0].name,
      realName: i.val[0].realName,
      semanticTag: i.semanticTag,
      labelCfg: {
        refY: 5,
        refX: 5,
        position: 'start',
        autoRotate: true,
        style: {
          fill: 'rgba(201, 79, 79, 1)',
        },

      },
    }
  })
  /// 默认全选
  select1s.value = temps
  select2s.value = temps2
  return {
    nodes, links, relation, type,
  }
}

function render(nodes, links) {
  g6Graph.data({
    nodes,
    edges: links.map((edge, i) => {
      edge.id = `edge${i}`
      return Object.assign({}, edge)
    }),
  })
  g6Graph.render()
  nextTick(() => {
    changeCanvansSize()
  })
}

function graphAddAction() {
  g6Graph.on('node:dragstart', (e) => {
    g6Graph.layout()
    refreshDragedNodePosition(e)
  })
  g6Graph.on('node:drag', (e) => {
    refreshDragedNodePosition(e)
  })
  g6Graph.on('node:dragend', (e) => {
    e.item.get('model').fx = null
    e.item.get('model').fy = null
  })

  g6Graph.on('afterrender', (evt) => {
    // g6Graph.fitView(0, { onlyOutOfViewPort: true, direction: 'y' })
  })

  /// 边选中
  // g6Graph.on('edge:click', (evt) => {
  //     const edge = evt.item;
  //     console.log('edge clicked', edge);
  //     g6Graph.setItemState(edge, 'selected', true);
  // });
  // 点击节点
  // 点击边
  g6Graph.on('node:mouseenter', (e) => {
    // 先将所有当前是 click 状态的边置为非 click 状态
    //   const clickEdges = g6Graph.findAllByState('edge', 'click');
    //   clickEdges.forEach(ce => {
    //     g6Graph.setItemState(ce, 'click', false);
    //   });
    // node 节点的hover
    if (e.item.hasState('hover'))
      return

    const nodes = g6Graph.getNodes()
    const edges = g6Graph.getEdges()
    const item = e.item.getModel('model')

    nodes.forEach((nd, index) => {
      const nds = nd.getModel('model')
      if (nds.fullName !== item.fullName && index !== 0) {
        g6Graph.updateItem(nd, {
          style: {
            opacity: 0.2,
          },
        })
      }
    })

    edges.forEach((ed, index) => {
      const eds = ed.getModel('model')
      if (eds.fullName !== item.fullName) {
        g6Graph.updateItem(ed, {
          labelCfg: {
            style: {
              opacity: 0,
            },
          },
          style: {
            endArrow: false,
            opacity: 0.2,
          },
        })
      }
    })
  })

  ///  选中的边 对应上的文字,  其他调透明度
  g6Graph.on('node:mouseleave', (e) => {
    const nodes = g6Graph.getNodes()
    const edges = g6Graph.getEdges()
    nodes.forEach((nd, index) => {
      g6Graph.updateItem(nd, {
        style: {
          opacity: 1,
        },
      })
    })

    edges.forEach((ed, index) => {
      const item = ed.getModel('model')
      if (!unSelect[1].includes(item.semanticTag) && !unSelect[0].includes(item.label)) {
        g6Graph.updateItem(ed, {
          labelCfg: {
            style: {
              opacity: 1,
            },
          },
          style: {
            opacity: 1,
            endArrow: {
              path: 'M 0,0 L 6,3 L 6,-3',
              fill: item.isBack ? '#c973ff' : 'rgba(201, 79, 79, 1)',
              stroke: item.isBack ? '#c973ff' : 'rgba(201, 79, 79, 1)',
            },
          },
        })
      }
    })
  })

  g6Graph.on('node:click', (e) => {
    console.log('xxx')
    ///  更新节点数据
    /// 如果主要是当前的 大球, id  就不需要了
    const first = nodeDatas.value[0]
    if (e.item.getModel('model').id === first.id)
      return
    getNodeData(e.item.getModel('model').id, true, e.item.getModel('model'))
  })
}

// 取消边的selected状态
// g6Graph.on('mountNode:click', (evt) => {
//     g6Graph.getEdges().forEach((edge) => {
//       g6Graph.setItemState(edge, 'selected', false);
//   });
// });

function refreshDragedNodePosition(e) {
  const model = e.item.get('model')
  model.fx = e.x
  model.fy = e.y
}

/// 选项选中
function btnClick(item, type) {
  item.select = !item.select

  /// 得把 对面的 全部选中
  // btnClick({text:'全部'},type===0?1:0)
  if (type === 0) {
    lanTypes.value.forEach((one) => {
      one.select = true
    })
  }
  else {
    relations.value.forEach((one) => {
      one.select = true
    })
  }

  if (item.text === '全部') {
    if (type === 0) {
      relations.value.forEach((one) => {
        one.select = !!item.select
      })
    }
    else {
      lanTypes.value.forEach((one) => {
        one.select = !!item.select
      })
    }
  }
  else {
    /// check 全部是否应该点亮
    if (type === 0) {
      let show = true
      for (let index = 1; index < relations.value.length; index++) {
        const element = relations.value[index]
        if (!element.select) {
          show = false
          relations.value[0].select = show
          break
        }
        relations.value[0].select = true
      }
    }
    else {
      /// check 全部是否应该点亮
      let show = true
      for (let index = 1; index < lanTypes.value.length; index++) {
        const element = lanTypes.value[index]
        if (!element.select) {
          show = false
          lanTypes.value[0].select = show
          break
        }
        lanTypes.value[0].select = true
      }
    }
  }

  /// / 查看未选中的
  const t1 = []
  const t2 = []
  relations.value.forEach((item) => {
    if (!item.select)
      t1.push(item.text)
  })
  lanTypes.value.forEach((item) => {
    if (!item.select)
      t2.push(item.text)
  })
  unSelect[0] = t1
  unSelect[1] = t2

  handleTheSelect()
}

/// 处理选中图标数据

function handleTheSelect() {
  const nodes = g6Graph.getNodes()
  const edges = g6Graph.getEdges()
  const nodeUnselect = unSelect[0]
  const nodeUnselect2 = unSelect[1]
  nodes.forEach((node) => {
    const pn = node.getModel('model')
    if (nodeUnselect.includes(pn.relation) || nodeUnselect2.includes(pn.semanticTag))
      g6Graph.setItemState(node, 'hover', true)
    else
      g6Graph.clearItemStates(node, 'hover')
  })

  edges.forEach((ce) => {
    const pn = ce.getModel('model')
    if (nodeUnselect.includes(pn.label) || nodeUnselect2.includes(pn.semanticTag)) {
      g6Graph.setItemState(ce, 'hover', true)
      g6Graph.updateItem(ce, {
        labelCfg: {
          style: {
            opacity: 0,
          },
        },
        style: {
          endArrow: false,
        },
      })
    }
    else {
      g6Graph.clearItemStates(ce, ['hover'])
      g6Graph.updateItem(ce, {
        labelCfg: {
          style: {
            opacity: 1,

          },
        },
        style: {
          opacity: 1,
          endArrow: {
            path: 'M 0,0 L 6,3 L 6,-3',
            fill: 'rgba(201, 79, 79, 1)',
            stroke: 'rgba(201, 79, 79, 1)',
          },
        },
      })
    }
  })
}

onMounted(() => {
  /// 获取当前查询内容
  setTimeout(() => {
    startRender()
    // getAllData()
    searchIdEnter()
    graphAddAction()
    searchIdEnter()
  }, 500)

  window.addEventListener('resize', () => {
    changeCanvansSize()
    g6Graph.fitCenter()
  })
})

function searchIdEnter() {
  const idd = route.path.split('/').reverse()[0]
  if (idd !== 'knowledgeInfo') {
    navPush = []
    getNodeData(idd)
  }
}

onUnmounted(() => {
  g6Graph.destroy()
})

// watch(GlobalScreen, (n, o) => {
//   /// 全屏按钮触发了
//   console.log(n)
//   setTimeout(() => {
//     changeCanvansSize()
//     g6Graph.fitCenter()
//   }, 300)
// })

/// 选中
function handleCheckAll(val) {
  selects1Indeter.value = false
  if (val) {
    select1s.value = relations.value.map(_ => _.text)
    unSelect[0] = []
  }
  else {
    select1s.value = []
    unSelect[0] = relations.value.map(_ => _.text)
  }

  handleTheSelect()
}
function handleCheckAll2(val) {
  selects2Indeter.value = false
  if (val) {
    select2s.value = lanTypes.value.map(_ => _.text)
    unSelect[1] = []
  }
  else {
    select2s.value = []
    unSelect[1] = lanTypes.value.map(_ => _.text)
  }

  handleTheSelect()
}

function unHave(arr, oriArr) {
  const temp = []
  oriArr.forEach((item) => {
    if (!arr?.includes(item.text))
      temp.push(item.text)
  })
  return temp
}

watch(select1s, (val) => {
  if (val.length === 0) {
    select1CheckAll.value = false
    selects1Indeter.value = false
  }
  else if (val.length === relations.value.length) {
    select1CheckAll.value = true
    selects1Indeter.value = false
  }
  else {
    selects1Indeter.value = true
  }
  unSelect[0] = unHave(val, relations.value)
  handleTheSelect()
})
watch(select2s, (val) => {
  if (val.length === 0) {
    select2CheckAll.value = false
    selects2Indeter.value = false
  }
  else if (val.length === lanTypes.value.length) {
    select2CheckAll.value = true
    selects2Indeter.value = false
  }
  else {
    selects2Indeter.value = true
  }
  unSelect[1] = unHave(val, lanTypes.value)
  handleTheSelect()
})
</script>

<template>
  <div>
    <PageCard>
      <PageTitle class="mb-14px">
        知识图谱
      </PageTitle>
      <div class="page-map">
        <div class="top-tools">
          <div style="padding:0 14px 0 14px;position: relative;display: flex;align-items: center">
            <!-- <input :value="searchContent" placeholder="请输入要查询的关键字" class="search" @input="inputChange" @blur="handleBlur">
        <i class="slmc-icon-search" /> -->
            <div mr-10px>
              关键字搜索
            </div>
            <n-input v-model:value="searchContent" style="width: 240px;" clearable placeholder="请输入要查询的关键字"
              @input="inputChange" @blur="handleBlur">
              <template #suffix>
                <i ml-8px class="slmc-icon-search" color="[#999]" />
              </template>
            </n-input>
            <div v-if="searchLists.length > 0" class="search-content">
              <div v-for="(item, index) in searchLists" :key="index" class="search-item" @click="searchItemClick(item)">
                <span>{{ item.entity }}</span>
                <span>{{ item.entityType }}</span>
              </div>
            </div>
          </div>
          <div class="relation-selects">
            <!-- <div class="top">
              <div style="width:4px;height:14px;background-color:#06aea6;margin-right:8px;" />
              <span>筛选属性关系</span>
            </div>
            <div class="selects">
              <div
                v-for="(item, index) in relations" :key="index" :class="item.select ? 'btn-selected' : 'btn'"
                @click="btnClick(item, 0)"
              >
                {{ item.text }}
              </div>
            </div> -->
            <div mr-10px>
              属性关系
            </div>
            <el-select-v2 v-model="select1s" :options="relations" :props="{ label: 'text', value: 'text' }" multiple
              clearable collapse-tags placeholder="请选择属性关系" popper-class="custom-header" :max-collapse-tags="2"
              style="width: 240px">
              <template #header>
                <el-checkbox v-model="select1CheckAll" :indeterminate="selects1Indeter" @change="handleCheckAll">
                  全部
                </el-checkbox>
              </template>
            </el-select-v2>
          </div>
          <div class="relation-selects" style="padding-top: 2px;">
            <!-- <div class="top">
              <div style="width:4px;height:14px;background-color: #06aea6;margin-right:8px;" />
              <span>筛选值的语义类型</span>
            </div>
            <div class="selects">
              <div v-for="(item, index) in lanTypes" :key="index" :class="item.select ? 'btn-selected' : 'btn'"
                @click="btnClick(item, 1)">
                {{ item.text }}
              </div>
            </div> -->
            <div mr-10px>
              语义类型
            </div>
            <el-select-v2 v-model="select2s" :options="lanTypes" :props="{ label: 'text', value: 'text' }" multiple
              clearable collapse-tags placeholder="请选择语义类型" popper-class="custom-header" :max-collapse-tags="2"
              style="width: 240px">
              <template #header>
                <el-checkbox v-model="select2CheckAll" :indeterminate="selects2Indeter" @change="handleCheckAll2">
                  全部
                </el-checkbox>
              </template>
            </el-select-v2>
          </div>
        </div>
        <div class="top" style="margin:14px 14px 0 14px" />
        <div style="padding: 14px 14px 0 14px;background-color: #fff;flex: 1;min-height:160px;position: relative;">
          <div id="mountNode" />
        </div>
      </div>
    </PageCard>
  </div>
</template>

<style scoped lang="scss">
.page-map {
  width: 100%;
  height: 100%;
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  background-color: #fff;
}

.top-tools {
  width: 100%;
  display: flex;
}

#mountNode {
  background-color: #f6f6f6;
  height: 100%;
  display: flex;
}

.search {
  width: 100%;
  height: 32px;
  border: 1px solid #d1d1d1;
  border-radius: 3px;
  padding: 0 10px;
  outline-color: #198EEB
}

.search-icon {
  position: absolute;
  width: 16px;
  height: 16px;
  right: 24px;
  top: 50%;
  transform: translateY(-50%);
}

.relation-selects {
  padding: 0 14px 0 14px;
  display: flex;
  align-items: center;
}

.top {
  display: flex;
  align-items: center;
  font-size: 14px;
  font-family: PingFang SC, PingFang SC-Medium;
  font-weight: 500;
  line-height: 14px;
  margin-bottom: 14px;
}

.selects {
  display: flex;
  flex-wrap: wrap;
  margin-top: 14px;
}

.btn {
  width: 84px;
  font-size: 12px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 3px;
  color: #333;
  border: 1px solid #cccccc;
  margin-right: 10px;
  margin-bottom: 10px;
}

.btn:hover {
  background: #ecf8ff;
  border: 1px solid #4fb1ff;
  color: #198EEB;
}

.btn-selected {
  width: 84px;
  font-size: 12px;
  height: 26px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 3px;
  margin-right: 10px;
  margin-bottom: 10px;
  background: #409eff;
  color: #fff;
  border: 1px solid #409eff;
}

.search-content {
  position: absolute;
  left: 15px;
  right: 16px;
  top: 40px;
  z-index: 99;
  background-color: #fff;
  max-height: 240px;
  overflow-y: auto;
  box-shadow: 1px 1px 5px 0px rgba(202, 202, 202, 0.90);
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
  padding: 6px 0;
}

.search-item {
  display: flex;
  justify-content: space-between;
  font-size: 13px;
  padding: 3px 12px;
}

.search-item:hover {
  background-color: #eee;
}

:deep(.pageCard-content) {
  display: flex;
  flex-direction: column;
}

:deep(.is-checked .el-checkbox__inner) {
  background-color: #409eff !important;
  border-color: #409eff !important;
}
</style>
