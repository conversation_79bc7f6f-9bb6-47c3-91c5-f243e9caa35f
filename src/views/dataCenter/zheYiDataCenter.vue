<script setup lang='ts'>
import dayjs from 'dayjs'
import updateLocale from 'dayjs/plugin/updateLocale'
import { useIntervalFn, useTransition } from '@vueuse/core'
import AgeSexData from './components/AgeSexData.vue'
import DiseaseData from './components/DiseaseData.vue'
import OrganizationData from './components/OrganizationData.vue'
import ChinaMapData from './components/ChinaMapData.vue'
import LiverData from './components/LiverData.vue'
import FollowFinishData from './components/FollowFinishData.vue'
import LiverCureData from './components/LiverCureData.vue'
import { Breadcrumb } from '@/layouts/common'
import patientCount from '@/assets/images/zheYiData/patient_count.png'
import hospitalCount from '@/assets/images/zheYiData/hospital_count.png'
import documentCount from '@/assets/images/zheYiData/document_count.png'
import followsCount from '@/assets/images/zheYiData/follows_count.png'
import { getZyDbStatistics, getZyOverviewStatistics } from '~/src/api/statistics'

import sectionFull from '@/assets/images/zheYiData/section_full.png'
import sectionNormal from '@/assets/images/zheYiData/section_normal.png'
import { router } from '~/src/router'

const props = defineProps({
  fullSreen: {
    type: Boolean,
    default: false,
  },
})
/// 统计动画
const isFullScr = ref(false)
/// 时间
dayjs.extend(updateLocale)
dayjs.updateLocale('en', {
  weekdays: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
})
const currentTime = ref(dayjs().format('YYYY-MM-DD dddd HH:mm:ss'))
let intervalId
function updateTime() {
  currentTime.value = dayjs().format('YYYY-MM-DD dddd HH:mm:ss')
}
const startTime = ref('2015-12-10')
/// 统计
const totalData = ref()
const themeClass = ref(props.fullSreen ? 'data-big' : 'data-normal')
const totalAniCount = ref(0)
const fullAniCount = ref(0)
const onlineAniCount = ref(0)
const standardAniCount = ref(0)

const overData = reactive({
  total: {
    title: '系统收录患者总数',
    count: null,
    icon: patientCount,
    animateCount: useTransition(totalAniCount, {
      duration: 1500,
    }),
  },
  full: {
    title: '完整患者数据数',
    count: null,
    icon: documentCount,
    animateCount: useTransition(fullAniCount, {
      duration: 1500,
    }),
  },
  online: {
    title: '上线机构数',
    count: null,
    icon: hospitalCount,
    animateCount: useTransition(onlineAniCount, {
      duration: 1500,
    }),
  },
  standard: {
    title: '标准化专病方案数',
    count: null,
    icon: followsCount,
    animateCount: useTransition(standardAniCount, {
      duration: 1500,
    }),
  },
})
// const headerCounts = [{
//   title: '系统收录患者总数',
//   count: '255031',
//   icon: patientCount,
// },
// {
//   title: '完整数据数',
//   count: '2311',
//   icon: documentCount,
// },
// {
//   title: '上线机构数',
//   count: '123',
//   icon: hospitalCount,
// },
// {
//   title: '标准化专病方案数',
//   count: '11111',
//   icon: followsCount,
// }]

function countFormatter(value) {
  value = Number(value)
  if (Number(value) >= 100000)
    return value / 10000
  else
    return value
}

function precisionCount(num) {
  num = Number(num)
  if (num > 100000) {
    const str = (num / 10000).toString()
    if (str.includes('.'))
      return str.split('.')[1].length
  }
  return 0
}

function getTotalData() {
  getZyDbStatistics().then((res) => {
    totalData.value = res?.data
  })

  getZyOverviewStatistics().then((res) => {
    overData.total.count = res?.data?.patient
    overData.full.count = res?.data?.completePatient
    overData.online.count = res?.data?.organ
    overData.standard.count = res?.data?.specPlan

    totalAniCount.value = countFormatter(overData.total.count)
    fullAniCount.value = countFormatter(overData.full.count)
    onlineAniCount.value = countFormatter(overData.online.count)
    standardAniCount.value = countFormatter(overData.standard.count)
  })
}

onMounted(() => {
  intervalId = setInterval(updateTime, 1000)
  getTotalData()
  randomCountAnimate()
})

function randomCountAnimate() {
  useIntervalFn(() => {
    totalAniCount.value = 0
    fullAniCount.value = 0
    onlineAniCount.value = 0
    standardAniCount.value = 0

    setTimeout(() => {
      totalAniCount.value = countFormatter(overData.total.count)
      fullAniCount.value = countFormatter(overData.full.count)
      onlineAniCount.value = countFormatter(overData.online.count)
      standardAniCount.value = countFormatter(overData.standard.count)
    }, 200)
  }, 10000)
}

onUnmounted(() => {
  clearInterval(intervalId)
})

function pushNewScreenTab() {
  ///
  const { href } = router.resolve({
    name: 'fullScreen_zheYiDataFullScreen',
  })
  window.open(href, '_blank')
}

function toggleFullScreen() {
  const elem = document.documentElement // 获取整个页面
  elem.onfullscreenchange = handleFullscreenChange
  if (!document.fullscreenElement)
    elem.requestFullscreen()
  else
    document.exitFullscreen()
}

function handleFullscreenChange(event) {
  const elem = event.target
  isFullScr.value = document.fullscreenElement === elem
}
</script>

<template>
  <div flex flex-col>
    <Breadcrumb
      v-if="!props.fullSreen"
      class="breadcrumb" :bread-list="[
        { title: '数据中心', key: 'dataCenter', link: '/dataCenter' },
        { title: '可视化数据', key: 'dataCenter_zheYiDataCenter', link: null },
      ]"
    />
    <div v-if="!props.fullSreen" h-33px />
    <div flex-1 :class="themeClass">
      <div flex items-center class="header">
        <div flex-1 class="time">
          <span>{{ startTime }}</span>
          <span mx-10px>—</span>
          <span>{{ currentTime }}</span>
        </div>
        <div flex-1 class="title">
          SLMC平台数据可视化看板
        </div>
        <div flex flex-1 items-center justify-end>
          <div class="time">
            <span>数据库总条数:</span>
            <span ml-5px>{{ totalData || '-' }}</span>
          </div>
          <n-button
            v-if="themeClass === 'data-normal'" style="margin-left: 10%;" color="#06AEA6" ghost
            @click="pushNewScreenTab"
          >
            投屏
          </n-button>
          <div v-else class="data-big-outbtn" @click="toggleFullScreen">
            <img h-20px w-20px src="@/assets/images/zheYiData/full_screen_btn.png">
            <div ml-10px>
              {{ !isFullScr ? '全屏' : '退出全屏' }}
            </div>
          </div>
        </div>
      </div>
      <div mt-18px flex class="counts">
        <div
          v-for="(item, index) in overData" :key="index" flex flex-1 items-center
          :class="`count-${index} count-content`"
        >
          <img h-56px w-56px :src="item.icon">
          <div ml-18px flex-col>
            <div class="title">
              {{ item.title }}
            </div>
            <div v-if="!item.count" text-26px class="value">
              -
            </div>
            <el-statistic
              v-else :precision="precisionCount(item.count)" class="value" mt-9px
              :value="item.animateCount"
            >
              <template #suffix>
                <span v-if="Number(item.count) >= 1000000">万</span>
              </template>
            </el-statistic>
          </div>
        </div>
      </div>
      <div class="patient-info" mt-20px>
        <div class="section" mb-15px>
          <img h-16px w-16px :src="fullSreen ? sectionFull : sectionNormal">
          <div ml-10px>
            患者分布情况
          </div>
        </div>
        <div class="layout" style="gap: 15px;display: grid;grid-template-columns: 1fr 1fr;">
          <AgeSexData :theme="themeClass" />
          <DiseaseData :theme="themeClass" />
          <OrganizationData :theme="themeClass" />
          <ChinaMapData :theme="themeClass" />
        </div>
      </div>
      <div class="screen-change" mt-20px>
        <div class="liver-info">
          <div class="section" mb-15px>
            <img h-16px w-16px :src="fullSreen ? sectionFull : sectionNormal">
            <div ml-10px>
              人工肝数据
            </div>
          </div>
          <LiverData :theme="themeClass" />
        </div>
        <div class="system-info">
          <div class="section" mb-15px>
            <img h-16px w-16px :src="fullSreen ? sectionFull : sectionNormal">
            <div ml-10px>
              系统运行情况
            </div>
          </div>
          <div mb-15px style="gap: 15px;display: grid;grid-template-columns: 1fr 1fr;">
            <FollowFinishData :theme="themeClass" h-330px />
            <LiverCureData :theme="themeClass" h-330px />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.breadcrumb{
  position: fixed;
  width: calc(100% - 230px);
}
.data-normal {
  background: #F4F5F6;
  padding: 14px;
  overflow-y: auto;

  .header {
    justify-content: space-between;

    .time {
      font-size: 14px;
      color: #666666;
      line-height: 14px;
    }

    .title {
      font-size: 24px;
      font-weight: Medium;
      text-align: center;
      color: #333333;
      line-height: 24px;
    }
  }

  .counts {
    gap: 10px 14px;
    justify-content: space-between;

    .count-content {
      border-radius: 3px;
      box-shadow: 0px 1px 4px 0px rgba(202, 202, 202, 0.50);
      padding: 20px;
    }

    .count-total {
      background: linear-gradient(180deg, #e6efff 0%, #ffffff 50%);
    }

    .count-full {
      background: linear-gradient(180deg, #deeff0 0%, #ffffff 50%);
    }

    .count-online {
      background: linear-gradient(180deg, #e5f0dd 0%, #ffffff 44%);
    }

    .count-standard {
      background: linear-gradient(180deg, #e2e0f1 0%, #ffffff 63%);
    }

    .title {
      font-size: 14px;
      color: #666666;
      line-height: 14px;
    }

    :deep(.el-statistic__number) {
      font-size: 26px;
      line-height: 26px;
      font-family: OPPOSans-H;
    }

    :deep(.el-statistic__suffix) {
      font-size: 16px;
      color: #333;
      line-height: 16px;
    }

  }

  .layout {
    div {
      height: 374px !important;
    }
  }

  .section {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 500;
  }

  @media (max-width: 1919px) {

    /* 在这个条件下应用的样式 */
    .screen-change {
      .system-info {
        margin-top: 20px;
      }
    }
  }

  @media (min-width: 1920px) {

    /* 在这个条件下应用的样式 */
    .screen-change {
      gap: 15px;
      display: grid;
      grid-template-columns: 1fr 1fr;
    }
  }

}

.data-big {
  background: #001338;
  overflow-y: auto;

  .header {
    justify-content: space-between;
    align-items: flex-start;
    background-image: url('@/assets/images/zheYiData/screen_header.png');
    background-size: cover;
    /* 或 contain, 100px 200px 等 */
    background-repeat: no-repeat;
    background-position: center;
    width: 100%;
    padding: 20px 20px 0 20px;
    /* 设置容器的宽度 */
    height: 90px;
    /* 设置容器的高度 */

    .time {
      font-size: 16px;
      color: #BDE3FF;
      line-height: 16px;
    }

    .title {
      font-size: 34px;
      font-weight: Medium;
      line-height: 34px;
      background-image: linear-gradient(180deg, #ffffff, #abd9ff 82%);
      /* 设置线性渐变背景 */
      -webkit-background-clip: text;
      /* 对 Chrome 和 Safari 应用背景剪裁 */
      background-clip: text;
      /* 对现代浏览器应用背景剪裁 */
      color: transparent;
      /* 使文本颜色透明，以显示背景渐变 */
      text-align: center;
      letter-spacing: 0.07px;
      text-shadow: 0px 3px 3px 0px rgba(255, 255, 255, 0.50) inset;
    }

    .data-big-outbtn {
      display: flex;
      align-items: center;
      color: #BDE3FF;
      margin-left: 15%;
    }
  }

  .counts {
    gap: 20px 14px;
    justify-content: space-between;
    padding: 0 20px;

    .count-content {
      border-radius: 3px;
      padding: 20px;

      .title {
        font-size: 16px;
        color: #8CCEFF;
        line-height: 16px;
      }

      .value {
        :deep(.el-statistic__number) {
          font-size: 26px;
          line-height: 26px;
          background-image: linear-gradient(180deg, #ffffff 25%, #0dcaf5);
          /* 设置线性渐变背景 */
          -webkit-background-clip: text;
          /* 对 Chrome 和 Safari 应用背景剪裁 */
          background-clip: text;
          /* 对现代浏览器应用背景剪裁 */
          color: transparent;
          /* 使文本颜色透明，以显示背景渐变 */
          font-family: OPPOSans-H;
        }

        font-size: 26px;
        line-height: 26px;
        background-image: linear-gradient(180deg, #ffffff 25%, #0dcaf5);
        /* 设置线性渐变背景 */
        -webkit-background-clip: text;
        /* 对 Chrome 和 Safari 应用背景剪裁 */
        background-clip: text;
        /* 对现代浏览器应用背景剪裁 */
        color: transparent;
        /* 使文本颜色透明，以显示背景渐变 */

      }
    }

    .count-total {
      background: linear-gradient(0deg, #062155, #06338a);
      border: 1px solid #3f63b5;
    }

    .count-full {
      background: linear-gradient(0deg, #062155, #084a5b 100%);
      border: 1px solid #206e93;
    }

    .count-online {
      background: linear-gradient(0deg, #062155, #20463d 100%);
      border: 1px solid #34707a;
    }

    .count-standard {
      background: linear-gradient(0deg, #062155, #262163 100%);
      border: 1px solid #4f52af;
    }

    .title {
      font-size: 14px;
      color: #666666;
      line-height: 14px;
    }

    :deep(.el-statistic__number) {
      font-size: 26px;
      line-height: 26px;
    }

    :deep(.el-statistic__suffix) {
      font-size: 16px;
      color: #333;
      line-height: 16px;
    }

  }

  .patient-info {
    padding: 0 20px;

    .section {
      color: #fff;
      display: flex;
      align-items: center;
    }

    .layout {
      gap: 14px !important;
      display: grid;
      grid-template-columns: 1fr 1fr 1fr 1fr !important;
      grid-template-rows: repeat(1, 428px) !important;

      div {
        height: 428px !important;
      }

    }
  }

  .screen-change {
    gap: 15px;
    display: grid;
    padding: 0 20px;
    grid-template-columns: 1fr 1fr;

    .section {
      color: #fff;
      display: flex;
      align-items: center;
    }

  }

}
</style>
