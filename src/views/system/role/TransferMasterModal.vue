<script lang='ts' setup>
import { nextTick } from 'vue'
import type { FormInst } from 'wowjoy-vui'
import { useMessage } from 'wowjoy-vui'
import { BasicModal, useModalInner } from '@/components/Modal'
import { getUserApi, transferMasterAdminApi } from '@/api/user/user'
import type { UserPageListRes } from '@/api/user/type'
import { useAuthStore } from '@/store'
import { isMasterAdmin } from '@/utils'

interface OptionsMasterType {
  label: string
  value: string
  organName: string
  organId: string
}

const props = defineProps({
  transferInfo: {
    type: Object as PropType<any>,
    default: () => {},
  },
})

const emit = defineEmits(['transferSuccess', 'register'])

const message = useMessage()

const formRef = ref<FormInst | null>(null)
const rules = {
  newId: {
    required: true,
    trigger: ['blur'],
    message: '内容不能为空',
  },

}
const defaultFormValue = {
  newId: '',
  adminName: '',
  code: '',
  id: '',
}
const formValue = ref({
  newId: '',
  adminName: '',
  code: '',
  id: '',

})

const [register, { closeModal }] = useModalInner()

// 验证
async function handleTransfer<T>(): Promise<Service.RequestResult<T>> {
  const params = {
    code: formValue.value.code,
    id: formValue.value.id,
    newId: formValue.value.newId,
  }
  try {
    const res = await transferMasterAdminApi<T>(params)
    return new Promise((resolve) => {
      resolve(res)
    })
  }
  catch (error) {
    return Promise.reject(error)
  }
}

// 验证
function handleConfirm() {
  formRef.value?.validate(async (errors) => {
    if (!errors) {
      const transferRes = await handleTransfer<string>()
      if (transferRes?.data) {
        closeModal()
        message.success('主管理员更换成功！')
        emit('transferSuccess')
        useAuthStore().resetAuthStore()
      }
    }
    else {
      console.log(errors)
    }
  })
}
// 重置表单
function resetFormValue() {
  formValue.value = { ...defaultFormValue }
}
// 弹窗打开关闭
function visibleChange(v: boolean) {
  v && props.transferInfo && nextTick(() => {
    formValue.value.code = props.transferInfo?.code
    formValue.value.id = props.transferInfo?.id
  })
  if (!v)
    resetFormValue()
}

const optionsMaster = ref<OptionsMasterType[]>([])
const loadingMaster = ref(false)
async function getUser<T>(searchName: string): Promise<T> {
  const params = {
    size: 1000,
    start: 1,
    searchName,
  }
  try {
    const res = await getUserApi<UserPageListRes>(params)
    if (res?.data) {
      const { records = [] } = res.data
      const createOptions = records?.filter((record) => {
        const { userRoleList = [] } = record
        const haveMaterRole = userRoleList.some(role => isMasterAdmin(role?.remark))

        return !haveMaterRole
      },
      ).map((item) => {
        return {
          label: item.userName,
          value: item.id,
          organName: item.organName,
          organId: item.organId,
        }
      })
      return new Promise((resolve) => {
        resolve(createOptions as T)
      })
    }
    else {
      return new Promise((resolve) => {
        resolve([] as T)
      })
    }
  }
  catch (error) {
    return Promise.reject(error)
  }
}
const searchText = ref('')
async function handleSearch(query: string) {
  try {
    searchText.value = query
    const res = await getUser<OptionsMasterType[]>(query)
    optionsMaster.value = res ?? []
  }
  catch (error) {

  }
}
async function onUpdateShow(show: boolean) {
  if (show) {
    try {
      const res = await getUser<OptionsMasterType[]>('')
      optionsMaster.value = res ?? []
    }
    catch (error) {

    }
  }
}
function renderLabel(option: any) {
  const userName = option.label as string
  const temp: string[] = []
  const child: VNode[] = []

  const searchVal = unref(searchText)
  const index = userName.indexOf(searchVal)

  temp.push(userName.substring(0, index), userName.substring(index, index + searchVal.length), userName.substring(index + searchVal.length))

  temp.forEach((i) => {
    child.push(h('span', {
      class: { color: i === searchVal ? 'text-primary' : 'text-#333' },
    }, i))
  })

  return h('div', {
    style: {
      display: 'flex',

    },
  },
  [
    h('div', null, child),
    h('div',
      {
        style: {
          color: '#999',
        },
      }, `（${option.organName}）`),
  ])
}
function renderTag({ option }: any) {
  return h(
    'div',
    null,
    { default: () => option.label },
  )
}
</script>

<template>
  <div class="">
    <BasicModal
      v-bind="$attrs"
      title="转让主管理员身份"
      :width="560"
      :footer-offset="14"
      ok-text="转让"
      :min-height="86"
      @register="register"
      @ok="handleConfirm"
      @visible-change="visibleChange"
    >
      <n-form
        ref="formRef"
        label-placement="left"
        class="my-54px ml-56px mr-75px"
        require-mark-placement="left"
        :model="formValue"
        :rules="rules"
        :label-width="110"
      >
        <n-form-item path="newId" label="新主管理员">
          <n-select
            v-model:value="formValue.newId"
            style="width:320px"
            placeholder="请输入本系统用户姓名或手机号"
            :options="optionsMaster"
            :loading="loadingMaster"
            clearable
            remote
            filterable
            :render-label="renderLabel"
            :render-tag="renderTag"
            @search="handleSearch"
            @update:show="onUpdateShow"
          />
        </n-form-item>
      </n-form>
    </BasicModal>
  </div>
</template>

<style scoped lang="scss">

</style>
