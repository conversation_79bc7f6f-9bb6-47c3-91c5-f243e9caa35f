<script lang='ts' setup>
import type { FormInst } from 'wowjoy-vui'
import { BasicModal, useModalInner } from '@/components/Modal'
import { checkRoleNameRoleApi, createRoleApi, updateRoleApi } from '@/api/role'
import type { RoleListData } from '@/api/role'

export interface RoleInfo extends RoleListData {

}

interface Props {
  roleInfo: RoleInfo
}

interface FormValue {
  id: string
  des: string
  name: string
  remark?: string
  roleMenuList?: string[]
  sort: number | null
  status: number | null
  updateTime: string
  roleUserCount: string
  createTime: string

}
const props = withDefaults(defineProps<Props>(), {
  roleInfo: () => ({
    id: '',
    des: '',
    name: '',
    remark: '',
    roleMenuList: [],
    sort: null,
    status: null,
    updateTime: '',
    createTime: '',
    roleUserCount: '',
  }),
})
const emit = defineEmits(['refresh', 'register'])
const formRef = ref<FormInst | null>(null)

const defaultFormValue = {
  id: '',
  des: '',
  name: '',
  remark: '',
  roleMenuList: [],
  sort: null,
  status: null,
  updateTime: '',
  roleUserCount: '',
  createTime: '',
}

const formValue = ref<FormValue>({
  ...defaultFormValue,
})
const rules = {
  name: {
    required: true,
    trigger: ['blur'],
    message: '请输入角色名称',
  },

}
const [register, { closeModal }] = useModalInner()

function resetFormValue() {
  formValue.value = {
    id: '',
    des: '',
    name: '',
    remark: '',
    roleMenuList: [],
    sort: null,
    status: null,
    updateTime: '',
    roleUserCount: '',
    createTime: '',
  }
}
const modelTitle = ref<'编辑角色' | '新增角色'>('新增角色')
/**
 * 弹窗打开/关闭 回调
 * @param show 弹窗打开/关闭
 */
function visibleChange(show: boolean) {
  if (show) {
    nextTick(() => {
      if (props.roleInfo?.id) {
        modelTitle.value = '编辑角色'

        formValue.value = { ...props.roleInfo }
      }
      else {
        formValue.value = { ...defaultFormValue }
        modelTitle.value = '新增角色'
      }
    })
  }
}
/**
 * 弹窗确认
 */
function handleConfirm() {
  formRef.value?.validate(async (errors) => {
    if (!errors) {
      const params = {
        ...unref(formValue.value),

      }
      const nameRepeat = await checkRoleNameRole({ id: params?.id, value: params.name })

      if (!nameRepeat) {
        if (params?.id)
          editRole(params)

        else
          createRole(params)
      }
    }
    else {
      console.log(errors)
    }
  })
}
/**
 * 角色名称重复
 * @param params  校验角色名称重复参数
 */
async function checkRoleNameRole(params: { id?: string; value: string }) {
  try {
    const res = await checkRoleNameRoleApi(params)
    if (res?.data)
      window.$message.error('角色名称重复！')

    return res.data
  }
  catch (error) {
    console.log(error)
  }
}

/**
 * 新增角色
 * @param params 新增角色参数
 */
async function createRole(params: FormValue) {
  try {
    const res = await createRoleApi(params)
    if (res.data) {
      window.$message.success('保存成功')
      closeModal()
      resetFormValue()
      emit('refresh', { start: 1 })
    }
  }
  catch (error) {
    console.log(error)
  }
}

async function editRole(params: FormValue) {
  try {
    const res = await updateRoleApi(params)
    if (res.data) {
      window.$message.success('保存成功')
      closeModal()
      resetFormValue()
      emit('refresh')
    }
  }
  catch (error) {
    console.log(error)
  }
}
/** 弹窗取消 */
function handleCancel() {
  resetFormValue()
}
</script>

<template>
  <BasicModal
    v-bind="$attrs"
    :title="modelTitle"
    :width="560"
    :footer-offset="144"
    @register="register"
    @ok="handleConfirm"
    @cancel="handleCancel"
    @visible-change="visibleChange"
  >
    <n-form
      ref="formRef"
      label-placement="left"
      class="mb-4px ml-18px mr-30px mt-24px"
      require-mark-placement="left"
      :model="formValue"
      :rules="rules"
      :label-width="82"
    >
      <n-form-item path="name" label="角色名称">
        <n-input
          v-model:value="formValue.name"
          placeholder="请输入"
          :maxlength="10"
          style="width:340px;"
          show-count
        />
      </n-form-item>
      <n-form-item path="des" label="角色描述">
        <n-input
          v-model:value="formValue.des"
          placeholder="请输入"
          :maxlength="20"
          style="width:340px;"
          show-count
          type="textarea"
          :rows="2"
        />
      </n-form-item>
    </n-form>
  </BasicModal>
</template>

<style scoped lang="scss">

</style>
