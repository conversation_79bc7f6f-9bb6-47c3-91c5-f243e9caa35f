<script lang='ts' setup>
import type { DataTableSortState } from 'wowjoy-vui'
import { NSwitch } from 'wowjoy-vui'
import dayjs from 'dayjs'
import RoleModal from './RoleModal.vue'
import TransferMasterModal from './TransferMasterModal.vue'
import VerifyMasterModal from './VerifyMasterModal.vue'
import { Breadcrumb } from '@/layouts/common'
import { ButtonSearch } from '@/components/Search'
import { PageTitle } from '@/components/Title'
import { BasicTable, RowAction } from '@/components/Table'
import type { FetchReload } from '@/components/Table'
import { deleteRoleApi, getRoleListApi, getRoleMemberApi, updateStatusApi } from '@/api/role'
import type { MemberListData, Results, RoleListData, RoleListParams } from '@/api/role'
import { useModal } from '@/components/Modal'
import { isMasterAdmin, localStg } from '@/utils'
import { usePermission, useRouterPush } from '@/hooks'
import { SvgIcon } from '@/components/Icon'

const { routerPush } = useRouterPush()
const { hasPermission } = usePermission()
const userInfo = localStg.get('userInfo')
const roleList = userInfo?.roleList || []

const userIsMaster = roleList.some(role => isMasterAdmin(role?.remark))

const tableRef = ref<InstanceType<typeof BasicTable> | null>(null)
const tableParams = reactive<RoleListParams>({
  searchName: '',
  size: 10,
  start: 1,
})
const defaultRoleInfo = {
  id: '',
  des: '',
  name: '',
  remark: '',
  roleMenuList: [],
  sort: null,
  status: null,
  updateTime: '',
  createTime: '',
  roleUserCount: '',
}
/** 当前编辑的角色 */
const roleInfo = ref<RoleListData>({
  ...defaultRoleInfo,
})

// 时间列涉及到异步排序，抽离处理特殊处理
const columnUpdate = ref({
  title: '创建时间',
  key: 'updateTime',
  sorter: true,
  sortOrder: 'descend',
  width: 110,
  render(row: RoleListData) {
    const { updateTime } = row
    return formatTime(updateTime)
  },

})
/** 0 正常，1禁用 */
enum ROLE_STATUS {
  ENABLE = 0,
  DISABLE = 1,
}
const columns = createColumns()
const actionColumns = hasPermission('system:config:role:operate') ? createActionColumns() : null
// 分页器
const paginationReactive = reactive({
  page: 1,
  pageSize: 10,
  pageSizes: [10, 20, 50, 100],
})

/** 创建表格列 */
function createColumns() {
  return [
    {
      title: '序号',
      width: 80,
      render(_: RoleListData, index: number) {
        return index + 1
      },

    },
    {
      title: '角色名称',
      key: 'roleName',
      // 角色名称最多限制十字
      width: 190,
      render(row: RoleListData) {
        const { name = '', remark = '' } = row
        return h(
          'div',
          {
            class: 'flex items-center',
          },
          [
            h('span', { class: 'mr-6px' }, { default: () => name }),
            isMasterAdmin(remark) && h(SvgIcon, {
              localIcon: 'slmc-icon-zhuguanliyuan1',
              class: 'text-20px',
            }),

          ],
        )
      },
    },
    {
      title: '描述',
      key: 'des',
      // 多限制二十个字
      width: 330,

    },
    {
      title: '成员数量',
      key: 'roleUserCount',
      width: 100,

    },
    columnUpdate.value,
    {
      title: '是否启用',
      key: 'status',
      width: 120,
      render(row: RoleListData) {
        const { status, id, remark } = row

        const active = status === ROLE_STATUS.ENABLE
        const changeStatus = status === ROLE_STATUS.ENABLE ? 1 : 0
        if (isMasterAdmin(remark)) {
          return '-'
        }
        else {
          return hasPermission('system:config:role:operate')
            ? h(NSwitch, { value: active, onUpdateValue: () => onChangeActive(changeStatus, id) })
            : '-'
        }
      },
    },

  ]
}
function createActionColumns() {
  return {
    title: '操作',
    key: 'address',
    width: 310,
    fixed: 'right',
    render(row: RoleListData) {
      const { id = '', roleUserCount, remark } = row
      return h(RowAction, {
        actions: [
          {
            label: '查看',
            onClick: () => handleLook(id),
            type: 'primary',
            text: true,
            ifShow: showLook(remark),
          },
          {
            label: '转让',
            onClick: () => handleTransfer(row),
            type: 'primary',
            text: true,
            ifShow: showTransfer(remark),
          },
          {
            label: '成员管理',
            onClick: () => handleMember(id),
            type: 'primary',
            text: true,
            ifShow: showMember(remark),
          },
          {
            label: '权限设置',
            onClick: () => handlePermission(id),
            type: 'primary',
            text: true,
            ifShow: showPermission(remark),
          },
          {
            label: '编辑',
            onClick: () => handleEdit(row),
            type: 'primary',
            text: true,
            ifShow: showEdit(remark),
          },
          {
            label: '删除',
            onClick: () => handleDelete(id, roleUserCount),
            type: 'primary',
            text: true,
            ifShow: showDelete(remark),
          },
        ],
      })
    },
  }
}
/**
 * 格式化时间
 * @param time 世间
 */
function formatTime(time: string) {
  return dayjs(time).format('YYYY-MM-DD')
}

/**
 * 表格排序
 * @param sorter 排序字段
 */
function handleSorterChange(sorter: DataTableSortState) {
  if (!sorter || sorter.columnKey === 'updateTime') {
    columnUpdate.value.sortOrder = !sorter ? false : sorter.order as any

    const sort = sorter.order === 'ascend' ? 'asc' : undefined
    // 默认是倒序 sort 字段有值 就是正序排了
    tableRef.value?.fetch({ start: 1, sort })
  }
}
/** 是否有查询权限 */
function hasSearchPermission() {
  return hasPermission('system:config:role:search')
}
// 加载表格数据
async function loadDataTable(res: { size: number; start: number }) {
  if (!hasSearchPermission())
    return
  return getRoleListApi({ ...tableParams, ...res })
}
/**
 * 通过角色名称搜索
 * @param query 角色名称
 */
function handleSearch(query?: string) {
  if (!hasSearchPermission())
    return
  tableParams.searchName = query
  tableRef.value?.fetch({ start: 1 })
}
/**
 * 切换角色状态弹框
 * @param changeStatus 角色当前状态
 * @param id 角色id
 */
function onChangeActive(changeStatus: number, id: string) {
  const title = `确定${changeStatus === ROLE_STATUS.ENABLE ? '启用' : '禁用'}该角色吗？`
  const content = changeStatus === ROLE_STATUS.ENABLE ? '' : '禁用后，该角色下所有成员将无该角色权限'
  window.$dialog.warning({
    title,
    content,
    positiveText: '确定',
    negativeText: '取消',
    positiveButtonProps: {
      type: 'primary',
    },
    onPositiveClick: () => {
      try {
        changeActive(changeStatus, id)
      }
      catch (error) {
        console.log(error)
      }
    },
  })
}
/**
 * 切换角色状态函数
 * @param changeStatus 角色当前状态
 * @param id 角色id
 */
async function changeActive(changeStatus: number, id: string) {
  const params = {
    idList: [id],
    status: changeStatus,
  }
  try {
    const res = await updateStatusApi(params)
    if (res?.data) {
      const successTest = changeStatus === ROLE_STATUS.ENABLE ? '启用成功' : '禁用成功'
      window.$message.success(successTest)
      onRefresh()
    }
  }
  catch (error) {
    console.log(error)
  }
}
// 刷新表格
function onRefresh(params?: FetchReload) {
  tableRef.value?.reload(params)
}
/**
 * 删除角色弹窗
 * @param id 角色id
 */
function handleDelete(id: string, roleUserCount: string) {
  window.$dialog.warning({
    title: '确定删除该角色吗？',
    positiveText: '确定',
    negativeText: '取消',
    btnGhost: true,
    onPositiveClick: async () => {
      try {
        onDelete(id, roleUserCount)
      }
      catch (error) {
        // window.$message.error('删除失败')
        console.log(error)
      }
    },
  })
}
/**
 * 删除角色
 * @param id 角色id
 */
async function onDelete(id: string, roleUserCount: string) {
  if (roleUserCount && !Number.isNaN(Number(roleUserCount)) && Number(roleUserCount) > 0) {
    window.$message.warning('当前角色有成员，无法删除')
    return
  }

  const res = await deleteRoleApi(id)
  if (res?.data) {
    window.$message.success('删除成功')
    onRefresh({ isRemove: true })
  }
}
/**
 * 是否显示删除按钮
 * @param remark 角色备注
 */
function showDelete(remark?: string) {
  if (!isMasterAdmin(remark))
    return true

  else
    return false
}
/** 跳转权限设置 */
function handlePermission(roleId: string) {
  routerPush({ name: 'system_permissionEdit', query: { roleId } })
}
/**
 * 是否显示权限设置按钮
 * @param remark 角色备注
 */
function showPermission(remark?: string) {
  if (!isMasterAdmin(remark))
    return true

  else
    return false
}
/** 跳转成员管理 */
function handleMember(roleId: string) {
  // 成员管理
  routerPush({
    name: 'system_memberManage',
    query: {
      roleId,
    },
  })
}
/**
 * 是否显示成员管理
 * @param remark 角色备注
 */
function showMember(remark?: string) {
  if (!isMasterAdmin(remark))
    return true

  else
    return false
}
/** 跳转主管理员查看 */
function handleLook(roleId: string) {
  // 权限设置
  routerPush({ name: 'system_permissionLook', query: { roleId } })
}
/**
 * 是否显示查看按钮
 * @param remark 当前行备注
 */
function showLook(remark?: string) {
  if (isMasterAdmin(remark))
    return true

  else
    return false
}
/**
 * 是否显示转让按钮
 * @param remark 当前行备注
 */
function showTransfer(remark?: string) {
  if (isMasterAdmin(remark) && userIsMaster)
    return true

  else
    return false
}

const [register, { openModal }] = useModal()
const roleModalRef = ref('')
/** 打开角色弹窗 */
function showCreateModal() {
  roleInfo.value = { ...defaultRoleInfo }
  openModal()
}
/** 编辑角色 */
function handleEdit(row: RoleListData) {
  // 编辑
  roleInfo.value = { ...row }
  openModal()
}

/**
 * 是否显示编辑按钮
 * @param remark 当前行备注
 */
function showEdit(remark?: string) {
  if (!isMasterAdmin(remark))
    return true

  else
    return false
}

// 转让的弹窗
const [registerTransfer, { openModal: openModalTransfer }] = useModal()

// 验证的弹窗
const [registerVerify, { openModal: openModalVerify }] = useModal()

const masterInfo = ref({
  name: '',
  phone: '',
  id: '',
})

/**
 * 获取主管理的用户信息
 * @param roleId 角色id
 */
async function getUserByRoleId(roleId: string) {
  try {
    const { data } = await getRoleMemberApi<Results<MemberListData[]>>({ roleId, size: 10, start: 1 })

    if (data?.records && data.records.length > 0)

      return data.records[0]
  }
  catch (error) {
    console.log(error)
  }
}

/**
 * 打开验证弹窗，进入转让流程
 * @param row 当前行角色信息
 */
async function handleTransfer(row: RoleListData) {
  const userData = await getUserByRoleId(row.id)

  if (userData) {
    masterInfo.value = {
      name: userData.userName,
      phone: userData.phone,
      id: userData.id,
    }

    openModalVerify()
  }
}

const transferInfo = ref({
  code: '',
  id: '',
})

let timer: any = null
function onVerifySuccess({ code, id }: { code: string; id: string }) {
  transferInfo.value.code = code
  transferInfo.value.id = id
  timer = setTimeout(() => {
    openModalTransfer()

    clearTimeout(timer)
  }, 500)
}
function onTransferSuccess() {
  tableRef.value?.reload()
}
onUnmounted(() => {
  clearTimeout(timer)
})
</script>

<template>
  <div>
    <Breadcrumb route-name="system_roleManage" />
    <PageCard breadcrumb>
      <PageTitle class="mb-14px">
        角色管理
      </PageTitle>
      <div class="flex justify-between">
        <ButtonSearch
          placeholder="请输入角色名称搜索"
          class="mb-14px"
          width="400px"
          @emit-search="handleSearch"
        />
        <n-button v-permission="'system:config:role:add'" type="primary" @click="showCreateModal">
          新增角色
        </n-button>
      </div>
      <BasicTable
        ref="tableRef"

        class="basicTable"
        :columns="columns"
        :action-column="actionColumns"
        :request="loadDataTable"
        :row-key="(row:any) => row.id"
        :pagination="paginationReactive"
        :scroll-x="1300"
        striped
        @update:sorter="handleSorterChange"
      />
      <RoleModal ref="roleModalRef" :role-info="roleInfo" @register="register" @refresh="onRefresh" />
      <VerifyMasterModal :row="masterInfo" @register="registerVerify" @verify-success="onVerifySuccess" />
      <TransferMasterModal :transfer-info="transferInfo" @register="registerTransfer" @transfer-success="onTransferSuccess" />
    </PageCard>
  </div>
</template>

<style scoped lang="scss">
.basicTable {
    :deep(.n-data-table-td.n-data-table-td--hover){
        background-color: white;

    }
    :deep(.n-data-table .n-data-table-th.n-data-table-th--hover ){
        background-color: #E7F9F7;
    }
    :deep(.n-data-table-tr.n-data-table-tr--striped .n-data-table-td){
        background-color: #f5f5f5;
    }
}
</style>
