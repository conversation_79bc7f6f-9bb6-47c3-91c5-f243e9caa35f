<script lang='ts' setup>
import { computed, onMounted, ref } from 'vue'
import { useWindowSize } from '@vueuse/core'
import { BasicTable } from '@/components/Table'
import type { FormSchema } from '@/components/Form'
import { BasicForm } from '@/components/Form'
import { useDate } from '@/hooks/date/useDate'
import { Breadcrumb } from '@/layouts/common'
import { getAppNameApi, getEventTypesApi, getLogListApi, getOperatorApi } from '@/api/system'

interface FormParams {
  rangePickerDate: number[]
  systemId: number
  userId: number
  eventType: string
  size: number
  start: number
}
interface Option {
  label: string
  value: string
}

const appNameOptions = ref<Option[]>([])
const operatorOption = ref<Option[]>([])
const eventTypeOption = ref<Option[]>([])

// 获取应用名称
async function getAppName() {
  try {
    const res = await getAppNameApi()

    if (res?.data) {
      for (const [key, value] of Object.entries(res?.data))
        appNameOptions.value.push({ label: value, value: key })
    }
  }
  catch (error) {
    console.log(error)
  }
}

// 获取操作者
async function getOperatorName() {
  try {
    const res = await getOperatorApi()

    if (res?.data) {
      for (const [key, value] of Object.entries(res?.data))
        operatorOption.value.push({ label: value, value: key })
    }
  }
  catch (error) {
    console.log(error)
  }
}
// 获取事件类型
async function getEventType() {
  try {
    const res = await getEventTypesApi<string[]>()

    if (res?.data) {
      res.data?.forEach((item) => {
        eventTypeOption.value.push({ label: item, value: item })
      })
    }
  }
  catch (error) {
    console.log(error)
  }
}

const { currentDate } = useDate()
const schemas = ref<FormSchema[]>([
  {
    field: 'rangePickerDate',
    component: 'NRangePicker',
    label: '时间',
    width: 263,
    defaultValue: [currentDate.startDayDate, currentDate.endDayDate],
  },
  {
    field: 'systemId',
    component: 'NSelect',
    label: '应用名称',
    width: 263,
    componentProps: {
      options: unref(appNameOptions),
    },
  },
  {
    field: 'userId',
    component: 'NSelect',
    label: '操作者',
    width: 263,
    componentProps: {
      options: unref(operatorOption),
    },
  },
  {
    field: 'eventType',
    component: 'NSelect',
    label: '事件类型',
    width: 263,
    componentProps: {
      options: unref(eventTypeOption),

    },

  },

])

// 表格列初始化
function createColumns() {
  return [
    {
      title: '时间',
      key: 'createTime',
      width: 170,
    },
    {
      title: '操作者',
      key: 'userName',
      width: 150,
    },
    {
      title: '应用名称',
      key: 'systemName',
      width: 150,
    },
    {
      title: '事件类型',
      key: 'eventType',
      width: 150,
    },
    {
      title: '详细数据',
      key: 'text',
    },
  ]
}

const columns = createColumns()

// 默认从参数
const defaultTableParams = reactive({
  size: 10,
  start: 1,
  begin: `${currentDate.startDayDate}`,
  end: `${currentDate.endDayDate}`,
  systemId: 0,
  userId: 0,
  eventType: '',

})

let tableParams = reactive({
  size: 10,
  start: 1,
  begin: `${currentDate.startDayDate}`,
  end: `${currentDate.endDayDate}`,
  systemId: 0,
  userId: 0,
  eventType: '',

})
// 分页器
const paginationReactive = reactive({
  page: 1,
  pageSize: 10,
  pageSizes: [10, 20, 50, 100],
})
const tableRef = ref<InstanceType<typeof BasicTable> | null>(null)

// 加载表格数据
async function loadDataTable(res: { size: number; start: number }) {
  return await getLogListApi({ ...tableParams, ...res })
}
function handleSubmit(params: FormParams) {
  const { rangePickerDate, ...payload } = params
  tableParams = { ...payload, begin: `${rangePickerDate[0]}`, end: `${rangePickerDate[1]}` }
  tableRef.value?.fetch({ start: 1 })
}

function handleReset() {
  tableParams = { ...defaultTableParams }
}
function init() {
  // 应用名称
  getAppName()
  // 操作者
  getOperatorName()
  // 事件类型
  getEventType()
}

onMounted(() => {
  init()
})
const { width } = useWindowSize()

const computedCols = computed(() => {
  if (unref(width) <= 1280)
    return 3
  return 5
})
</script>

<template>
  <div>
    <Breadcrumb route-name="system_log" />
    <PageCard breadcrumb>
      <PageTitle class="mb-14px">
        系统日志
      </PageTitle>
      <BasicForm
        :schemas="schemas" :grid-props="{ cols: computedCols, xGap: '20', yGap: '0' }"
        label-width="auto" is-inline
        @submit="handleSubmit" @reset="handleReset"
      />
      <BasicTable
        ref="tableRef"

        :columns="columns"
        :request="loadDataTable"
        :row-key="(row:any) => row.id"
        :pagination="paginationReactive"
        :scroll-x="1000"
        striped
      />
    </PageCard>
  </div>
</template>

<style scoped lang="scss">

</style>
