<script lang='ts' setup>
import type { FormInst } from 'wowjoy-vui'
import { useMessage } from 'wowjoy-vui'
import { nextTick } from 'vue'
import { useDebounceFn } from '@vueuse/core'
import type { OrganizationRes } from '@/api/organization/type'
import { checkPhone<PERSON><PERSON>, createUser<PERSON>pi, editUser<PERSON><PERSON> } from '@/api/user/user'
import type { CreateUserParams } from '@/api/user/type'
import { BasicModal, useModalInner } from '@/components/Modal'
import { flattenChildren, isPhone } from '@/utils/common'
import { localStg } from '@/utils'
import { getDoctorGroupListApi } from '@/api/doctor'
import type { DoctorGroupRecord } from '@/api/doctor'
import { useAuthStore } from '@/store'

interface FormValue {
  id: string
  userName: string
  phone: string
  userNumber: string
  name: string
  organId: string
  title: string
  job: string
  userType: string // 用户类型 0主管理员 1子管理员 2普通用户
  organName: string
  category: null | string
  medicalTeamId: null | string
  medicalTeamName: string
}

const props = defineProps({
  organ: {
    type: Object as PropType<OrganizationRes | null>,
    default: () => {},
  },
  rowData: {
    type: Object as PropType<any>,
    default: () => {},
  },
  isEdit: {
    type: Boolean,
    default: false,
  },

})

const emit = defineEmits(['refresh', 'register'])

const message = useMessage()

const { userInfo } = useAuthStore()

const organList = flattenChildren(userInfo.organList)
const organId = userInfo.organId

const editor = computed(() => props.isEdit)

const formRef = ref<FormInst | null>(null)
const rules = {
  userName: {
    required: true,
    trigger: ['blur'],
    message: '请输入用户姓名',
  },
  phone: {
    required: true,
    trigger: ['blur'],
    validator(_, value: string) {
      if (!value)
        return new Error('请输入手机号')

      else if (!isPhone(value))
        return new Error('请输入合法的手机号')

      return true
    },
  },
  name: {
    required: true,
    trigger: ['blur'],
    message: '请输入用户名',
  },
  organName: {
    required: true,
    trigger: ['blur'],
    message: '请输入用户名',
  },
  organId: {
    required: true,
    trigger: ['blur'],
    message: '请输入所属机构',
  },
  category: {
    required: true,
    trigger: ['blur'],
    message: '请输入类别',
  },

}
const formValue = ref<FormValue>({
  id: '',
  userName: '',
  phone: '',
  userNumber: '',
  name: '',
  organId: '',
  title: '',
  job: '',
  userType: '2', // 用户类型 0主管理员 1子管理员 2普通用户
  organName: '',
  category: null,
  medicalTeamId: null,
  medicalTeamName: '',
})

const [register, { closeModal }] = useModalInner()

function resetFormValue() {
  formValue.value = {
    id: '',
    userName: '',
    phone: '',
    userNumber: '',
    name: '',
    organId: '',
    title: '',
    job: '',
    userType: '2', // 用户类型 0主管理员 1子管理员 2普通用户
    organName: '',
    category: null,
    medicalTeamId: null,
    medicalTeamName: '',
  }
}

const categoryOptions = [
  { label: '医生', value: '医生' },
  { label: '护士', value: '护士' },
  { label: '其它', value: '其它' },
]
const medicalTeamOptions = ref<DoctorGroupRecord[]>([])
// 手机号重复校验
async function checkPhone(params: { value: string }) {
  try {
    const res = await checkPhoneApi(params)
    if (res) {
      return new Promise((resolve) => {
        resolve(res?.data)
      })
    }
  }
  catch (error) {
    console.log(error)
  }
}
// 搜索做防抖
const getMedicalTeam = useDebounceFn(async (name = '') => {
  const res = await getDoctorGroupListApi<DoctorGroupRecord[]>({ name, organId })
  if (res.data) {
    if (!name) {
      const defaultArray = [{ doctorGroupName: '全部', doctorGroupId: '' }] as DoctorGroupRecord[]

      medicalTeamOptions.value = defaultArray.concat(res.data)
    }
    else {
      medicalTeamOptions.value = res.data
    }
  }
}, 300)
/**
 * 查找患者姓名
 * @param query 搜索key
 */
function handleSearchMedicalTeam(query: string) {
  getMedicalTeam(query)
}
/**
 * 患者名字打开/关闭回调
 * @param show 下拉打开/关闭
 */
function handleUpdateShowMedicalTeam(show: boolean) {
  if (show)
    getMedicalTeam('')
}
function handleUpdateMedicalTeam(value: string, option: DoctorGroupRecord) {
  formValue.value.medicalTeamId = value
  formValue.value.medicalTeamName = option.doctorGroupName
}

// 新增用户
async function createUser(params: CreateUserParams) {
  try {
    const checkPhoneRes = await checkPhone({ value: params.phone })

    if (checkPhoneRes) {
      message.error('手机号重复')
      return
    }

    const res = await createUserApi(params)

    if (res.data) {
      message.success('保存成功')
      closeModal()
      resetFormValue()
      emit('refresh')
    }
  }
  catch (error) {
    console.log(error)
  }
}

// 编辑用户
async function editUser(params: any) {
  try {
    const res = await editUserApi(params)
    if (res.data) {
      message.success('保存成功')
      closeModal()
      resetFormValue()
      emit('refresh')
    }
  }
  catch (error) {
    console.log(error)
  }
}
function handleConfirm() {
  formRef.value?.validate((errors) => {
    if (!errors) {
      const userInfo = localStg.get('userInfo')

      const createBy: string = userInfo?.id ?? ''
      const params = {
        ...unref(formValue.value),
        medicalTeam: formValue.value.medicalTeamName,
        createBy,
        updateType: '0',
        status: 0, // （0正常 1停用）
      }

      if (params?.id)
        editUser(params)

      else
        createUser(params)
    }
    else {
      console.log(errors)
    }
  })
}

function handleCancel() {
  resetFormValue()
}

// 手机号输入事件
function handlePhoneBlur() {
  if (isPhone(formValue.value.phone))
    formValue.value.name = formValue.value.phone
}

const userTypeName = ref('普通用户')
const modelTitle = ref('')

function visibleChange(show: boolean) {
  const USER_TYPE: Record<string, string> = {
    0: '主管理员',
    1: '子管理员',
    2: '普通用户',
  }
  if (show) {
    nextTick(() => {
      if (props.isEdit) {
        modelTitle.value = '编辑用户'
        const {
          userName, phone, userNumber, name, organId, title, job, userType, organName, id, category,
          medicalTeam,
        } = props.rowData

        formValue.value = {
          userName,
          phone,
          userNumber,
          name,
          organId,
          title,
          job,
          userType,
          organName,
          id,
          category,
          medicalTeamId: medicalTeam,
          medicalTeamName: medicalTeam,
        }
        userTypeName.value = USER_TYPE[userType]
      }
      else {
        const { id = '', organName = '' } = props.organ as OrganizationRes
        formValue.value.organName = organName
        formValue.value.organId = id
        modelTitle.value = '新增用户'
      }
    })
  }
}

function init() {
  getMedicalTeam('')
}
onMounted(() => {
  init()
})
</script>

<template>
  <div>
    <BasicModal
      v-bind="$attrs"
      :title="modelTitle"
      :width="720"
      @register="register"
      @ok="handleConfirm"
      @cancel="handleCancel"
      @visible-change="visibleChange"
    >
      <n-form
        ref="formRef"
        label-placement="left"
        class="my-24px ml-17px mr-30px"
        require-mark-placement="left"
        :model="formValue"
        :rules="rules"
        :label-width="100"
      >
        <n-grid x-gap="12" :cols="2">
          <n-form-item-gi v-show="isEdit" path="userNumber" label="用户ID">
            <n-input
              v-model:value="formValue.userNumber"
              placeholder="请输入"
              :maxlength="50"
              disabled
              style="width:340px;"
            />
          </n-form-item-gi>
          <n-form-item-gi path="userName" label="用户姓名">
            <n-input
              v-model:value="formValue.userName"
              placeholder="请输入"
              :maxlength="50"
              style="width:340px;"
            />
          </n-form-item-gi>
          <n-form-item-gi v-if="!editor" path="phone" label="手机号">
            <n-input
              v-model:value="formValue.phone"
              placeholder="请输入"
              :maxlength="11"
              style="width:340px;"
              @blur="handlePhoneBlur"
            />
          </n-form-item-gi>
          <n-form-item-gi v-if="editor" path="phone" label="手机号">
            <n-input
              v-model:value="formValue.phone"
              placeholder="请输入"
              :maxlength="11"
              style="width:340px;"
              disabled
              @blur="handlePhoneBlur"
            />
          </n-form-item-gi>
          <n-form-item-gi path="name" label="用户名">
            <n-input
              v-model:value="formValue.name"
              placeholder="根据手机号自动获取"
              :maxlength="50"
              style="width:340px;"
              disabled
            />
          </n-form-item-gi>
          <n-form-item-gi v-if="!editor" path="organName" label="所属机构">
            <n-input
              v-model:value="formValue.organName"
              placeholder="请输入"
              :maxlength="50"
              style="width:340px;"
              disabled
            />
          </n-form-item-gi>
          <n-form-item-gi v-if="editor" path="organName" label="所属机构">
            <n-select
              v-model:value="formValue.organId"
              placeholder="请输入"
              :maxlength="50"
              value-field="id"
              label-field="organName"
              style="width:340px;"
              :options="organList"
            />
          </n-form-item-gi>
          <n-form-item-gi path="title" label="职称">
            <n-input
              v-model:value="formValue.title"
              placeholder="请输入"
              :maxlength="50"
              style="width:340px;"
            />
          </n-form-item-gi>
          <n-form-item-gi path="job" label="职务">
            <n-input
              v-model:value="formValue.job"
              placeholder="请输入"
              :maxlength="50"
              style="width:340px;"
            />
          </n-form-item-gi>
          <n-form-item-gi path="category" label="类别">
            <n-select
              v-model:value="formValue.category"
              placeholder="请选择"

              style="width:340px;"
              :options="categoryOptions"
            />
          </n-form-item-gi>
          <n-form-item-gi path="medicalTeam" label="所在医疗组">
            <n-select
              v-model:value="formValue.medicalTeamId"
              placeholder="请选择"
              style="width:340px;"
              :options="medicalTeamOptions"
              filterable
              clearable
              remote
              label-field="doctorGroupName"
              value-field="doctorGroupId"
              @search="handleSearchMedicalTeam"
              @update:show="handleUpdateShowMedicalTeam"
              @update:value="handleUpdateMedicalTeam"
            />
          </n-form-item-gi>
          <n-form-item-gi path="userType" label="身份类型">
            <n-input
              v-model:value="userTypeName"
              placeholder="请输入"
              :maxlength="50"
              style="width:340px;"
              disabled
            />
          </n-form-item-gi>
        </n-grid>
      </n-form>
    </BasicModal>
  </div>
</template>

<style scoped lang="scss">

</style>
