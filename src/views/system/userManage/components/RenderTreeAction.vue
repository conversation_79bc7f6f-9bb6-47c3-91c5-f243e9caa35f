<script lang='ts' setup>
const props = defineProps({
  level: {
    type: Number,
  },
  childSize: {
    type: Number,
    default: 0,
  },
})

const emit = defineEmits(['create', 'delete', 'edit'])

const isShowDelete = computed(() => {
  if (props?.level) {
    const level = Number(props.level)
    if (level === 1 || level > 4)
      return false
    else
      return true
  }
  else {
    return false
  }
})
const isShowCreate = computed(() => {
  if (props?.level) {
    const level = Number(props.level)
    if (level > 3)
      return false
    else
      return true
  }
  else {
    return false
  }
})
const isShowEdit = computed(() => {
  if (props?.level) {
    const level = Number(props.level)
    if (level === 1 || level > 4)
      return false
    else
      return true
  }
  else {
    return false
  }
})
function handleCreate() {
  emit('create')
}
function handleDelete() {
  emit('delete')
}
function handleEdit() {
  emit('edit')
}
</script>

<template>
  <div v-permission="'system:config:user:operate'" class="w-100px rounded-3px bg-white text-12px">
    <div v-if="isShowEdit" class="p-8px text-center hover-bg-#FFFBE0">
      <n-button text @click="handleEdit">
        编辑
      </n-button>
    </div>
    <div v-if="isShowCreate" class="p-8px text-center hover-bg-#FFFBE0">
      <n-button text @click="handleCreate">
        新增子部门
      </n-button>
    </div>
    <div v-if="isShowDelete" class="p-8px text-center hover-bg-#FFFBE0">
      <n-button text @click="handleDelete">
        删除
      </n-button>
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>
