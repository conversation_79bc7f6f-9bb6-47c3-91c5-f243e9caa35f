<script lang='ts' setup>
import { userSortApi } from '@/api/user/user'

const props = defineProps({
  id: {
    type: String,
  },
  orderNum: {
    type: Number,
  },

})
const emit = defineEmits(['refresh', 'hiddenEditIcon'])
const popoverShow = ref(false)

const orderNum = ref<null | number>(null)

async function onConfirm() {
  const params = {
    id: props.id,
    orderNum: toRaw(orderNum.value),
  }

  const res = await userSortApi(params)
  if (res?.data) {
    orderNum.value = null
    popoverShow.value = false
    window.$message.success('保存成功')
    emit('refresh')
    emit('hiddenEditIcon')
  }
}

function onCancel() {
  popoverShow.value = false
  orderNum.value = null
  emit('hiddenEditIcon')
}

function onShowPopover() {
  orderNum.value = props.orderNum
  popoverShow.value = true
}
function clickOutside() {
  popoverShow.value = false
  orderNum.value = null
  emit('hiddenEditIcon')
}

function validator(sort: number) {
  const reg = /^\+?[1-9]\d*$/
  return reg.test(`${sort}`)
}
function parseOrderNum(input: string) {
  return input
}
</script>

<template>
  <div class="">
    <n-popover trigger="click" placement="bottom-start" style="left: -16px;" raw :show="popoverShow" @clickoutside="clickOutside">
      <template #trigger>
        <SvgIcon

          local-icon="slmc-icon-edit1"
          size="16"
          style="cursor: pointer;"
          @click="onShowPopover"
        />
      </template>
      <n-alert type="info" :bordered="false">
        修改序号可更改排序
      </n-alert>
      <div class="bg-#fff">
        <div class="px-30px pb-18px pt-24px">
          <n-input-number
            v-model:value="orderNum" placeholder="序号数字"

            clearable :show-button="false" :validator="validator" @parse="parseOrderNum"
          />
        </div>
        <div class="w-300px pb-18px">
          <n-space justify="center">
            <n-button type="primary" @click="onConfirm">
              保&nbsp;&nbsp;存
            </n-button>
            <n-button @click="onCancel">
              取&nbsp;&nbsp;消
            </n-button>
          </n-space>
        </div>
      </div>
    </n-popover>
  </div>
</template>

<style scoped lang="scss">

</style>
