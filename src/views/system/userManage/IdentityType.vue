<script lang='ts' setup>
import { SvgIcon } from '@/components/Icon'

defineProps({
  userType: String,
})
const typeText: Record<string, string> = {
  0: '主管理员',
  1: '子管理员',
  2: '普通用户',
}
</script>

<template>
  <div v-if="userType" class="flex items-center">
    <span class="mr-4px">{{ typeText[userType] }}</span>
    <SvgIcon v-if="userType === '0'" local-icon="slmc-icon-zhuguanliyuan" size="16" />
  </div>
</template>

<style scoped lang="scss">

</style>
