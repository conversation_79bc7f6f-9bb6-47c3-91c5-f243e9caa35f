<script lang='ts' setup>
import Organization from './Organization.vue'
import UserTable from './UserTable.vue'
import { Breadcrumb } from '@/layouts/common'
import type { OrganizationRes } from '@/api/organization/type'

const currentOrgan = ref<OrganizationRes | null>(null)
const shrinkRef = ref(false)

function handleGetOrgan(organ: OrganizationRes) {
  currentOrgan.value = organ
}
function onChangeShrink(shrink: boolean) {
  shrinkRef.value = shrink
}
</script>

<template>
  <div>
    <Breadcrumb route-name="system_userManage" />
    <PageCard breadcrumb padding="0px 0px 0px 0px">
      <div  class="userManage">
        <Organization class="basis-230px" @get-organ="handleGetOrgan" @change-shrink="onChangeShrink" />
        <UserTable :organ="currentOrgan" :shrink="shrinkRef" />
      </div>
    </PageCard>
  </div>
</template>

<style scoped lang="scss">
.userManage {
    display: flex;
    justify-content: flex-start;
    height: 100%;
    width:100%
}
</style>
