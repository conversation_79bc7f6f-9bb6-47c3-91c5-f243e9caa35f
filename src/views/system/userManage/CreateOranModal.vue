<script lang='ts' setup>
import type { FormInst } from 'wowjoy-vui'
import { BasicModal, useModalInner } from '@/components/Modal'
import { createOrganApi, getOrganApi, updateSortApi } from '@/api/organization/organization'
import type { CreateOrganParams, OrganizationRes } from '@/api/organization/type'
import { SvgIcon } from '@/components/Icon'

const props = defineProps({
  parentId: {
    type: String,
  },
  level: {
    type: Number,
    default: 1,
  },
  orderNum: {
    type: Number,
    default: 1,
  },

  organ: {

  },
})

const emit = defineEmits(['refresh', 'register', 'resetState'])

const formRef = ref<FormInst | null>(null)
const rules = {

  organName: {
    required: true,
    trigger: ['blur'],
    message: '请选择部门名称',
  },
  parentId: {
    required: true,
    trigger: ['blur'],
    message: '请选择上级部门',
  },

}
const formValue = ref({
  id: '',
  organName: '',
  parentId: null,
  status: 0, // 0正常 1停用
})

function resetFormValue() {
  formValue.value = {
    id: '',
    parentId: null,
    organName: '',
    status: 0, // 0正常 1停用

  }
}

const [register, { closeModal }] = useModalInner()

const organList = ref([])

function recursionTree(tree: OrganizationRes[]): any[] {
  const currentLevel = props.level
  function recursion(node: OrganizationRes) {
    if ((node.organLevel !== currentLevel - 1) && currentLevel >= 1)
      node.disabled = true

    if (node.organLevel === 3)
      delete node.children
    if (node?.children?.length === 0 || !node?.children) {
      node.prefix = () => h(SvgIcon, {
        localIcon: 'slmc-icon-wenjian1',
        class: 'text-06AEA6 -ml-8px',
        size: '16',
      })
    }

    if (node?.children)
      node.children.forEach(recursion)
  }

  tree.forEach(recursion)

  return tree
}

/** 获取机构列表 */
async function getOrganizations() {
  try {
    const params = {
      size: 1000,
      start: 1,

    }
    const res = await getOrganApi<OrganizationRes[]>(params)
    organList.value = recursionTree(res?.data ?? [])
  }
  catch (error) {
    console.log(error)
  }
}

// 新增机构
async function createOrgan(params: CreateOrganParams) {
  try {
    const res = await createOrganApi(params)

    if (res.data) {
      window.$message.success('保存成功')
      closeModal()
      resetFormValue()
      emit('refresh', { id: '', organName: params.organName })
      emit('resetState')
    }
  }
  catch (error) {
    console.log(error)
  }
}
// 编辑机构
async function editOrgan(params: CreateOrganParams) {
  try {
    const res = await updateSortApi([params])

    if (res.data) {
      window.$message.success('保存成功')
      closeModal()
      resetFormValue()
      emit('refresh', { id: '', organName: params.organName })
      emit('resetState')
    }
  }
  catch (error) {
    console.log(error)
  }
}
const modelTitle = ref<'新增子部门' | '编辑子部门'>('新增子部门')

/**
 * 弹窗的打开/关闭
 */
function visibleChange(show: boolean) {
  if (show) {
    nextTick(() => {
      formValue.value.parentId = props.parentId
      if (props.organ?.id) {
        modelTitle.value = '编辑子部门'

        formValue.value.id = props.organ?.id
        formValue.value.organName = props.organ?.organName
      }
      else {
        modelTitle.value = '新增子部门'
      }
    })

    getOrganizations()
  }
}

function updateParentOrgan(value: string) {
  formValue.value.parentId = value
}
function handleConfirm() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      const params = {
        ...unref(formValue.value),

        organLevel: props?.level ?? 1,
        orderNum: props?.orderNum ?? 1,
      }
      if (props.organ?.id)
        editOrgan(params)

      else
        createOrgan(params)
    }
    else {
      console.log(errors)
    }
  })
}

function handleCancel() {
  resetFormValue()
  emit('resetState')
}

/** 渲染最内层图标 */
function renderIcon() {
  return h(
    SvgIcon,
    {
      localIcon: 'slmc-icon-wenjian1',
      class: 'text-06AEA6',
      size: '16',
    },

  )
}

function renderTag({ option }: any) {
  const isLeaf = !option?.children || option?.children?.length <= 0
  const renderIconName = isLeaf ? 'slmc-icon-wenjian1' : 'slmc-icon-wenjianjiaguanbi'
  return h(
    'div',
    {
      class: 'flex justify-start items-center',
    },
    [h(
      SvgIcon,
      {
        localIcon: renderIconName,
        class: 'text-06AEA6 mr-10px',
        size: '16',
      },

    ),
    h('span', null, { default: () => option.organName }),
    ],
  )
}
const isShowSearchIcon = ref(false)

const iconWidth = computed(() => {
  return isShowSearchIcon.value ? 'auto' : '7px'
})
/*
4 -- > 选择4级 ---> 当前4级机构变成了5级机构  X
4 -- > 选择3级 ---> 直接上级，没毛病，还是4级
4 -- > 选择2级-- -> 当前4级机构下无子机构，变成3级
4 -- > 选择1级-- -> 当前4级机构下无子机构，变成2级

小结: 4级只能选择1、2、3级，——————————4级机构可以变成 3级，2级和还是维持4级
——————————————————————————————————————————————
3 -- > 选择4级 ---> 向下选择，那么这个3级就会变成了5级，并且如果改3级有子机构，现在最多层级变成了6级  X
3 -- > 选择3级-- -> 当前3级机构变成了4级，如果改3级下面还有子机构，那么现在最多层级变成了5级 X
3 -- > 选择2级-- -> 当前3机构还是3级
3 -- > 选择1级-- -> 当前3机构还是2级，3级下面的子机构，4级变成3级

小结： 3级机构只能选择1、2，————————3级机构可以变成2级和维持3级
——————————————————————————————————————————————
2 -- > 选择4级 ---> 向下选择，那么这个2级就会变成了5级，并且如果改2级有子机构，现在最多层级变成了7级  X
2 -- > 选择3级-- -> 向下选择，当前2级机构变成了4级，如果改2级下面还有子机构，那么现在最多层级变成了6级 X
2 -- > 选择2级-- -> 当前2级机构变成了3级, 如果改2级下面还有子机构，那么现在最多层级变成了5级 X
2 -- > 选择1级-- -> 当前2机构还是2级

小结：2级机构只能选择1级————————只能维持2级
*/
</script>

<template>
  <div id="oranModal">
    <BasicModal
      v-bind="$attrs" :title="modelTitle" :width="560" :footer-offset="110" to="#oranModal"
      @register="register" @ok="handleConfirm" @cancel="handleCancel" @visible-change="visibleChange"
    >
      <n-form
        ref="formRef" label-placement="left" class="oranModal my-24px ml-17px mr-30px"
        require-mark-placement="left" :model="formValue" :rules="rules" :label-width="100"
      >
        <n-form-item path="organName" label="部门名称">
          <n-input
            v-model:value="formValue.organName" :maxlength="10" placeholder="请输入" show-count
            style="width:340px;"
          />
        </n-form-item>
        <n-form-item path="parentId" label="上级部门">
          <n-tree-select
            v-model:show="isShowSearchIcon" filterable :options="organList" label-field="organName"
            key-field="id" clearable class="tree" style="width:340px;" :render-tag="renderTag"
            :render-icon="renderIcon" default-expand-all to="#oranModal"
            :value="formValue.parentId"
            @update:value="updateParentOrgan"
          >
            <template v-if="isShowSearchIcon" #arrow>
              <SvgIcon local-icon="slmc-icon-search" size="16" style="color: #999; cursor: pointer;" />
            </template>
          </n-tree-select>
        </n-form-item>
      </n-form>
    </BasicModal>
  </div>
</template>

<style scoped lang="scss">
.oranModal {
    :deep(.n-tree-node-folder-content) {
        margin-top: 0px;
    }

    :deep(.n-tree-node) {
        display: flex;
        align-items: center;
    }

    :deep(.n-base-selection .n-base-suffix .n-base-suffix__arrow) {
        width: v-bind(iconWidth);
    }

   :deep(.n-base-selection .n-base-suffix.n-base-suffix--active .n-base-suffix__arrow){
    transform: unset;
   }
}
</style>
