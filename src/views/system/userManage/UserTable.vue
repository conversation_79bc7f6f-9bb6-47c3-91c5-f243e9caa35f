<script lang='ts' setup>
import { h, unref } from 'vue'
import dayjs from 'dayjs'
import { useMessage } from 'wowjoy-vui'
import CreateUserModal from './CreateUserModal.vue'
import RenderSortEdit from './components/RenderSortEdit.vue'
import type { OrganizationRes } from '@/api/organization/type'
import { PageTitle } from '@/components/Title'
import { ButtonSearch } from '@/components/Search'
import { batchDeleteApi, deleteUserApi, getUserApi, updateUserStatusApi } from '@/api/user/user'
import type { UserPageListRecord, UserPageListRes } from '@/api/user/type'
import { useModal } from '@/components/Modal'
import { BasicTable, RowAction } from '@/components/Table'
import { RenderStatus } from '@/components/business/UiRender'
import { isMasterAdmin, localStg } from '@/utils'
import { usePermission } from '@/hooks'
import type { FetchReload } from '@/components/Table/src/types/table'
import { SvgIcon } from '@/components/Icon'

const props = defineProps({
  organ: {
    type: Object as PropType<OrganizationRes | null>,
    default: () => {},
  },
  shrink: {
    type: Boolean,
    default: false,
  },
})

const { hasPermission } = usePermission()

const tableWidth = computed(() => {
  return props.shrink ? '100%' : 'calc(100% - 280px)'
})
/**  状态 */
const STATE_TYPE = {
  /** 启用 */
  OPEN: 0,
  /** 禁用 */
  CLOSE: 1,
}

const message = useMessage()

const userModalRef = ref('')
const userInfo = localStg.get('userInfo')
const roleList = userInfo?.roleList || []

const loginUserId = userInfo?.id

const defaultTableParams = reactive({
  searchName: '',
  size: 10,
  start: 1,
  userType: '',
  organId: '',
})
let tableParams = reactive({
  searchName: '',
  size: 10,
  start: 1,
  userType: '',
  organId: '',
})

const [register, { openModal }] = useModal()
// 是否编辑
const isEdit = ref(false)
function showModal({ edit }: { edit: boolean }) {
  isEdit.value = edit
  openModal()
}
/** 是否有查询权限 */
function hasSearchPermission() {
  return hasPermission('system:config:user:search')
}
// 加载表格数据
async function loadDataTable(res: { size: number; start: number }) {
  if (!hasSearchPermission())
    return
  return await getUserApi<UserPageListRes>({ ...tableParams, ...res })
}

// 格式化时间
function formatTime(time: string | null) {
  if (!time)
    return '-'
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

function createColumns() {
  return [
    {
      type: 'selection',
    },
    {
      title: '序号',
      key: 'orderNum',
      width: 80,
      render(row: UserPageListRecord) {
        const { orderNum, id } = row
        const showRenderSortEdit = hasPermission('system:config:user:operate')
        let currentEvent = null
        return h('div', { class: 'flex items-center justify-start' }, [
          h('span', { class: 'mr-10px' }, { default: () => orderNum }),
          showRenderSortEdit && h(RenderSortEdit, {
            class: 'renderSortEdit',
            id,
            orderNum,
            onRefresh: () => {
              onRefresh()
            },
            onHiddenEditIcon: () => {
              currentEvent.style.visibility = ''
            },
            onClick: (e) => {
              currentEvent = e.currentTarget
              e.currentTarget.style.visibility = 'visible'
            },
          }),

        ])
      },
    },
    {
      title: '姓名',
      key: 'userName',
      width: 120,
    },
    {
      title: '状态',
      key: 'status',
      width: 120,
      render(row: UserPageListRecord) {
        const { status } = row
        const state: Record<number, { text: string; color: string }> = {
          0: {
            text: '启用',
            color: '#4ACFB1',
          },
          1: {
            text: '禁用',
            color: '#CCCCCC',
          },
        }
        return h(RenderStatus, { text: state[status].text, color: state[status].color })
      },
    },
    {
      title: '手机号',
      key: 'phone',
      width: 140,
    },

    {
      title: '所在医疗组',
      key: 'medicalTeam',
      width: 140,
    },
    {
      title: '角色',
      key: 'userType',
      width: 330,

      render(row: UserPageListRecord) {
        const { userRoleList = [] } = row
        return userRoleList.map(role => role.name).join(';')
      },
    },

    {
      title: '所属机构',
      key: 'organName',
      width: 200,
    },
    // {
    //   title: '创建时间',
    //   key: 'createTime',
    //   render(row: UserPageListRecord) {
    //     const { createTime } = row
    //     return formatTime(createTime)
    //   },
    // },
    {
      title: '最近登录时间',
      key: 'loginTime',
      width: 220,
      render(row: UserPageListRecord) {
        const { loginTime } = row
        return formatTime(loginTime)
      },
    },

  ]
}
// 启用
async function handleEnable(id: string[]) {
  try {
    const res = await updateUserStatusApi({ idList: [...id], status: STATE_TYPE.OPEN })
    if (res?.data) {
      message.success('启用成功')
      onRefresh()
    }
  }
  catch (error) {

  }
}
// 禁用
async function handleDisable(id: string[]) {
  try {
    const res = await updateUserStatusApi({ idList: [...id], status: 1 })
    if (res?.data) {
      message.success('禁用成功')
      onRefresh()
    }
  }
  catch (error) {

  }
}
function handleEnableAction(status: number, id: string) {
  status === STATE_TYPE.OPEN ? handleDisable([id]) : handleEnable([id])
}

// 当前多选的用户id
const checkedRowKeys = ref<string[]>([])
// 当前多选的用户类型
const checkedRowHaveMaster = ref<boolean>(false)
function onCheckedRowKeys(_: string[], rows: UserPageListRecord[]) {
  const userRoleSet = new Set()
  rows.forEach((item) => {
    item?.userRoleList?.forEach((role) => {
      if (isMasterAdmin(role?.remark))
        userRoleSet.add(role.id)
    })
  })

  checkedRowHaveMaster.value = userRoleSet.size >= 1
}
// 批量启用
function onBatchEnable() {
  if (!checkedRowKeys.value.length) {
    message.warning('请选择要启用的用户')
    return
  }
  if (loginUserId && checkedRowKeys.value.includes(loginUserId)) {
    message.error('该操作不能包含当前登录用户！')
    return
  }

  handleEnable([...unref(checkedRowKeys)])
}

// 批量禁用
function onBatchDisable() {
  if (!checkedRowKeys.value.length) {
    message.warning('请选择要禁用的用户')
    return
  }
  // 当前登录用户不能禁用/启用自己
  if (loginUserId && checkedRowKeys.value.includes(loginUserId)) {
    message.error('该操作不能包含当前登录用户！')
    return
  }

  handleDisable([...unref(checkedRowKeys)])
}
async function batchDelete() {
  const params = {
    idList: unref(checkedRowKeys.value),
  }
  const res = await batchDeleteApi(params)
  if (res?.data) {
    message.success('删除成功')
    onRefresh({ isRemove: true })
  }
}
// 批量删除
function onBatchDelete() {
  if (!checkedRowKeys.value.length) {
    message.warning('请选择要删除的用户')
    return
  }
  if (loginUserId && checkedRowKeys.value.includes(loginUserId)) {
    message.error('该操作不能包含当前登录用户！')
    return
  }

  const isHaveMaster = checkedRowHaveMaster.value

  if (isHaveMaster) {
    message.error('主管理员不可被删！')
    return
  }

  window.$dialog?.warning({
    title: '确定删除此用户吗？',
    content: '一旦删除数据不可恢复，请确认',
    positiveText: '确定',
    negativeText: '取消',
    positiveButtonProps: {
      type: 'primary',
    },
    onPositiveClick: () => {
      batchDelete()
    },
  })
}

// 当前编辑的行数据
const rowData = ref({})

// 编辑用户
function handleEdit(row: UserPageListRecord) {
  // 管理员可以编辑一切用户（不区分主、子管理员）
  rowData.value = { ...row }
  showModal({ edit: true })
}
// 删除用户
async function handleDelete(id: string) {
  window.$dialog?.warning({
    title: '确定删除此用户吗？',
    content: '一旦删除数据不可恢复，请确认',
    positiveText: '确定',
    negativeText: '取消',
    positiveButtonProps: {
      type: 'primary',
      ghost: true,
    },
    negativeButtonProps: {
      type: 'primary',
      ghost: true,
    },
    onPositiveClick: async () => {
      const res = await deleteUserApi({ id })
      if (res?.data) {
        message.success('删除成功')
        onRefresh({ isRemove: true })
      }
    },
  })
}
// 是否可以禁用
function showDisabled(userRoleList: unknown[], status: number, id: string) {
  const haveMaterRole = userRoleList.some(role => isMasterAdmin(role?.remark))

  if (haveMaterRole)
    return false
  if (`${loginUserId}` === id)
    return false

  return true
}
// 是否可以删除
function showDelete(userRoleList: unknown[], id: string) {
  const haveMaterRole = userRoleList.some(role => isMasterAdmin(role?.remark))
  if (haveMaterRole)
    return false
  if (`${loginUserId}` === id)
    return false

  return true
}
// 是否显示编辑
function showEdit() {
  return true
}
function createActionColumns() {
  return {
    title: '操作',
    key: 'address',
    width: 160,
    fixed: 'right',
    render(row: UserPageListRecord) {
      const { status, id, userRoleList } = row
      return h(RowAction, {
        actions: [
          {
            label: `${status === STATE_TYPE.OPEN ? '禁用' : '启用'}`,
            onClick: () => handleEnableAction(status, id),
            type: 'primary',
            text: true,
            ifShow: showDisabled(userRoleList, status, id),
          },
          {
            label: '编辑',
            onClick: () => handleEdit(row),
            type: 'primary',
            text: true,
            ifShow: showEdit(),
          },
          {
            label: '删除',
            onClick: () => handleDelete(id),
            type: 'primary',
            text: true,
            ifShow: showDelete(userRoleList, id),
          },
        ],
      })
    },
  }
}
const tableRef = ref<InstanceType<typeof BasicTable> | null>(null)
const columns = createColumns()
const actionColumns = hasPermission('system:config:user:operate') ? createActionColumns() : null
// 分页器
const paginationReactive = reactive({
  page: 1,
  pageSize: 10,
  pageSizes: [10, 20, 50, 100],
})
// 刷新表格
function onRefresh(params?: FetchReload) {
  tableRef.value?.reload(params)
  checkedRowKeys.value = []
}

// 机构改变，列表重新刷新
watch(() => props.organ?.id, (newVal) => {
  if (newVal) {
    tableParams = { ...defaultTableParams, organId: `${newVal}` }
    tableRef?.value?.fetch({ start: 1 })
  }
})

function handleSearch(query: string) {
  if (!hasSearchPermission())
    return
  tableParams.searchName = query
  tableRef.value?.fetch({ start: 1 })
}

// 批量导入
function batchImport() {
  message.info('功能开发中！')
}
const optionsBatch = [
  { label: '批量启用', key: 'onBatchEnable' },
  { label: '批量禁用', key: 'onBatchDisable' },
  { label: '批量删除', key: 'onBatchDelete' },
]
function handleBatchSelect(key: string | number, option: any) {
  const strategyAction = {
    onBatchEnable: () => onBatchEnable(),
    onBatchDisable: () => onBatchDisable(),
    onBatchDelete: () => onBatchDelete(),
  }
  strategyAction[key]()
}
const iconRotate = ref('rotate(0deg)')
function updateButtonShow(show: boolean) {
  iconRotate.value = show ? 'rotate(180deg)' : 'rotate(0deg)'
}
</script>

<template>
  <div id="userTable" class="userTable">
    <PageTitle class="mb-14px">
      用户管理
    </PageTitle>
    <ButtonSearch
      placeholder="请输入用户ID/姓名/手机号搜索"
      class="mb-14px"
      width="400px"
      @emit-search="handleSearch"
    />
    <n-space :size="[10, 10]" class="mb-14px">
      <n-button v-permission="'system:config:user:operate'" type="primary" @click="showModal({ edit: false })">
        新增用户
      </n-button>
      <!-- TIP:批量导入暂时不做 -->
      <!-- <n-button type="primary" ghost @click="batchImport">
        批量导入
      </n-button> -->

      <n-dropdown
        trigger="click" width="trigger"
        to="#userTable"
        :options="optionsBatch" @select="handleBatchSelect"
        @update:show="updateButtonShow"
      >
        <n-button v-permission="'system:config:user:operate'" type="primary" ghost icon-placement="right" class="batchButton">
          批量操作
          <template #icon>
            <SvgIcon local-icon="slmc-icon-drop_down" size="12" class="icon-drop_down" />
          </template>
        </n-button>
      </n-dropdown>
    </n-space>

    <BasicTable
      ref="tableRef"
      v-model:checked-row-keys="checkedRowKeys"

      :columns="columns"
      :request="loadDataTable"
      :row-key="(row:UserPageListRecord) => row.id"
      :action-column="actionColumns"
      :pagination="paginationReactive"
      :on-mounted-request="false"
      :scroll-x="2000"
      striped

      @update:checked-row-keys="onCheckedRowKeys"
    />

    <CreateUserModal ref="userModalRef" :organ="organ" :row-data="rowData" :is-edit="isEdit" @register="register" @refresh="onRefresh" />
  </div>
</template>

<style scoped lang="scss">
.userTable {
//    width:calc(100% - 230px);
   width:v-bind(tableWidth);
    padding: 14px;
    :deep(.renderSortEdit) {
        visibility: hidden;

    }
    :deep(.n-data-table .n-data-table-tr:not(.n-data-table-tr--summary):hover){
        .renderSortEdit{
            visibility: visible;

        }
    }
    :deep(.n-button .n-button__content ~ .n-button__icon){
        margin: 0px;
    }

}
#userTable {
    :deep(.n-dropdown-option-body__prefix ){
        display: none;
    }
    :deep(.n-dropdown-option-body__suffix){
        display: none;
    }
    :deep(.n-dropdown-option-body__label){
        text-align: center
        ;
    }
    :deep(.icon-drop_down){
       transform: v-bind(iconRotate);
    }
}
</style>
