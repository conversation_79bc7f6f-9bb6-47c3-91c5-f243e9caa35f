<script lang='ts' setup>
import { h, nextTick, onMounted } from 'vue'
import { NPopover, useMessage } from 'wowjoy-vui'
import { match } from 'pinyin-pro'
import CreateOranModal from './CreateOranModal.vue'
import RenderTreeAction from './components/RenderTreeAction.vue'
import { usePermission } from '@/hooks'
import type { OrganizationRes } from '@/api/organization/type'
import { InputSearch } from '@/components/Search'
import { SvgIcon } from '@/components/Icon'
import { localStg } from '@/utils'
import { deleteOrganApi, getOrganApi, updateSortApi } from '@/api/organization/organization'
import { useModal } from '@/components/Modal'
import arrowSideLeftImg from '@/assets/images/arrow-sideLeft.png'
import arrowSideRightImg from '@/assets/images/arrow-sideRight.png'
import { EmptyList } from '@/components/Empty'
import { RenderKeyString } from '@/components/business/UiRender'

const emit = defineEmits(['getOrgan', 'changeShrink'])
const message = useMessage()

const userInfo = localStg.get('userInfo')
const { hasPermission } = usePermission()
const loading = ref(false)
const currentLoginDeptId = userInfo?.deptId || ''

const selectedKeys = ref<string[]>([])
const expandedKeys = ref<string[]>([])
const currentLoginOrgan = {
  id: userInfo?.deptId || '',
  organName: userInfo?.deptName || '',

}

// 是否是搜索状态
const isSearchState = ref(false)
// 当前搜索的关键字
const searchKey = ref('')
function handleSearch(query: string) {
  searchKey.value = query

  isSearchState.value = Boolean(query)
  getOrganizations(query, false, null)
}

const [register, { openModal }] = useModal()

const organizationList = ref<OrganizationRes[]>([])

// 获取机构列表
async function getOrganizations(query = '', isInit = false, organ: { id: string; organName: string;organLevel?: number } | null) {
  try {
    const params = {
      size: 1000,
      start: 1,
      searchName: query,
    }
    const res = await getOrganApi<OrganizationRes[]>(params)
    const { tree, expandedNodeKeys } = recursionTree(res?.data ?? [])
    organizationList.value = tree

    expandedKeys.value = expandedNodeKeys

    if (isInit && res.data?.length) {
      if (organ?.id)
        handleClick(organ)
    }
  }
  catch (error) {
    console.log(error)
  }
}

const currentSelect = ref<null | string>(null)
// 机构click
function handleClick(row: OrganizationRes) {
  if ([1, 2].includes(row.organLevel))
    return
  selectedKeys.value = [row.id]

  if (row?.id) {
    currentSelect.value = row?.id
    emit('getOrgan', {
      ...row,
    })
  }
}

const showPopover = ref(false)
/** 当前新增机构的父id */
const parentId = ref('')
/** 当前新增机构的level */
const organLevel = ref(1)
/** 当前新增机构的序号 */
const orderNum = ref(1)
const currentActionNode = ref('')
const actionOrgan = ref(null)

function renderSuffix({ option }: { option: Auth.OrganList }) {
  const showEditIcon = hasPermission('system:config:user:operate')
  if (showEditIcon) {
    return h(
      NPopover,
      {
        showArrow: true,
        placement: 'bottom',
        trigger: 'click',
        raw: true,
        show: currentActionNode.value === option.id && showPopover.value,
        onClickoutside: () => {
          showPopover.value = false
        },
      },
      {
        trigger: () => h('div', {
          class: 'flex items-center cursor-pointer',
          text: true,
        }, [
          h(
            SvgIcon,
            {
              localIcon: 'slmc-icon-more1',
              class: 'showEditIcon',
              onClick: () => {
                showPopover.value = true
                currentActionNode.value = option.id as string
                const organ = {
                  id: option?.id || '',
                  organName: option?.organName || '',
                  organLevel: option.organLevel,

                }
                handleClick(organ)
              },

            },

          ),
        ]),
        default: () => h(RenderTreeAction, {
          nodeId: option.id,
          level: option?.organLevel,

          onCreate: () => {
            actionOrgan.value = null
            parentId.value = option.id as string
            orderNum.value = option?.children?.length ? option.children.length + 1 : 1
            organLevel.value = option?.organLevel ? option.organLevel + 1 : 1
            openModal()
            showPopover.value = false
          },
          onEdit: () => {
            parentId.value = option.parentId as string
            orderNum.value = option.orderNum
            organLevel.value = option?.organLevel
            actionOrgan.value = { id: option.id, organName: option.organName }
            openModal()
            showPopover.value = false
          },
          onDelete: async () => {
            showPopover.value = false
            const res = await deleteOrganApi(option.id as string)
            if (res.data) {
              message.success('删除成功')
              onRefresh(currentLoginOrgan)
            }
          },

        },
        ),

      },

    )
  }
}

function renderIcon() {
  return h(
    SvgIcon,
    {
      localIcon: 'slmc-icon-wenjian1',
      class: 'text-06AEA6',
      size: '16',
    },

  )
}

// 树节点的属性
function nodeProps({ option }: { option: Auth.OrganList }) {
  const nodeLevel = `nodeLevel-${option.organLevel}`
  return {
    onclick: (e: Event) => {
      const targetNodeName = (e.target as HTMLInputElement).nodeName

      if (['svg', 'path'].includes(targetNodeName)) {
        return null
      }
      else {
        const organ = {
          id: option?.id || '',
          organName: option?.organName || '',
          organLevel: option.organLevel,

        }
        handleClick(organ)
      }
    },
    class: `nodeProps ${nodeLevel}`,

  }
}
// 是否收起
const isShrink = ref(false)
const borderRightPx = computed(() => {
  const width = isShrink.value ? '0px' : '1px'
  return width
})
function handleShrink(shrink: boolean) {
  isShrink.value = shrink
  emit('changeShrink', shrink)
}

function init() {
  getOrganizations('', true, currentLoginOrgan)
}

// 刷新机构列表
function onRefresh(organ: { id: string; organName: string }) {
  getOrganizations('', true, organ)
}
function onResetState() {
  parentId.value = ''
  organLevel.value = 1
  orderNum.value = 1
  actionOrgan.value = null
}

function recursionTree(tree: OrganizationRes[]): any[] {
  const expandedKeys = new Set()
  function recursion(node: OrganizationRes) {
    if (node.organLevel < 4 && !node?.children)
      node.children = []

    if (node?.children?.length === 0 || !node?.children) {
      node.prefix = () => h(SvgIcon, {
        localIcon: 'slmc-icon-wenjian1',
        class: 'text-06AEA6 -ml-8px',
        size: '16',
      })
      node.isLeaf = true
    }

    if (searchKey.value) {
      if (node?.expandStatus && node?.expandStatus === '1')
        expandedKeys.add(node.id)
    }
    else {
      expandedKeys.add(node.id)
    }

    if (node?.children)
      node.children.forEach(recursion)
  }

  tree.forEach(recursion)

  return {
    tree,
    expandedNodeKeys: [...Array.from(expandedKeys)],
  }
}

function dragNodeCallback(flattenArray: unknown[]) {
  loading.value = true
  nextTick(async () => {
    await updateSortApi(flattenArray)
    const params = {
      size: 1000,
      start: 1,
      searchName: searchKey.value,
    }
    const res = await getOrganApi<OrganizationRes[]>(params)
    organizationList.value = recursionTree(res?.data ?? []).tree
    loading.value = false
  })
}
/**
 * 扁平化数据并且手动添加orderNum字段
 * @param tree 树
 */

function flattenTree(tree: OrganizationRes[]): any[] {
  const flattened: any[] = []

  function flatten(node: OrganizationRes, index: number) {
    node.orderNum = index + 1
    flattened.push(node)
    if (node.children)
      node.children.forEach((child, childIndex) => flatten(child, childIndex))
  }

  tree.forEach((node, index) => flatten(node, index))

  return flattened
}
/***
 * 找到当前拖拽节点和当前做拽节点的数组
 */
function findSiblingsAndIndex(
  node: Auth.OrganList,
  nodes?: Auth.OrganList[],
): [Auth.OrganList[], number] | [null, null] {
  if (!nodes)
    return [null, null]
  for (let i = 0; i < nodes.length; ++i) {
    const siblingNode = nodes[i]
    if (siblingNode.id === node.id)
      return [nodes, i]
    const [siblings, index] = findSiblingsAndIndex(node, siblingNode.children)
    if (siblings && index !== null)
      return [siblings, index]
  }
  return [null, null]
}

function canDragNode(dropPosition: 'before' | 'inside' | 'after', dragNode: Auth.OrganList, targetNode: Auth.OrganList) {
  const { organLevel: dragNodeLevel } = dragNode
  const { organLevel: targetNodeLevel } = targetNode
  if (dropPosition === 'inside') {
    const targetNodeChildren = targetNode?.children ?? []
    const haveSameName = targetNodeChildren.findIndex(item => item.organName === dragNode.organName)
    const isSameParentId = dragNode.parentId === targetNode.id

    if (haveSameName > -1 && !isSameParentId) {
      window.$message.warning('名称重复，无法移动')
      return false
    }

    const canInside = (dragNodeLevel === targetNodeLevel + 1) && targetNodeLevel <= 4
    return canInside
  }
  else if (dropPosition === 'before') {
    const [nodeSiblings] = findSiblingsAndIndex(
      targetNode,
      organizationList.value,
    )
    const targetNodeSiblings = nodeSiblings ?? []
    const haveSameName = targetNodeSiblings.findIndex(item => item.organName === dragNode.organName)
    const isSameParentId = dragNode.parentId === targetNode.parentId

    if (haveSameName > -1 && !isSameParentId) {
      window.$message.warning('名称重复，无法移动')
      return false
    }

    const canBefore = dragNodeLevel === targetNodeLevel
    return canBefore
  }
  else if (dropPosition === 'after') {
    const [nodeSiblings] = findSiblingsAndIndex(
      targetNode,
      organizationList.value,
    )
    const targetNodeSiblings = nodeSiblings ?? []
    const haveSameName = targetNodeSiblings.findIndex(item => item.organName === dragNode.organName)
    const isSameParentId = dragNode.parentId === targetNode.parentId

    if (haveSameName > -1 && !isSameParentId) {
      window.$message.warning('名称重复，无法移动')
      return false
    }
    const canAfter = dragNodeLevel === targetNodeLevel
    return canAfter
  }
  return false
}
/** 拖拽的回调 */
async function onHandleDrop(data: {
  node: Auth.OrganList
  dragNode: Auth.OrganList
  dropPosition: 'before' | 'inside' | 'after'
  event: DragEvent
}) {
  const { dragNode, node, dropPosition } = data

  // 找到当前拖拽节点和当前做拽节点的数组
  const [dragNodeSiblings, dragNodeIndex] = findSiblingsAndIndex(
    dragNode,
    organizationList.value,
  )
  //   console.log('dragNodeSiblings---', dragNodeSiblings)

  //   console.log('*********************')

  //   console.log('dragNode=====', dragNode)

  //   console.log('++++++++++++++++')

  //   console.log('node=====', node)
  //   console.log('dropPosition=====', dropPosition)
  if (dragNodeSiblings === null || dragNodeIndex === null)
    return
  //   console.log('canDragNode', canDragNode(dropPosition, dragNode.organLevel, node.organLevel))

  if (!canDragNode(dropPosition, dragNode, node))
    return

  // 在当前拖拽节点的同一层级中 删除该节点
  dragNodeSiblings.splice(dragNodeIndex, 1)

  // 拖拽节点进行插入操作
  if (dropPosition === 'inside') {
    dragNode.parentId = node.id
    dragNode.organLevel = node.organLevel + 1
    if (node.children)
      node.children.unshift(dragNode)

    else

      node.children = [dragNode]
  }// 拖拽节点，插入某个节点之前
  else if (dropPosition === 'before') {
    dragNode.parentId = node.parentId
    dragNode.organLevel = node.organLevel
    const [nodeSiblings, nodeIndex] = findSiblingsAndIndex(
      node,
      organizationList.value,
    )
    if (nodeSiblings === null || nodeIndex === null)
      return
    nodeSiblings.splice(nodeIndex, 0, dragNode)
  }// 拖拽节点，插入某个节点之后
  else if (dropPosition === 'after') {
    dragNode.parentId = node.parentId
    dragNode.organLevel = node.organLevel
    const [nodeSiblings, nodeIndex] = findSiblingsAndIndex(
      node,
      organizationList.value,
    )
    if (nodeSiblings === null || nodeIndex === null)
      return
    nodeSiblings.splice(nodeIndex + 1, 0, dragNode)
  }
  const flattenArray = Array.from(flattenTree(organizationList.value))

  dragNodeCallback(flattenArray)
}
/**
 * 搜索过滤函数
 * @param pattern 匹配字符串
 * @param node 当前节点
 */
function onFilterNode(pattern: string, node: any) {
  // TODO:优化 ts类型定义
  const { organName = '' } = node

  const isStringMatch = organName.toLowerCase().includes(pattern.toLowerCase())
  const isPinyinMatch = match(organName, pattern)?.length > 0

  return isStringMatch || isPinyinMatch
}
onMounted(() => {
  init()
})
</script>

<template>
  <div class="organization" :class="[isShrink ? 'shrink' : 'expand']">
    <div class="w-full flex items-center p-10px pr-9px">
      <InputSearch
        placeholder="请输入机构名称查询" width="260"
        :style="{ display: `${isShrink ? 'none' : 'block'}` }" @emit-search="handleSearch"
      />
      <!-- <SvgIcon v-if="haveOrganPermission" local-icon="slmc-icon-add" size="16" style="color: #ccc; cursor: pointer" @click="showModal" /> -->
    </div>
    <div v-permission="'system:config:user:search'" class="list">
      <!-- <div v-if="isSearchState && organizationList.length === 0" class="h-full flex">
        <EmptyList />
      </div>
      <template v-else>
        <div v-for="item in organizationList" :key="item.id" class="item" :class="{ 'text-primary bg-#fffbe0': currentSelect === item.id }" @click="() => handleClick(item)">
          <RenderKeyString :text="item.organName" :search-key="searchKey" />
        </div>
      </template> -->
      <n-spin type="uni" :show="loading">
        <n-tree
          key-field="id"
          label-field="organName" class="tree h-full"
          :data="organizationList"
          :render-suffix="renderSuffix"
          :render-icon="renderIcon"
          :node-props="nodeProps"
          :default-selected-keys="[currentLoginDeptId]"
          :selected-keys="selectedKeys"
          :default-expanded-keys="expandedKeys"
          draggable block-line
          @drop="onHandleDrop"
        >
          <template #default="{ data }">
            <div class="custom-tree-node flex items-center">
              <RenderKeyString :max-width="240" :search-key="searchKey" :text="data?.organName " />
            </div>
          </template>
          <template #empty>
            <div class="h-full flex pt-40vh">
              <EmptyList />
            </div>
          </template>
        </n-tree>
      </n-spin>
    </div>
    <img v-if="isShrink" :src="arrowSideRightImg" alt="" class="arrowSideImg imgShrink" @click="handleShrink(false)">
    <img v-else :src="arrowSideLeftImg" alt="" class="arrowSideImg imgExpand" @click="handleShrink(true)">
  </div>
  <CreateOranModal :parent-id="parentId" :order-num="orderNum" :level="organLevel" :organ="actionOrgan" @register="register" @refresh="onRefresh" @reset-state="onResetState" />
</template>

<style scoped lang="scss">
.shrink {
    width: 0px;

    .imgShrink {
        left: 0px;
    }

}

.expand {
    width: 280px;
    min-width:280px;
    .imgExpand {
        left: 280px;
    }
}

.organization {
    border-right: v-bind(borderRightPx) solid #cccccc;
    position: relative;
    .list {
        width: 100%;
        font-size: 12px;
        height: calc(100% - 52px);
        overflow-y:auto;
    }

    .arrowSideImg {
        display: inline-block;
        width: 14px;
        position: absolute;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
    }

}

.tree {
    height: 100%;
    :deep(.showEditIcon) {
        visibility: hidden;
        color:#333
    }

    :deep(.nodeProps:hover) {
        .showEditIcon {
            visibility: visible;
            color:#333
        }
    }
    :deep(.n-tree-node--pending){
        .showEditIcon {
            visibility: visible;
            color:#333
        }
    }
    :deep(.n-tree-node-folder-content){
        margin-top: 0px;
    }
    :deep(.n-tree-node){
        display: flex;
        align-items: center;
    }
    :deep( .n-tree-node:not(.n-tree-node--disabled).n-tree-node--selected .n-tree-node-content){
        color:#06AEA6
    }

    :deep(.n-tree-node:not(.n-tree-node--disabled).n-tree-node--pending.nodeLevel-2){
        background-color: #ffffff;
    }
    :deep(.n-tree-node:not(.n-tree-node--disabled).n-tree-node--pending.nodeLevel-1){
        background-color: #ffffff;
    }

}
</style>
