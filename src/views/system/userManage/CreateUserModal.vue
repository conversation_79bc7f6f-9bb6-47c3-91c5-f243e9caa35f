<script lang='ts' setup>
import type { FormInst } from 'wowjoy-vui'
import { useMessage } from 'wowjoy-vui'
import { match } from 'pinyin-pro'
import { nextTick } from 'vue'
import { useDebounceFn } from '@vueuse/core'
import type { OrganizationRes } from '@/api/organization/type'
import { checkPhone<PERSON>pi, createUserApi, editUserApi } from '@/api/user/user'
import { getOrganApi } from '@/api/organization/organization'
import type { CreateUserParams } from '@/api/user/type'
import { BasicModal, useModalInner } from '@/components/Modal'
import { isPhone } from '@/utils/common'
import { isMasterAdmin, localStg } from '@/utils'
import { getDoctorGroupListApi } from '@/api/doctor'
import type { DoctorGroupRecord } from '@/api/doctor'
import { useAuthStore } from '@/store'
import { SvgIcon } from '@/components/Icon'
import { getRoleListApi } from '@/api/role'
import { RenderKeyString } from '@/components/business/UiRender'
import { EmptyList } from '@/components/Empty'

interface FormValue {
  id: string
  /** 用户姓名 */
  userName: string
  /** 手机号 */
  phone: string
  organId: string
  /** 职称 */
  title: string
  /** 职务 */
  job: string
  /** 机构名称 */
  organName: string
  /** 所在医疗组id */
  medicalTeamId: null | string
  /** 所在医疗组名称 */
  medicalTeamName: string
  /** 角色id */
  userRoleList: string[]
}

const props = defineProps({
  organ: {
    type: Object as PropType<OrganizationRes | null>,
    default: () => {},
  },
  rowData: {
    type: Object as PropType<any>,
    default: () => {},
  },
  isEdit: {
    type: Boolean,
    default: false,
  },

})

const emit = defineEmits(['refresh', 'register'])

const message = useMessage()

const { userInfo } = useAuthStore()
const loginUserId = userInfo?.id

/** 是否是编辑状态 */
const isEdit = computed(() => props.isEdit)

const canEditRole = computed(() => {
  return loginUserId !== props.rowData.id
})

const formRef = ref<FormInst | null>(null)
const rules = {
  userName: {
    required: true,
    trigger: ['blur'],
    message: '请输入姓名',
  },
  phone: {
    required: true,
    trigger: ['blur'],
    validator(_, value: string) {
      if (!value)
        return new Error('请输入手机号')

      else if (!isPhone(value))
        return new Error('请输入合法的手机号')

      return true
    },
  },

  organName: {
    required: true,
    trigger: ['blur'],
    message: '请输入用所属机构',
  },

}
const formValue = ref<FormValue>({
  id: '',
  userName: '',
  phone: '',
  organId: '',
  title: '',
  job: '',
  organName: '',
  medicalTeamId: null,
  medicalTeamName: '',
  userRoleList: [],
})

const [register, { closeModal }] = useModalInner()

function resetFormValue() {
  formValue.value = {
    id: '',
    userName: '',
    phone: '',
    organId: '',
    title: '',
    job: '',
    organName: '',
    medicalTeamId: null,
    medicalTeamName: '',
    userRoleList: [],
  }
}

/** 所在医疗组 */
const medicalTeamOptions = ref<DoctorGroupRecord[]>([])
/** 手机号重复校验 */
async function checkPhone(params: { value: string }) {
  try {
    const res = await checkPhoneApi(params)
    if (res) {
      return new Promise((resolve) => {
        resolve(res?.data)
      })
    }
  }
  catch (error) {
    console.log(error)
  }
}
// 搜索做防抖
const getMedicalTeam = useDebounceFn(async (name = '') => {
  /** 用户登陆时所属机构 */
  const organId = userInfo.organId
  const res = await getDoctorGroupListApi<DoctorGroupRecord[]>({ name, organId })
  if (res.data) {
    if (!name) {
      const defaultArray = [{ doctorGroupName: '全部', doctorGroupId: '' }] as DoctorGroupRecord[]

      medicalTeamOptions.value = defaultArray.concat(res.data)
    }
    else {
      medicalTeamOptions.value = res.data
    }
  }
}, 300)
/**
 * 查找医疗组
 * @param query 搜索key
 */
function handleSearchMedicalTeam(query: string) {
  getMedicalTeam(query)
}
/**
 * 医疗组打开/关闭回调
 * @param show 下拉打开/关闭
 */
function handleUpdateShowMedicalTeam(show: boolean) {
  if (show)
    getMedicalTeam('')
}
/** 医疗组更新选项 */
function handleUpdateMedicalTeam(value: string, option: DoctorGroupRecord) {
  formValue.value.medicalTeamId = value
  formValue.value.medicalTeamName = option?.doctorGroupName
}

// 新增用户
async function createUser(params: CreateUserParams) {
  try {
    const checkPhoneRes = await checkPhone({ value: params.phone })

    if (checkPhoneRes) {
      message.error('手机号重复')
      return
    }

    const res = await createUserApi(params)

    if (res.data) {
      message.success('保存成功')
      closeModal()
      resetFormValue()
      emit('refresh')
    }
  }
  catch (error) {
    console.log(error)
  }
}

// 编辑用户
async function editUser(params: any) {
  try {
    const res = await editUserApi(params)
    if (res.data) {
      message.success('保存成功')
      closeModal()
      resetFormValue()
      emit('refresh')
    }
  }
  catch (error) {
    console.log(error)
  }
}
/** 弹窗确认 */
function handleConfirm() {
  formRef.value?.validate((errors) => {
    if (!errors) {
      const userInfo = localStg.get('userInfo')

      const createBy: string = userInfo?.id ?? ''
      const userRoleList = formValue.value.userRoleList.map(roleId => ({ id: roleId }))
      const params = {
        ...unref(formValue.value),
        medicalTeam: formValue.value.medicalTeamName ?? '',
        userRoleList,
        createBy,
        status: 0, // （0正常 1停用）
      }

      if (params?.id)
        editUser(params)

      else
        createUser(params)
    }
    else {
      console.log(errors)
    }
  })
}

function handleCancel() {
  resetFormValue()
}

const searchKey = ref('')
/** 选择的机构 */
const checkedOrgan = ref(null)
/** 默认选中的机构 */
const defaultSelectedKeys = ref([])
const expandedKeys = ref<string[]>([])
/** 机构列表 */
const organizationList = ref<OrganizationRes[]>([])
/** 角色列表 */
const roleList = ref([])
/** 弹窗名称 */
const modelTitle = ref('')

/** 获取角色管理 */
async function getRoleList() {
  const res = await getRoleListApi({ size: 1000, start: 1 })
  if (res?.data) {
    const roleArray = res.data?.records ?? []
    roleList.value = roleArray.map((role) => {
      const roleOtherPayload = {
        disabled: false,
      }
      if (role.remark && isMasterAdmin(role.remark))
        roleOtherPayload.disabled = true

      return {
        ...role,
        ...roleOtherPayload,
      }
    }).filter(role => role.status === 0)
  }
}

function onUpdateRole(value: string[]) {
  if (value.length > 5) {
    window.$dialog.warning('角色最多选5个')
    return
  }

  formValue.value.userRoleList = [...value]
}
function recursionTree(tree: OrganizationRes[]): any[] {
  const expandedKeys = new Set()
  function recursion(node: OrganizationRes) {
    if (node?.children?.length === 0 || !node?.children) {
      node.prefix = () => h(SvgIcon, {
        localIcon: 'slmc-icon-wenjian1',
        class: 'text-06AEA6 -ml-8px',
        size: '16',
      })
      node.isLeaf = true
    }

    if (searchKey.value) {
      if (node?.expandStatus && node?.expandStatus === '1')
        expandedKeys.add(node.id)
    }
    else {
      expandedKeys.add(node.id)
    }

    if (node?.children)
      node.children.forEach(recursion)
  }

  tree.forEach(recursion)

  return {
    tree,
    expandedNodeKeys: [...Array.from(expandedKeys)],
  }
}

// 搜索做防抖
const getOrganizations = useDebounceFn(async (name = '') => {
  /** 用户登陆时所属机构 */
  try {
    const params = {
      size: 1000,
      start: 1,
      searchName: name,
    }
    const res = await getOrganApi<OrganizationRes[]>(params)
    const { tree, expandedNodeKeys } = recursionTree(res?.data ?? [])
    organizationList.value = tree

    expandedKeys.value = expandedNodeKeys
  }
  catch (error) {
    console.log(error)
  }
}, 300)
/**
 * 查找机构
 * @param query 搜索key
 */
function handleSearchOrgan(query: string) {
  getOrganizations(query)
}

/**
 * 弹窗的打开/关闭
 */
function visibleChange(show: boolean) {
  if (show) {
    nextTick(() => {
      if (props.isEdit) {
        modelTitle.value = '编辑用户'
        const {
          userName, phone, organId, title, job, organName, id,
          medicalTeam, userRoleList,
        } = props.rowData

        const roleId = userRoleList.map(role => role.id)
        defaultSelectedKeys.value = [organId]
        checkedOrgan.value = { id: organId, organName }

        formValue.value = {
          userName,
          phone,
          organId,
          title,
          job,
          organName,
          id,
          medicalTeamId: medicalTeam || null,
          medicalTeamName: medicalTeam,
          userRoleList: roleId,
        }
      }
      else {
        const { id = '', organName = '' } = props.organ as OrganizationRes
        formValue.value.organName = organName
        formValue.value.organId = id
        defaultSelectedKeys.value = [id]
        checkedOrgan.value = { ...props.organ }
        // TODO：登录是用户所在机构
        modelTitle.value = '新增用户'
      }
    })

    getOrganizations()
    getRoleList()
  }
}
/** 渲染最内层图标 */
function renderIcon() {
  return h(
    SvgIcon,
    {
      localIcon: 'slmc-icon-wenjian1',
      class: 'text-06AEA6',
      size: '16',
    },

  )
}
/** 节点选中的回调 */
function onSelectKeys(keys: Array<string | number>, option: Array<any | null>,
  meta: { node: any | null; action: 'select' | 'unselect' }) {
  const selectOrgan = meta.node
  // 大于二级机构才能被选择
  if (selectOrgan.organLevel > 2) {
    checkedOrgan.value = selectOrgan
    formValue.value.organName = selectOrgan.organName
    formValue.value.organId = selectOrgan.id
  }
}
/**
 * 搜索过滤函数
 * @param pattern 匹配字符串
 * @param node 当前节点
 */
function onFilterNode(pattern: string, node: any) {
  // TODO:优化 ts类型定义
  const { organName = '' } = node

  const isStringMatch = organName.toLowerCase().includes(pattern.toLowerCase())
  const isPinyinMatch = match(organName, pattern)?.length > 0

  return isStringMatch || isPinyinMatch
}
function init() {
  getMedicalTeam('')
}
onMounted(() => {
  init()
})
</script>

<template>
  <div>
    <BasicModal
      v-bind="$attrs"
      :title="modelTitle"
      :width="720"
      :height="510"
      @register="register"
      @ok="handleConfirm"
      @cancel="handleCancel"
      @visible-change="visibleChange"
    >
      <n-form
        ref="formRef"
        label-placement="left"
        class="my-24px ml-17px mr-30px"
        require-mark-placement="left"
        :model="formValue"
        :rules="rules"
        :label-width="100"
      >
        <n-grid :cols="2">
          <n-form-item-gi path="userName" label="姓名">
            <n-input
              v-model:value="formValue.userName"
              placeholder="请输入"
              :maxlength="50"
            />
          </n-form-item-gi>
          <n-form-item-gi path="phone" label="手机号">
            <n-input
              v-model:value="formValue.phone"
              placeholder="请输入"
              :maxlength="11"

              :disabled="isEdit"
            />
          </n-form-item-gi>
          <n-form-item-gi path="title" label="职称">
            <n-input
              v-model:value="formValue.title"
              placeholder="请输入"
              :maxlength="50"
            />
          </n-form-item-gi>
          <n-form-item-gi path="job" label="职务">
            <n-input
              v-model:value="formValue.job"
              placeholder="请输入"
              :maxlength="50"
            />
          </n-form-item-gi>
          <n-form-item-gi path="userRoleList" label="角色">
            <n-select
              :value="formValue.userRoleList"
              placeholder="请选择"
              style="width:236px;"
              :options="roleList"
              label-field="name"
              value-field="id"
              multiple
              max-tag-count="responsive"
              :disabled="!canEditRole"
              @update:value="onUpdateRole"
            />
          </n-form-item-gi>
          <n-form-item-gi path="medicalTeam" label="所在医疗组">
            <n-select
              v-model:value="formValue.medicalTeamId"
              placeholder="请选择"

              :options="medicalTeamOptions"
              filterable
              clearable
              remote
              label-field="doctorGroupName"
              value-field="id"
              @search="handleSearchMedicalTeam"
              @update:show="handleUpdateShowMedicalTeam"
              @update:value="handleUpdateMedicalTeam"
            />
          </n-form-item-gi>
          <n-form-item-gi label="所属机构" :span="2" path="organName">
            <div class="h-320px w-full flex justify-start text-#333">
              <div class="w-326px border border-#d1d1d1 border-rd-3px">
                <div class="h-32px flex items-center bg-#E7F9F7 pl-10px">
                  选择部门
                </div>
                <div>
                  <n-input
                    v-model:value="searchKey"
                    placeholder="请输入部门名称搜索"
                    style="--n-border:1px solid transparent;
                    --n-border-focus:1px solid transparent;
                    --n-border-hover:1px solid transparent;
                    --n-box-shadow-focus:none"
                    @update:value="handleSearchOrgan"
                  >
                    <template #suffix>
                      <SvgIcon local-icon="slmc-icon-search" color="#ccc" class="cursor-pointer" size="16" />
                    </template>
                  </n-input>
                  <n-divider margin="0px 0px 0px 0px" class="px-10px" />
                </div>
                <div class="h-253px overflow-auto pt-10px">
                  <n-tree
                    label-field="organName"
                    key-field="id"
                    class="tree"
                    :data="organizationList"
                    :render-icon="renderIcon"
                    selectable block-line
                    :default-selected-keys="defaultSelectedKeys"
                    :watch-props="['defaultSelectedKeys']"
                    :default-expanded-keys="expandedKeys"
                    @update:selected-keys="onSelectKeys"
                  >
                    <template #default="{ data }">
                      <div class="custom-tree-node flex items-center justify-between">
                        <RenderKeyString :max-width="200" :search-key="searchKey" :text="data?.organName " />
                        <n-radio
                          v-if="[3, 4].includes(data?.organLevel)"
                          class="pr-10px"
                          :checked="checkedOrgan?.id === data.id"
                          :value="data.id"
                          :name="data.organName"
                        />
                      </div>
                    </template>
                    <template #empty>
                      <div class="h-full flex pt-100px">
                        <EmptyList />
                      </div>
                    </template>
                  </n-tree>
                </div>
              </div>
              <div class="w-36px flex items-center justify-center">
                <SvgIcon local-icon="slmc-icon-crumbs1" color="#666" />
              </div>
              <div class="w-210px border border-#d1d1d1 border-rd-3px">
                <div class="h-32px flex items-center bg-#E7F9F7 pl-10px">
                  已选择 ({{ checkedOrgan ? 1 : 0 }}/1)
                </div>
                <div class="p-10px">
                  <n-ellipsis style="max-width: 200px">
                    {{ checkedOrgan?.organName }}
                  </n-ellipsis>
                </div>
              </div>
            </div>
          </n-form-item-gi>
        </n-grid>
      </n-form>
    </BasicModal>
  </div>
</template>

<style scoped lang="scss">
.tree {
    :deep(.n-tree-node-folder-content){
        margin-top: 0px;
    }
    :deep(.n-tree-node){
        display: flex;
        align-items: center;
    }
}
</style>
