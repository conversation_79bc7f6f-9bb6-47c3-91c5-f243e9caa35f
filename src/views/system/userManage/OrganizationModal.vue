<script lang='ts' setup>
import type { FormInst } from 'wowjoy-vui'
import { useMessage } from 'wowjoy-vui'

import { organGroupOptions, organTypeOptions } from './constant'
import { createOrganApi } from '@/api/organization/organization'
import type { CreateOrganParams } from '@/api/organization/type'
import { BasicModal, useModalInner } from '@/components/Modal'

const props = defineProps({
  parentId: {
    type: String,
  },
  level: {
    type: Number,
    default: 1,
  },
  orderNum: {
    type: Number,
    default: 1,
  },
})
const emit = defineEmits(['refresh', 'register', 'resetState'])
const message = useMessage()

const formRef = ref<FormInst | null>(null)
const rules = {
  organGroup: {
    required: true,
    trigger: ['blur', 'change'],
    message: '请选择机构分组',
  },
  organType: {
    required: true,
    trigger: ['blur', 'change'],
    message: '请选择机构类型',
  },
  organName: {
    required: true,
    trigger: ['blur'],
    message: '请选择机构名称',
  },

}
const formValue = ref({
  organGroup: null,
  organType: null,
  organName: '',
  organDesc: '',
  status: 0, // 0正常 1停用
})

function resetFormValue() {
  formValue.value = {
    organGroup: null,
    organType: null,
    organName: '',
    organDesc: '',
    status: 0, // 0正常 1停用

  }
}

const [register, { closeModal }] = useModalInner()

// 新增机构
async function createOrgan(params: CreateOrganParams) {
  try {
    const res = await createOrganApi(params)

    if (res.data) {
      message.success('保存成功')
      closeModal()
      resetFormValue()
      emit('refresh', { id: '', organName: params.organName })
      emit('resetState')
    }
  }
  catch (error) {
    console.log(error)
  }
}
function handleConfirm() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      const params = {
        ...unref(formValue.value),
        parentId: props?.parentId || null,
        organLevel: props?.level ?? 1,
        orderNum: props?.orderNum ?? 1,
      }

      createOrgan(params)
    }
    else {
      console.log(errors)
    }
  })
}

function handleCancel() {
  resetFormValue()
  emit('resetState')
}
</script>

<template>
  <div class="">
    <BasicModal
      v-bind="$attrs"
      title="新增机构"
      :width="560"
      :footer-offset="110"
      @register="register"
      @ok="handleConfirm"
      @cancel="handleCancel"
    >
      <n-form
        ref="formRef"
        label-placement="left"
        class="my-24px ml-17px mr-30px"
        require-mark-placement="left"
        :model="formValue"
        :rules="rules"
        :label-width="100"
      >
        <n-form-item path="organGroup" label="机构分组">
          <n-select
            v-model:value="formValue.organGroup"
            placeholder="请选择"
            style="width:340px;"
            :options="organGroupOptions"
          />
        </n-form-item>
        <n-form-item path="organType" label="机构类型">
          <n-select
            v-model:value="formValue.organType"
            placeholder="请选择"
            :options="organTypeOptions"
            style="width:340px;"
          />
        </n-form-item>
        <n-form-item path="organName" label="机构名称">
          <n-input
            v-model:value="formValue.organName"
            placeholder="请输入"
            :maxlength="50"
            style="width:340px;"
          />
        </n-form-item>
        <n-form-item path="organDesc" label="机构简介">
          <n-input
            v-model:value="formValue.organDesc"
            :maxlength="300"
            placeholder="请输入"
            type="textarea"
            show-count
            style="width:340px;"
          />
        </n-form-item>
      </n-form>
    </BasicModal>
  </div>
</template>

<style scoped lang="scss">

</style>
