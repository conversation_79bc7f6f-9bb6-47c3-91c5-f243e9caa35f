<script setup lang="ts">
import type { FormInst, FormRules } from 'wowjoy-vui'
import { useDialog } from 'wowjoy-vui'
import { addTagList, deleteTagList, getTagList, tagIsUsedAPI, updateTag } from '@/api/followCenter'
import { useAuthStore } from '@/store'
import { Breadcrumb } from '@/layouts/common'
import type { BreadLit } from '@/layouts/common'

defineOptions({
  name: 'FollowTag',
})

const auth = useAuthStore()
const dialog = useDialog()
const updateRef = ref<any>(null)

const userInfo = auth.userInfo
const isTopLevel = auth.isTopLevel

const tableData = ref([])

const tagRule: FormRules = {
  tagName: [
    {
      required: true,
      trigger: 'blur',
      validator(_: any, value: string | null) {
        if (!value) {
          return new Error('请输入标签名称')
        }
        else {
          // eslint-disable-next-line no-async-promise-executor
          return new Promise(async (resolve, reject) => {
            const params = {
              tagName: value,
              tagId: '',
            }

            if (updateRef?.value?.tagId)
              params.tagId = updateRef?.value?.tagId

            const { data } = await tagIsUsedAPI(params)

            if (!data)
              reject(new Error('标签名称不能重复'))

            else
              resolve()
          })
        }
      },
    },
  ],
}

const showModal = ref(false)
const formRef = ref<FormInst | null>(null)
const isEditor = ref(false)
const deleteConfirm = ref(false)
const tagName = ref('')
const tagStatus = ref('ALL')

const formValue = ref({
  tagName: '',
})

const page = reactive({
  page: 1,
  size: 10,
  total: 0,
})

const statusOptions = [
  {
    label: '全部',
    value: 'ALL',
  },
  {
    label: '启用',
    value: 1,
  },
  {
    label: '停用',
    value: 0,
  },
]

async function getTags() {
  try {
    const params = {
      tagName: tagName.value,
      tagStatus: tagStatus.value === 'ALL' ? null : !!tagStatus.value,
      organId: userInfo.organId,
      userId: userInfo.id,
      page: page.page,
      size: page.size,
    }
    const { data } = await getTagList<any>(params)
    tableData.value = data.records
    page.total = Number(data.total)
  }
  catch (error) {
    console.log(error)
  }
}

function addTags() {
  updateRef.value = {
    tagId: null,
  }
  formValue.value.tagName = ''
  isEditor.value = true
  showModal.value = true
}

function deleteHandler(row: any) {
  // deleteConfirm.value = true
  // updateRef.value = row

  dialog.warning({
    title: '确定删除此标签吗？',
    positiveText: '确定',
    negativeText: '取消',
    btnGhost: true,
    onPositiveClick: async () => {
      try {
        const { data } = await deleteTagList(row.tagId)
        if (data) {
          window.$message.success('删除成功')
          getTags()
        }
      }
      catch (error) {
        // window.$message.error('删除失败')
      }
    },
  })
}

function changeTags(item: typeof formValue.value) {
  formValue.value.tagName = item.tagName
  updateRef.value = item
  isEditor.value = false
  showModal.value = true
}

function onPositiveClick() {
  deleteTagList(updateRef.value.tagId).then((res) => {
    if (res.data)
      window.$message.success('删除成功')

    else
      window.$message.error('删除失败')
  })
  showModal.value = false

  deleteConfirm.value = false
}

function addTagHandler() {
  formRef.value?.validate((errors) => {
    if (!errors) {
      if (isEditor.value) {
        const params = {
          createId: userInfo.id,
          createName: userInfo.organName,
          createOrganId: userInfo.organId,
          tagName: formValue.value.tagName,
        }
        addTagList(params).then((res: any) => {
          if (res.data) {
            window.$message.success('添加成功')
            showModal.value = false
            getTags()
          }
        })
      }
      else {
        const params = {
          ...updateRef.value,
          tagName: formValue.value.tagName,
        }
        updateTag(params).then((res: any) => {
          if (res.data) {
            window.$message.success('修改成功')
            getTags()
            showModal.value = false
          }
          else {
            window.$message.error('修改失败')
          }
        })
      }
    }
  })

  // if()
}

async function handleEnable(row: any) {
  try {
    const params = {
      tagId: row.tagId,
      isEnable: !row.isEnable,
      updateId: userInfo.id,
      updateName: userInfo.organName,
    }

    const { data } = await updateTag(params)
    if (data) {
      window.$message.success('操作成功')
      getTags()
    }
    else {
      window.$message.error('操作失败')
    }
  }
  catch (error) {
    window.$message.error('操作失败')
  }
}

const renderBread: BreadLit[] = [
  {
    title: '系统设置',
    link: null,
    key: 'system',
  },
  {
    title: '标签管理',
    link: null,
    key: 'system_tag',
  },
]

function handleUpdatePageSize() {
  page.page = 1
  getTags()
}

onMounted(() => {
  getTags()
})
</script>

<template>
  <div>
    <Breadcrumb :bread-list="renderBread" />
    <PageCard breadcrumb offset-bottom="60" class="h-[calc(100%)]">
      <PageTitle border class="flex">
        标签管理
      </PageTitle>
      <div my-20px flex justify-between>
        <div flex items-center>
          <div color="#666" flex-shrink-0>
            状态
          </div>
          <n-select
            v-model:value="tagStatus" ml-10px mr-20px w-180px
            :options="statusOptions"
            @update:value="getTags"
          />
          <n-input-group>
            <n-input v-model:value="tagName" placeholder="请输入标签名称" class="!w-368px" clearable />
            <div bg="primary" h-32px w-32px flex-center cursor-pointer style="border-radius: 0 3px 3px 0;" @click="getTags">
              <SvgIcon
                local-icon="slmc-icon-search"
                size="16"
                style="color: #fff; cursor: pointer;"
              />
            </div>
          </n-input-group>
        </div>

        <n-button v-if="isTopLevel" type="primary" @click="addTags">
          新增标签
        </n-button>
      </div>

      <el-table
        stripe
        class="mt-[20px]" :data="tableData" style="width: 100%"
      >
        <el-table-column label="序号" type="index" width="100" />
        <el-table-column label="标签名称">
          <template #default="{ row }">
            <div flex items-center gap-6px>
              <div>{{ row.tagName }}</div>
              <img v-if="row.tagCategory === 'REGULATION'" width="16" height="16" src="@/assets/images/tag-default.png" alt="">
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="isEnable" label="状态">
          <template #default="{ row }">
            <div flex items-center gap-6px>
              <div
                class="h-6px w-6px b-rd-50%" :class="[row.isEnable ? 'bg-#4acfb1' : 'bg-#ccc']"
              />
              <div>
                {{ row.isEnable ? '启用' : '停用' }}
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column v-if="isTopLevel" label="操作">
          <template #default="{ row }">
            <div flex items-center gap-6px>
              <div uno-link @click="handleEnable(row)">
                {{ row.isEnable ? '停用' : '启用' }}
              </div>
              <template v-if="row.tagCategory !== 'REGULATION'">
                <div h-14px w-1px bg="#3B8FD9" />
                <div
                  uno-link
                  @click="changeTags(row)"
                >
                  修改
                </div>
                <div h-14px w-1px bg="#3B8FD9" />
                <div
                  uno-link
                  @click="deleteHandler(row)"
                >
                  删除
                </div>
              </template>
            </div>
          </template>
        </el-table-column>
        <template #empty>
          <div flex justify-center>
            <DataEmpty />
          </div>
        </template>
      </el-table>

      <div v-if="page.total > 0" mt-20px w-full flex justify-end>
        <n-pagination
          v-model:page="page.page"
          v-model:page-size="page.size"
          :item-count="page.total"
          :page-sizes="[5, 10, 20, 30]"
          show-size-picker
          show-quick-jumper
          @update:page="getTags"
          @update:page-size="handleUpdatePageSize"
        />
      </div>
    </PageCard>
    <n-modal
      v-model:show="showModal"
      preset="card"
      :style="{ width: '540px' }"
      :title="`${isEditor ? '新增' : '修改'}标签`"
      head-style="divide"
    >
      <div h-60px>
        <div>
          <n-form
            ref="formRef"
            label-placement="left"
            class="my-24px ml-17px mr-30px"
            require-mark-placement="left"
            :model="formValue"
            :label-width="100"
            :rules="tagRule"
          >
            <n-form-item path="tagName" label="标签名称">
              <n-input
                v-model:value="formValue.tagName"
                placeholder="请输入"
                :maxlength="50"
              />
            </n-form-item>
          </n-form>
        </div>
      </div>
      <template #footer>
        <div ml-120px flex items-center>
          <n-button type="primary" @click="addTagHandler">
            保存
          </n-button>
          <n-button ml-20px @click="showModal = false">
            取消
          </n-button>
        </div>
      </template>
    </n-modal>
    <n-modal
      v-model:show="deleteConfirm"
      :mask-closable="false"
      preset="dialog"
      title="确定删除此标签吗？"
      positive-text="确定"
      negative-text="取消"
      @positive-click="onPositiveClick"
      @negative-click="deleteConfirm = false"
    />
  </div>
</template>
