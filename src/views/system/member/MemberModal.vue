<script lang='ts' setup>
import { match } from 'pinyin-pro'
import { isArray } from '@/utils/common/is'
import { BasicModal, useModalInner } from '@/components/Modal'
import { ButtonSearch } from '@/components/Search'
import { addRoleMemberApi, getRoleOtherUserApi } from '@/api/role'
import type { MemberListData, RoleOtherUserData } from '@/api/role'
import { RenderKeyString } from '@/components/business/UiRender'
import { SvgIcon } from '@/components/Icon'
import { EmptyList } from '@/components/Empty'

const emit = defineEmits(['refresh', 'register'])
const route = useRoute()
const ROLE_ID = route.query?.roleId as string

const [register, { closeModal }] = useModalInner()

const userResponseData = ref<RoleOtherUserData[]>([])
const searchKey = ref<string>('')
const checkedKeys = ref<string[]>([])
const expandedKeys = ref<string[]>([])
const checkedUserNode = ref<MemberListData[]>([])

/**
 * 搜索用户
 * @param query 搜索关键字
 */
function handleSearch(query: string) {
  searchKey.value = query
  getRoleOtherUser(query)
}
/** 重置弹窗参数 */
function resetFormValue() {
  checkedKeys.value = []
  checkedUserNode.value = []
}
/** 弹窗确认 */
function handleConfirm() {
  const userIds = checkedUserNode.value.map(user => user?.id)

  const params = {
    userIds,
    roleId: ROLE_ID,
  }
  if (userIds?.length > 0)
    addRoleMember(params)
  else
    window.$message.warning('请先选择用户')
}
/**
 * 新增角色下的成员
 * @param params {userIds:用户id,roleId:角色id}
 */
async function addRoleMember(params: { userIds: string[]; roleId: string }) {
  const res = await addRoleMemberApi(params)
  if (res.data) {
    window.$message.success('保存成功')
    closeModal()
    resetFormValue()
    emit('refresh', { start: 1 })
  }
}
/** 弹窗取消 */
function handleCancel() {
  resetFormValue()
}
/**
 * 弹窗打开/关闭回调
 * @param show 弹窗打开/关闭
 */
function visibleChange(show: boolean) {
  if (show)
    getRoleOtherUser('')

  else
    resetFormValue()
}
function recursionTree(tree: any[]) {
  const expandedKeys = new Set()
  function recursion(node: any) {
    if ((node?.children?.length === 0 || !node?.children) && !node?.userName) {
      node.prefix = () => h(SvgIcon, {
        localIcon: 'slmc-icon-wenjian1',
        class: 'text-06AEA6 -ml-8px',
        size: '16',
      })
      node.isLeaf = true
    }
    if (searchKey.value) {
      if (node?.expandStatus && node?.expandStatus === '1')
        expandedKeys.add(node.id)
    }
    else {
      expandedKeys.add(node.id)
    }
    if (node?.children)
      node.children.forEach(recursion)
  }

  tree.forEach(recursion)

  return {
    tree,
    expandedNodeKeys: [...Array.from(expandedKeys)],
  }
}
/** 获取机构/用户列表 */
async function getRoleOtherUser(name = '') {
  try {
    const params = {
      roleId: ROLE_ID,
      searchName: name,
    }
    const res = await getRoleOtherUserApi<RoleOtherUserData[]>(params)
    if (res?.data) {
      const { tree, expandedNodeKeys } = recursionTree(res?.data ?? [])
      userResponseData.value = tree

      expandedKeys.value = expandedNodeKeys
    }
  }
  catch (error) {
    console.log(error)
  }
}
/**
 * 获取当前用户拥有的角色数量
 * @param node 当前节点
 */
function getUserIncludeRoleSize(node: MemberListData) {
  if (node?.userName && node?.userRoleList && isArray(node.userRoleList))
    return node.userRoleList?.length
  else
    return 0
}
/**
 * 是否满足条件数量
 * @param node 当前节点
 */
function isConformToCount(node: MemberListData) {
  const conformCount = 5
  const size = getUserIncludeRoleSize(node)

  return size >= conformCount
}
/**
 * 搜索过滤函数
 * @param pattern 匹配字符串
 * @param node 当前节点
 */
function onFilterNode(pattern: string, node: any) {
  // TODO:优化 ts类型定义
  const { organName = '', userName = '' } = node
  const isStringMatch = organName.toLowerCase().includes(pattern.toLowerCase()) || userName.toLowerCase().includes(pattern.toLowerCase())
  const isPinyinMatch = match(organName, pattern)?.length > 0 || match(userName, pattern)?.length > 0
  return isStringMatch || isPinyinMatch
}

/**
 * 手动勾选函数
 * @param keys 选中的keys
 * @param option 选中的对象
 */
function onCheckedKeys(keys: Array<string>, option: Array<MemberListData>) {
  const userListNode = option.filter(node => node?.userName)

  checkedKeys.value = [...keys]
  checkedUserNode.value = userListNode
}

/** 清空 */
function clearAll() {
  resetFormValue()
}
/**
 * 取消单个节点的选中
 * @param node 点击的节点对象
 */
function clearNode(node: MemberListData) {
  const { id: nodeId } = node

  const arr = checkedKeys.value.filter(id => id !== nodeId)
  checkedKeys.value = [...arr]

  checkedUserNode.value = checkedUserNode.value.filter(node => node.id !== nodeId)
}
</script>

<template>
  <BasicModal
    v-bind="$attrs"
    title="新增成员"
    :width="560"
    :height="480"
    footer-button="barFooter"
    @register="register"
    @ok="handleConfirm"
    @cancel="handleCancel"
    @visible-change="visibleChange"
  >
    <div class="flex">
      <div>
        <div class="mr-20px">
          <ButtonSearch
            placeholder="请输入机构名称/用户名称查询"
            class="mb-14px ml-18px mt-24px"
            width="300px"
            @emit-search="handleSearch"
          />
        </div>
        <div class="h-410px overflow-auto pr-10px">
          <n-tree
            label-field="organName"
            key-field="id"
            checkbox-placement="right"
            :data="userResponseData"
            cascade checkable block-line
            :default-expanded-keys="expandedKeys"
            :checked-keys="checkedKeys"
            check-strategy="child"
            @update:checked-keys="onCheckedKeys"
          >
            <template #default="{ data }">
              <div class="custom-tree-node flex items-center">
                <SvgIcon
                  v-if="data?.userName"
                  local-icon="slmc-icon-user"
                  size="16"
                  color="#06AEA6"
                  class="mr-6px"
                />
                <RenderKeyString :max-width="240" :search-key="searchKey" :text="data?.userName ? data.userName : data?.organName" />
                <n-tooltip v-if="isConformToCount(data)" trigger="hover">
                  <template #trigger>
                    <SvgIcon

                      local-icon="slmc-icon-information"
                      size="16"
                      color="#000000"
                      class="ml-6px opacity-50"
                    />
                  </template>
                  当前用户角色已满5个
                </n-tooltip>
              </div>
            </template>
            <template #empty>
              <div class="h-full flex pt-200px">
                <EmptyList />
              </div>
            </template>
          </n-tree>
        </div>
      </div>
      <div class="mt-24px h-456px w-full border-l border-l-#CCCCCC">
        <div class="mb-10px ml-20px flex justify-between pr-32px">
          <SectionTitle>
            已选择（{{ checkedUserNode.length }}个）
          </SectionTitle>
          <span class="cursor-pointer text-#3B8FD9" @click="clearAll">清空</span>
        </div>
        <div v-if="checkedUserNode?.length" class="h-430px overflow-auto">
          <div v-for="node in checkedUserNode" :key="node.id" class="h-32px flex cursor-pointer items-center justify-between px-20px hover-bg-#FFFBE0">
            <span>{{ node.userName }}</span>
            <SvgIcon
              local-icon="slmc-icon-deletex2"
              size="10"
              @click="clearNode(node)"
            />
          </div>
        </div>
        <div v-else class="h-432px flex items-center justify-center">
          <n-empty :show-icon="false">
            <span class="ml-4px">无数据</span>
          </n-empty>
        </div>
      </div>
    </div>
  </BasicModal>
</template>

<style scoped lang="scss">

</style>
