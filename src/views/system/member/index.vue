<script lang='ts' setup>
import MemberModal from './MemberModal.vue'
import LookMember from './LookMember.vue'
import type { MemberListData, MemberListParams } from '@/api/role'
import { Breadcrumb } from '@/layouts/common'
import { PageTitle } from '@/components/Title'
import { BasicTable, RowAction } from '@/components/Table'
import { getRoleMemberApi, removeMemberApi } from '@/api/role'
import { useModal } from '@/components/Modal'
import type { FetchReload } from '@/components/Table'
import { useAuthStore } from '@/store'

const userInfo = useAuthStore().userInfo
// 当前登录用户ID
const LOGIN_USER_ID = userInfo?.id

const route = useRoute()
const ROLE_ID = route.query?.roleId as string

const tableRef = ref<InstanceType<typeof BasicTable> | null>(null)
const tableParams = reactive<MemberListParams>({
  searchName: '',
  size: 10,
  start: 1,
  roleId: ROLE_ID,

})

const columns = createColumns()
const actionColumns = createActionColumns()
// 分页器
const paginationReactive = reactive({
  page: 1,
  pageSize: 10,
  pageSizes: [10, 20, 50, 100],
})
/** 批量操作选中keys */
const checkedRowKeysRef = ref<string[]>([])
/** 创建表格列 */
function createColumns() {
  return [
    {
      type: 'selection',
      disabled(row: MemberListData) {
        return row.id === LOGIN_USER_ID
      },
    },
    {
      title: '序号',
      width: 80,
      render(_: MemberListData, index: number) {
        return index + 1
      },

    },
    {
      title: '姓名',
      key: 'userName',
      width: 100,

    },
    {
      title: '用户ID',
      key: 'userNumber',
      width: 110,
    },
    {
      title: '所在医院',
      key: 'hospitalName',
      // 最多限制十字
      width: 160,

    },
    {
      title: '所在部门',
      key: 'organName',
      // 最多限制十字
      width: 160,

    },
    {
      title: '职务',
      key: 'job',
      width: 110,

    },
    {
      title: '职称',
      key: 'title',
      width: 110,
    },

  ]
}
/** 操作列 */
function createActionColumns() {
  return {
    title: '操作',
    key: 'address',
    width: 100,
    fixed: 'right',
    render(row: MemberListData) {
      const { id = '' } = row
      return h(RowAction, {
        actions: [
          {
            label: '查看',
            onClick: () => handleLook(row),
            type: 'primary',
            text: true,
          },
          {
            label: '移除',
            onClick: () => handleRemove(id),
            type: 'primary',
            text: true,
            ifShow: showRemove(id),
          },

        ],
      })
    },
  }
}

// 加载表格数据
async function loadDataTable(res: { size: number; start: number }) {
  return getRoleMemberApi({ ...tableParams, ...res })
}

// 刷新表格
function onRefresh(params?: FetchReload) {
  tableRef.value?.reload(params)
}
function handleCheck(rowKeys: string[]) {
  checkedRowKeysRef.value = rowKeys
}

/**
 * 单个移除用户
 * @param userId 被移除的用户id
 */
function handleRemove(userId: string) {
  removeMember([userId])
}

/**
 * 是否显示移除按钮，不能移除当前登录用户
 * @param userId 当前行用户id
 */
function showRemove(userId: string) {
  return LOGIN_USER_ID !== userId
}

/** 批量移除 */
function batchRemove() {
  if (checkedRowKeysRef.value.length <= 0) {
    window.$message.warning('请先勾选需要移除的成员')
    return
  }

  showDeleteDialog(toRaw(checkedRowKeysRef.value))
}
/**
 * 移除用户二次确认弹窗
 * @param userIds 被移除的用户id
 */
function showDeleteDialog(userIds: string[]) {
  window.$dialog.warning({
    title: '确定从该角色中移除选中成员？',
    positiveText: '确定',
    negativeText: '取消',
    btnGhost: true,
    onPositiveClick: () => {
      removeMember(userIds)
    },
  })
}
/**
 * 移除用户函数
 * @param userIds 被移除的用户id
 */
async function removeMember(userIds: string[]) {
  // 移除后该用户不再拥有该角色权限，用户进行下一次请求时强制退出登录，再次登录时生效；
  const params = {
    userIds, roleId: ROLE_ID,
  }
  const res = await removeMemberApi(params)
  if (res?.data) {
    window.$message.success('移除成功')
    onRefresh({ isRemove: true })
  }
}

const [register, { openModal }] = useModal()
const memberModalRef = ref('')
/** 打开角色弹窗 */
function showCreateModal() {
  openModal()
}
const [registerLookMember, { openModal: showMemberDetailModal }] = useModal()

const memberInfo = ref <MemberListData | null>(null)
function handleLook(row: MemberListData) {
  memberInfo.value = { ...row }
  showMemberDetailModal()
}
</script>

<template>
  <div>
    <Breadcrumb route-name="system_memberManage" />
    <PageCard breadcrumb>
      <PageTitle class="mb-14px">
        成员管理
      </PageTitle>
      <div class="mb-14px flex">
        <n-button type="primary" class="mr-10px" @click="showCreateModal">
          新增成员
        </n-button>
        <n-button type="primary" ghost @click="batchRemove">
          批量移除
        </n-button>
      </div>
      <BasicTable
        ref="tableRef"

        class="basicTable"
        :columns="columns"
        :action-column="actionColumns"
        :request="loadDataTable"
        :row-key="(row:any) => row.id"
        :pagination="paginationReactive"
        :scroll-x="1100"
        striped
        @update:checked-row-keys="handleCheck"
      />
      <MemberModal ref="memberModalRef" @register="register" @refresh="onRefresh" />
      <LookMember :member-info="memberInfo" @register="registerLookMember" />
    </PageCard>
  </div>
</template>

<style scoped lang="scss">

</style>
