<script lang='ts' setup>
import { isArray } from '@/utils/common/is'
import { BasicModal, useModalInner } from '@/components/Modal'
import type { MemberListData } from '@/api/role'
import { formatNullValueToShortBar } from '@/utils/common'

interface Props {
  memberInfo: MemberListData | null
}

const props = withDefaults(defineProps<Props>(), {
  memberInfo: () => ({
    category: '',
    createBy: '',
    createTime: '',
    id: '',
    job: '',
    loginTime: '',
    medicalTeam: '',
    name: '',
    organId: '',
    organName: '',
    phone: '',
    remark: '',
    status: 0,
    title: '',
    updateTime: '',
    updateType: '',
    userName: '',
    userNumber: '',
    userPowerList: [],
    userRoleList: [],
    userType: '',
    visibleApplication: '',
    hospitalId: '',
    hospitalName: '',
  }),
})

const [register, { closeModal }] = useModalInner()

function handleCancel() {
  closeModal()
}

const computedRoleName = computed(() => {
  if (props.memberInfo?.userRoleList && isArray(props.memberInfo?.userRoleList))
    return props.memberInfo?.userRoleList?.map(role => role.name).join(',')
  else
    return null
})
</script>

<template>
  <BasicModal
    v-bind="$attrs"
    title="查看用户信息"
    :width="560"
    footer-button="singleClose"
    @register="register"
    @cancel="handleCancel"
  >
    <div class="px-20px py-24px">
      <n-descriptions label-placement="left" :column="2" :content-style="{ color: '#333333' }" :label-style="{ display: 'inline-block', textAlign: 'right' }">
        <n-descriptions-item label="用户ID" :label-style="{ display: 'inline-block', textAlign: 'right', width: '56px' }">
          {{ formatNullValueToShortBar(props.memberInfo?.userNumber) }}
        </n-descriptions-item>
        <n-descriptions-item label="姓名" :label-style="{ display: 'inline-block', textAlign: 'right', width: '84px' }">
          {{ formatNullValueToShortBar(props.memberInfo?.userName) }}
        </n-descriptions-item>
        <n-descriptions-item label="手机号" :label-style="{ display: 'inline-block', textAlign: 'right', width: '56px' }">
          {{ formatNullValueToShortBar(props.memberInfo?.phone) }}
        </n-descriptions-item>
        <n-descriptions-item label="所属机构" :label-style="{ display: 'inline-block', textAlign: 'right', width: '84px' }">
          {{ formatNullValueToShortBar(props.memberInfo?.organName) }}
        </n-descriptions-item>
        <n-descriptions-item label="职称" :label-style="{ display: 'inline-block', textAlign: 'right', width: '56px' }">
          {{ formatNullValueToShortBar(props.memberInfo?.title) }}
        </n-descriptions-item>
        <n-descriptions-item label="职务" :label-style="{ display: 'inline-block', textAlign: 'right', width: '84px' }">
          {{ formatNullValueToShortBar(props.memberInfo?.job) }}
        </n-descriptions-item>
        <n-descriptions-item label="角色" :label-style="{ display: 'inline-block', textAlign: 'right', width: '56px' }">
          <span class="display-block w-175px">
            {{ formatNullValueToShortBar(computedRoleName) }}
          </span>
        </n-descriptions-item>
        <n-descriptions-item label="所在医疗组" :label-style="{ width: '84px' }">
          {{ formatNullValueToShortBar(props.memberInfo?.medicalTeam) }}
        </n-descriptions-item>
      </n-descriptions>
    </div>
  </BasicModal>
</template>

<style scoped lang="scss">

</style>
