<script setup lang="ts">
import { useDebounceFn } from '@vueuse/core'
import { getDocGroupListAPI } from '~/src/api/statement'
import { useAuthStore } from '@/store'
import { userAllUser } from '@/hooks/useAllUser'
import { getOrganApi } from '~/src/api/organization/organization'
import type { OrganizationRes } from '~/src/api/organization/type'
import { SvgIcon } from '~/src/components/Icon'
import { addOrUpdateDoctorGroupAPI } from '~/src/api/system/basesetting'

const { userInfo } = useAuthStore()
const { userList } = userAllUser()
const tableData = ref<any[]>([])

async function getGroupList() {
  try {
    const { data } = await getDocGroupListAPI({
      organId: userInfo.organId,
    })
    tableData.value = data
  }
  catch (error) {

  }
}

const searchKey = ref('')
const expandedKeys = ref<string[]>([])
const organizationList = ref<OrganizationRes[]>([])
const isModalShow = ref(false)
const formValue = ref({
  id: null,
  doctorGroupName: null,
  leader: null,
  members: [],
})

function recursionTree(tree: OrganizationRes[]): any[] {
  const expandedKeys = new Set()
  function recursion(node: any) {
    if (node?.children?.length === 0 || !node?.children) {
      node.prefix = () => h(SvgIcon, {
        localIcon: 'slmc-icon-wenjian1',
        class: 'text-06AEA6 -ml-8px',
        size: '16',
      })
      node.isLeaf = true
    }

    if (searchKey.value) {
      if (node?.expandStatus && node?.expandStatus === '1')
        expandedKeys.add(node.id)
    }
    else {
      expandedKeys.add(node.id)
    }

    if (node?.children)
      node.children.forEach(recursion)
  }

  tree.forEach(recursion)

  return {
    tree,
    expandedNodeKeys: [...Array.from(expandedKeys)],
  } as any
}

const getOrganizations = useDebounceFn(async (name = '') => {
  /** 用户登陆时所属机构 */
  try {
    const params = {
      size: 1000,
      start: 1,
      searchName: name,
    }
    const res = await getOrganApi<OrganizationRes[]>(params)
    const { tree, expandedNodeKeys } = recursionTree(res?.data ?? []) as any

    organizationList.value = tree

    expandedKeys.value = expandedNodeKeys
  }
  catch (error) {
    console.log(error)
  }
}, 300)

function handleSearchOrgan(query: string) {
  getOrganizations(query)
}

getOrganizations()

/** 渲染最内层图标 */
function renderIcon() {
  return h(
    SvgIcon,
    {
      localIcon: 'slmc-icon-wenjian1',
      class: 'text-06AEA6',
      size: '16',
    },

  )
}

/** 选择的机构 */
const checkedOrgan = ref<any>({
  id: '',
  organName: '',
})

/** 节点选中的回调 */
function onSelectKeys(_keys: Array<string | number>, _option: Array<any | null>,
  meta: { node: any | null; action: 'select' | 'unselect' }) {
  const selectOrgan = meta.node
  // 大于二级机构才能被选择
  if (selectOrgan.organLevel > 2)
    checkedOrgan.value = selectOrgan
}

const formRef = ref<any>(null)
async function handleSaveOrAdd() {
  try {
    const isError = await formRef.value?.validate()
    if (isError)
      return false

    if (!checkedOrgan.value.id) {
      window.$message.warning('请选择所属机构')

      return false
    }

    const params = {
      organId: userInfo.organId,
      departmentId: checkedOrgan.value?.id,
      departmentName: checkedOrgan.value?.organName,
      doctorGroupName: formValue.value.doctorGroupName,
      leader: formValue.value.leader,
      users: formValue.value.members,
    }

    if (formValue.value.id) {
      const { data } = await addOrUpdateDoctorGroupAPI({
        ...params,
        id: formValue.value.id,
      })
      if (data) {
        window.$message.success('修改成功')
        isModalShow.value = false
        getGroupList()
      }
    }
    else {
      const { data } = await addOrUpdateDoctorGroupAPI(params)

      if (data) {
        window.$message.success('新增成功')
        isModalShow.value = false
        getGroupList()
      }
    }
  }
  catch (error) {

  }
}

function handleAddBtnClick() {
  formValue.value.doctorGroupName = null
  formValue.value.leader = null
  formValue.value.members = []
  checkedOrgan.value = {
    id: '',
    organName: '',
  }
  isModalShow.value = true
}

function handleEdit(row) {
  formValue.value.id = row.id
  formValue.value.doctorGroupName = row?.doctorGroupName
  formValue.value.leader = row.leader?.userId
  formValue.value.members = row.users?.map(i => i.userId)
  checkedOrgan.value.id = row?.departmentId
  checkedOrgan.value.organName = row?.departmentName
  isModalShow.value = true
}

onMounted(() => {
  getGroupList()
})
</script>

<template>
  <div>
    <n-button type="primary" mb-20px w-100px @click="handleAddBtnClick">
      新 增
    </n-button>
    <el-table show-overflow-tooltip :data="tableData">
      <el-table-column label="序号" width="80" type="index" />
      <el-table-column label="医生组名称" :formatter="({ doctorGroupName }) => doctorGroupName || '-'" />
      <el-table-column label="所属科室" :formatter="({ departmentName }) => departmentName || '-'" />
      <el-table-column label="组长" :formatter="({ leader }) => leader?.userName || '-'" />
      <el-table-column label="成员" :formatter="({ users }) => users?.map((i) => i.userName).join(',') || '-'" />

      <el-table-column label="操作" width="80" fixed="right">
        <template #default="{ row }">
          <span uno-link @click="handleEdit(row)">编辑</span>
        </template>
      </el-table-column>
    </el-table>

    <n-modal
      v-model:show="isModalShow"
      w-720px head-style="divide"
      preset="card"
      title="新增医生组"
    >
      <n-form
        ref="formRef"
        label-placement="left"
        class="my-24px ml-17px mr-30px"
        require-mark-placement="left"
        :model="formValue"
        :label-width="100"
      >
        <n-grid :cols="1">
          <n-form-item-gi :rule="[{ required: true, message: '请输入医生组名称' }]" path="doctorGroupName" label="医生组名称">
            <n-input v-model:value="formValue.doctorGroupName" show-count :maxlength="10" />
          </n-form-item-gi>
          <n-form-item-gi :rule="[{ required: true, message: '请选择组长' }]" path="leader" label="组长">
            <n-select v-model:value="formValue.leader" :options="userList" />
          </n-form-item-gi>
          <n-form-item-gi :rule="[{ required: true, message: '请选择成员' }]" path="members" label="成员">
            <n-select v-model:value="formValue.members" multiple max-tag-count="responsive" :options="userList" />
          </n-form-item-gi>
          <n-form-item-gi label="所属科室">
            <div class="h-320px w-full flex justify-start text-#333">
              <div class="w-326px border border-#d1d1d1 border-rd-3px">
                <div class="h-32px flex items-center bg-#E7F9F7 pl-10px">
                  选择科室
                </div>
                <div>
                  <n-input
                    v-model:value="searchKey"
                    placeholder="请输入科室名称搜索"
                    style="--n-border:1px solid transparent;
                    --n-border-focus:1px solid transparent;
                    --n-border-hover:1px solid transparent;
                    --n-box-shadow-focus:none"
                    @update:value="handleSearchOrgan"
                  >
                    <template #suffix>
                      <SvgIcon local-icon="slmc-icon-search" color="#ccc" class="cursor-pointer" size="16" />
                    </template>
                  </n-input>
                  <n-divider margin="0px 0px 0px 0px" class="px-10px" />
                </div>
                <div class="h-253px overflow-auto pt-10px">
                  <n-tree
                    label-field="organName"
                    key-field="id"
                    class="tree"
                    :data="organizationList"
                    :render-icon="renderIcon"
                    selectable block-line
                    :default-expanded-keys="expandedKeys"
                    @update:selected-keys="onSelectKeys"
                  >
                    <template #default="{ data }">
                      <div class="custom-tree-node flex items-center justify-between">
                        <RenderKeyString :max-width="200" :search-key="searchKey" :text="data?.organName " />
                        <n-radio
                          v-if="[3, 4].includes(data?.organLevel)"
                          class="pr-10px"
                          :checked="checkedOrgan?.id === data.id"
                          :value="data.id"
                          :name="data.organName"
                        />
                      </div>
                    </template>
                    <template #empty>
                      <div class="h-full flex pt-100px">
                        <EmptyList />
                      </div>
                    </template>
                  </n-tree>
                </div>
              </div>
              <div class="w-36px flex items-center justify-center">
                <SvgIcon local-icon="slmc-icon-crumbs1" color="#666" />
              </div>
              <div class="w-210px border border-#d1d1d1 border-rd-3px">
                <div class="h-32px flex items-center bg-#E7F9F7 pl-10px">
                  已选择 ({{ checkedOrgan ? 1 : 0 }}/1)
                </div>
                <div class="p-10px">
                  <n-ellipsis style="max-width: 200px">
                    {{ checkedOrgan?.organName }}
                  </n-ellipsis>
                </div>
              </div>
            </div>
          </n-form-item-gi>
          <n-form-item-gi label=" ">
            <n-button mr-20px type="primary" w-100px @click="handleSaveOrAdd">
              保 存
            </n-button>
            <n-button w-100px @click="isModalShow = false">
              取 消
            </n-button>
          </n-form-item-gi>
        </n-grid>
      </n-form>
    </n-modal>
  </div>
</template>
