<script setup lang="ts">
import DoctorGroup from './DoctorGroup.vue'
import { Breadcrumb } from '@/layouts/common'
</script>

<template>
  <div>
    <Breadcrumb route-name="systemBaseSetting" />

    <PageCard breadcrumb>
      <PageTitle class="mb-14px">
        基础设置
      </PageTitle>

      <n-tabs :width="100">
        <n-tab-pane name="医生组管理">
          <DoctorGroup />
        </n-tab-pane>
      </n-tabs>
    </PageCard>
  </div>
</template>
