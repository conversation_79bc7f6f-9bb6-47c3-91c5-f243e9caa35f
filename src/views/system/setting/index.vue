<script setup lang="ts">
// import LayoutBreadcrumb from '@/components/layouts/breadcrumb/index.vue'
import { BasicTable, RowAction } from '@/components/Table'

defineOptions({
  name: 'FollowTag',
})

// const { routerPush } = useRouterPush()

const columns = createColumns()

const showModal = ref(false)
const formRef = ref(null)

const shareList = ref([
  {
    value: true,
    label: '是',
  },
  {
    value: false,
    label: '否',
  },
])

// function goPatientFiles(row: any) {
//   routerPush({ name: 'patient_file' })
// }

function createColumns() {
  return [
    {
      title: '序号',
      key: 'index',
      render(_: any, index: number) {
        return index + 1
      },
    },
    {
      title: '机构名称',
      key: '机构名称',
      render() {
        return '111'
      },
    },
    {
      title: '复查提醒提前发送天数',
      key: '复查提醒提前发送天数',
    },
    {
      title: '数据是否共享',
      key: '数据是否共享',
      // render(row: any) {
      //   return h(RowAction, {
      //     actions: [
      //       {
      //         label: '数据是否共享',
      //         onClick: () => goPatientFiles(row),
      //         type: 'primary',
      //         text: true,
      //       },

      //     ],
      //   })
      // },
    },

  ]
}

const tableRef = ref<InstanceType<typeof BasicTable> | null>(null)
function loadDataTable() {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        data: {
          records: [
            { 机构名称: '1111', 复查提醒提前发送天数: '复查提醒提前发送天数', 数据是否共享: '111' },
            { 机构名称: '1111' },
            { 机构名称: '1111' },
            { 机构名称: '1111' },
            { 机构名称: '1111' },
          ],
        },
      })
    }, 1000)
  })
}
const actionColumns = createActionColumns()
function createActionColumns() {
  return {
    title: '操作',
    key: 'address',
    width: 90,
    render(row: any) {
      return h(RowAction, {
        actions: [
          {
            label: '就诊记录',
            onClick: () => goDetail(row),
            type: 'primary',
            text: true,
          },

        ],
      })
    },
  }
}
function goDetail(row: any) {
  showModal.value = true

  // routerPush({ name: 'liverData_patientDetail' })
}

function handleSearch() {

}

const formValue = ref({
  days: '',
  isShare: '',
})

const paginationReactive = reactive({
  page: 1,
  pageSize: 10,
  pageSizes: [10, 20, 30, 40],
})
</script>

<template>
  <div>
    <PageCard offset-bottom="60" class="h-[calc(100%)]">
      <PageTitle border class="flex">
        <div>标签管理</div>
      </PageTitle>
      <div mt-20px>
        <ButtonSearch
          placeholder="请输入机构名称"
          class="mb-14px"
          width="400px"
          @emit-search="handleSearch"
        />
      </div>
      <BasicTable
        ref="tableRef"

        :columns="columns"
        :request="loadDataTable"
        :row-key="(row:any) => row.id"
        :action-column="actionColumns"
        :pagination="paginationReactive"
      />
    </PageCard>
    <n-modal
      v-model:show="showModal"
      preset="card"
      :style="{ width: '540px' }"
      title="添加证书"
      head-style="divide"
    >
      <div h-100px>
        <div>
          <n-form
            ref="formRef"
            label-placement="left"
            class="my-24px ml-17px mr-30px"
            require-mark-placement="left"
            :model="formValue"
            :label-width="180"
          >
            <n-form-item path="adminName" label="复查提醒提前发送天数">
              <n-input
                v-model:value="formValue.days"
                placeholder="请输入"
                :maxlength="50"
                w-300px
              />
            </n-form-item>
            <n-form-item path="phone" label="数据是否共享">
              <n-radio-group v-model:value="formValue.isShare" name="radiogroup">
                <n-space item-style="display: flex;" :size="[40, 0]">
                  <n-radio v-for="(item, index) in shareList" :key="index" :value="item.value">
                    {{ item.label }}
                  </n-radio>
                </n-space>
              </n-radio-group>
            </n-form-item>
          </n-form>
        </div>
      </div>
      <template #footer>
        <div flex items-center justify-center>
          <n-button type="info">
            保存
          </n-button>
          <n-button ml-20px>
            取消
          </n-button>
        </div>
      </template>
    </n-modal>
  </div>
</template>

<style scoped lang="scss">
.addSubAdmin {
    position: relative;
    .permissionScope {
        display: flex;
        align-items: center;
        padding: 14px;
        background: #f5f5f5;
    }
}
</style>
