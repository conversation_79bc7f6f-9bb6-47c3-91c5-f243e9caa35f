<script lang='ts' setup>
import { onMounted } from 'vue'
import type { TreeInst } from 'wowjoy-vui'
import { useAuthStore } from '@/store'
import { getPermissionListApi } from '@/api/menu'
import { Breadcrumb } from '@/layouts/common'
import type { MemberListData, Results } from '@/api/role'
import { getRoleDetailApi, getRoleMemberApi, getRoleMenuIdApi } from '@/api/role'
import { isMasterAdmin } from '@/utils'

const auth = useAuthStore()
const { userInfo } = auth
const userName = ref('')

const roleList = userInfo?.roleList || []
const isMasterRole = roleList.some(role => isMasterAdmin(role?.remark))

const route = useRoute()
const router = useRouter()
const ROLE_ID = route.query?.roleId as string

const treeRef = ref<TreeInst | null>(null)

const permissionData: any = ref([])

async function getPermissionList() {
  const res = await getPermissionListApi()

  if (res?.data) {
    const childMenu = res.data
    recursionPermission(childMenu)
    permissionData.value = [
      {
        menuName: '全部权限',
        id: 'all',
        children: childMenu,
        prefix: () => undefined,
      },
    ]
  }
}
const checkedKeys = ref([])

function recursionPermission(tree: any) {
  function recursionTree(node: any) {
    node.prefix = () => undefined
    if (node?.children) {
      node.children?.forEach((item) => {
        recursionTree(item)
      })
    }
  }

  tree.forEach((node) => {
    recursionTree(node)
  })
}

const dataRange = ref('1')
const appointRangeList = ref(null)
const organList = ref([])
const roleDetail = ref({})

/* const DATA_RANGE_LIST = [
  { label: '所在机构', value: '1' },
  { label: '指定机构', value: '-1' },
  { label: '全部机构', value: '0' },
] */

// async function getOrganDataList() {
//   const res = await getOrganDataListApi()
//   if (res?.data)
//     organList.value = res.data
// }
async function getRoleDetail(roleId: string) {
  const res = await getRoleDetailApi(roleId)
  if (res?.data) {
    roleDetail.value = res.data
    if (['0', '1'].includes(res.data?.dataRange)) { dataRange.value = res.data.dataRange }
    else {
      dataRange.value = '-1'
      appointRangeList.value = res.data.dataRange?.split(',')
    }
  }
}
/**
 * 获取主管理的用户信息
 * @param roleId 角色id
 */
async function getUserByRoleId(roleId: string) {
  try {
    const { data } = await getRoleMemberApi<Results<MemberListData[]>>({ roleId, size: 10, start: 1 })

    if (data?.records && data.records.length > 0)

      userName.value = data.records[0]?.userName
  }
  catch (error) {
    console.log(error)
  }
}
async function getRoleMenuId(roleId: string) {
  const res = await getRoleMenuIdApi(roleId)
  if (res?.data)

    checkedKeys.value = res.data.map(menu => menu.menuId)
}
function init() {
  getPermissionList()
  getRoleDetail(ROLE_ID)
  getRoleMenuId(ROLE_ID)
  //   getOrganDataList()
  getUserByRoleId(ROLE_ID)
}

onMounted(() => {
  init()
})

function onBack() {
  router.back()
}
</script>

<template>
  <div>
    <Breadcrumb route-name="system_permissionEdit" />
    <PageCard breadcrumb>
      <PageTitle class="mb-14px">
        查看主管理员权限
      </PageTitle>
      <section class="mt-14px w-full flex items-center justify-start">
        <div class="w-74px text-right text-#666">
          角色：
        </div>
        <div>
          {{ roleDetail.name }}
        </div>
      </section>
      <section class="mt-14px w-full flex items-center justify-start">
        <div class="w-74px text-right text-#666">
          角色描述：
        </div>
        <div>
          {{ roleDetail.des }}
        </div>
      </section>
      <section class="mt-14px w-full flex items-center justify-start">
        <div class="w-74px text-right text-#666">
          主管理员：
        </div>
        <div>
          {{ userName }}
        </div>
      </section>
      <div class="mt-14px h-70% w-full flex items-start justify-start">
        <div class="mr-10px w-70px text-right text-#666">
          <span>系统权限</span>
        </div>
        <div class="treeBlock h-100% w-full overflow-auto border border-#d1d1d1">
          <n-tree
            ref="treeRef"
            key-field="id" label-field="menuName" class="tree"
            cascade block-line default-expand-all
            :data="permissionData"
          >
            <template #default="{ node, data, parent }">
              <div class="custom-tree-node">
                <n-ellipsis style="max-width: 1040px">
                  <span>{{ node.label }}</span>
                </n-ellipsis>
              </div>
            </template>
          </n-tree>
        </div>
      </div>

      <!-- <section class="mt-14px w-full flex items-center justify-start">
        <div class="mr-10px w-74px flex items-start justify-start">
          <SvgIcon class="" local-icon="slmc-icon-app_required1" color="#F36969" />
          <span>数据权限</span>
        </div>
        <div>
          <n-radio-group v-model:value="dataRange" name="radiogroup" class="">
            <n-space :size="[40, 0]" align="center">
              <div v-for="organ in DATA_RANGE_LIST" :key="organ.value" class="flex items-center justify-start">
                <n-radio class="items-center" :value="organ.value">
                  <div class="w-full flex items-center justify-start">
                    <span>{{ organ.label }}</span>
                  </div>
                </n-radio>
                <n-select
                  v-if="organ.value === '-1'"
                  v-model:value="appointRangeList"
                  :disabled="dataRange !== '-1'"
                  :options="organList"
                  style="width:273px"
                  class="ml-10px"
                  label-field="organName"
                  value-field="id"
                  multiple
                  :max-tag-count="3"
                />
              </div>
            </n-space>
          </n-radio-group>
        </div>
      </section> -->
      <div class="ml-74px mt-24px">
        <n-button @click="onBack">
          返&nbsp;&nbsp;回
        </n-button>
      </div>
    </PageCard>
  </div>
</template>

<style scoped lang="scss">
.treeBlock {
    border: 1px solid #ddd;

    padding-top: 5px;
    padding-bottom: 5px;
    :deep(.n-tree .n-tree-node){
        align-items: stretch;
    }
    .tree {
      :deep(.n-tree-node-content){
          padding-left: 0;
        //   margin-left: -6px;

      }
      :deep(.n-tree-node-switcher){
        margin: 0 3px 0 10px;
      }
  }
}
</style>
