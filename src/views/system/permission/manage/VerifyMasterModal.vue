<script lang='ts' setup>
import { nextTick } from 'vue'
import type { FormInst } from 'wowjoy-vui'
import { useMessage } from 'wowjoy-vui'
import { BasicModal, useModalInner } from '@/components/Modal'
import { getVerifyCodeApi, verifyCodeApi } from '@/api/user/user'

const props = defineProps({
  row: {
    type: Object as PropType<any>,
    default: () => {},
  },
})

const emit = defineEmits(['verifySuccess', 'register'])

const message = useMessage()

const formRef = ref<FormInst | null>(null)
const rules = {
  adminName: {
    required: true,
    trigger: ['blur'],
    message: '内容不能为空',
  },
  phone: {
    required: true,
    trigger: ['blur'],
    message: '内容不能为空',
  },

  code: {
    required: true,
    trigger: ['blur'],
    message: '内容不能为空',
  },

}
const defaultFormValue = {
  id: '',
  adminName: '',
  phone: '',
  code: '',

}
const formValue = ref({
  id: '',
  adminName: '',
  phone: '',
  code: '',

})

const [register, { closeModal }] = useModalInner()

// 验证
async function handleVerifyCode<T>(): Promise<Service.RequestResult<T>> {
  const params = {
    code: formValue.value.code,
    phone: formValue.value.phone,
  }
  try {
    const res = await verifyCodeApi<T>(params)
    return new Promise((resolve) => {
      resolve(res)
    })
  }
  catch (error) {
    return Promise.reject(error)
  }
}

// 验证
function handleConfirm() {
  formRef.value?.validate(async (errors) => {
    if (!errors) {
      const verifyCodeRes = await handleVerifyCode<string>()
      if (verifyCodeRes?.data) {
        closeModal()
        message.success('核验通过，请尽快更新主管理员！', { duration: 1000 })
        emit('verifySuccess', { code: verifyCodeRes?.data, id: formValue.value.id })
      }
    }
    else {
      console.log(errors)
    }
  })
}

function resetFormValue() {
  formValue.value = { ...defaultFormValue }
}

const COUNT_TIME = 60
const buttonText = ref('获取验证码')
const second = ref<number>(COUNT_TIME)
const disabled = ref(false)
let timer: any = null
// 获取验证码
async function getVerificationCode() {
  const params = {
    phone: formValue.value.phone,
  }

  const res = await getVerifyCodeApi<string>(params)

  if (res) {
    disabled.value = true
    buttonText.value = '重新获取'
    timer = setInterval(() => {
      second.value--

      if (second.value <= 0) {
        clearInterval(timer)
        disabled.value = false
        second.value = COUNT_TIME
      }
    }, 1000)
  }
}
onUnmounted(() => {
  clearInterval(timer)
  timer = null
})
function visibleChange(v: boolean) {
  v && props.row && nextTick(() => {
    formValue.value.adminName = props.row?.userName
    formValue.value.phone = props.row?.phone
    formValue.value.id = props.row?.id
    buttonText.value = '获取验证码'

    disabled.value = false
  })

  if (!v) {
    resetFormValue()
    clearInterval(timer)
    timer = null
    second.value = COUNT_TIME
  }
}
</script>

<template>
  <div class="">
    <BasicModal
      v-bind="$attrs"
      title="身份验证"
      :width="560"
      :footer-offset="70"
      ok-text="验证"
      @register="register"
      @ok="handleConfirm"

      @visible-change="visibleChange"
    >
      <n-alert type="info">
        换绑主管理员账号需验证当前主管理员信息
      </n-alert>
      <n-form
        ref="formRef"
        label-placement="left"
        class="my-24px ml-17px mr-30px"
        require-mark-placement="left"
        :model="formValue"
        :rules="rules"
        :label-width="120"
      >
        <n-form-item path="adminName" label="当前主管理员">
          <n-input
            v-model:value="formValue.adminName"
            placeholder="请输入"
            :maxlength="50"
            disabled
            style="width:320px;"
          />
        </n-form-item>
        <n-form-item path="phone" label="手机号">
          <n-input
            v-model:value="formValue.phone"
            placeholder="请输入"
            :maxlength="11"
            style="width:212px;"
            disabled
          />
          <n-button v-if="disabled" class="ml-10px" disabled>
            {{ second }}秒
          </n-button>
          <n-button v-else class="ml-10px" type="primary" @click="getVerificationCode">
            {{ buttonText }}
          </n-button>
        </n-form-item>
        <n-form-item path="code" label="验证码">
          <n-input
            v-model:value="formValue.code"
            placeholder="请输入"
            :maxlength="10"
            style="width:320px;"
          />
        </n-form-item>
      </n-form>
    </BasicModal>
  </div>
</template>

<style scoped lang="scss">

</style>
