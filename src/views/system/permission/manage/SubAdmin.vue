<script lang='ts' setup>
import dayjs from 'dayjs'
import { useDialog, useMessage } from 'wowjoy-vui'
import { BasicTable, RowAction } from '@/components/Table'
import { ButtonSearch } from '@/components/Search'
import { deleteAdminApi, getUser<PERSON>pi } from '@/api/user/user'
import { isArray } from '@/utils/common'
import type { UserPageListRecord, UserPageListRes, UserPowerList } from '@/api/user/type'
import type { FetchReload } from '@/components/Table/src/types/table'
import { useRouterPush } from '@/hooks'
import { localStg } from '@/utils'

const userInfo = localStg.get('userInfo')
// 当前登录用户ID
const userType = userInfo?.userType

const { routerPush } = useRouterPush()

const dialog = useDialog()
const message = useMessage()

const tableParams = reactive({
  size: 10,
  start: 1,
  userType: '1',
  searchName: '',
  sort: 'desc',
})

function goAddSubPage() {
  routerPush({ name: 'system_permissionEdit' })
}
function handleLook(row: UserPageListRecord) {
  const { id } = row
  routerPush({ name: 'system_permissionLook', query: { id } })
}
function handleEdit(row: UserPageListRecord) {
  const { id } = row

  routerPush({ name: 'system_permissionEdit', query: { id } })
}

function handleDelete(id: string) {
  dialog.warning({
    title: '确定删除该管理员吗？',
    positiveText: '确定',
    negativeText: '取消',
    btnGhost: true,
    positiveButtonProps: {
      type: 'primary',
    },
    negativeButtonProps: {
      type: 'primary',
    },
    onPositiveClick: async () => {
      const res = await deleteAdminApi(id)
      if (res.data) {
        message.success('删除成功')
        onRefresh({ isRemove: true })
      }
    },
  })
}
// 加载表格数据
async function loadDataTable(res: { size: number; start: number }) {
  return await getUserApi<UserPageListRes>({ ...tableParams, ...res })
}
// 格式化时间
function formatTime(time: string) {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}
// 权限数量
const PERMISSION_COUNT = 4
// 是否拥有全部权限
function haveAllPermission(permission: string[]) {
  return permission.length === PERMISSION_COUNT
}
// 拥有的权限
function havePermission(permission: UserPowerList[]) {
  if (!isArray(permission))
    return []
  const arr = permission.map(item => item.menuName)
  return arr
}
function createColumns() {
  return [

    {
      title: '用户ID ',
      key: 'userNumber',
      width: 150,
    },
    {
      title: '子管理员 ',
      key: 'userName',
      width: 120,
    },
    {
      title: '所属机构 ',
      key: 'organName',
      width: 200,

    },
    {
      title: '手机号',
      key: 'phone',
      width: 140,
    },
    {
      title: '拥有权限 ',
      key: 'visibleApplication',

      render(row: UserPageListRecord) {
        const { userPowerList } = row

        const permissions = havePermission(userPowerList)

        const isAll = haveAllPermission(permissions)

        return userPowerList
          ? isAll ? '全部权限' : permissions.join('，')
          : '-'
      },
    },
    {
      title: '创建时间 ',
      key: 'createTime',
      width: 170,
      render(row: UserPageListRecord) {
        const { createTime } = row
        return formatTime(createTime)
      },
    },

  ]
}

/**
 * 是否显示编辑按钮，只有主管理可以
 *
 */
function showEditButton() {
  return userType === '0'
}

/**
 * 是否显示删除按钮，只有主管理可以
 *
 */
function showDeleteButton() {
  return userType === '0'
}
function createActionColumns() {
  return {
    title: '操作',
    key: 'address',
    width: 160,
    fixed: 'right',
    render(row: UserPageListRecord) {
      return h(RowAction, {
        actions: [
          {
            label: '查看',
            onClick: () => handleLook(row),
            type: 'primary',
            text: true,
          },
          {
            label: '编辑',
            onClick: () => handleEdit(row),
            type: 'primary',
            text: true,
            ifShow: showEditButton(),
          },
          {
            label: '删除',
            onClick: () => handleDelete(row.id),
            type: 'primary',
            text: true,
            ifShow: showDeleteButton(),
          },

        ],
      })
    },
  }
}

const tableRef = ref<InstanceType<typeof BasicTable> | null>(null)
const columns = createColumns()
const actionColumns = createActionColumns()
// 分页器
const paginationReactive = reactive({
  page: 1,
  pageSize: 10,
  pageSizes: [10, 20, 50, 100],
})
function onRefresh(params?: FetchReload) {
  tableRef?.value?.reload(params)
}
function handleSearch(query: string) {
  tableParams.searchName = query
  tableRef?.value?.fetch({ start: 1 })
}
</script>

<template>
  <div class="subAdmin">
    <ButtonSearch
      placeholder="请输入用户ID/用户姓名/手机号"
      class="mb-14px"
      width="400px"
      @emit-search="handleSearch"
    />
    <n-button type="primary" class="mb-14px" :disabled="userType !== '0'" @click="goAddSubPage">
      添加子管理员
    </n-button>
    <BasicTable
      ref="tableRef"

      :columns="columns"
      :request="loadDataTable"
      :row-key="(row:any) => row.id"
      :action-column="actionColumns"
      :pagination="paginationReactive"
      :scroll-x="1000"
      striped
    />
  </div>
</template>

<style scoped lang="scss">
.subAdmin {
    margin-top: 2px;
    padding-left: 14px;
    padding-right: 14px;
}
</style>
