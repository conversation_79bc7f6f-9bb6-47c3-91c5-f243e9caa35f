<script lang='ts' setup>
import MasterAdmin from './MasterAdmin.vue'
import SubAdmin from './SubAdmin.vue'
import { SvgIcon } from '@/components/Icon'
import { Breadcrumb } from '@/layouts/common'
</script>

<template>
  <div>
    <Breadcrumb route-name="system_permissionManage" />
    <PageCard breadcrumb padding="0px 0px 0px 0px">
      <n-tabs animated type="card">
        <n-tab-pane name="a" tab="主管理员">
          <MasterAdmin />
        </n-tab-pane>
        <n-tab-pane name="b" tab="子管理员">
          <SubAdmin />
          <template #tab>
            <span class="mr-6px">子管理员</span>
            <n-popover trigger="hover" raw arrow-style="background-color:rgba(0,0,0,0.70);left:170px" placement="bottom">
              <template #trigger>
                <SvgIcon local-icon="slmc-icon-information" size="16" color="#ccc" />
              </template>
              <div
                style="background-color:rgba(0,0,0,0.70);
                     color:#fff;
                     padding:15px 20px 10px 20px;
                     width:340px;height:60px;
                     box-sizing: border-box;
                     font-size: 12px;"
              >
                由主管理员委派或授权的一类管理员角色，可以在被授权的范围内行使其对应的权利。请谨慎操作。
              </div>
            </n-popover>
          </template>
        </n-tab-pane>
      </n-tabs>
    </PageCard>
  </div>
</template>

<style scoped lang="scss">

</style>
