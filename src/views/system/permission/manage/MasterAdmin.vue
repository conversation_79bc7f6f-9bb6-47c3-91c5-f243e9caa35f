<script lang='ts' setup>
import dayjs from 'dayjs'
import TransferMasterModal from './TransferMasterModal.vue'
import VerifyMasterModal from './VerifyMasterModal.vue'
import { isArray } from '@/utils/common'
import { localStg } from '@/utils'
import { BasicTable, RowAction } from '@/components/Table'
import { getUserApi } from '@/api/user/user'
import { useModal } from '@/components/Modal'
import type { UserPageListRecord, UserPageListRes, UserPowerList } from '@/api/user/type'

const userInfo = localStg.get('userInfo')
// 当前登录用户ID
const userType = userInfo?.userType

const tableParams = reactive({
  size: 10,
  start: 1,
  userType: '0',
})

const tableRef = ref<InstanceType<typeof BasicTable> | null>(null)
const columns = createColumns()
const actionColumns = createActionColumns()

// 当前操作行数据
const rowData = ref({})

// 加载表格数据
async function loadDataTable(res: { size: number; start: number }) {
  return await getUserApi<UserPageListRes>({ ...tableParams, ...res })
}

// 格式化时间
function formatTime(time: string) {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}
// 权限数量
const PERMISSION_COUNT = 4

// 是否拥有全部权限
function haveAllPermission(permission: string[]) {
  return permission.length === PERMISSION_COUNT
}
// 拥有的权限
function havePermission(permission: UserPowerList[]) {
  if (!isArray(permission))
    return []
  const arr = permission.map(item => item.menuName)
  return arr
}
// 转让的弹窗
const [registerTransfer, { openModal: openModalTransfer }] = useModal()

// 验证的弹窗
const [registerVerify, { openModal: openModalVerify }] = useModal()

function handleMakeOver(row: UserPageListRecord) {
  rowData.value = { ...row }

  openModalVerify()
}

function createColumns() {
  return [

    {
      title: '用户ID ',
      key: 'userNumber',
      width: 150,
    },
    {
      title: '主管理员 ',
      key: 'userName',
      width: 150,
    },
    {
      title: '所属机构 ',
      key: 'organName',
      width: 200,
    },
    {
      title: '手机号',
      key: 'phone',
    },
    {
      title: '拥有权限 ',
      key: 'visibleApplication',
      render(row: UserPageListRecord) {
        const { userPowerList } = row

        const permissions = havePermission(userPowerList)
        const isAll = haveAllPermission(permissions)

        // return isAll ? '全部权限' : permissions.join('，')
        return '全部权限'
      },
    },
    {
      title: '创建时间 ',
      key: 'createTime',
      render(row: UserPageListRecord) {
        const { createTime } = row
        return formatTime(createTime)
      },
    },

  ]
}
/**
 * 是否显示转让按钮，只有主管理可以
 */
function showMakeOverButton() {
  return userType === '0'
}
function createActionColumns() {
  return {
    title: '操作',
    key: 'address',
    width: 90,
    render(row: any) {
      return h(RowAction, {
        actions: [
          {
            label: '转让',
            onClick: () => handleMakeOver(row),
            type: 'primary',
            text: true,
            ifShow: showMakeOverButton(),
          },

        ],
      })
    },
  }
}

const transferInfo = ref({
  code: '',
  id: '',
})

let timer: any = null
function onVerifySuccess({ code, id }: { code: string; id: string }) {
  transferInfo.value.code = code
  transferInfo.value.id = id
  timer = setTimeout(() => {
    openModalTransfer()

    clearTimeout(timer)
  }, 500)
}

function onTransferSuccess() {
  tableRef.value?.reload()
}
onUnmounted(() => {
  clearTimeout(timer)
})
</script>

<template>
  <div class="masterAdmin">
    <BasicTable
      ref="tableRef"

      :columns="columns"
      :request="loadDataTable"
      :row-key="(row:any) => row.id"
      :action-column="actionColumns"
      :pagination="false"
      :scroll-x="800"
    />
    <VerifyMasterModal :row="rowData" @register="registerVerify" @verify-success="onVerifySuccess" />
    <TransferMasterModal :transfer-info="transferInfo" @register="registerTransfer" @transfer-success="onTransferSuccess" />
  </div>
</template>

<style scoped lang="scss">
.masterAdmin {
    margin-top: 2px;
    padding-left: 14px;
    padding-right: 14px;
}
</style>
