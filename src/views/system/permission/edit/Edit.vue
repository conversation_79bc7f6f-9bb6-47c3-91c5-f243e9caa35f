<script lang='ts' setup>
import { onMounted } from 'vue'
import type { TreeInst, TreeOption } from 'wowjoy-vui'

import { getPermissionListApi } from '@/api/menu'
import { Breadcrumb } from '@/layouts/common'
import { getRoleDetail<PERSON>pi, getRoleMenuIdApi, updateRole<PERSON><PERSON>, updateRoleMenuIdApi } from '@/api/role'
import { getOrganDataListApi } from '@/api/organization/organization'
import { SvgIcon } from '@/components/Icon'

const route = useRoute()
const router = useRouter()
const ROLE_ID = route.query?.roleId as string

const treeRef = ref<TreeInst | null>(null)

// 默认展开的节点
const defaultCheckedKeys = new Set<string>()
// defaultCheckedKeys.add('5')

const permissionData: any = ref([])

async function getPermissionList() {
  const res = await getPermissionListApi()

  if (res?.data) {
    const childMenu = res.data
    recursionPermission(childMenu)

    permissionData.value = [
      {
        menuName: '全部权限',
        id: 'all',
        children: childMenu,
        prefix: () => undefined,
      },
    ]
  }
}
const checkedKeys = ref([])

function recursionPermission(tree: any) {
//   const nodeId = []
  function recursionTree(node: any) {
    node.prefix = () => undefined
    if (node?.children) {
      node.children?.forEach((item) => {
        recursionTree(item)
      })
    }
    // else {
    //   node.def === 1 && nodeId.push(node.id)

    //   node.checkboxDisabled = node.def === 1
    // }
  }

  tree.forEach((node) => {
    recursionTree(node)
  })

//   return nodeId
}

const dataRange = ref('1')
const appointRangeList = ref(null)
const organList = ref([])
const roleDetail = ref({})

const DATA_RANGE_LIST = [
  { label: '所在机构', value: '1' },
  { label: '指定机构', value: '-1' },
  { label: '全部机构', value: '0' },
]

async function getOrganDataList() {
  const res = await getOrganDataListApi()
  if (res?.data)
    organList.value = res.data?.filter(organ => organ.organLevel === 2)
}
async function getRoleDetail(roleId: string) {
  const res = await getRoleDetailApi(roleId)
  if (res?.data) {
    roleDetail.value = res.data
    if (['0', '1'].includes(res.data?.dataRange)) { dataRange.value = res.data.dataRange }
    else {
      dataRange.value = '-1'
      appointRangeList.value = res.data.dataRange?.split(',')
    }
  }
}
async function getRoleMenuId(roleId: string) {
  const res = await getRoleMenuIdApi(roleId)
  if (res?.data)
    checkedKeys.value = res.data.filter(menu => menu.status === 1).map(menu => menu.menuId)
}
function init() {
  getPermissionList()
  getRoleDetail(ROLE_ID)
  getRoleMenuId(ROLE_ID)
  getOrganDataList()
}

onMounted(() => {
  init()
})

function onCheckedKeys(keys: Array<string | number>, option: Array<TreeOption | null>) {
  checkedKeys.value = [...keys]
}
function onBack() {
  router.back()
}
function getIndeterminateKeys() {
  return treeRef?.value?.getIndeterminateData().keys
}
async function onConfirm() {
  const indeterminateKeys = getIndeterminateKeys()
    .filter(key => key !== 'all')
    .map(key => ({ roleId: ROLE_ID, menuId: key, status: 0 }))
  const allCheckedKeys = toRaw(checkedKeys.value)
    .filter(key => key !== 'all')
    .map(key => ({ roleId: ROLE_ID, menuId: key, status: 1 }))

  const roleMenuList = [...indeterminateKeys, ...allCheckedKeys]

  const updateRangeParams = {
    dataRange: dataRange.value,
    id: ROLE_ID,
  }
  if (dataRange.value === '-1')
    updateRangeParams.dataRange = toRaw(appointRangeList.value).join(',')

  const updateMenuPayload = {
    roleMenuList,
    id: ROLE_ID,
  }

  const res = [await updateRoleApi(updateRangeParams), await updateRoleMenuIdApi(updateMenuPayload)]

  const isSuccess = res.every(({ data }) => !!data)
  if (isSuccess) {
    window.$message.success('保存成功')
    onBack()
  }
}
</script>

<template>
  <div>
    <Breadcrumb route-name="system_permissionEdit" />
    <PageCard breadcrumb>
      <PageTitle class="mb-14px">
        权限设置
      </PageTitle>
      <section class="mt-14px w-full flex items-center justify-start">
        <div class="w-84px text-right text-#666">
          角色：
        </div>
        <div>
          {{ roleDetail.name }}
        </div>
      </section>
      <section class="mt-14px w-full flex items-center justify-start">
        <div class="w-84px text-right text-#666">
          角色描述：
        </div>
        <div>
          {{ roleDetail.des }}
        </div>
      </section>
      <div class="mt-14px h-70% w-full flex items-start justify-start">
        <div class="mr-10px w-84px flex items-start justify-start">
          <SvgIcon class="" local-icon="slmc-icon-app_required1" color="#F36969" />
          <span class="text-#666">系统权限</span>
        </div>
        <div class="treeBlock h-100% w-full overflow-auto border border-#d1d1d1">
          <n-tree
            ref="treeRef"
            key-field="id" label-field="menuName" class="tree"
            checkable cascade block-line default-expand-all
            :data="permissionData"
            :checked-keys="checkedKeys"

            @update:checked-keys="onCheckedKeys"
          >
            <template #default="{ node, data, parent }">
              <div class="custom-tree-node">
                <n-ellipsis style="max-width: 1040px">
                  <span>{{ node.label }}</span>
                  <!-- ————{{ data.id }} - - - <span class="text-20px">{{ data.power }}</span> -->
                </n-ellipsis>
              </div>
            </template>
          </n-tree>
        </div>
      </div>

      <section class="mt-14px w-full flex items-center justify-start">
        <div class="mr-10px w-84px flex items-start justify-start">
          <SvgIcon class="" local-icon="slmc-icon-app_required1" color="#F36969" />
          <span class="text-#666">数据权限</span>
        </div>
        <div>
          <n-radio-group v-model:value="dataRange" name="radiogroup" class="">
            <n-space :size="[40, 0]" align="center">
              <div v-for="organ in DATA_RANGE_LIST" :key="organ.value" class="flex items-center justify-start">
                <n-radio class="items-center" :value="organ.value">
                  <div class="w-full flex items-center justify-start">
                    <span>{{ organ.label }}</span>
                  </div>
                </n-radio>
                <n-select
                  v-if="organ.value === '-1'"
                  v-model:value="appointRangeList"
                  :disabled="dataRange !== '-1'"
                  :options="organList"
                  style="width:273px"
                  class="ml-10px"
                  label-field="organName"
                  value-field="id"
                  multiple
                  :max-tag-count="3"
                />
              </div>
            </n-space>
          </n-radio-group>
        </div>
      </section>
      <div class="ml-95px mt-24px">
        <n-button type="primary" class="mr-16px" @click="onConfirm">
          保&nbsp;&nbsp;存
        </n-button>
        <n-button @click="onBack">
          取&nbsp;&nbsp;消
        </n-button>
      </div>
    </PageCard>
  </div>
</template>

<style scoped lang="scss">
.treeBlock {
    border: 1px solid #ddd;

    padding-top: 5px;
    padding-bottom: 5px;
    :deep(.n-tree .n-tree-node){
        align-items: stretch;
    }
    .tree {
      :deep(.n-tree-node-content){
          padding-left: 0;
        //   margin-left: -6px;

      }
      :deep(.n-tree-node-switcher){
        margin: 0 3px 0 10px;
      }
  }
}
</style>
