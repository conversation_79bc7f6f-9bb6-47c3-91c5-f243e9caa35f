<script lang='ts' setup>
import { onMounted } from 'vue'
import { getPermissionListApi } from '@/api/menu'
import { Breadcrumb } from '@/layouts/common'
import type { BasicTable } from '@/components/Table'

interface TreeNode {
  id: any
  menuName: any
  menuType: any
  orderNum: any
  parentId: any
  power: any
  level: number
  children?: TreeNode[]
}

function flattenTree(tree: TreeNode[]): any[] {
  const flattened: any[] = []

  function getRolSpanLength(node: TreeNode, length: number) {
    const spanCount = 0
    if (node?.children) {
      node.children.forEach((child: TreeNode) => {
        return getRolSpanLength(child, spanCount)
      })
    }
    else {
      return spanCount
    }
  }

  function flatten(node: TreeNode, level: number) {
    let treeLevel = level
    const rolSpanLength = 1
    /*
    判断该列单元格占几个
    1.是否有children？
     - 没有，占1行
     - 有，判断children的数量然后算 占几行

    */

    const obj = {
      id: node.id,
      menuName: node.menuName,
      menuNameV2: node.menuName,
      menuNameV3: node.menuName,
      menuNameV4: node.menuName,
      menuType: node.menuType,
      orderNum: node.orderNum,
      parentId: node.parentId,
      power: node.power,
      level: treeLevel,
      rowSpan: 1,
      length: node?.children?.length,
    }
    flattened.push(obj)
    if (node?.children) {
      treeLevel = level + 1
      node.children.forEach((child: TreeNode) => {
        flatten(child, treeLevel)
      })
    }
  }

  tree.forEach((node: TreeNode) => {
    flatten(node, 0)
  })
  return flattened
}

const permissionData = ref([])

async function getPermissionList() {
  const res = await getPermissionListApi()

  if (res?.data) {
    const flattenedTree = flattenTree(res.data)

    permissionData.value = flattenedTree
  }
}

const tableRef = ref<InstanceType<typeof BasicTable> | null>(null)
const columns = createColumns()

function createColumns() {
  const getRolSpanByPower = {
    'lddp:dataScreen': 2,
  }
  return [
    {
      type: 'selection',
    },
    {
      title: '一级分类',
      tree: false,
      key: 'menuName',
      titleRowSpan: 4,
      rowSpan: (rowData) => {
        // console.log(rowData)
        const { power, length } = rowData

        return length ? 1 + length : 1
      },

    },
    {
      title: '二级分类',
      key: 'menuNameV2',
      // 角色名称最多限制十字
      rowSpan: (rowData) => {
        // console.log(rowData)
        const { power, length } = rowData

        return length ? 1 + length : 1
      },

    },
    {
      title: '三级分类',
      key: 'menuNameV3',
      // 多限制二十个字

    },
    {
      title: '四级分类',
      key: 'menuNameV4',

    },

  ]
}

function init() {
  getPermissionList()
}

onMounted(() => {
  init()
})
</script>

<template>
  <div>
    <Breadcrumb route-name="system_permissionEdit" />
    <PageCard breadcrumb>
      <PageTitle class="mb-14px">
        权限设置
      </PageTitle>

      <n-data-table
        ref="tableRef"
        :columns="columns"
        :data="permissionData"
        :row-key="(row:any) => row.id"
        :bordered="true"
        children-key="null"
        :single-line="false"
        :single-column="false"
      />
    </PageCard>
  </div>
</template>

<style scoped lang="scss">

</style>
