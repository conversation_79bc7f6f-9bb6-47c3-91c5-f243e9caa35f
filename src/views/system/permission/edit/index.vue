<script lang='ts' setup>
import type { FormInst, TreeInst } from 'wowjoy-vui'
import { useMessage } from 'wowjoy-vui'
import { useRouter } from 'vue-router'
import { useDebounceFn } from '@vueuse/core'
import { PageTitle } from '@/components/Title'
import { getUser<PERSON>pi, insertAdmin<PERSON>pi, userDetail<PERSON>pi } from '@/api/user/user'
import { getMenusApi } from '@/api/system'
import type { MenuRes } from '@/api/system/type'
import type { BreadLit } from '@/layouts/common'
import type { UserDetailRes, UserPageListRes, UserPowerList, UserPowerListParams } from '@/api/user/type'

import { Breadcrumb, LayoutFooter } from '@/layouts/common'

/** 管理员下拉 */
interface OptionsMasterType {
  label: string
  value: string

}
/**  用户权限列表 */
interface UserPowerListType {
  id: string
  menuName: string
  power?: string
  menuType?: string
  orderNum?: number
  parentId?: string
  children?: UserPowerListType[]
  prefix?: any
}

/** 权限id对应的Map */
interface UserPermissionMap {
  userId: string
  powerRange: string

}

const message = useMessage()
const router = useRouter()
const route = useRoute()

const treeRef = ref<TreeInst | null>(null)

// 是否是编辑
const isEdit = computed(() => {
  return route.query?.id !== undefined
})

const formRef = ref<FormInst | null>(null)
const rules = {
  adminId: {
    required: true,
    trigger: ['blur'],
    message: '内容不能为空',
  },

}

const formValue = ref<{ adminId: null | string; adminName: string }>({
  adminId: null,
  adminName: '',
})

// 管理员下拉列表
const optionsAdmin = ref<OptionsMasterType[]>([])
const loadingAdmin = ref(false)

/**
 * 根据关键字搜寻获取管理员
 * @param searchName 搜索关键字
 */
async function getUser<T>(searchName = ''): Promise<T> {
  const params = {
    size: 1000,
    start: 1,
    userType: '2',
    searchName,
  }
  try {
    const res = await getUserApi<UserPageListRes>(params)
    if (res?.data) {
      const { records = [] } = res.data
      const createOptions = records?.map((item) => {
        return {
          label: item.userName,
          value: item.id,
          organName: item.organName,
          organId: item.organId,
        }
      })
      return new Promise((resolve) => {
        resolve(createOptions as T)
      })
    }
    else {
      return new Promise((resolve) => {
        resolve([] as T)
      })
    }
  }
  catch (error) {
    return Promise.reject(error)
  }
}

// 搜索的文本
const searchText = ref('')

/**
 * 管理员下拉列表显示/关闭触发回调
 * @param show 下拉列表显示/关闭
 */
async function onUpdateShowAdmin(show: boolean) {
  if (show) {
    try {
      searchText.value = ''
      const res = await getUser<OptionsMasterType[]>()
      optionsAdmin.value = res ?? []
    }
    catch (error) {

    }
  }
}
// 搜索做防抖
const debouncedFn = useDebounceFn(async (query: string) => {
  try {
    searchText.value = query
    const res = await getUser<OptionsMasterType[]>(query)
    optionsAdmin.value = res ?? []
  }
  catch (error) {
    console.log(error)
  }
}, 300)

// 搜索子管理员
function handleSearchAdmin(query: string) {
  debouncedFn(query)
}
/**
 * 自定义渲染select下拉标签
 * @param option 自定义下拉参数
 */
function renderLabel(option: any) {
  const userName = option.label as string
  const temp: string[] = []
  const child: VNode[] = []

  const searchVal = unref(searchText)
  const index = userName.indexOf(searchVal)

  temp.push(userName.substring(0, index), userName.substring(index, index + searchVal.length), userName.substring(index + searchVal.length))

  temp.forEach((i) => {
    child.push(h('span', {
      class: { color: i === searchVal ? 'text-primary' : 'text-#333' },
    }, i))
  })

  return h('div', {
    style: {
      display: 'flex',

    },
  },
  [
    h('div', null, child),

  ])
}
/**
 * 自定义渲染select标签
 * @param option 自定义下拉参数
 */
function renderTag({ option }: any) {
  return h(
    'div',
    null,
    { default: () => option.label },
  )
}

// 拥有的权限
const userPowerMenu = ref<UserPowerListType[]>([])

function handleCancel() {
  router.back()
}

const userPermissionMap = new Map<string, UserPermissionMap>()

const menuList = ref<MenuRes[]>([])

// 默认展开的节点
const defaultCheckedKeys = new Set<string>()
/** 获取用户详情  */
async function getUserDetail() {
  const id = route.query?.id as string
  if (!id)
    return

  try {
    const res = await userDetailApi<UserDetailRes>(id)

    if (res?.data) {
      backfill(res.data.id, res.data.userName)
      getUserPowerMap(res.data.userPowerList)
    }
  }
  catch (error) {
    console.log(error)
  }
}

/**
 * 获取用户姓名和id去回填表单渲染显示
 * @param id 当前编辑用户的id
 * @param userName 当前编辑用户姓名
 */
function backfill(id: string, userName: string) {
  formValue.value.adminId = `${id}`
  const adminName = userName
  const option = { label: adminName, value: `${id}` }
  optionsAdmin.value = [option]
}

/**
 * 把当前用户的权限以id为key,内容为value存map
 * @param userPowerList 当前编辑用户拥有的权限
 */
function getUserPowerMap(userPowerList: UserPowerList[]) {
  if (!userPowerList)
    return

  const parentId = ['5', '6', '9']

  userPowerList.forEach((item) => {
    if (item.menuId) {
      const mapValue = {
        powerRange: item?.powerRange,
        userId: item?.userId,
      }
      userPermissionMap.set(item.menuId, mapValue)
    }
    if (!parentId.includes(item.menuId))
      defaultCheckedKeys.add(item.menuId)
  })
}
/**
 * 检测当前节点是否是叶子节点，删除children字段
 * @param node 当前菜单节点
 */
function checkNodeLeaf(node: MenuRes) {
  node.prefix = () => null

  if (node.children && node.children.length === 0) {
    delete node.children
  }
  else {
    node?.children?.forEach((item) => {
      checkNodeLeaf(item)
    })
  }
}
/** 获取权限列表的菜单 */
async function getMenuList() {
  // 根据菜单列表和用户拥有的权限去勾选默认节点
  // tip:目前size取100
  const params = {
    size: 1000,
    start: 1,
    status: 0,
  }
  try {
    const res = await getMenusApi<MenuRes[]>(params)

    if (res?.data) {
      menuList.value = res.data ?? []

      const childMenu = res.data?.map((item) => {
        checkNodeLeaf(item)
        return {
          ...item,
          prefix: () => null,

        }
      }).sort((a, b) => {
        if (a?.children)
          a.children = a.children.sort((c, d) => c.orderNum - d.orderNum)
        if (b?.children)
          b.children = b.children.sort((e, f) => e.orderNum - f.orderNum)
        return a.orderNum - b.orderNum
      })

      userPowerMenu.value = [
        {
          menuName: '全部权限',
          id: 'all',
          children: childMenu,
          prefix: () => null,
        },
      ]
    }
  }
  catch (error) {
    console.log(error)
  }
}

function treeToArray(tree: UserPowerListType[]): UserPowerListParams[] {
  return tree.reduce((res, item) => {
    const { children } = item

    const otherParams = {
      menuId: item.id,
      userId: formValue.value.adminId,

      power: item.power,
      menuName: item.menuName,
    }

    Object.assign({}, otherParams)
    return res.concat(otherParams, children && children.length ? treeToArray(children) : [])
  }, [])
}
// 确认
function handleConfirm() {
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      if (!formValue.value.adminId)
        return

      const treeCheckedKeys = treeRef.value?.getCheckedData().keys?.filter(key => key !== 'all') as string[]
      const treeIndeterminateKeys = treeRef.value?.getIndeterminateData().keys?.filter(key => key !== 'all') as string[]

      const getKeys: string[] = []
      treeCheckedKeys && getKeys.push(...treeCheckedKeys)
      treeIndeterminateKeys && getKeys.push(...treeIndeterminateKeys)

      const powerList = treeToArray(unref(userPowerMenu)).filter((tree: UserPowerListParams) => getKeys?.includes(tree?.menuId))

      const params = {
        id: formValue.value.adminId,
        userPowerList: powerList,
      }

      try {
        const res = await insertAdminApi(params)
        if (res.data) {
          message.success('保存成功')
          router.back()
        }
      }
      catch (error) {

      }
    }
    else {
      console.log(errors)
    }
  })
}
/** 页面的初始化函数 */
async function init() {
  isEdit.value && await getUserDetail()

  await getMenuList()
}
onMounted(() => {
  init()
})

const renderBread = computed(() => {
  const title = route.query?.id ? '编辑子管理员' : '添加子管理员'
  const list: BreadLit[] = [
    { title: '系统设置', link: '/system', key: 'system' },
    { title: '权限管理', link: '/system/permissionManage', key: 'system_permissionManage' },
    { title, link: null, key: 'system_permissionEdit' },
  ]
  return list
})
</script>

<template>
  <div>
    <Breadcrumb :bread-list="renderBread" />
    <PageCard breadcrumb footer>
      <PageTitle>基础信息</PageTitle>
      <n-form
        ref="formRef" label-placement="left" class="mt-14px" require-mark-placement="left" :model="formValue"
        :rules="rules"
      >
        <n-form-item path="adminId" label="子管理员">
          <n-select
            v-model:value="formValue.adminId" style="width:320px" placeholder="请选择成员"
            :options="optionsAdmin" :loading="loadingAdmin" clearable filterable remote
            :render-label="renderLabel" :render-tag="renderTag" :disabled="isEdit" @search="handleSearchAdmin"
            @update:show="onUpdateShowAdmin"
          />
        </n-form-item>
      </n-form>
      <div class="flex">
        <div class="mr-11px pl-15px text-#666">
          权限配置
        </div>
        <div class="treeBlock w-full flex-1">
          <n-tree
            ref="treeRef"
            block-line default-expand-all cascade checkable
            key-field="id" label-field="menuName" class="tree"
            :data="userPowerMenu"
            :default-checked-keys="[...defaultCheckedKeys]"
            :watch-props="['defaultCheckedKeys']"
          />
        </div>
      </div>
    </PageCard>
    <LayoutFooter @confirm="handleConfirm" @cancel="handleCancel" />
  </div>
</template>

<style scoped lang="scss">
.treeBlock {
    border: 1px solid #ddd;

    padding-top: 5px;
    padding-bottom: 5px;
    .tree {
      :deep(.n-tree-node-content){
          padding-left: 0;
          margin-left: -6px;

      }
      :deep(.n-tree-node-switcher){
        margin: 0 3px 0 10px;
      }
  }
}
</style>
