<script setup lang='ts'>
import { useIntervalFn, useTransition } from '@vueuse/core'
import dayjs from 'dayjs'
import GroupView from './components/GroupView.vue'
import TimesView from './components/TimesView.vue'
import { Breadcrumb } from '@/layouts/common'
import patientIcon from '@/assets/images/antibacterial/patients.png'
import drugIcon from '@/assets/images/antibacterial/drug.png'
import kangJunIcon from '@/assets/images/antibacterial/kangjun.png'
import taceIcon from '@/assets/images/antibacterial/tace.png'
import { getTheDddDetailInfo, getThedddOverViewData } from '~/src/api/statistics'

const timeRef = ref()
const groupRef = ref()
const timesRange = ref()
const loadingStatus = ref(false)
const showDraw = ref(false)
const timesBtn = reactive({
  one: {
    title: '近一个月',
    select: false,
    times: [dayjs().subtract(1, 'month').startOf('month').format('YYYY-MM-DD'), dayjs().subtract(1, 'month').endOf('month').format('YYYY-MM-DD')],
  },
  three: {
    title: '近三个月',
    select: false,
    times: [dayjs().subtract(2, 'month').startOf('month').format('YYYY-MM-DD'), dayjs().endOf('month').format('YYYY-MM-DD')],
  },
  six: {
    title: '近六个月',
    select: false,
    times: [dayjs().subtract(5, 'month').startOf('month').format('YYYY-MM-DD'), dayjs().endOf('month').format('YYYY-MM-DD')],
  },
  tweel: {
    title: '近一年',
    select: false,
    times: [dayjs().subtract(11, 'month').startOf('month').format('YYYY-MM-DD'), dayjs().endOf('month').format('YYYY-MM-DD')],
  },
})

const patientAniCount = ref(0)
const kangJunAniCount = ref(0)
const taceAniCount = ref(0)
const drugAniCount = ref(0)
const overData = reactive({
  patient: {
    title: '患者总数',
    count: '25567',
    icon: patientIcon,
    animateCount: useTransition(patientAniCount, {
      duration: 1500,
    }),
  },
  drug: {
    title: '抗菌药使用强度',
    count: '5.23',
    icon: drugIcon,
    animateCount: useTransition(drugAniCount, {
      duration: 1500,
    }),
  },
  tace: {
    title: 'TACE患者数',
    count: '25123',
    icon: taceIcon,
    animateCount: useTransition(taceAniCount, {
      duration: 1500,
    }),
  },
  kangjun: {
    title: 'TACE抗菌药使用强度',
    count: '3.14',
    icon: kangJunIcon,
    animateCount: useTransition(kangJunAniCount, {
      duration: 1500,
    }),
  },
})

const activeScaleIndex = ref('group')
const selectDetail = ref()

function precisionCount(num) {
  num = Number(num)
  if (num > 100000) {
    const str = (num / 10000).toString()
    if (str.includes('.'))
      return str.split('.')[1].length
  }
  else {
    const str = num.toString()
    if (str.includes('.'))
      return str.split('.')[1].length
  }
  return 0
}

function countFormatter(value) {
  value = Number(value)
  if (Number(value) >= 100000)
    return value / 10000
  else
    return value
}
function randomCountAnimate() {
  useIntervalFn(() => {
    patientAniCount.value = 0
    kangJunAniCount.value = 0
    taceAniCount.value = 0
    drugAniCount.value = 0

    setTimeout(() => {
      patientAniCount.value = countFormatter(overData.patient.count)
      kangJunAniCount.value = countFormatter(overData.kangjun.count)
      taceAniCount.value = countFormatter(overData.tace.count)
      drugAniCount.value = countFormatter(overData.drug.count)
    }, 200)
  }, 3000)
}

function getTheOverviewData() {
  if (timesRange.value) {
    loadingStatus.value = true
    getThedddOverViewData({
      startTime: timesRange.value[0],
      endTime: timesRange.value[1],
    }).then((res) => {
      //   console.log(res?.data)
      loadingStatus.value = false
      overData.patient.count = res?.data?.total
      overData.drug.count = res?.data?.strength
      overData.tace.count = res?.data?.taceTotal
      overData.kangjun.count = res?.data?.taceStrength

      patientAniCount.value = countFormatter(overData.patient.count)
      drugAniCount.value = countFormatter(overData.drug.count)
      taceAniCount.value = countFormatter(overData.tace.count)
      kangJunAniCount.value = countFormatter(overData.kangjun.count)
    }).catch((_) => {
      loadingStatus.value = false
    })
  }
}

function timeSelect(value) {
  timesRange.value = value.times
  for (const key in timesBtn) {
    const element = timesBtn[key]
    element.select = false
  }
  value.select = true
}

function customTimeSel() {
  for (const key in timesBtn) {
    const element = timesBtn[key]
    element.select = false
  }
}

watch(timesRange, () => {
  getTheOverviewData()
})

onMounted(() => {
  //   randomCountAnimate()
  timeSelect(timesBtn.one)
})

function tabChange() {
  /// 切换了 table, 刷新
  groupRef.value.setTheOptions()
  timeRef.value.setTheOptions()
}

function disabledDate(time) {
  if (time.getTime() < dayjs('2022-01-01 00:00:00').valueOf())
    return true

  return time.getTime() > Date.now()
}

const detailCounts = reactive({
  total: {
    title: '组内患者数',
    count: 80,
  },
  inDay: {
    title: '住院总天数',
    count: 940,
  },
  ddds: {
    title: 'DDDs值',
    count: 3.56,
  },
  strength: {
    title: '抗菌药物使用强度',
    count: 7.87,
  },
})

const taceOptions = ref([{
  label: '全部',
  value: '0',
},
{
  label: '是',
  value: '1',
},
{
  label: '否',
  value: '2',
},
])

const drugOptions = ref([
  {
    label: '是',
    value: '1',
  },
  {
    label: '否',
    value: '2',
  },
  {
    label: '全部',
    value: '0',
  },
])
const paginationReactive = reactive({
  page: 1,
  size: 10,
  total: 10,
  pageSizes: [10, 20, 50],
})
const taceValue = ref('0')
const drugUseValue = ref('1')

function checkTheDetail(select) {
  /// 查看详情
  showDraw.value = true
  selectDetail.value = select
  detailCounts.total.count = select?.data?.total
  detailCounts.inDay.count = select?.data?.inDays
  detailCounts.ddds.count = select?.data?.ddds
  detailCounts.strength.count = select?.data?.strength

  /// 如果是时间段统计的话,那得需要限定一下时间
  getTheDetailData()
}

const drawer = ref()
const patients = ref([])
const showDrawTimes = reactive({
  start: '',
  end: '',
})

function getTheDetailData() {
  console.log('详情')

  let startT = timesRange.value[0]
  let endT = timesRange.value[1]
  if (activeScaleIndex.value === 'times') {
    /// / 处理时间
    const month = selectDetail.value?.data.month
    switch (month) {
      case timesRange.value[0].substring(0, 7):
        /// 第一个
        startT = timesRange.value[0]
        endT = dayjs(month).endOf('month').format('YYYY-MM-DD')
        break
      case timesRange.value[1].substring(0, 7):
        /// 最后一个
        startT = dayjs(month).startOf('month').format('YYYY-MM-DD')
        endT = timesRange.value[1]
        break
      default:
        startT = dayjs(month).startOf('month').format('YYYY-MM-DD')
        endT = dayjs(month).endOf('month').format('YYYY-MM-DD')
        break
    }
  }
  showDrawTimes.start = startT
  showDrawTimes.end = endT
  getTheDddDetailInfo({
    endTime: endT,
    group: selectDetail.value?.title,
    size: paginationReactive.size,
    start: paginationReactive.page,
    startTime: startT,
    tace: taceValue.value,
    anti: drugUseValue.value,
  }).then((res) => {
    console.log(res)
    patients.value = res?.data?.records
    paginationReactive.total = res?.data?.total
  })
}

function closeTheDraw() {
  // 关闭
  paginationReactive.page = 1
  paginationReactive.size = 10
  paginationReactive.total = 0
  taceValue.value = '0'
}

function taceChange() {
  ///
  paginationReactive.page = 1
  paginationReactive.size = 10
  paginationReactive.total = 0

  getTheDetailData()
}
</script>

<template>
  <div flex flex-col>
    <Breadcrumb
      class="bread-crumb" :bread-list="[
        { title: '统计分析', key: 'analysis', link: '/statement' },
        { title: '抗菌药使用情况报表', key: 'analysis_antibacterial', link: null },
      ]"
    />
    <div h-33px />
    <div class="page-content">
      <PageTitle>
        抗菌药使用情况报表
      </PageTitle>
      <div mt-15px flex items-center>
        <div color="#666" mr-10px>
          统计时间
        </div>
        <div>
          <el-date-picker
            v-model="timesRange" :disabled-date="disabledDate" style="width: 280px;" unlink-panels
            type="daterange" value-format="YYYY-MM-DD" range-separator="～" start-placeholder="开始日期"
            end-placeholder="结束日期" @change="customTimeSel"
          />
        </div>
        <div ml-10px flex items-center style="gap: 0 10px;">
          <div
            v-for="(item, index) in timesBtn" :key="index"
            :class="item.select ? 'time-btn-active time-btn' : 'time-btn'" @click="timeSelect(item)"
          >
            {{ item.title }}
          </div>
        </div>
      </div>
      <div v-loading="loadingStatus" mt-14px style="gap: 0 14px;display: flex;align-items: center;">
        <div
          v-for="(item, index) in overData" :key="index" flex flex-1 items-center
          :class="`count-${index} count-content`"
        >
          <img h-56px w-56px :src="item.icon">
          <div ml-18px flex-col>
            <div class="title">
              {{ item.title }}
            </div>
            <div v-if="!item.count" text-26px class="value">
              -
            </div>
            <el-statistic
              v-else :precision="precisionCount(item.count)" class="value" mt-9px
              :value="item.animateCount"
            >
              <template #suffix>
                <span v-if="Number(item.count) >= 1000000">万</span>
              </template>
            </el-statistic>
          </div>
        </div>
      </div>
      <PageTitle mt-21px>
        抗菌药使用强度统计
      </PageTitle>
      <el-tabs v-model="activeScaleIndex" mt-12px @tab-change="tabChange">
        <el-tab-pane lazy name="group" label="按医疗组统计">
          <GroupView ref="groupRef" :times-range="timesRange" @group-click="checkTheDetail" />
        </el-tab-pane>
        <el-tab-pane lazy name="times" label="按时间段统计">
          <TimesView ref="timeRef" :times-range="timesRange" @group-click="checkTheDetail" />
        </el-tab-pane>
      </el-tabs>
      <el-drawer ref="drawer" v-model="showDraw" size="800" @close="closeTheDraw">
        <template #header>
          <PageTitle style="font-weight: 500;">
            {{ `${selectDetail?.title} - 抗菌药物使用详情` }}
            <span style="font-weight: normal;color: #666;font-size: 14px;margin-left: 10px;">{{ timesRange?.length > 0
              ? `${showDrawTimes.start} 至 ${showDrawTimes.end}` : '' }}</span>
          </PageTitle>
        </template>
        <template #default>
          <div h-full flex-col>
            <el-divider style="margin:10px 0" />
            <div mt-6px style="display: flex; gap: 0 6px; align-items: center">
              <div v-for="(item, key) in detailCounts" :key="key" class="item-count">
                <div class="count">
                  {{ item.count }}
                </div>
                <div>{{ item.title }}</div>
              </div>
              <div flex-col flex-1>
                <div flex flex-1 items-center justify-end flex-self-end>
                  <div color="#666" mr-10px text-14px>
                    是否TACE患者
                  </div>
                  <el-select-v2
                    v-model="taceValue" :options="taceOptions" placeholder="选择" style="width: 100px"
                    @change="taceChange"
                  />
                </div>
                <div mt-4px flex flex-1 items-center justify-end flex-self-end>
                  <div color="#666" mr-10px text-14px>
                    是否使用抗菌药
                  </div>
                  <el-select-v2
                    v-model="drugUseValue" :options="drugOptions" placeholder="选择" style="width: 100px"
                    @change="taceChange"
                  />
                </div>
              </div>
            </div>
            <div class="table-header">
              <div>序号</div>
              <div>患者姓名</div>
              <div>病历号</div>
              <div>住院天数</div>
              <div>TACE手术时间</div>
            </div>
            <div mt-4px overflow-y-auto>
              <div v-if="patients?.length > 0" h-full flex-col>
                <div flex-1 overflow-y-auto>
                  <div v-for="(item, index) in patients" :key="index" mt-4px>
                    <div class="table-header table-name">
                      <div>
                        {{ (Number(paginationReactive.page) - 1) * Number(paginationReactive.size) + index + 1 }}
                      </div>
                      <div style="font-size: 18px;">
                        {{ item.patientName }}
                      </div>
                      <div> {{ item.patienRecordNo || '-' }}</div>
                      <div ml-5px>
                        {{ item.inDays }}
                      </div>
                      <div ml-8px style="white-space: pre-line;">
                        {{ item.taceTime ? item.taceTime?.replaceAll(',', '\n') : '-' }}
                      </div>
                    </div>
                    <el-table
                      :data="item.drugs" border
                      style="width: 100%;border: 0.5px solid #d1d1d1;border-top: none;"
                    >
                      <el-table-column prop="drugName" label="抗菌药物名称" min-width="32%" />
                      <el-table-column prop="ddd" label="DDD值" min-width="12%" />
                      <el-table-column
                        label="开始时间" min-width="17%" :formatter="({ startDate }) => {
                          return startDate ? dayjs(startDate).format('YYYY-MM-DD') : '-'
                        }"
                      />
                      <el-table-column
                        prop="end" label="结束时间" min-width="17%" :formatter="({ endDate }) => {
                          return endDate ? dayjs(endDate).format('YYYY-MM-DD') : '-'
                        }"
                      />
                      <el-table-column prop="frequencyName" label="频次" min-width="16%" />
                      <el-table-column
                        prop="total" label="总量" min-width="18%" :formatter="({ dose, doseUnit }) => {
                          return dose + doseUnit
                        }"
                      />
                    </el-table>
                  </div>
                </div>
                <div mt-14px flex justify-end>
                  <n-pagination
                    v-model:page="paginationReactive.page" v-model:page-size="paginationReactive.size"
                    :to="false" :item-count="paginationReactive.total" :page-sizes="[10, 20, 30]" show-size-picker
                    show-quick-jumper @update:page="getTheDetailData" @update:page-size="getTheDetailData"
                  />
                </div>
              </div>
              <DataEmpty v-else show-text="暂无数据" h-300px flex-col items-center justify-center />
            </div>
          </div>
        </template>
      </el-drawer>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.page-content {
  flex: 1;
  background: #ffffff;
  box-shadow: 0px 1px 4px 0px rgba(202, 202, 202, 0.50);
  margin: 14px;
  padding: 14px;

  .time-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    font-size: 14px;
    height: 32px;
    color: #333;
    border: 1px solid #dbdbdb;
    border-radius: 3px;

    &:hover {
      color: #06aea6;
      border: 1px solid #06aea6;
    }

    &:active {
      background: #e7f9f7;
      border: 1px solid #06aea6;
      color: #06aea6;
    }
  }

  .time-btn-active {
    background: #e7f9f7;
    border: 1px solid #06aea6;
    color: #06aea6;
  }

  .count-content {
    border-radius: 3px;
    padding: 20px;
    height: 96px;
  }

  .count-patient {
    background: #5B96FD1A;
  }

  .count-drug {
    background: #F5F4FEFF;
  }

  .count-tace {
    background: #F5F9F2FF;
  }

  .count-kangjun {
    background: #EBF9F6FF;
  }

  .title {
    font-size: 14px;
    color: #666666;
    line-height: 14px;
  }

  :deep(.el-statistic__number) {
    font-size: 26px;
    line-height: 26px;
    font-family: OPPOSans-H;
  }

  :deep(.el-statistic__suffix) {
    font-size: 16px;
    color: #333;
    line-height: 16px;
  }
}

:deep(.el-tabs__item.is-active) {
  color: #06aea6;
}

:deep(.el-tabs__item) {
  min-width: 124px;
  display: flex;
  justify-content: center;
  padding: 0;

  &:hover {
    color: #06aea6;
  }
}

:deep(.el-tabs__active-bar) {
  background-color: #06aea6;
  min-width: 124px;
  // min-width: 82px !important;
}

.bread-crumb {
  position: fixed;
  width: calc(100% - 230px);
}

.item-count {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 5px 10px;
  height: 56px;
  background: rgba(91, 150, 253, 0.10);
  border-radius: 3px;
  width: fit-content;
  min-width: 120px;

  .count {
    font-size: 18px;
    font-family: OPPOSans-H;
    text-align: center;
    color: #333333;
    line-height: 18px;
    margin-bottom: 8px;
  }

  color: #666;
  font-size: 14px;

}

:deep(.el-drawer__header) {
  margin-bottom: 0;
  padding-top: 14px;
}

:deep(.el-drawer__body) {
  padding-top: 0px;
}

.table-header {
  display: grid;
  margin-top: 14px;
  grid-template-columns: 70px 1fr 1fr 1fr 1.3fr;
  padding: 12px 0;
  background: #e7f9f7;
  box-shadow: 0px -1px 0px 0px rgba(204, 204, 204, 0.50) inset;

  div {
    font-size: 14px;
    color: #333;
    font-weight: 500;
    display: flex;
    align-items: center;
    padding-left: 20px;
  }
}

.table-name {
  margin-top: 0;
  background: #f5f5f5;
  border: 1px solid #d1d1d1;
  box-shadow: none;
}

:deep(.el-table th.el-table__cell) {
  background-color: #ffffff !important;
  box-shadow: none;
  border-color: #d1d1d1;
  border-right: none;
}

:deep(.el-table--border .el-table__cell) {
  border-color: #d1d1d1;
}

:deep(.el-drawer__body) {
  overflow: hidden;
}
</style>
