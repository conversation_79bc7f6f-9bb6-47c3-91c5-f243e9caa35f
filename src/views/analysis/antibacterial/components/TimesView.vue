<script setup lang='ts'>
import * as echarts from 'echarts'
import { getThedddTimeGroup } from '~/src/api/statistics'
import detailPNG from '@/assets/images/antibacterial/kangjun_detail.png'

const props = defineProps({
  timesRange: {
    type: Array,
  },
})
const emits = defineEmits(['groupClick'])
const loadingStatus = ref(false)
const currentGroup = ref('感染科　高海女组')
const selectOptions = ref([{
  label: '感染科　高海女组',
  value: '感染科　高海女组',
}, {
  label: '感染科　汤灵玲组',
  value: '感染科　汤灵玲组',
}, {
  label: '感染科　朱梦飞组',
  value: '感染科　朱梦飞组',
}, {
  label: '陈平组',
  value: '陈平组',
}, {
  label: '感染科　颜华东组',
  value: '感染科　颜华东组',
},
{
  label: '感染科　盛国平组',
  value: '感染科　盛国平组',
}])
const chartRef = shallowRef()
let myChart
const options = reactive({
  tooltip: {
    trigger: 'axis',
  },
  legend: {
    data: ['抗菌药使用强度', 'TACE抗菌药使用强度'],
  },
  grid: {
    left: '2.5%',
    right: '2%',
  },
  xAxis: {
    type: 'category',
    boundaryGap: true,
    axisLabel: {
      color: '#999',
    },
    axisLine: {
      lineStyle: {
        color: '#ccc',
      },
    },
    data: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
  },
  yAxis: {
    type: 'value',
    splitNumber: 4,
    splitLine: {
      lineStyle: {
        color: '#E8E8E8',
      },
    },
    axisLabel: {
      color: '#999',
    },
  },
  series: [
    {
      name: '抗菌药使用强度',
      type: 'line',
      stack: 'Total',
      data: [3, 3.5, 3, 2.5, 3, 2.8, 3, 3.2, 3.5, 3, 0, 0],
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: '#9D96F56f',
          },
          {
            offset: 1,
            color: 'rgba(255,255,255,0.1)',
          },
        ]),
      },
      symbolSize: 6,
      lineStyle: {
        color: '#9D96F5',
        opacity: 1,
      },
      itemStyle: {
        color: '#9D96F5',
      },
      z: 2,
    },
    {
      name: 'TACE抗菌药使用强度',
      type: 'line',
      stack: 'Total',
      data: [2, 2.3, 2.1, 2, 1.8, 2, 2.5, 2.8, 2.9, 2, 0, 0],
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: '#68D0D46f',
          },
          {
            offset: 1,
            color: 'rgba(255,255,255,0.1)',
          },
        ]),
      },
      symbolSize: 6,
      lineStyle: {
        color: '#68D0D4',
        opacity: 1,
      },
      itemStyle: {
        color: '#68D0D4',
      },
      z: 1,
    },
  ],
})

const typeDatas = ref({})
const tableOptions = {
  table: {
    header: {
      top: '医疗组',
      bottom: '统计项目',
    },
    lefts: [
      { name: '患者总数', key: 'total' },
      { name: '平均住院日', key: 'averageInDays' },
      { name: 'DDDs', key: 'ddds' },
      { name: '抗菌药使用强度', key: 'strength' },
      { name: 'TACE患者数', key: 'taceTotal' },
      { name: 'TACE患者平均住院天数', key: 'averageTaceInDays' },
      { name: 'TACE患者DDDs', key: 'taceDdds' },
      { name: 'TACE患者抗菌药使用强度', key: 'taceStrength' },
    ],
  },
}

function setTheOptions() {
  nextTick(() => {
    myChart.setOption(options)
    myChart.resize()
  })
}

/// 处理列表数据
function handleTheColumnData(arr: Array<any>) {
  // 动态加入
  // arr = ['2022-01', '2022-02', '2022-03', '2022-04']
  const currentType = tableOptions
  const vnodes: Array<any> = [
    {
      key: 'title',
      title() {
        return h('div', { class: 'table-one flex-col h-full', style: { width: '100%', height: '50px', backgroundColor: '#e7f9f7' } }, [
          h(
            'div',
            { class: 'table-left flex-1', style: { lineHeight: 'normal' } },
            currentType.table?.header.top,
          ),
          h(
            'div',
            { class: 'table-top flex-1', style: { paddingBottom: '5px', lineHeight: 'normal' } },
            currentType.table?.header.bottom,
          ),
        ])
      },
      fixed: 'left',
      width: 200,
      render(_: any, index: number) {
        return h('div', { style: { textAlign: 'left', flex: '1' } }, currentType.table?.lefts[index].name)
      },
    },
  ]

  arr.forEach((element: any) => {
    vnodes.push({
      key: 'time',
      title() {
        return h('div', {
          style: { textAlign: 'center', flex: '1', backgroundColor: '#E7f9F7', borderColor: '#D1D1D1', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center' },
          onClick: () => {
            emits('groupClick', {
              title: currentGroup.value,
              data: element,
            })
          },
        }, [
          h('div', {}, element.month),
          h('img', { src: detailPNG, class: 'w-16px h-16px ml-5px' }),
        ])
      },
      render(xx: any, index: number) {
        console.log(xx)
        return h(
          'div',
          { style: { textAlign: 'left', flex: '1' } },
          element[xx.key],
        )
      },
    })
  })

  return {
    columns: vnodes,
    datas: currentType.table?.lefts,
  }
}

function getTheDataFromNetWork() {
  if (props.timesRange) {
    loadingStatus.value = true
    getThedddTimeGroup({
      startTime: props.timesRange[0],
      group: currentGroup.value,
      endTime: props.timesRange[1],
    }).then((res) => {
      //   console.log(res)
      loadingStatus.value = false
      const dataMap = setTheChartAndTableDataFromNet(res?.data)
      /// 设置Chart
      options.xAxis.data = dataMap.months
      options.series[0].data = dataMap.ddds
      options.series[1].data = dataMap.taceDdds
      setTheOptions()
      /// 设置表格
      typeDatas.value = handleTheColumnData(dataMap.oriData)
    }).catch((_) => {
      loadingStatus.value = false
    })
  }
}

function groupSelect(title) {
  /// 医生组选择
  currentGroup.value = title
  getTheDataFromNetWork()
}

function setTheChartAndTableDataFromNet(data) {
  const months = []
  const ddds = []
  const taceDdds = []
  data.forEach((element) => {
    months.push(element.month)
    ddds.push(element.strength)
    taceDdds.push(element.taceStrength)
  })
  return {
    months,
    ddds,
    taceDdds,
    oriData: data,
  }
}

watch(() => props.timesRange, () => {
  getTheDataFromNetWork()
}, { immediate: true })

onMounted(() => {
  myChart = echarts.init(chartRef.value)
  window.addEventListener('resize', () => {
    myChart?.resize()
  })
})

defineExpose({
  setTheOptions,
})
</script>

<template>
  <div v-loading="loadingStatus" class="content">
    <div relative h-265px w-full>
      <div absolute style="z-index: 99;">
        <n-select v-model:value="currentGroup" :options="selectOptions" placeholder="选择医疗组" style="width: 180px"
          @change="groupSelect" />
      </div>
      <div ref="chartRef" h-265px w-full />
    </div>
    <div mt-14px class="table">
      <n-data-table mt-14px :single-line="false" :bordered="true" :columns="typeDatas.columns"
        :data="typeDatas.datas" />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.content {
  display: flex;
  flex-direction: column;
  width: 100%;

  :deep(.table-one) {
    position: relative;

    background: linear-gradient(to bottom left,
        transparent 50%,
        #d1d1d1,
        transparent 52%);

    .table-left {
      text-align: right;
      direction: rtl;
      padding-right: 12px;
      padding-top: 5px;
    }

    .table-top {
      padding-left: 10px;
    }
  }

  :deep(.n-data-table-th) {
    &:nth-child(1) {
      padding: 0;

      .n-data-table-th__title-wrapper {
        height: 100%;

        .n-data-table-th__title {
          height: 100%;
        }
      }
    }

    background-color: #e7f9f7;
    border-color: #d1d1d1;
  }
}

:deep(.n-data-table) {
  --n-merged-border-color: #d1d1d1
}
</style>
