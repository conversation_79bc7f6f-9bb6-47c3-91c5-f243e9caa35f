<script setup lang='ts'>
import * as echarts from 'echarts'
import { getThedddDoctorGroup } from '~/src/api/statistics'
import detailPNG from '@/assets/images/antibacterial/kangjun_detail.png'

const props = defineProps({
  timesRange: {
    type: Array,
  },
})
const emits = defineEmits(['groupClick'])
const loadingStatus = ref(false)
const mapData = reactive({})
const groupChart = shallowRef()
const tableOptions = {
  table: {
    header: {
      top: '医疗组',
      bottom: '统计项目',
    },
    lefts: [
      { name: '医疗组成员', key: 'doctors' },
      { name: '患者总数', key: 'total' },
      { name: '平均住院日', key: 'averageInDays' },
      { name: 'DDDs', key: 'ddds' },
      { name: '抗菌药使用强度', key: 'strength' },
      { name: 'TACE患者数', key: 'taceTotal' },
      { name: 'TACE患者平均住院天数', key: 'averageTaceInDays' },
      { name: 'TACE患者DDDs', key: 'taceDdds' },
      { name: 'TACE患者抗菌药使用强度', key: 'taceStrength' },
    ],
  },
}
const typeDatas = ref({})
let myChart
const groupOption = reactive({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
  },
  legend: {
    data: ['抗菌药使用强度', 'TACE抗菌药使用强度'],
    itemGap: 30,
    itemHeight: 12,
    icon: 'path://M 0 0 H 12 V 12 H 0 Z',
    textStyle: {
      color: '#666',
      padding: [0, 0, 0, -6],
    },
  },
  grid: {
    left: '10px',
    right: '10px',
    bottom: '2%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    data: ['医疗组1', '医疗组2', '医疗组3', '医疗组4', '医疗组5', '医疗组6', '医疗组7', '医疗组8'],
    axisLine: {
      lineStyle: {
        color: '#ccc',
      },
    },
    axisLabel: {
      color: '#999',
    },
  },
  yAxis: {
    type: 'value',
    splitLine: {
      lineStyle: {
        color: '#E8E8E8',
      },
    },
    axisLabel: {
      color: '#999',
    },
  },
  series: [
    {
      name: '抗菌药使用强度',
      type: 'bar',
      data: [3, 4.890, 2, 3.5, 3.1, 1.5, 2.3, 3.8],
      itemStyle: {
        color: '#b19cf9',
      },
      barWidth: 30,
    },
    {
      name: 'TACE抗菌药使用强度',
      type: 'bar',
      data: [2, 2.5, 1, 3.23, 2, 1.3, 1.5, 2.4],
      itemStyle: {
        color: '#67e0e3',
      },
      barWidth: 30,
    },
  ],
})

onMounted(() => {
  myChart = echarts.init(groupChart.value)
  window.addEventListener('resize', () => {
    setTimeout(() => {
      myChart?.resize()
    }, 100)
  })
  myChart.getZr().on('click', (params) => {
    // 获取点击位置的像素坐标
    const pointInPixel = [params.offsetX, params.offsetY]
    // 将像素坐标转换为图表的X轴坐标
    const pointInGrid = myChart.convertFromPixel({ seriesIndex: 0 }, pointInPixel)
    if (pointInGrid && pointInGrid[0] !== null) {
      const xAxisValue = pointInGrid[0] // X轴上的值
      // 四舍五入找到最接近的类目
      const closestIndex = Math.round(xAxisValue)
      // 获取类目数据
      const xAxisData = groupOption.xAxis.data
      // console.log(xAxisData[closestIndex])
      emits('groupClick', {
        title: xAxisData[closestIndex],
        data: mapData[xAxisData[closestIndex]],
      })
    }
  })
})

function setTheOptions() {
  myChart.setOption(groupOption)
  nextTick(() => {
    myChart.resize()
  })
  setTimeout(() => {
    myChart?.resize()
  }, 200)
}

// function setTheTableData() {
//   typeDatas.value = handleTheColumnData([
//     {
//       groupName: '医疗组 1',

//     },
//     {
//       groupName: '医疗组 2',

//     },
//     {
//       groupName: '医疗组 3',

//     },
//   ])
// }

/// 处理列表数据
function handleTheColumnData(arr: Array<any>) {
  // 动态加入
  // arr = ['2022-01', '2022-02', '2022-03', '2022-04']
  const currentType = tableOptions
  const vnodes: Array<any> = [
    {
      key: 'title',
      title() {
        return h('div', { class: 'table-one flex-col h-full', style: { width: '100%', backgroundColor: '#E7f9F7' } }, [
          h(
            'div',
            { class: 'table-left flex-1', style: { lineHeight: 'normal' } },
            currentType.table?.header.top,
          ),
          h(
            'div',
            { class: 'table-top flex-1', style: { paddingBottom: '5px', lineHeight: 'normal' } },
            currentType.table?.header.bottom,
          ),
        ])
      },
      fixed: 'left',
      width: 200,
      render(_: any, index: number) {
        return h('div', {
          style: { textAlign: 'left', flex: '1' },
        }, currentType.table?.lefts[index].name)
      },
    },
  ]

  arr.forEach((element: any) => {
    vnodes.push({
      key: 'group',
      title() {
        return h('div', {
          style: { textAlign: 'center', flex: '1', backgroundColor: '#E7f9F7', borderColor: '#D1D1D1', cursor: 'pointer', display: 'flex', alignItems: 'center', justifyContent: 'center', minWidth: '163px' },
          onClick: () => {
            emits('groupClick', {
              title: element.name,
              data: element,
            })
          },
        }, [
          h('div', {}, element.name),
          h('img', { src: detailPNG, class: 'w-16px h-16px ml-5px' }),
        ])
      },
      render(xx: any, index: number) {
        console.log(xx)
        return h(
          'div',
          { style: { textAlign: 'left', flex: '1' } },
          Array.isArray(element[xx.key]) ? element[xx.key].join('、') : element[xx.key],
        )
      },
    })
    mapData[element.name] = element
  })

  return {
    columns: vnodes,
    datas: currentType.table?.lefts,
  }
}

function getTheDataFromNet() {
  //
  if (props.timesRange) {
    loadingStatus.value = true
    getThedddDoctorGroup({
      startTime: props.timesRange[0],
      endTime: props.timesRange[1],
    }).then((res) => {
      // console.log(res)
      const dataMap = setTheChartAndTableDataFromNet(res?.data)
      /// 设置这个统计图
      groupOption.xAxis.data = dataMap.groups
      groupOption.series[0].data = dataMap.ddds
      groupOption.series[1].data = dataMap.taceDdds
      /// 设置这个表格图
      typeDatas.value = handleTheColumnData(dataMap.oriData)
      setTheOptions()
      loadingStatus.value = false
    }).catch((_) => {
      loadingStatus.value = false
    })
  }
}

function setTheChartAndTableDataFromNet(data) {
  ///
  const groups = []
  const ddds = []
  const taceDdds = []
  data.forEach((element) => {
    groups.push(element.name)
    ddds.push(element.strength)
    taceDdds.push(element.taceStrength)
  })
  return {
    groups,
    ddds,
    taceDdds,
    oriData: data,
  }
}

watch(() => props.timesRange, () => {
  getTheDataFromNet()
}, { immediate: true })

defineExpose({
  setTheOptions,
})
</script>

<template>
  <div v-loading="loadingStatus" class="group-content">
    <div ref="groupChart" h-250px w-full />
    <div mt-14px class="table">
      <n-data-table
        class="scrol-table"
        mt-14px :single-line="false" :bordered="true" :columns="typeDatas.columns"
        :data="typeDatas.datas"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
.group-content {
  display: flex;
  flex-direction: column;

  :deep(.table-one) {
    position: relative;

    background: linear-gradient(to bottom left,
        transparent 50%,
        #d1d1d1,
        transparent 52%);

    .table-left {
      text-align: right;
      direction: rtl;
      padding-right: 12px;
      padding-top: 5px;
    }

    .table-top {
      padding-left: 10px;
    }
  }

  :deep(.n-data-table-th) {
    &:nth-child(1) {
      padding: 0;

      .n-data-table-th__title-wrapper {
        height: 100%;

        .n-data-table-th__title {
          height: 100%;
        }
      }

    }

    background-color: #e7f9f7;
    border-color: #d1d1d1;
  }

}

:deep(.n-data-table) {
  --n-merged-border-color: #d1d1d1
}

.scrol-table{
  &::-webkit-scrollbar {
      /* 隐藏默认的滚动条 */
      -webkit-appearance: none;
    }
    &::-webkit-scrollbar:horizontal{
        /* 设置水平滚动条厚度 */
        height: 2px;
    }
    & ::-webkit-scrollbar-thumb {
      /* 滚动条的其他样式定制，注意，这个一定也要定制，否则就是一个透明的滚动条 */
      border-radius: 8px;   /* 设置滚动条的圆角 */
      border: 2px solid rgba(255,255,255,.4);  /* 设置滚动条的边框 */
      background-color: rgba(0, 0, 0, .5); /* 设置滚动条的颜色填充 */
    }
}
</style>
