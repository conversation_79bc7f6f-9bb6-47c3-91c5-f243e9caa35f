<script setup lang="ts">
import { Breadcrumb } from '@/layouts/common'
</script>

<template>
  <Breadcrumb route-name="analysis_statement" />

  <div class="content-box">
    <div flex items-center>
      <span>随访时间:</span>
      <n-date-picker type="date" clearable />
      <span mx-14px>-</span>
      <n-date-picker type="date" clearable />
    </div>
    <n-grid x-gap="30" :cols="4" mt-14px>
      <n-gi bg="~ #3AC9A8" h-90px leading-60px>
        <div flex justify-between>
          <div flex flex-wrap>
            <div mx-14px>
              <div h-20px text-20px color="#fff">
                21
              </div>
              <div color="#fff" text-12px>
                随访总人数
              </div>
            </div>
          </div>
          <div mr-20px mt-10px>
            people
          </div>
        </div>
      </n-gi>
      <n-gi style="background-color: rgba(165,184,209,0.15);" h-90px leading-60px>
        <div flex justify-between>
          <div flex flex-wrap>
            <div mx-14px>
              <div h-20px text-20px color="##333333">
                21
              </div>
              <div color="#666666" text-12px>
                消息发送量
              </div>
            </div>
          </div>
          <div mr-20px mt-10px>
            people
          </div>
        </div>
      </n-gi>
      <n-gi style="background-color: rgba(165,184,209,0.15);" h-90px leading-60px>
        <div flex justify-between>
          <div flex flex-wrap>
            <div mx-14px>
              <div h-20px text-20px color="##333333">
                22
              </div>
              <div color="#666666" text-12px>
                我的树苗
              </div>
            </div>
          </div>
          <div mr-20px mt-10px>
            people
          </div>
        </div>
      </n-gi>
    </n-grid>

    <!-- <PageTitle>
      统计报表
      <span ml-10px color="[#666]">(截止统计时间: )</span>
    </PageTitle> -->
  </div>
</template>

<style scoped lang="scss">
.content {
    padding: 0;
    display: flex;
    flex-direction: column;
}

.content-box {
    height: 164px;
    background-color: white;
    margin: 14px;
    padding: 14px;
    display: flex;
    flex-direction: column;
}
</style>
