export type Tab = 'follow' | 'message' | 'tree'
export interface FollowDate {
  startTime: string | null
  endTime: string | null
}

export interface Card {
  type: 'follow' | 'message' | 'tree'
  label: string
  count: number
  icon: string
  activeIcon: string
}

export const CurrentTabKey: InjectionKey<Ref<Tab>> = Symbol('CurrentTabKey')
export const DateFormKey: InjectionKey<Ref<FollowDate>> = Symbol('DateFormKey')
export const CardKey: InjectionKey<Ref<Card[]>> = Symbol('CardKey')
