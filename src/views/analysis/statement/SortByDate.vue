<script setup lang="ts">
import * as echarts from 'echarts'
import { <PERSON><PERSON><PERSON>, DateForm<PERSON>ey } from './types'
import { CountFollowDateAPI } from '@/api/statement'

const chartOption: echarts.EChartsOption = ({
  tooltip: {
    trigger: 'axis',
    borderWidth: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    formatter: (params) => {
      return `
        <div style="color: #fff;font-size:12px">
          ${params[0].axisValueLabel}  
        </div>
        <div style="display: flex;align-items: center;gap:5px;color: #fff">
          <div style="width: 10px;height:10px;background: #5B96FD;border-radius: 50%;"></div>
          <div>
            已入组：${params[0].value}
          </div>
        </div>
      `
    },
  },
  grid: {
    left: '20px',
    right: '20px',
    bottom: '20px',
    top: '50px',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    axisLine: {
      lineStyle: {
        color: '#ccc',
      },
    },
    axisLabel: {
      showMinLabel: true,
      showMaxLabel: true,
    },
    data: [],
  },
  yAxis: {
    type: 'value',
    name: '人',
    minInterval: 1,
    axisLine: {
      lineStyle: {
        color: '#ccc',
      },
    },
  },
  series: [
    {
      name: 'Email',
      type: 'line',
      data: [],
      lineStyle: {
        color: '#5B96FD',
      },
      itemStyle: {
        color: '#5B96FD',
      },
    },
  ],
})

const tableData = ref<any[]>([])

const isChartEmpty = ref(true)

const dateForm = inject(DateFormKey)!
const planList = inject<Ref<any[]>>('planList')!
const cardList = inject(CardKey)!

const TeamTrendRef = ref<HTMLElement>()
const planListIds = ref([])

let myChart: echarts.ECharts | null = null

function renderChart() {
  if (TeamTrendRef.value) {
    myChart?.dispose()
    myChart = echarts.init(TeamTrendRef.value)
    myChart.setOption(chartOption as any)
  }
}

const isLoading = ref(false)
async function getData() {
  try {
    isLoading.value = true
    const { data } = await CountFollowDateAPI({
      ...dateForm.value,
      queryIdList: planListIds.value,
    })

    if (data) {
      tableData.value = data.countPlanTmp
      cardList.value[0].count = data.followNum
      const xData = data.countDate?.map(item => item.keyName)
      const yData = data.countDate?.map(item => item.countNum)

      // @ts-expect-error any
      chartOption.xAxis.data = xData
      chartOption.series![0].data = yData

      isChartEmpty.value = yData.every((item: any) => item === 0)

      await nextTick()

      renderChart()
    }
  }
  catch (error) {

  }
  finally {
    isLoading.value = false
  }
}

defineExpose({
  getData,
})

window.onresize = () => {
  myChart?.resize()
}

watch(dateForm, () => {
  getData()
},
{
  deep: true,
})
</script>

<template>
  <n-spin type="uni" :show="isLoading" size="medium">
    <div my-14px flex items-center gap-10px>
      <div text="#666">
        随访计划
      </div>
      <n-select
        v-model:value="planListIds"
        :options="planList"
        multiple
        max-tag-count="responsive"
        label-field="planTemplateName"
        value-field="planTemplateId"
        class="!w-530px" placeholder="全部"
        @update:value="getData"
      />
    </div>

    <div mt-10px>
      <SectionTitle>
        <span text="#333">入组情况趋势图</span>
      </SectionTitle>

      <div v-if="isChartEmpty" my-70px flex items-center justify-center>
        <n-empty description="无数据" />
      </div>
      <div v-else ref="TeamTrendRef" h-300px w-full />

      <SectionTitle>
        <span text="#333">入组情况统计表</span>
      </SectionTitle>

      <el-table mt-14px :data="tableData" max-height="400">
        <el-table-column type="index" label="序号" min-width="80" />
        <el-table-column label="随访计划" :formatter="({ keyName }) => keyName || '-'" min-width="80" />
        <el-table-column label="已入组" min-width="80" :formatter="({ countNum }) => countNum || '-'">
          <template #header>
            <div flex items-center gap-5px>
              <div>
                已入组 (人)
              </div>
              <n-tooltip>
                <template #trigger>
                  <SvgIcon local-icon="slmc-icon-information" color="#666" />
                </template>
                <span>随访累计已入组人数</span>
              </n-tooltip>
            </div>
          </template>
        </el-table-column>
        <template #empty>
          <div my-50px flex items-center justify-center>
            <DataEmpty />
          </div>
        </template>
      </el-table>
    </div>
  </n-spin>
</template>
