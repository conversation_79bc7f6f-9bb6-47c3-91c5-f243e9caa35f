<script lang='ts' setup>
import { DateFormKey } from './types'
import LARGE_ICON from '@/assets/images/statement/large_tree.png'
import TINY_ICON from '@/assets/images/statement/tiny_tree.png'
import SMALL_ICON from '@/assets/images/statement/small_tree.png'
import { getDepartTotalAPI } from '~/src/api/statement'
import { getOrganApi } from '@/api/organization/organization'
import type { OrganizationRes } from '~/src/api/organization/type'

const treeCards = ref([
  {
    type: 'tiny',
    label: '小树苗',
    count: 0,
    icon: TINY_ICON,
  },
  {
    type: 'small',
    label: '小树',
    count: 0,
    icon: SMALL_ICON,
  },
  {
    type: 'large',
    label: '大树',
    count: 0,
    icon: LARGE_ICON,
  },

])

const tableData = ref<any[]>([])

const dateForm = inject(DateFormKey)!
const departments = ref<any[]>([])
const departmentOptions = ref<any[]>([])

async function getDepartList() {
  const params = {
    size: 1000,
    start: 1,
    searchName: '',
  }
  const { data } = await getOrganApi<OrganizationRes[]>(params)
  const valiedDepartments: {
    value: string
    label: string
  }[] = []
  if (data) {
    function recursionTree(organList: OrganizationRes[]) {
      for (const item of organList) {
        if ([3, 4].includes(item.organLevel)) {
          valiedDepartments.push({
            value: item.id,
            label: item.organName,
          })
        }
        if (item.children)
          recursionTree(item.children)
      }
    }

    recursionTree(data)

    departmentOptions.value = valiedDepartments

    if (valiedDepartments.length === 1)
      departments.value = valiedDepartments.map(item => item.value)
  }
}

async function getDepartTotal() {
  try {
    const { data } = await getDepartTotalAPI({
      start: dateForm.value.startTime,
      end: dateForm.value.endTime,
      departments: departments.value,
    })

    treeCards.value[0].count = data.small
    treeCards.value[1].count = data.medium
    treeCards.value[2].count = data.large

    tableData.value = data.departs
  }
  catch (error) {

  }
}

function getSummaries(params) {
  const smallTotal = params.data?.map(item => item.small).reduce((a, b) => a + b, 0) || 0
  const mediumTotal = params.data?.map(item => item.medium).reduce((a, b) => a + b, 0) || 0
  const largeTotal = params.data?.map(item => item.large).reduce((a, b) => a + b, 0) || 0
  const allTotal = params.data?.map(item => item.total).reduce((a, b) => a + b, 0) || 0
  return ['合计', '', smallTotal, mediumTotal, largeTotal, allTotal]
}

onMounted(() => {
  getDepartTotal()
  getDepartList()
})
</script>

<template>
  <div class="bg-#fff" mx-14px mb-14px overflow-auto p-14px>
    <PageTitle>
      统计 - 树苗总数
    </PageTitle>
    <div my-14px h-1px w-full bg="#ccc" />
    <div overflow-auto class="h-[calc(100vh-362px)] pb-14px">
      <div flex items-center gap-10px>
        <div>
          科室
        </div>
        <n-select
          v-model:value="departments"
          :options="departmentOptions"
          multiple
          placeholder="全部" class="!w-530px"
          @update:value="getDepartTotal"
        />
      </div>

      <div my-20px flex gap-14px>
        <div v-for="(card) in treeCards" :key="card.type" bg="#1AC3BB/8" h-88px w-265px b-rd-3px px-10px py-14px border-t="2px solid #1AC3BB/40">
          <div h-full flex items-center justify-between>
            <div>
              <div text="#333 20px" font-600>
                {{ card.count }}
              </div>
              <div mt-5px text="#666 12px">
                {{ card.label }}
              </div>
            </div>
            <img h-66px w-78px :src="card.icon">
          </div>
        </div>
      </div>
      <SectionTitle my-14px>
        <span text="#333">入组情况统计表</span>
      </SectionTitle>
      <el-table :data="tableData" default-expand-all show-summary :summary-method="getSummaries">
        <el-table-column label="序号" type="index" width="80" />
        <el-table-column label="科室&医生组" min-width="80" :formatter="({ departmentName }) => departmentName || '-'" />
        <el-table-column label="小树苗 (健康值≤299)" min-width="80" :formatter="({ small }) => small || '0'" />
        <el-table-column label="小树 (健康值300～999)" min-width="80" :formatter="({ medium }) => medium || '0'" />
        <el-table-column label="大树 (健康值≥1000)" min-width="80" :formatter="({ large }) => large || '0'" />
        <el-table-column label="合计" min-width="80">
          <template #default="{ row }">
            <span text="#FF9B54">{{ row.total }}</span>
          </template>
        </el-table-column>
        <el-table-column type="expand">
          <template #default="props">
            <div bg="#F5F5F5" w-full flex items-center px-20px py-12px>
              <div w-full>
                <div v-for="(item, index) in props.row.groups" :key="index">
                  <div ml-73px>
                    <div flex justify-between>
                      <div class="w-18%">
                        {{ item.groupName || '-' }}
                      </div>
                      <div class="w-18%">
                        {{ item.small || '0' }}
                      </div>
                      <div class="w-18%">
                        {{ item.medium || '0' }}
                      </div>
                      <div class="w-18%">
                        {{ item.large || '0' }}
                      </div>
                      <div text="#FF9B54" class="w-18%">
                        {{ item.total }}
                      </div>
                      <div class="w-1.2%" />
                    </div>
                  </div>
                  <div v-if="index !== props.row.groups.length - 1" my-12px h-1px w-full bg="#d1d1d1" />
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <template #empty>
          <div my-50px flex items-center justify-center>
            <DataEmpty />
          </div>
        </template>
      </el-table>
    </div>
  </div>
</template>

<style scoped>
:deep(.el-table__footer-wrapper > table) {
  border:1px solid #d1d1d1;
  height: 50px;
}

:deep(.el-table__footer-wrapper > table  > tfoot > tr > td) {
  background: #fff!important;
}

:deep(.el-table__footer-wrapper > table  > tfoot > tr > td > div) {
  color: #FF9B54;
  font-size: 14px;
}
</style>
