<script setup lang="ts">
import dayjs from 'dayjs'
import { <PERSON><PERSON><PERSON>, CurrentT<PERSON><PERSON><PERSON>, Date<PERSON><PERSON><PERSON><PERSON> } from './types'
import { Breadcrumb } from '@/layouts/common'

const currentTab = inject(CurrentTabKey)!
const dateForm = inject(DateFormKey)!
const cardList = inject(CardKey)!

function handleCardClick(type) {
  currentTab.value = type
}
</script>

<template>
  <Breadcrumb route-name="analysis_statement" />

  <div class="content-box">
    <div flex items-center>
      <span text="#666" mr-10px>随访时间:</span>
      <n-date-picker
        v-model:formatted-value="dateForm.startTime"
        :is-date-disabled="(ts:number) => dayjs(ts).isAfter(dayjs(dateForm.endTime))"
        value-format="yyyy-MM-dd 00:00:00"
        type="date"
      />
      <div bg="#999" mx-14px h-1px w-10px />
      <n-date-picker
        v-model:formatted-value="dateForm.endTime"
        :is-date-disabled="(ts:number) => dayjs(ts).isBefore(dayjs(dateForm.startTime))"
        value-format="yyyy-MM-dd 23:59:59"
        type="date"
      />
    </div>
    <div mt-14px flex gap-14px>
      <div
        v-for="card in cardList" :key="card.type"
        :class="card.type === currentTab ? 'active' : 'dis_active'"
        h-80px w-265px cursor-pointer items-center b-rd-3px px-14px
        @click="handleCardClick(card.type)"
      >
        <div h-full flex items-center justify-between>
          <div>
            <div text="20px" class="count" font-600>
              <n-number-animation :from="0" :to="card.count" show-separator />
            </div>
            <div mt-8px text="12px" class="label" leading-none>
              {{ card.label }}
            </div>
          </div>

          <img h-40px w-40px :src="currentTab === card.type ? card.activeIcon : card.icon" alt="">
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.content-box {
  height: 164px;
  background-color: white;
  margin: 14px;
  padding: 14px;
  display: flex;
  flex-direction: column;
}

.active {
  background: rgba(58, 201, 168, 1);
  box-shadow: 0px 4px 8px 0px rgba(58,201,168,0.30);

  .label {
    color: #fff;
  }

  .count {
    color: #fff;
  }
}

.dis_active {
  background: rgba(165, 184, 209, 0.15);

  .label {
    color: #666;
  }

  .count {
    color: #333;
  }
}
</style>
