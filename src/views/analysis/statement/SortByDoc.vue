<script setup lang="ts">
import * as echarts from 'echarts'
import { DateFormKey } from './types'
import { countDocAPI } from '@/api/statement'

const chartOption: echarts.EChartsOption = ({
  tooltip: {
    trigger: 'axis',
    borderWidth: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    formatter: (params) => {
      const hasJoin = params?.find(item => item.seriesName === '已入组')
      const noJoin = params?.find(item => item.seriesName === '未入组')
      const rePatient = params?.find(item => item.seriesName === '复诊患者')
      return `
        <div style="color: #fff;font-size:12px">
          ${params[0]?.axisValueLabel}  
        </div>
        <div style="display: flex;align-items: center;gap:5px;color: #fff">
          <div style="width: 10px;height:10px;background: #5B96FD;border-radius: 50%;"></div>
          <div>
            已入组：${hasJoin?.value || 0}
          </div>
        </div>
        <div style="display: flex;align-items: center;gap:5px;color: #fff">
          <div style="width: 10px;height:10px;background: #FFC569;border-radius: 50%;"></div>
          <div>
            未入组：${noJoin?.value || 0}
          </div>
        </div>
        <div style="display: flex;align-items: center;gap:5px;color: #fff">
          <div style="width: 10px;height:10px;background: #68D0D4;border-radius: 50%;"></div>
          <div>
            复诊患者：${rePatient?.value || 0}
          </div>
        </div>
      `
    },
  },
  legend: {
    data: ['已入组', '未入组', '复诊患者'],
  },
  grid: {
    left: '20px',
    right: '20px',
    bottom: '20px',
    top: '50px',
    containLabel: true,
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    axisLine: {
      lineStyle: {
        color: '#ccc',
      },
    },
    axisLabel: {
      showMinLabel: true,
      showMaxLabel: true,
    },
    data: [],
  },
  yAxis: {
    type: 'value',
    name: '人次',
    minInterval: 1,
    axisLine: {
      lineStyle: {
        color: '#ccc',
      },
    },
  },
  series: [
    // 入组
    {
      name: '已入组',
      type: 'line',
      data: [],
      lineStyle: {
        color: '#5B96FD',
      },
      itemStyle: {
        color: '#5B96FD',
      },
    },
    // 未入组
    {
      name: '未入组',
      type: 'line',
      data: [],
      lineStyle: {
        color: '#FFC569',
      },
      itemStyle: {
        color: '#FFC569',
      },
    },
    // 已复诊
    {
      name: '复诊患者',
      type: 'line',
      data: [],
      lineStyle: {
        color: '#68D0D4',
      },
      itemStyle: {
        color: '#68D0D4',
      },
    },
  ],
})

const docIds = ref<any[]>([])
const chartRef = ref<HTMLElement>()
let myChart: echarts.ECharts | null = null
const dateForm = inject(DateFormKey)!
const docGroupList = inject<Ref<any[]>>('docGroupList')!

const tableData = ref<any[]>([])

const isLoading = ref(false)

const isChartEmpty = ref(true)

async function getData() {
  try {
    isLoading.value = true
    const { data } = await countDocAPI({
      ...dateForm.value,
      queryIdList: docIds.value,
    })

    if (data) {
      const xData = data.countDate?.map(item => item.keyName)
      const yData1 = data.countDate?.map(item => item.countNum)
      const yData2 = data.countNoFollow?.map(item => item.countNum)
      const yData3 = data.countVisit?.map(item => item.countNum)

      // @ts-expect-error any
      chartOption.xAxis.data = xData
      chartOption.series![0].data = yData1
      chartOption.series![1].data = yData2
      chartOption.series![2].data = yData3

      isChartEmpty.value = [...yData1, ...yData2, ...yData3].every(item => item === 0)

      tableData.value = data.countPlanTmp

      await nextTick()

      renderChart()
    }
    else {
      window.$message.error('获取数据失败')
    }
  }
  catch (error) {
    window.$message.error('获取数据失败')
  }
  finally {
    isLoading.value = false
  }
}

function renderChart() {
  if (chartRef.value) {
    myChart?.dispose()
    myChart = echarts.init(chartRef.value)
    myChart.setOption(chartOption as any)
  }
}

window.onresize = () => {
  myChart?.resize()
}

watch(dateForm, () => {
  getData()
},
{
  deep: true,
})

function getSummaries(params) {
  const countNumTotal = params.data?.map(item => item.countNum).reduce((a, b) => a + b, 0) || 0
  const countNoFollowTotal = params.data?.map(item => item.NoFollowNum).reduce((a, b) => a + b, 0) || 0
  const countVisitTotal = params.data?.map(item => item.visitNum).reduce((a, b) => a + b, 0) || 0
  const rate = `${countNumTotal ? ((countVisitTotal / countNumTotal) * 100).toFixed(1) : '0'}%`
  return ['合计', '', countNumTotal, countNoFollowTotal, countVisitTotal, rate]
}

defineExpose({
  getData,
})
</script>

<template>
  <n-spin type="uni" :show="isLoading" size="medium">
    <div my-14px flex items-center gap-10px>
      <div text="#666">
        医生组
      </div>
      <n-select
        v-model:value="docIds"
        :options="docGroupList"
        multiple
        max-tag-count="responsive"
        label-field="doctorGroupName"
        value-field="id"
        class="!w-530px" placeholder="全部"
        @update:value="getData"
      />
    </div>

    <div mt-10px>
      <SectionTitle>
        <span text="#333">入组情况趋势图</span>
      </SectionTitle>
      <div v-if="isChartEmpty" my-70px flex items-center justify-center>
        <n-empty description="无数据" />
      </div>
      <div v-else ref="chartRef" h-300px w-full />
    </div>

    <div>
      <SectionTitle my-14px>
        <span text="#333">入组情况统计表</span>
      </SectionTitle>

      <el-table :data="tableData" default-expand-all show-summary :summary-method="getSummaries">
        <el-table-column label="序号" type="index" width="80" />
        <el-table-column label="医生组" min-width="200" :formatter="({ keyName }) => keyName || '-'" />
        <el-table-column min-width="200" :formatter="({ countNum }) => countNum || 0">
          <template #header>
            <div flex items-center gap-5px>
              <div>
                已入组 (人)
              </div>
              <n-tooltip>
                <template #trigger>
                  <SvgIcon local-icon="slmc-icon-information" color="#666" />
                </template>
                <span>随访累计已入组人数</span>
              </n-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="未入组 (人)" min-width="200" :formatter="({ noFollowNum }) => noFollowNum || 0" />
        <el-table-column min-width="200" :formatter="({ visitNum }) => visitNum || 0">
          <template #header>
            <div flex items-center gap-5px>
              <div>
                复诊患者数 (人)
              </div>
              <n-tooltip>
                <template #trigger>
                  <SvgIcon local-icon="slmc-icon-information" color="#666" />
                </template>
                <span>科室累计复诊人数</span>
              </n-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column min-width="200" :formatter="({ keyName }) => keyName || '-'">
          <template #default="{ row }">
            {{ row.countNum ? `${(row.visitNum / (row.countNum) * 100).toFixed(1)}%` : '0.0%' }}
          </template>
          <template #header>
            <div flex items-center gap-5px>
              <div>
                复诊率
              </div>
              <n-tooltip>
                <template #trigger>
                  <SvgIcon local-icon="slmc-icon-information" color="#666" />
                </template>
                <span>复诊人数/随访人数</span>
              </n-tooltip>
            </div>
          </template>
        </el-table-column>
        <el-table-column type="expand">
          <template #default="props">
            <div bg="#F5F5F5" w-full flex items-center px-20px py-12px>
              <div w-full>
                <div v-for="(item, index) in props.row.doctorGrp" :key="index">
                  <div ml-75px>
                    <div flex justify-between>
                      <div class="w-19%">
                        {{ item.keyName || '-' }}
                      </div>
                      <div class="w-19%">
                        {{ item.countNum || '0' }}
                      </div>
                      <div class="w-19%">
                        {{ item.noFollowNum || '0' }}
                      </div>
                      <div class="w-19%">
                        {{ item.visitNum || '0' }}
                      </div>
                      <div class="w-19%">
                        {{ item.countNum ? `${((item.visitNum / item.countNum) * 100).toFixed(1)}%` : '0' }}
                      </div>
                      <div class="w-1.2%" />
                    </div>
                  </div>
                  <div v-if="index !== props.row.doctorGrp.length - 1" my-12px h-1px w-full bg="#d1d1d1" />
                </div>
              </div>
            </div>
          </template>
        </el-table-column>
        <template #empty>
          <div my-50px flex items-center justify-center>
            <DataEmpty />
          </div>
        </template>
      </el-table>
    </div>
  </n-spin>
</template>

<style scoped>
:deep(.el-table__footer-wrapper > table) {
  border:1px solid #d1d1d1;
  height: 50px;
}

:deep(.el-table__footer-wrapper > table  > tfoot > tr > td) {
  background: #fff!important;
}

:deep(.el-table__footer-wrapper > table  > tfoot > tr > td > div) {
  color: #FF9B54;
  font-size: 14px;
}
</style>
