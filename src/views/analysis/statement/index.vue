<script lang="ts" setup>
import dayjs from 'dayjs'
import Header from './Header.vue'
import TotalContent from './TotalContent.vue'
import TreeContent from './TreeContent.vue'
import type { Card, FollowDate, Tab } from './types'
import { <PERSON><PERSON><PERSON>, CurrentTab<PERSON>ey, DateForm<PERSON>ey } from './types'
import { getPlanListApi } from '@/api/intelligentFollow/manage'
import { useAuthStore } from '@/store'
import TREE_ICON from '@/assets/images/statement/tree.png'
import FOLLOW_ICON from '@/assets/images/statement/people.png'
import TREE_ACTIVE_ICON from '@/assets/images/statement/tree-active.png'
import FOLLOW_ACTIVE_ICON from '@/assets/images/statement/people-active.png'
import { getDocGroupListAPI, getSaplingTotalAPI } from '~/src/api/statement'

const { userInfo } = useAuthStore()
const activeTab = ref<Tab>('follow')
const dateForm = ref<FollowDate>({
  startTime: dayjs().subtract(3, 'month').format('YYYY-MM-DD 00:00:00'),
  endTime: dayjs().format('YYYY-MM-DD 23:59:59'),
})

const cardList = ref<Card[]>([
  {
    type: 'follow',
    label: '随访总人数',
    count: 0,
    icon: FOLLOW_ICON,
    activeIcon: FOLLOW_ACTIVE_ICON,
  },
  // {
  //   type: 'message',
  //   label: '消息发送量',
  //   count: 0,
  //   icon: MSG_ICON,
  // },
  {
    type: 'tree',
    label: '树苗总数',
    count: 0,
    icon: TREE_ICON,
    activeIcon: TREE_ACTIVE_ICON,
  },
])

const planList = ref<any[]>([])
const docGroupList = ref<any[]>([])

async function getPlanList() {
  try {
    const { data } = await getPlanListApi({
      search: '',
      operateId: userInfo.id,
    })

    if (data)
      planList.value = data?.filter((item: any) => [1, 3].includes(item.tempState))
  }
  catch (error) {

  }
}

async function getDocGroupList() {
  try {
    const { data } = await getDocGroupListAPI({
      organId: userInfo.organId,
      name: '',
    })
    docGroupList.value = data
  }
  catch (error) {

  }
}

async function getSaplingTotal() {
  try {
    const { data } = await getSaplingTotalAPI({
      start: dateForm.value.startTime,
      end: dateForm.value.endTime,
    })

    cardList.value[cardList.value.length - 1].count = data || 0
  }
  catch (error) {

  }
}

getPlanList()
getDocGroupList()
getSaplingTotal()

provide(CurrentTabKey, activeTab)
provide(DateFormKey, dateForm)
provide('planList', planList)
provide('docGroupList', docGroupList)
provide(CardKey, cardList)
</script>

<template>
  <div class="content">
    <Header />
    <TotalContent v-if="activeTab === 'follow'" />
    <!-- <Message v-if="activeTab === 'message'" /> -->
    <TreeContent v-if="activeTab === 'tree'" />
  </div>
</template>

<style scoped lang="scss">
.content {
    height: 100%;
    padding: 0;
    display: flex;
    flex-direction: column;
}
:deep( .n-tabs-tab--active){
    background: #06aea6 !important;
    border-radius: 3px 3px 0px 0px !important;
    color: #ffffff !important;
}
</style>
