<script lang='ts' setup>
import SortByDate from './SortByDate.vue'
import SortByDoc from './SortByDoc.vue'

const SortByDateRef = ref<InstanceType<typeof SortByDate>>()
const SortByDocRef = ref<InstanceType<typeof SortByDoc>>()
const activeButton = ref<'DATE' | 'DOC'>('DATE')

watch(activeButton, (val) => {
  if (val === 'DATE')
    SortByDateRef.value?.getData()
  else if (val === 'DOC')
    SortByDocRef.value?.getData()
}, {
  flush: 'post',
})

onMounted(() => {
  SortByDateRef.value?.getData()
})
</script>

<template>
  <div class="bg-#fff" mx-14px p-14px pb-0>
    <PageTitle>
      统计 - 随访总人数
    </PageTitle>
    <div my-14px h-1px w-full bg="#ccc" />
    <div overflow-auto class="h-[calc(100vh-360px)] pb-14px">
      <n-button-group>
        <n-button :type="activeButton === 'DATE' ? 'primary' : undefined" @click="activeButton = 'DATE'">
          按日期统计
        </n-button>
        <n-button :type="activeButton === 'DOC' ? 'primary' : undefined" @click="activeButton = 'DOC'">
          按医生组统计
        </n-button>
      </n-button-group>

      <template v-if="activeButton === 'DATE'">
        <SortByDate ref="SortByDateRef" />
      </template>
      <template v-if="activeButton === 'DOC'">
        <SortByDoc ref="SortByDocRef" />
      </template>
    </div>
  </div>
</template>
