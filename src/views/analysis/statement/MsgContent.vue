<script lang='ts' setup>
import type { TableColumnCtx } from 'element-plus'
import * as echarts from 'echarts'
import { DateFormKey } from './types'

interface User {
  id: string
  name: string
  amount1: string
  amount2: string
  amount3: number
}

interface SpanMethodProps {
  row: User
  column: TableColumnCtx<User>
  rowIndex: number
  columnIndex: number
}

const pieTotal = 1048 + 735 + 580

const pieChartOption = ref<echarts.EChartsOption>({
  tooltip: {
    show: true,
    trigger: 'item',
    borderWidth: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    formatter: (params) => {
      return `
        <div style="color: #fff;font-size:12px">
          ${params.data.name}
        </div>
        <div style="color: #fff;font-size:12px">
          发送量：${params.data.value}
        </div>
        <div style="color: #fff;font-size:12px">
          占比：${`${(params.data.value / pieTotal * 100).toFixed(1)}%`}
        </div>
      `
    },
  },
  legend: {
    top: '0',
    left: 'center',
    itemWidth: 12,
  },
  series: [
    {
      name: 'pie',
      type: 'pie',
      radius: ['40%', '60%'],
      center: ['50%', '50%'],
      avoidLabelOverlap: false,
      label: {
        show: true,
        position: 'outer',
        alignTo: 'labelLine',
        bleedMargin: 5,
        formatter: (params) => {
          return `${params.name}：${params.value}（${((params.value as number) / pieTotal * 100).toFixed(1)}%）`
        },
      },
      labelLine: {
        show: true,
      },
      data: [
        { value: 1048, name: '小程序', itemStyle: { color: '#5B96FD' } },
        { value: 735, name: '短信', itemStyle: { color: '#FFC569' } },
        { value: 580, name: 'AI电话', itemStyle: { color: '#3AC9A8' } },
      ],
    },
  ],
  graphic: { // 使用 graphic 元素添加自定义图形
    type: 'text',
    left: 'center',
    top: 'center',
    style: {
      text: String(pieTotal), // 在这里设置你想要的文字
      fill: '#333', // 文字颜色
      fontSize: 18,
      fontWeight: 600,
    },
  },
})
const barChartOption = ref<echarts.EChartsOption>({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow',
    },
  },
  legend: {},
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true,
  },
  xAxis: {
    type: 'value',
    boundaryGap: [0, 0.01],
    axisLabel: {
      showMinLabel: true,
      showMaxLabel: true,
    },
  },
  yAxis: {
    name: '发送方式',
    type: 'category',
    data: ['小程序', 'AI电话', '短信'],
  },
  series: [
    {
      name: '2011',
      type: 'bar',
      color: '#5B96FD',
      data: [18203, 23489, 29034],
    },
    {
      name: '2012',
      type: 'bar',
      color: '#9EC97F',
      data: [19325, 23438, 31000],
    },
  ],
})

function objectSpanMethod({
  row,
  column,
  rowIndex,
  columnIndex,
}: SpanMethodProps) {
  if (rowIndex === 4) {
    return {
      rowspan: 1,
      colspan: 1,
    }
  }

  // if (rowIndex === 2) {
  // //   return {
  // //     rowspan: 1,
  // //     colspan: 3,
  // //   }
  // }
  // else {
  //   return {
  //     rowspan: 0,
  //     colspan: 0,
  //   }
  // }
}

const tableData = ref(
  [
    {
      id: '1',
      name: 'Tom',
      plan: '随访计划名称随访计划名称随访计最多二十字',
      amount1: '234',
      amount2: '3.2',
      amount3: 10,
    },
    {
      id: '2',
      name: 'Tom',
      plan: '随访计划名称随访计划名称随访计最多二十字',
      amount1: '165',
      amount2: '4.43',
      amount3: 12,
    },
    {
      id: '3',
      name: 'Tom',
      amount1: '324',
      plan: '随访计划名称随访计划名称随访计最多二十字',
      amount2: '1.9',
      amount3: 9,
    },
    {
      id: '4',
      name: 'Tom',
      amount1: '324',
      plan: '随访计划名称随访计划名称随访计最多二十字',
      amount2: '1.9',
      amount3: 9,
    },
    {
      id: '5',
      amount1: '123',
      plan: '',
      amount2: '456',
      amount3: 9,
    },
  ],
)

const dateForm = inject(DateFormKey)!
const planList = inject<Ref<any[]>>('planList')!
const planListIds = ref([])

const PieRef = ref<HTMLElement>()
const BarRef = ref<HTMLElement>()

let pieChart: echarts.ECharts | null = null
let barChart: echarts.ECharts | null = null

function renderChart() {
  if (PieRef.value) {
    pieChart?.dispose()
    pieChart = echarts.init(PieRef.value)
    pieChart.setOption(pieChartOption.value as any)
  }

  if (BarRef.value) {
    barChart?.dispose()
    barChart = echarts.init(BarRef.value)
    barChart.setOption(barChartOption.value as any)
  }
}

onMounted(() => {
  renderChart()
  // chartInit(chartOption.value)
  // chartInit(chartOption2.value, 'teamTrend2')
})
</script>

<template>
  <div class="h-[calc(100vh-280px)] bg-#fff" mx-14px mb-14px overflow-auto p-14px>
    <PageTitle>
      统计 - 消息发送量
    </PageTitle>

    <div my-14px flex items-center gap-10px>
      <div text="#666">
        随访计划
      </div>
      <n-select
        v-model:value="planListIds"
        :options="planList"
        multiple
        max-tag-count="responsive"
        label-field="planTemplateName"
        value-field="planTemplateId"
        class="!w-530px" placeholder="全部"
        @update:value="getData"
      />
    </div>

    <div h-312px w-full flex border-b="1px solid #e8e8e8">
      <div flex-1 border-r="1px solid #e8e8e8">
        <SectionTitle>
          发送量
        </SectionTitle>
        <div ref="PieRef" mt-20px h-280px w-full />
      </div>
      <div flex-1>
        <SectionTitle ml-14px>
          接收量
        </SectionTitle>
        <div ref="BarRef" mt-20px h-240px w-full />
      </div>
    </div>
  </div>
</template>
