<script lang="ts" setup>
import { NTooltip, type RadioGroupProps } from 'wowjoy-vui'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import _ from 'lodash'
import { useThemeStore } from '~/src/store'
import { Breadcrumb } from '@/layouts/common'
import { getStatisticsDatas, statisticsAPI } from '~/src/api/statistics'
import { localStg } from '~/src/utils/storage/local'
import { SvgIcon } from '~/src/components/Icon'
import { usePermission } from '@/hooks'

const { hasPermission } = usePermission()

const theme = useThemeStore()
const sbday = ref('')
const showSpin = ref(false)
const userInfo = localStg.get('userInfo')
type RadioGroupThemeOverrides = NonNullable<RadioGroupProps>['themeOverrides']
const setColors: RadioGroupThemeOverrides = {
  buttonColorActive: '#06aea6',
}
const shortTimes = {
  本月: [dayjs().startOf('month').valueOf(), dayjs().valueOf()],
  上月: [dayjs().subtract(1, 'month').startOf('month').valueOf(), dayjs().subtract(1, 'month').endOf('month').valueOf()],
  最近三个月: [dayjs().subtract(2, 'month').startOf('month').valueOf(), dayjs().valueOf()],
  最近六个月: [dayjs().subtract(5, 'month').startOf('month').valueOf(), dayjs().valueOf()],
  最近12个月: [dayjs().subtract(11, 'month').startOf('month').valueOf(), dayjs().valueOf()],
}
let myChart: echarts.ECharts | null

function getBtns() {
  const btnDatas = [
    {
      isShow: hasPermission('lddp:statistics:main:hepatitisBCured'),
      type: '乙肝-临床治愈',
      range: [dayjs().subtract(11, 'month').startOf('month').valueOf(), dayjs().valueOf()],
      cacheData: null,
      requestMethod: 'GET',
      requestUrl: statisticsAPI.hepatitisBCured,
      table: {
        header: {
          top: '时间',
          bottom: '统计维度',
        },
        lefts: [
          { name: '乙肝患者总数' },
          { name: '纳入随访患者数' },
          { name: '纳入随访治愈率', tip: '进行随访的乙肝患者中，临床治愈患者所占比例' },
          { name: '未纳入随访患者数' },
          { name: '未纳入随访治愈率', tip: '未进行随访的乙肝患者中，临床治愈患者所占比例' },
        ],
      },
      chart: {
        tableName: '临床治愈率趋势图',
        colors: ['#24BEE8', '#FF9B54'],
        xName: '时间',
        series: [{ name: '纳入随访治愈率' }, { name: '未纳入随访治愈率' }],
        yName: '',
        showLegend: false,
      },
    },
    {
      type: '乙肝-DNA转阴率',
      requestMethod: 'GET',
      isShow: hasPermission('lddp:statistics:main:hepatitisBDna'),
      range: [dayjs().subtract(11, 'month').startOf('month').valueOf(), dayjs().valueOf()],
      cacheData: null,
      requestUrl: statisticsAPI.hepatitisBDna,
      table: {
        header: {
          top: '时间',
          bottom: '统计维度',
        },
        lefts: [
          { name: '乙肝患者总数' },
          { name: '纳入随访患者数' },
          { name: '纳入随访转阴率', tip: '进行随访的乙肝患者中，DNA指标转阴的患者所占比例' },
          { name: '未纳入随访患者数' },
          { name: '未纳入随访转阴率', tip: '未进行随访的乙肝患者中，DNA指标转阴的患者 所占比例' },
        ],
      },
      chart: {
        tableName: '乙肝DNA转阴率趋势图',
        colors: ['#24BEE8', '#FF9B54'],
        xName: '时间',
        series: [{ name: '纳入随访转阴率' }, { name: '未纳入随访转阴率' }],
        yName: '',
        showLegend: true,
      },
    },
    {
      isShow: hasPermission('lddp:statistics:main:hepatitisCNucleic'),
      type: '丙肝-核酸检测率',
      requestMethod: 'GET',
      range: [dayjs().subtract(11, 'month').startOf('month').valueOf(), dayjs().valueOf()],
      cacheData: null,
      requestUrl: statisticsAPI.hepatitisCNucleic,
      width: 192,
      table: {
        header: {
          top: '时间',
          bottom: '统计维度',
        },
        lefts: [
          { name: '丙肝患者总数' },
          { name: '纳入随访患者数' },
          { name: '纳入随访核酸检测率', tip: '进行随访的丙肝患者中，完成核酸检测的患者所占比例' },
          { name: '未纳入随访患者数' },
          { name: '未纳入随访核酸检测率', tip: '未进行随访的丙肝患者中，完成核酸检测的患者所占比例' },
        ],
      },
      chart: {
        tableName: '核酸检测率趋势图',
        colors: ['#24BEE8', '#FF9B54'],
        xName: '时间',
        series: [{ name: '纳入随访核酸检测率' }, { name: '未纳入随访核酸检测率' }],
        yName: '',
        showLegend: true,
      },
    },
    {
      isShow: hasPermission('lddp:statistics:main:hepatitisCCured'),
      type: '丙肝-临床治愈率',
      requestMethod: 'GET',
      range: [dayjs().subtract(11, 'month').startOf('month').valueOf(), dayjs().valueOf()],
      cacheData: null,
      requestUrl: statisticsAPI.hepatitisCCured,
      table: {
        header: {
          top: '时间',
          bottom: '统计维度',
        },
        lefts: [
          { name: '丙肝患者总数' },
          { name: '纳入随访患者数' },
          { name: '纳入随访治愈率', tip: '进行随访的丙肝患者中，HCV-RNA最近一次检验结果低于检测限的患者所占比例' },
          { name: '未纳入随访患者数' },
          { name: '未纳入随访治愈率', tip: '未进行随访的丙肝患者中，HCV-RNA最近一次检验结果低于检测限的患者所占比例' },
        ],
      },
      chart: {
        tableName: '临床治愈率趋势图',
        colors: ['#24BEE8', '#FF9B54'],
        series: [{ name: '纳入随访治愈率' }, { name: '未纳入随访治愈率' }],
        xName: '时间',
        yName: '',
        showLegend: true,
      },
    },
    {
      isShow: hasPermission('lddp:statistics:main:bmi'),
      type: '脂肪肝-BMI达标率',
      requestMethod: 'POST',
      range: [dayjs().subtract(11, 'month').startOf('month').valueOf(), dayjs().valueOf()],
      cacheData: null,
      requestUrl: statisticsAPI.bmi,
      width: 192,
      table: {
        header: {
          top: '时间',
          bottom: '统计维度',
        },
        lefts: [
          { name: '脂肪肝患者总数', key: 'total' },
          { name: '纳入随访患者数', key: 'follow' },
          { name: '纳入随访BMI达标率', key: 'followBMI', tip: '进行随访的脂肪肝患者中，BMI小于24的患者所占比例' },
          { name: '未纳入随访患者数', key: 'noFollow' },
          { name: '未纳入随访BMI达标率', key: 'noFollowBMI', tip: '未进行随访的脂肪肝患者中，BMI小于24的患者所占比例' },
        ],
      },
      chart: {
        tableName: 'BMI达标率趋势图',
        colors: ['#24BEE8', '#FF9B54'],
        series: [{ name: '纳入随访BMI达标率', key: 'followBMI' }, { name: '未纳入随访BMI达标率', key: 'noFollowBMI' }],
        xName: '时间',
        yName: '',
        showLegend: true,
      },
    },
    {
      isShow: hasPermission('lddp:statistics:main:weightLoss'),
      type: '脂肪肝-48周减重率',
      requestMethod: 'POST',
      range: [dayjs().subtract(11, 'month').startOf('month').valueOf(), dayjs().valueOf()],
      cacheData: null,
      requestUrl: statisticsAPI.weightLoss,
      table: {
        header: {
          top: '时间',
          bottom: '统计维度',
        },
        lefts: [
          { name: '脂肪肝患者总数', key: 'total' },
          { name: '纳入随访患者数', key: 'follow' },
          { name: '纳入随访减重率', key: 'followWeightLoss', tip: '进行随访的脂肪肝患者中，48周时体重减少≥10% 患者所占比例' },
          { name: '未纳入随访患者数', key: 'noFollow' },
          { name: '未纳入随访减重率', key: 'noFollowWeightLoss', tip: '未进行随访的脂肪肝患者中，48周时体重减少≥10% 患者所占比例' },
        ],
      },
      chart: {
        tableName: '48周减重率趋势图',
        colors: ['#24BEE8', '#FF9B54'],
        series: [{ name: '纳入随访减重率', key: 'followWeightLoss' }, { name: '未纳入随访减重率', key: 'noFollowWeightLoss' }],
        xName: '时间',
        showLegend: true,
      },
    },
  ]

  return btnDatas.filter(data => data.isShow)
}
const btns = ref(getBtns())
const selectTypeIndex = ref(0)

/// 处理列表数据
function handleTheColumnData(arr: Array<any>) {
  // 动态加入
  // arr = ['2022-01', '2022-02', '2022-03', '2022-04']
  const currentType = btns.value[selectTypeIndex.value]
  const vnodes: Array<any> = [
    {
      key: 'title',
      title() {
        return h('div', { class: 'table-one', style: { width: '100%' } }, [
          h(
            'div',
            { class: 'table-left', style: { lineHeight: '20px' } },
            currentType.table?.header.top,
          ),
          h(
            'div',
            { class: 'table-top', style: { lineHeight: '20px' } },
            currentType.table?.header.bottom,
          ),
        ])
      },
      fixed: 'left',
      width: currentType.width ?? 158,
      render(_: any, index: number) {
        return h('div', { class: 'flex items-center' }, [
          h('div', { class: 'mr-4px' }, currentType.table?.lefts[index].name),
          currentType.table?.lefts[index]?.tip
            ? h(NTooltip, { trigger: 'hover', placement: 'right' }, {
              trigger: () => h(SvgIcon, {
                localIcon: 'slmc-icon-information',
                class: 'text-#0000004D ml-6px',
              }),
              default: () => h('span', currentType.table?.lefts[index].tip),
            })
            : h('div'),
        ])
      },
    },
  ]

  arr.forEach((element: any) => {
    vnodes.push({
      key: 'time',
      title() {
        return h('div', { style: { textAlign: 'center', minWidth: '76px', flex: '1' } }, element.time)
      },
      render(xx: any, index: number) {
        return h(
          'div',
          { style: { textAlign: 'right', minWidth: '76px', flex: '1' } },
          element[xx.key ?? xx.name],
        )
      },
    })
  })

  return {
    columns: vnodes,
    datas: currentType.table?.lefts,
  }
}

/// 时间限制选择
// function dateSelect(ts: number) {
//   return ts > Date.now()
// }

const typeDatas = ref({})
const jlh = _.debounce(() => {
  updateTableAndChartData(true)
}, 800)

function typeSelect() {
  const curr = btns.value[selectTypeIndex.value]
  curr.range = [dayjs().subtract(11, 'month').startOf('month').valueOf(), dayjs().valueOf()],
  /// 重置时间
  /// 节流
  jlh()
}

/// /echarts 相关
function typeChartOptionsSet(xDatas, yDatas) {
  // const xDatas = ['30以下', '30-39', '40-49', '50-59']
  // const xDatas = ['2020', '2021', '2022', '2023']
  const currentType = btns.value[selectTypeIndex.value]
  const series: Array<echarts.SeriesOption> = []
  currentType?.chart?.series?.forEach((element, index) => {
    series.push({
      name: element.name,
      data: yDatas[index],
      type: 'line',
      showSymbol: true,
      symbolSize: 6,
      emphasis: {
        disabled: true,
        scale: 1.8,
      },
    })
  })

  const options1: echarts.EChartsOption = {
    legend: {
      right: 14,
    },
    tooltip: {
      backgroundColor: 'rgba(0,0,0,0.7)',
      textStyle: {
        fontSize: '12px',
        color: 'white',
      },
      trigger: 'axis',
      padding: 0,
      axisPointer: {
        type: 'line',
        show: false,
      },
      formatter(params) {
        let itemstr = ''
        params.forEach((element) => {
          itemstr += `<div flex items-center my-8px>
              <div w-5px h-1px style=background-color:${element.color}></div>
              <div w-8px h-8px rounded-4px style='border:1px solid ${element.color}'></div>
              <div w-5px h-1px mr-5px  style=background-color:${element.color}></div>
              <div>
                <span mr-3px>${element.seriesName}:</span>
                <span>${element.data}%</span>
                </div>
            </div>`
        })
        const top = `<div>${params[0].axisValue}</div>`
        return `<div px-8px pt-8px>
          ${top}
          ${itemstr}
          </div>`
      },
      borderColor: 'rgba(0,0,0,0,0)',
    },
    color: currentType.chart?.colors,
    xAxis: {
      data: xDatas,
      name: currentType.chart?.xName,
      nameTextStyle: {
        color: '#999',
      },
      axisLine: {
        onZero: true,
        lineStyle: {
          color: '#e8e8e8',
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#999',
      },
    },
    yAxis: {
      show: true,
      name: '',
      axisLabel: {
        show: true,
        interval: 'auto',
        formatter: '{value} %',
        color: '#999',
      },
      max: 100, // 最大值
      min: 0, // 最小值
      nameTextStyle: {
        color: '#999',
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#e8e8e8',
        },
      },
    },
    grid: {
      bottom: 0,
      left: 0,
      right: 42,
      top: 30,
      containLabel: true,
    },
    series,
  }

  return options1
}

function drawTheChart(data: Array<any>) {
  const xDatas: Array<string> = []
  const yDatas: Array<Array<any>> = [[], []]
  const currentType = btns.value[selectTypeIndex.value]

  data?.forEach((element) => {
    xDatas.push(element.time)

    const y0v = element[currentType.chart?.series[0]?.key ?? currentType.chart?.series[0]?.name]
    const y1v = element[currentType.chart?.series[1]?.key ?? currentType.chart?.series[1]?.name]
    yDatas[0].push(Number.parseFloat(y0v) || 0)
    yDatas[1].push(Number.parseFloat(y1v) || 0)
  })

  const option = typeChartOptionsSet(xDatas, yDatas)
  myChart?.clear()
  option && myChart?.setOption(option)
  nextTick(() => {
    myChart?.resize()
  })
}

function initChart() {
  /// 有个动画效果,导致初始化有问
  setTimeout(() => {
    myChart ??= echarts.init(document.getElementById('mychart')!, { renderer: 'svg' })

    jlh()
  }, 500)
}

onMounted(() => {
  initChart()
  // updateTableAndChartData(true)
})

onUnmounted(() => {
  myChart?.dispose()
  myChart = null
})

/// 时间选控制
function timeFormaterSet(value, formatValue) {
  if (value) {
    let startUnix = dayjs(value[0]).valueOf()
    let endUnix = dayjs(value[1]).endOf('month').valueOf()

    if (endUnix > Date.now())
      endUnix = Date.now()

    if (startUnix > Date.now())
      startUnix = dayjs().startOf('month').valueOf()

    const mondif = dayjs(endUnix).diff(startUnix, 'month')
    /// 时间最长跨度不能超过12个月
    if (mondif > 11) {
      /// 则 限定在12 以内
      endUnix = dayjs(startUnix).add(11, 'month').endOf('month').valueOf()
    }

    const curr = btns.value[selectTypeIndex.value]
    curr.range = [startUnix, endUnix]
    if (mondif > 11)
      window.$message.warning('时间跨度最多可选择12个月')
  }

  updateTableAndChartData(true)
}

function updateTableAndChartData(refresh: boolean) {
  if (refresh) {
    getDatasFromNetWork()
  }
  else {
    const curr = btns.value[selectTypeIndex.value]
    if (curr.cacheData == null || curr.cacheData?.length === 0)
      getDatasFromNetWork()
    else
      setTableAndChartData(curr.cacheData)
  }
}

/// 获取数据
function getDatasFromNetWork() {
  const curr = btns.value[selectTypeIndex.value]
  const url = curr.requestUrl
  if (curr.range == null)
    return
  const startT = dayjs(curr.range[0]).format('YYYY-MM')
  const endT = dayjs(curr.range[1]).format('YYYY-MM')
  showSpin.value = true

  getStatisticsDatas(url, curr.requestMethod, curr.requestMethod === 'POST'
    ? {
        start: dayjs(curr.range[0]).format('YYYY-MM-DD HH:ss:mm'),
        end: dayjs(curr.range[1]).format('YYYY-MM-DD HH:ss:mm'),
        organId: userInfo?.organId,
      }
    : null, {
    startTime: startT,
    endTime: endT,
    organId: userInfo?.organId,
  }).then((res) => {
    showSpin.value = false
    curr.cacheData = res?.data
    if (curr.cacheData != null) {
      /// 赋值

      const cudata = btns.value[selectTypeIndex.value]
      setTableAndChartData(cudata.cacheData)
    }
  })
  sbday.value = dayjs().format('YYYY-MM-DD HH:mm:ss')
}

function setTableAndChartData(data) {
  // 设置table
  typeDatas.value = handleTheColumnData(data ?? [])
  // 设置图表
  drawTheChart(data)
}

window.addEventListener('resize', () => {
  myChart?.resize()
})
</script>

<template>
  <div class="content">
    <Breadcrumb route-name="analysis_specialDisease" />
    <div class="content-box">
      <PageTitle>
        专病报表
        <span ml-10px color="[#666]">(截止统计时间: {{ sbday }})</span>
      </PageTitle>
      <div mt-15px overflow-x-auto overflow-y-hidden>
        <n-radio-group
          v-model:value="selectTypeIndex" style="width: fit-content;" :theme-overrides="setColors"
          name="radiobuttongroup2" mode="full" @update:value="typeSelect"
        >
          <n-radio-button v-for="(item, index) in btns" :key="index" :value="index" :label="item.type" />
        </n-radio-group>
      </div>
      <div mt-14px flex items-center>
        <div mr-10px>
          统计时间
        </div>
        <n-date-picker
          v-model:value="btns[selectTypeIndex].range" style="width: 280px" separator="~" format="yyyy-MM-dd" type="monthrange"
          :shortcuts="shortTimes"
          @update-formatted-value="timeFormaterSet"
        />
      </div>
      <n-spin type="uni" size="medium" :show="showSpin" style="flex: 1;">
        <div style="height: 100%;display: flex;flex-direction: column;">
          <n-data-table mt-14px :single-line="false" :bordered="true" :columns="typeDatas.columns" :data="typeDatas.datas" />
          <SectionTitle mb-14px mt-20px>
            {{ btns[selectTypeIndex].chart?.tableName ?? '' }}
          </SectionTitle>
          <div id="mychart" style="width: 100%;flex:1;height: 300px;min-height: 300px;" />
        </div>
      </n-spin>
    </div>
  </div>
</template>

<style scoped lang="scss">
.content {
  padding: 0;
  display: flex;
  flex-direction: column;
}

.content-box {
  height: 100%;
  background-color: white;
  margin: 14px;
  padding: 14px;
  display: flex;
  flex-direction: column;
}

:deep(.n-input-wrapper:nth-child(1)) {
  padding-left: 0;
}

:deep(.table-one) {
  position: relative;

  background: linear-gradient(to bottom left,
      transparent 50%,
      #d1d1d1,
      transparent 52%);

  .table-left {
    text-align: right;
    direction: rtl;
    padding-right: 10px;
  }

  .table-top {
    padding-left: 10px;
  }
}

:deep(.n-data-table-th) {
  &:nth-child(1) {
    padding: 0;
  }
}

:deep(.n-data-table-td) {
  position: relative;
}

#mychart {
  flex: 1;
  width: 100%;
}

:deep(.n-spin-content){
  height: 100%;
}
:deep(.n-data-table:not(.n-data-table--single-line) .n-data-table-td){
  height: 32px;
}
:deep(.n-data-table){
  --n-merged-border-color:#d1d1d1
}
</style>
