<script lang="ts" setup>
import { NTooltip, type RadioGroupProps } from 'wowjoy-vui'
import * as echarts from 'echarts'
import dayjs from 'dayjs'
import _ from 'lodash'
import { Breadcrumb } from '@/layouts/common'
import { useThemeStore } from '~/src/store'
import { getStatisticsDatas, statisticsAPI } from '@/api/statistics/index'
import { localStg } from '~/src/utils/storage/local'
import { SvgIcon } from '~/src/components/Icon'
import { getSbOrganList } from '~/src/api/organization/organization'
import { planPatientCount } from '~/src/api/screen'
import { usePermission } from '@/hooks'

const { hasPermission } = usePermission()

const theme = useThemeStore()
const sbday = ref('')
const userInfo = localStg.get('userInfo')
const route = useRoute()
type RadioGroupThemeOverrides = NonNullable<RadioGroupProps>['themeOverrides']
const setColors: RadioGroupThemeOverrides = {
  buttonColorActive: '#06aea6',
}

let myChart: echarts.ECharts | null
const lineRef = ref()
const shortTimes = {
  本月: [dayjs().startOf('month').valueOf(), dayjs().valueOf()],
  上月: [dayjs().subtract(1, 'month').startOf('month').valueOf(), dayjs().subtract(1, 'month').endOf('month').valueOf()],
  最近三个月: [dayjs().subtract(2, 'month').startOf('month').valueOf(), dayjs().valueOf()],
  最近六个月: [dayjs().subtract(5, 'month').startOf('month').valueOf(), dayjs().valueOf()],
  最近12个月: [dayjs().subtract(11, 'month').startOf('month').valueOf(), dayjs().valueOf()],
}

function getOptionDatas() {
  const optionDatas = [{
    isShow: hasPermission('lddp:statistics:main:all'),
    type: 'SLMC患者总数',
    timeRange: [dayjs().subtract(11, 'month').startOf('month').valueOf(), dayjs().valueOf()],
    timeType: 'monthrange',
    cacheData: null,
    chartName: '患者统计',
    requestMethod: 'POST',
    requestUrl: statisticsAPI.allPatients,
    shortcuts: shortTimes,
    values: ['total', 'follow', 'qoq', 'yoy'],
    defaultTime: [dayjs().subtract(11, 'month').startOf('month').valueOf(), dayjs().valueOf()],
  }, {
    isShow: hasPermission('lddp:statistics:main:age_sex'),
    type: '年龄(性别)分布',
    chartName: '年龄(性别)人数统计',
    requestMethod: 'POST',
    timeRange: [dayjs().subtract(11, 'month').startOf('month').valueOf(), dayjs().valueOf()],
    cacheData: null,
    shortcuts: null,
    timeType: 'monthrange',
    requestUrl: statisticsAPI.ageSex,
    defaultTime: [dayjs().subtract(11, 'month').startOf('month').valueOf(), dayjs().valueOf()],
  }, {
    isShow: hasPermission('lddp:statistics:main:follow'),
    type: '随访率',
    requestMethod: 'GET',
    chartName: '随访率趋势图',
    timeRange: [dayjs().subtract(11, 'month').startOf('month').valueOf(), dayjs().valueOf()],
    requestUrl: statisticsAPI.sfRate,
    cacheData: null,
    shortcuts: null,
    timeType: 'monthrange',
    shortcuts: shortTimes,
    defaultTime: [dayjs().subtract(11, 'month').startOf('month').valueOf(), dayjs().valueOf()],
  }, {
    isShow: hasPermission('lddp:statistics:main:disease'),
    type: '疾病分类占比',
    requestMethod: 'POST',
    chartName: '疾病分布统计',
    timeRange: [dayjs().subtract(3, 'year').startOf('year').valueOf(), dayjs().valueOf()],
    cacheData: null,
    shortcuts: null,
    timeType: 'yearrange',
    requestUrl: statisticsAPI.diseases,
    defaultTime: [dayjs().subtract(3, 'year').startOf('year').valueOf(), dayjs().valueOf()],
  }]

  return optionDatas.filter(data => data.isShow)
}
const optionDatas = ref(getOptionDatas())

const selectTypeIndex = ref(0)

// 获取配置表
function getOptions() {
  const curr = optionDatas.value[selectTypeIndex.value]
  switch (curr.type) {
    case 'SLMC患者总数':
      return {
        header: {
          top: '时间',
          bottom: '统计维度',
        },
        width: 144,
        lefts: [
          { name: '患者总数' },
          { name: '纳入随访患者数' },
          { name: '环比增长', tip: '与上月相比,患者增长率' },
          { name: '同比增长', tip: '与上年度同期相比,患者增长率' },
        ],
        colors: ['#24BEE8', '#FF9B54'],
        xName: '时间',
        yName: '人数',
        showLegend: true,
        series: ['患者总数', '新增患者数'],
      }
    case '年龄(性别)分布':
      return {
        header: {
          top: '年龄',
          bottom: '性别',
        },
        lefts: [{ name: '男' }, { name: '女' }],
        colors: ['#24BEE8', '#FA8383', '#3AC9A8'],
        xName: '年龄',
        yName: '人数',
        showLegend: true,
        series: ['男', '女'],
      }
    case '随访率':
      return {
        header: {
          top: '时间',
          bottom: '统计维度',
        },
        lefts: [{ name: '计划随访人数' }, { name: '完成随访人数' }, { name: '随访率' }],
        colors: ['#24BEE8'],
        xName: '时间',
        yName: '',
        showLegend: true,
        type: 'line', // 折线图
        series: ['随访率'],
        formater: true,
        yaxis: {
          axisLine: {
            show: true,
          },
          axisLabel: {
            show: true,
            interval: 'auto',
            formatter: '{value} %',
          },
          max: 100, // 最大值
          min: 0, // 最小值
        },
      }
    case '疾病分类占比':
      return {
        header: {
          top: '时间',
          bottom: '统计维度',
        },
        lefts: [
          { name: '患者总数' },
          { name: '乙肝' },
          { name: '脂肪肝' },
          { name: '丙肝' },
          { name: '其他肝病' },
        ],
        colors: ['#FF9B54', '#24BEE8', '#FA8383', '#3AC9A8'],
        xName: '时间',
        yName: '人数',
        showLegend: true,
        series: ['乙肝', '脂肪肝', '丙肝', '其他肝病'],
      }
    default:
      return {
        header: {
          top: '时间',
          bottom: '医院名称',
        },
        lefts: [{ name: '树兰(杭州)' }, { name: '树兰(衢州)' }],
      }
  }
}

/// 处理列表数据
function handleTheColumnData(arr: Array<any>, tabValues: any) {
  // 动态加入
  // arr = ['2022-01', '2022-02', '2022-03', '2022-04']
  const options = getOptions()
  const curr = optionDatas.value[selectTypeIndex.value]

  const lineHeight = curr.type === '疾病分类占比' ? '25px' : '20px'

  const vnodes: [...any] = [
    {
      key: 'title',
      title() {
        return h('div', { class: 'table-one' }, [
          h(
            'div',
            { class: 'table-left', style: { lineHeight } },
            options?.header.top,
          ),
          h(
            'div',
            { class: 'table-top', style: { lineHeight } },
            options?.header.bottom,
          ),
        ])
      },
      fixed: 'left',
      width: 144,
      render(_: any, index: number) {
        return h('div', {
          style: {
            'display': 'flex',
            'align-items': 'center',
          },
        }, [
          h('div', {}, options?.lefts[index].name),
          options?.lefts[index]?.tip
            ? h(NTooltip, { trigger: 'hover', placement: 'right' }, {
              trigger: () => h(SvgIcon, {
                localIcon: 'slmc-icon-information',
                class: 'text-#0000004D ml-6px',
              }),
              default: () => h('span', options?.lefts[index].tip),
            })
            : h('div', {}, ''),
        ])
      },
    },
  ]

  function cutomTitle(element: any) {
    if (curr.type === '疾病分类占比') {
      return h('div', { style: { textAlign: 'center', lineHeight: '25px' } }, [
        h('div', { style: { position: 'relative' } }, element),
        h('div', { style: { position: 'absolute', left: 0, right: 0, background: '#d1d1d1', height: '1px' } }),
        h('div', { style: { display: 'flex' } }, [
          h('div', { style: { flex: 1, borderRight: '1px solid #d1d1d1', paddingRight: '10px' } }, '人数'),
          h('div', { style: { flex: 1, paddingLeft: '10px' } }, '占比'),
        ]),
      ])
    }
    else {
      return h('div', { style: { textAlign: 'center', minWidth: '76px', flex: '1' } }, element)
    }
  }

  arr.forEach((element: any) => {
    vnodes.push({
      key: 'time',
      title() {
        return cutomTitle(element)
      },
      render(_: any, index: number) {
        if (curr.type === '疾病分类占比') {
          return h('div', { style: { textAlign: 'center', display: 'flex' } }, [
            h(
              'div',
              { style: { textAlign: 'right', flex: 1, paddingRight: '10px' } },
              tabValues[element][0][index],
            ),
            h('div', { style: { position: 'absolute', top: 0, bottom: 0, background: '#d1d1d1', width: '1px', left: '50%' } }),
            h(
              'div',
              { style: { textAlign: 'right', flex: 1, paddingLeft: '10px' } },
              tabValues[element][1][index],
            ),
          ])
        }
        const val = tabValues[element][index]
        let color = '#333'
        if (val.includes('-') && val.length > 1)
          color = '#F36969'
        return h(
          'div',
          { style: { textAlign: 'right', minWidth: '76px', flex: '1', color } },
          val,
        )
      },
    })
  })

  return {
    columns: vnodes,
    datas: options?.lefts,
  }
}

/// 时间限制选择
// function dateSelect(ts: number) {
//   return ts > Date.now()
// }

const typeDatas = ref({})
const jlh = _.throttle(() => {
  updateTableAndChartData(true)
}, 200)

function typeSelect(index) {
  // Object.assign(currentType, optionDatas.value[index])
  /// 节流
  const curr = optionDatas.value[selectTypeIndex.value]
  curr.timeRange = curr.defaultTime
  jlh()
}

/// /echarts 相关
function typeChartOptionsSet(xDatas: Array<string>, yValuse: Map<string, Array<any>>) {
  const opt = getOptions()
  const series: Array<echarts.SeriesOption> = []
  if (opt.type === 'line') {
    opt?.series?.forEach((element) => {
      series.push({
        name: element,
        data: yValuse[element],
        type: 'line',
        showSymbol: true,
        symbolSize: 6,
        emphasis: {
          disabled: true,
          scale: 1.8,
        },
      })
    })
  }
  else {
    opt?.series?.forEach((element) => {
      series.push({
        name: element,
        type: 'bar',
        barWidth: 14,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: 'rgba(0,0,0,0.3)',
          },
        },
        data: yValuse[element],
      })
    })
  }

  const legend = opt.type === 'line'
    ? {
        right: 14,
      }
    : {
        show: opt?.showLegend,
        selectedMode: true,
        right: 14,
        icon: 'path://M 10 10 L 30 10 L 30 30 L 10 30 z',
      }

  const options1: echarts.EChartsOption = {
    legend,
    tooltip: {
      backgroundColor: 'rgba(0,0,0,0.7)',
      textStyle: {
        fontSize: '12px',
        color: 'white',
      },
      trigger: 'axis',
      padding: 0,
      axisPointer: {
        type: opt?.type ?? 'shadow',
      },
      formatter(params) {
        let itemstr = ''
        params.forEach((element) => {
          if (selectTypeIndex.value === 2) {
            itemstr += `<div flex items-center my-8px>
              <div w-5px h-1px style=background-color:${element.color}></div>
              <div w-8px h-8px rounded-4px style='border:1px solid ${element.color}'></div>
              <div w-5px h-1px mr-5px  style=background-color:${element.color}></div>
              <div>
                <span mr-3px>${element.seriesName}:</span>
                <span>${element.data}%</span>
                </div>
            </div>`
          }
          else if (selectTypeIndex.value === 3) {
            itemstr += `<div flex items-center my-8px>
              <div w-8px h-8px mr-5px style=background-color:${element.color}></div>
              <div>
                <span mr-3px>${element.seriesName}:</span>
                <span>${`${element.data}人`}</span>
                <span ml-10px>${yValuse[element.name][`${element.seriesName}percent`]}</span>
                </div>
            </div>`
          }
          else {
            itemstr += `<div flex items-center my-8px>
              <div w-8px h-8px mr-5px style=background-color:${element.color}></div>
              <div>
                <span mr-3px>${element.seriesName}:</span>
                <span>${element.data}</span>
                </div>
            </div>`
          }
        })
        const top = `<div>${params[0].axisValue}</div>`
        return `<div px-8px pt-8px>
          ${top}
          ${itemstr}
          </div>`
      },
      borderColor: 'rgba(0,0,0,0,0)',
    },
    color: opt?.colors,
    xAxis: {
      data: xDatas,
      name: opt?.xName,
      nameTextStyle: {
        color: '#999',
      },
      axisLine: {
        onZero: true,
        lineStyle: {
          color: '#e8e8e8',
        },
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#999',
      },
    },
    yAxis: opt?.yaxis ?? {
      show: true,
      minInterval: 1,
      nameTextStyle: {
        color: '#999',
      },
      name: opt.yName ?? '人数',
      axisLine: {
        show: true,
        lineStyle: {
          color: '#e8e8e8',
        },
      },
      axisLabel: {
        color: '#999',
      },
    },
    grid: {
      bottom: 0,
      left: 0,
      right: 42,
      top: 30,
      containLabel: true,
    },
    series,
  }

  return options1
}

/// 机构选择

const sbjgOptions = ref([])
const currentJGId = ref()
/// 获取机构列表
function getOrganList() {
  return getSbOrganList().then((res) => {
    /// / 机构赋值
    // const sbjg = repetAddchildren(res.data ?? [])
    // console.log(sbjg)
    sbjgOptions.value = res.data ?? []
    /// 默认选中第一个
    if (sbjgOptions.value.length > 0)
      currentJGId.value = sbjgOptions.value[0].id
  })
}

function organSelect(v) {
  /// 选择机构

  currentJGId.value = v.id
  getDatasFromNetWork()
}

/// 添加子机构
// function repetAddchildren(arr) {
//   const temp = []
//   arr.forEach((element) => {
//     // const two = {
//     //   label: element.organName,
//     //   key: element.id,
//     // }

//     if (element.children && element.children.length > 0) {
//       const three = repetAddchildren(element.children)
//       element.children = three
//     }
//     // else {
//     //   two.prefix = () => {
//     //     return h(SvgIcon, {
//     //       localIcon: 'slmc-icon-wenjian1',
//     //       size: '14',
//     //     })
//     //   }
//     // }
//     temp.push(two)
//   })

//   return temp
// }

function drawTheChart(xDatas, chartValues) {
  const option = typeChartOptionsSet(xDatas, chartValues)
  myChart?.clear()
  option && myChart?.setOption(option)
  nextTick(() => {
    myChart?.resize()
  })
}

function initChart() {
  myChart ??= echarts.init(lineRef.value)
}

onMounted(async () => {
  initChart()
  // await getOrganList()
  const jType = route.query?.indexType

  for (let index = 0; index < optionDatas.value.length; index++) {
    const element = optionDatas.value[index]
    if (element.type === jType) {
      selectTypeIndex.value = index
      break
    }
  }

  /// 带过来的 机构id
  // if (route.query?.organId)
  //   currentJGId.value = route.query?.organId

  updateTableAndChartData(true)
})

onUnmounted(() => {
  myChart?.dispose()
  myChart = null
})

/// 时间选控制
function timeFormaterSet(value, formatValue) {
  const currType = optionDatas.value[selectTypeIndex.value]

  if (value) {
    let startUnix = dayjs(value[0]).valueOf()
    let endUnix = dayjs(value[1]).endOf('month').valueOf()

    if (endUnix > Date.now())
      endUnix = Date.now()

    if (startUnix > Date.now())
      startUnix = dayjs().startOf('month').valueOf()

    const mondif = dayjs(endUnix).diff(startUnix, 'month')
    /// 时间最长跨度不能超过12个月
    if (mondif > 11 && currType.type !== '疾病分类占比') {
      /// 则 限定在12 以内
      endUnix = dayjs(startUnix).add(11, 'month').endOf('month').valueOf()
      window.$message.warning('时间跨度最多可选择12个月')
    }

    if (currType.type === '疾病分类占比') {
      /// 时间跨度不能超过10年
      const yeardiff = dayjs(endUnix).diff(startUnix, 'year')
      if (yeardiff >= 9)
        endUnix = dayjs(startUnix).add(9, 'year').endOf('year').valueOf()

      else
        endUnix = dayjs(endUnix).endOf('year').valueOf()

      if (endUnix > Date.now())
        endUnix = Date.now()

      if (yeardiff > 9)
        window.$message.warning('时间跨度最多可选择10年')
    }
    currType.timeRange = [startUnix, endUnix]
  }

  updateTableAndChartData(true)
}

/// 更新图表数据
function updateTableAndChartData(refresh: boolean) {
  /// 一种是需要重新获取数据
  if (refresh) {
    /// 掉接口刷新
    getDatasFromNetWork()
  }
  else {
    const curr = optionDatas.value[selectTypeIndex.value]
    if (curr.cacheData == null || curr.cacheData?.length === 0)
      getDatasFromNetWork()
    else
      setTableAndChartData(curr.cacheData)
  }
}

window.addEventListener('resize', () => {
  myChart?.resize()
})

/// 获取接口数据
async function getDatasFromNetWork() {
  ///
  sbday.value = dayjs().format('YYYY-MM-DD HH:mm:ss')
  const curr = optionDatas.value[selectTypeIndex.value]
  if (curr.timeRange == null)
    return
  const startT = dayjs(curr.timeRange[0]).format('YYYY-MM-DD HH:mm:ss')
  const endT = dayjs(curr.timeRange[1]).format('YYYY-MM-DD HH:mm:ss')
  const rels = await getStatisticsDatas(curr.requestUrl, curr.requestMethod, {
    start: startT,
    end: endT,
    organId: userInfo?.organId,
  }, {
    startTime: startT,
    endTime: endT,
    organId: userInfo?.organId,
  })

  /// / 如果是患者总数, 加一个新增患者数
  if (curr.chartName === '患者统计') {
    const newRes = await planPatientCount({
      startTime: startT,
      endTime: endT,
      organId: userInfo?.organId,
    })
    curr.cacheData = rels?.data
    setTableAndChartData(curr.cacheData, newRes?.data)
  }
  else {
    curr.cacheData = rels?.data
    /// 赋值
    setTableAndChartData(curr.cacheData)
  }
}

///  处理数据
function setTableAndChartData(data: any, newpatientChart: any = null) {
  const curr = optionDatas.value[selectTypeIndex.value]
  let xDatas: Array<any> = []
  const tableValues = {}
  let chartValues = {}
  let iindex = 0
  switch (curr.type) {
    case 'SLMC患者总数':
      chartValues = {
        患者总数: [],
        新增患者数: [],
      }
      data.forEach((element) => {
        xDatas.push(element.time)
        tableValues[element.time] = [element.total, element.follow, element.qoq, element.yoy]
        chartValues['患者总数'].push(element.total)
        chartValues['新增患者数'].push(newpatientChart[element.time])
      })
      break
    case '年龄(性别)分布':
      chartValues = {
        男: [],
        女: [],
        // 未知: [],
      }
      xDatas = ['30岁以下', '30-39岁', '40-49岁', '50-54岁', '55-59岁', '60-65岁', '65岁以上']
      for (const key in data) {
        tableValues[xDatas[iindex]] = [data[key].nan, data[key].nv]
        chartValues['男'].push(data[key].nan)
        chartValues['女'].push(data[key].nv)
        // chartValues['未知'].push(data[key].bunanbunv)
        iindex++
      }
      break
    case '随访率':
      chartValues = {
        随访率: [],
      }
      data?.forEach((element) => {
        xDatas.push(element?.time ?? '')
        /// 处理一下小数
        tableValues[element.time!] = [element?.total, element?.finishedNum, `${element?.rate.toFixed(2)}%`,
        ]
        chartValues['随访率'].push(element?.rate.toFixed(2))
      })
      break
    case '疾病分类占比':
      chartValues = {
        乙肝: [],
        脂肪肝: [],
        丙肝: [],
        其他肝病: [],
        乙肝percent: '',
        脂肪肝percent: '',
        丙肝percent: '',
        其他肝病percent: '',
      }
      data?.forEach((element) => {
        xDatas.push(element?.time)
        tableValues[element?.time] = [[element?.all?.count, element?.disease1?.count, element.disease2?.count, element.disease3?.count, element?.disease0?.count], [element.all.percent, element?.disease1?.percent, element?.disease2?.percent, element?.disease3?.percent, element?.disease0?.percent]]
        chartValues['乙肝'].push(element?.disease1?.count)
        chartValues['脂肪肝'].push(element?.disease2?.count)
        chartValues['丙肝'].push(element?.disease3?.count)
        chartValues['其他肝病'].push(element?.disease0?.count)
        chartValues[element?.time] = {
          乙肝percent: element?.disease1?.percent,
          脂肪肝percent: element?.disease2?.percent,
          丙肝percent: element?.disease3.percent,
          其他肝病percent: element?.disease0.percent,
        }
      })
      break
  }
  typeDatas.value = handleTheColumnData(xDatas ?? [], tableValues)
  drawTheChart(xDatas, chartValues)
}
</script>

<template>
  <div class="content">
    <Breadcrumb route-name="analysis_statement" />
    <div class="content-box">
      <PageTitle>
        统计报表
        <span ml-10px color="[#666]">(截止统计时间: {{ sbday }})</span>
      </PageTitle>
      <div mt-15px>
        <n-radio-group
          v-model:value="selectTypeIndex"
          :theme-overrides="setColors"
          name="radiobuttongroup2"
          mode="full"
          @update:value="typeSelect"
        >
          <n-radio-button
            v-for="(item, index) in optionDatas"
            :key="index"
            :value="index"
            :label="item.type"
          />
        </n-radio-group>
      </div>
      <div mt-14px flex items-center>
        <div v-if="selectTypeIndex !== 1" mr-20px flex items-center>
          <div mr-10px>
            统计时间
          </div>
          <n-date-picker
            v-model:value="optionDatas[selectTypeIndex].timeRange"
            style="width: 280px"
            separator="~"
            format="yyyy-MM-dd"
            :type="optionDatas[selectTypeIndex].timeType"
            :shortcuts="optionDatas[selectTypeIndex].shortcuts"
            @update-formatted-value="timeFormaterSet"
          />
        </div>
        <!-- <div mr-10px>
          机构名称
        </div>
        <JgSelectTree :default-value="currentJGId" :options="sbjgOptions" @value-select="organSelect" /> -->
      </div>
      <n-data-table
        mt-14px
        :single-line="false"
        :bordered="true"
        :columns="typeDatas.columns"
        :data="typeDatas.datas"
      />
      <SectionTitle mb-14px mt-20px>
        {{ optionDatas[selectTypeIndex].chartName }}
      </SectionTitle>
      <div id="mychart" ref="lineRef" style="width: 100%; flex: 1; height: 400px; min-height: 400px;" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.content {
    padding: 0;
    display: flex;
    flex-direction: column;
}

.content-box {
    height: 100%;
    background-color: white;
    margin: 14px;
    padding: 14px;
    display: flex;
    flex-direction: column;
}

:deep(.n-input-wrapper:nth-child(1) ){
    padding-left: 0;
}

:deep(.table-one) {
    width: 144px;
    position: relative;

    background: linear-gradient(
        to bottom left,
        transparent 50%,
        #d1d1d1,
        transparent 52%
    );

    div {
        width: 144px;
    }
    .table-left {
        text-align: right;
        direction: rtl;
        padding-right: 10px;
    }

    .table-top {
        padding-left: 10px;
    }
}

:deep(.n-data-table-th) {
    &:nth-child(1) {
        padding: 0;
    }
}
:deep(.n-data-table-td){
  position: relative;
}

#mychart {
    flex: 1;
    width: 100%;
}

:deep(.n-data-table:not(.n-data-table--single-line) .n-data-table-td){
  height: 32px;
}
:deep(.n-data-table){
  --n-merged-border-color:#d1d1d1
}
</style>
