<script lang='ts' setup>
import { AuxiliaryExamine, BaseInfo, FollowManage, HealthSummary, LabExamine, PatientHeader, TreatMethod, VitalSign } from './components'
import type { BreadLit } from '@/layouts/common'
import { Breadcrumb } from '@/layouts/common'

const route = useRoute()
const PATIENT_ID = route.query?.patientId as string
const PATIENT_NAME = route.query?.patientName as string
const goEdit = route.query?.isEdit
const type = route.query?.type
const nTab = route.query?.nTab
enum TABS {
  HealthSummary = 'HEALTH_SUMMARY',
  BaseInfo = 'BASE_INFO',
  VitalSign = 'VITAL_SIGN',
  TreatMethod = 'TREAT_METHOD',
  LabExamine = 'LAB_EXAMINE',
  AuxiliaryExamine = 'AUXILIARY_EXAMINE',
  FollowManage = 'FOLLOW_MANAGE',
}

const tagVale = ref(nTab || TABS.BaseInfo)
const sbTab = ref()

const renderBread = computed(() => {
  const list: BreadLit[] = [
    { title: '专病数据库', link: '/specificDisease', key: 'specificDisease' },
    { title: '患者列表', link: '/specificDisease/list', key: 'specificDisease_list' },
    { title: `${PATIENT_NAME}患者档案`, link: null, key: 'specificDisease_detail' },
  ]
  return list
})

function sbjianting(name, oldName) {
  if (oldName === 'BASE_INFO') {
    /// SB弹窗
    if (sbTab.value.activeComponent !== 'EDIT')
      return true

    return new Promise((resolve) => {
      window.$dialog.warning({
        title: '离开此页面将清空未保存数据，确定离开？',
        positiveText: '留在此页面',
        negativeText: '离开',
        positiveButtonProps: {
          color: '#06AEA6',
        },
        onPositiveClick: () => {
          console.log('xxx')
          resolve(false)
        },
        onNegativeClick: () => {
          console.log('111')
          resolve(true)
        },
      },
      )
    })
  }
  return true
}
</script>

<template>
  <div class="specificDisease">
    <Breadcrumb :bread-list="renderBread" />
    <div class="p-14px">
      <PatientHeader />
      <n-card class="card">
        <n-tabs animated type="card" :tab-style="{ width: '116px' }" :default-value="tagVale" @before-leave="sbjianting">
          <n-tab-pane :name="TABS.BaseInfo" :tab="type === '慢乙肝' ? '专病档案' : '基础信息'">
            <BaseInfo ref="sbTab" :edit-index="1" :default-type="goEdit ? 'EDIT' : 'BASE'" />
          </n-tab-pane>
          <n-tab-pane :name="TABS.HealthSummary" tab="健康小结">
            <HealthSummary />
          </n-tab-pane>
          <n-tab-pane :name="TABS.VitalSign" tab="生命体征">
            <VitalSign />
          </n-tab-pane>
          <n-tab-pane :name="TABS.TreatMethod" tab="治疗技术">
            <TreatMethod />
          </n-tab-pane>
          <n-tab-pane :name="TABS.LabExamine" tab="实验室检查">
            <LabExamine />
          </n-tab-pane>
          <n-tab-pane :name="TABS.AuxiliaryExamine" tab="辅助检查">
            <AuxiliaryExamine />
          </n-tab-pane>
          <n-tab-pane :name="TABS.FollowManage" tab="随访管理">
            <div class="h-[calc(100vh-212px)]" p-20px>
              <FollowManage />
            </div>
          </n-tab-pane>
        </n-tabs>
      </n-card>
    </div>
  </div>
</template>

<style scoped lang="scss">
.specificDisease {
    :deep(.n-card.n-card--bordered){
        border:0px;
    }

    .card {
        height: calc(100vh - 190px);

        box-shadow: 0px 1px 4px 0px rgba(202,202,202,0.50);
        :deep(.n-card__content){
           padding:0px;
           height:100%;

        }
        :deep(.n-tabs){
            height: 100%;
            .n-tab-pane {
                height: 100%;
                padding-top: 0px;
            }
        }
        :deep(.n-tabs-pane-wrapper){
            flex:1
        }
    }
}
</style>
