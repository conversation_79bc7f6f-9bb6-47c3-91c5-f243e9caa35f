<script lang='ts' setup>
import dayjs from 'dayjs'
import isBetween from 'dayjs/plugin/isBetween'
import TaskCard from './components/TaskCard.vue'
import TaskItemCard from './components/TaskItemCard.vue'
import { CurrentTaskKey, type Plan, PlansKey } from './types'
import EDU from './components/Education.vue'
import CHECK from './components/Check.vue'
import SCALE from './components/Scale.vue'
import { getPlanPhaseAPI } from '@/api/intelligentFollow/plan'

defineOptions({
  name: 'FollowManage',
})

dayjs.extend(isBetween)

const taskDetailComponentMap = {
  EDU,
  CHECK,
  SCALE,
}

const route = useRoute()

const isSpecificDisease = computed(() => {
  return route.name === 'specificDisease_detail'
})

const patientId = route.query.patientId as string
const pName = route.query.pName as string | undefined

const planTemplateName = ref('')

defineExpose({
  planTemplateName,
})

const plans = ref<Plan[]>([])
const currentPlanIndex = ref(-1)
const currentPhaseIndex = ref(-1)
const currentTaskIndex = ref(-1)
const isLoading = ref(false)

provide(PlansKey, plans)
provide('currentPlanIndex', currentPlanIndex)
provide('currentPhaseIndex', currentPhaseIndex)
provide('currentTaskIndex', currentTaskIndex)
provide('isLoading', isLoading)

async function getPlanPhase() {
  try {
    const { data } = await getPlanPhaseAPI(patientId)
    if (data) {
      planTemplateName.value = data[0].planTemplateName
      const sortByYearData = data?.reduce((acc, plan) => {
        const year = new Date(plan.followTime).getFullYear()
        if (!acc[year])
          acc[year] = []

        acc[year].push(plan)
        return acc
      }, {})

      const formattedData = Object.entries(sortByYearData).map(([year, phases]) => ({
        year: Number.parseInt(year, 10),
        phases,
      })).sort((a, b) => a.year - b.year)

      formattedData?.forEach((item: any) => {
        item.phases?.forEach((item2: any) => {
          if (dayjs().isBetween(item2.followTimeStart, item2.followTimeEnd, 'day', '[]') && item2.finishRate < 1)
            item2.phaseStatus = 'START'

          if (item2.tasks)
            item2.tasks = Object.values(item2?.tasks).flat()
        })
      })

      plans.value = formattedData as any

      currentPlanIndex.value = 0
      currentPhaseIndex.value = 0
      currentTaskIndex.value = 0
    }
  }
  catch (error) {
    console.log(error)
  }
}

function handlePlanClick(planIndex: number, phaseIndex: number) {
  currentPlanIndex.value = planIndex
  currentPhaseIndex.value = phaseIndex
  currentTaskIndex.value = 0
}

function handleTaskClick(taskIndex: number) {
  currentTaskIndex.value = taskIndex
}

const currentPhase = computed(() => {
  if (currentPlanIndex.value === -1 || currentPhaseIndex.value === -1)
    return null

  return plans.value[currentPlanIndex.value].phases[currentPhaseIndex.value]
})

const currentTask = computed(() => {
  if (currentPhase.value?.tasks)
    return currentPhase.value.tasks[currentTaskIndex.value]
  else
    return null
})

provide(CurrentTaskKey, currentTask)

onMounted(() => {
  if (pName)
    planTemplateName.value = pName

  else
    getPlanPhase()
})
</script>

<template>
  <n-spin type="uni" :show="isLoading">
    <div h-full flex>
      <div v-if="currentPlanIndex > -1 && currentTask" w-219px flex-shrink-0 overflow-auto pb-14px class="h-[calc(100vh-250px)]">
        <div flex gap-10px>
          <div sub-title />
          随访方案
        </div>
        <n-select v-model:value="planTemplateName" disabled class="my-14px !w-203px" />
        <n-timeline :icon-size="10">
          <n-timeline-item v-for="(plan, planIndex) in plans" :key="planIndex">
            <template #header>
              <span text="#666 12px">
                {{ plan.year }}年
              </span>
            </template>
            <div flex="~ col" gap-14px>
              <TaskCard
                v-for="(phase, phaseIndex) in plan.phases" :key="phase.phaseId"
                :phase="phase"
                :is-active="planIndex === currentPlanIndex && phaseIndex === currentPhaseIndex"
                @click="handlePlanClick(planIndex, phaseIndex)"
              />
            </div>
            <template #icon>
              <SvgIcon local-icon="slmc-icon-dian" />
            </template>
          </n-timeline-item>
        </n-timeline>
      </div>
      <template v-if="currentPlanIndex > -1 && currentTask">
        <div :class="isSpecificDisease ? 'h-[calc(100vh-250px)]' : 'h-[calc(100vh-220px)]'" mr-14px w-1px flex-shrink-0 bg="#ccc" />
        <div w-205px flex-shrink-0 overflow-auto pb-14px class="h-[calc(100vh-250px)]">
          <div flex items-center gap-10px>
            <div sub-title />
            随访阶段-V{{ currentPhase?.phaseTemplateCode }}
          </div>
          <div my-14px>
            <span text="#666">
              实际到访日期：
            </span>
            <span text="#333">
              {{ currentPhase?.reachTime ? dayjs(currentPhase?.reachTime).format('YYYY-MM-DD') : '-' }}
            </span>
          </div>
          <div flex="~ col" gap-14px overflow-auto>
            <TaskItemCard
              v-for="(task, taskIndex) in currentPhase?.tasks"
              :key="task.taskId"
              :task="task"
              :is-active="taskIndex === currentTaskIndex"
              @click="handleTaskClick(taskIndex)"
            />
          </div>
        </div>
        <div :class="isSpecificDisease ? 'h-[calc(100vh-250px)]' : 'h-[calc(100vh-220px)]'" mr-14px w-1px flex-shrink-0 bg="#ccc" />
        <div flex-1>
          <div flex gap-10px>
            <div sub-title />
            {{ currentTask?.projectName }}
          </div>
          <div mt-15px flex justify-between>
            <div flex>
              <div text="#666">
                随访窗口期：
              </div>
              <div text="#333">
                {{ dayjs(currentPhase?.followTimeStart).format('YYYY-MM-DD') }} ~ {{ dayjs(currentPhase?.followTimeEnd).format('YYYY-MM-DD') }}
              </div>
            </div>
            <div flex>
              <div text="#666">
                提交时间：
              </div>
              <div text="#333">
                {{ currentTask?.finishedTime ? dayjs(currentTask?.finishedTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}
              </div>
            </div>
          </div>
          <div mt-15px :class="isSpecificDisease ? 'h-[calc(100vh-340px)]' : 'h-[calc(100vh-300px)]'" overflow-auto>
            <component
              :is="taskDetailComponentMap[currentTask.projectType]"
              v-if="currentTask?.projectType"
              :task-id="currentTask.taskId"
              :project-id="currentTask.projectId"
              :project-type="currentTask.projectType"
              :check-inspect="currentTask.checkInspect"
            />
          </div>
        </div>
      </template>
      <template v-else>
        <div :class="isSpecificDisease ? 'h-[calc(100vh-315px)]' : 'h-[calc(100vh-270px)]'" flex flex-1 items-center justify-center>
          <n-empty size="large" description="无数据" />
        </div>
      </template>
    </div>
  </n-spin>
</template>

<style scoped>
:deep(.n-timeline .n-timeline-item .n-timeline-item-timeline .n-timeline-item-timeline__circle){
  border:none
}
</style>
