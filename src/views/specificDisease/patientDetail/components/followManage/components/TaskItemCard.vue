<script setup lang="ts">
import type { Status, Task } from '../types'

defineProps<{
  task: Task
  isActive: boolean
}>()

const statusMap: Record<Status, {
  color: string
  icon: string
}> = {
  OVERTIME: {
    icon: 'slmc-icon-yiyuqi',
    color: 'text-#A5B8D1',
  },
  FINISH: {
    icon: 'slmc-icon-done1',
    color: 'text-#3AC9A8',
  },
  START: {
    icon: 'slmc-icon-jinhangzhong',
    color: 'text-#ff9152',
  },
  WAIT: {
    icon: 'slmc-icon-jinhangzhong',
    color: 'text-#c5c5c5',
  },
  CUTOFF: {
    icon: 'slmc-icon-jinhangzhong',
    color: 'text-#c5c5c5',
  },
}

const projectTypeMap: Record<string, string> = {
  EDU: '患者宣教',
  CHECK: '检验检查',
  SCALE: '随访问卷',
  QUOTA: '重点指标',
}
</script>

<template>
  <div class="i-card" :class="{ 'i-card-active': isActive }">
    <div flex items-center gap-6px leading-none>
      <SvgIcon :class="statusMap[task.taskStatus]?.color" size="16" :local-icon="statusMap[task.taskStatus]?.icon" />
      <n-ellipsis text="#333 14px" font-500>
        {{ task.projectName }}
      </n-ellipsis>
    </div>
    <div text="#666 12px" ml-22px mt-8px leading-none>
      {{ projectTypeMap[task.projectType] }}
    </div>
  </div>
</template>

<style lang="scss" scoped>
.i-card {
  --uno: h-56px w-190px cursor-pointer b-1 b-rd-3px p-10px bg-#f5f5f5 b-#f5f5f5;
  box-sizing: border-box;

  &:hover {
    --uno: bg-#fff b-1px b-#06AEA6;
  }
}

.i-card-active {
  --uno: bg-#F1FCFB b-1px b-#06AEA6 cursor-not-allowed;

  &:hover {
    --uno: bg-#F1FCFB b-1px b-#06AEA6;
  }
}
</style>
