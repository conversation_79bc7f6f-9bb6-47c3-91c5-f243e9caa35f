<script setup lang="ts">
import { Model } from 'survey-core'
import { CurrentTaskKey, PlansKey } from '../types'
import { getScaleOnTask } from '@/api/toolsFollow'
import 'survey-core/survey.i18n'
import 'survey-core/defaultV2.min.css'
import { saveAnswerAPI } from '@/api/toolsFollow/survey'
import { useAuthStore } from '@/store'
import '@/utils/survey/customFun'
import '@/utils/survey/customComponent'

const props = defineProps<{
  taskId: string
  taskStatus: string
}>()

const emit = defineEmits(['commitSuccess'])

const { userInfo } = useAuthStore()

const currentTask = inject(CurrentTaskKey)!
const plans = inject(PlansKey)!
const isLoading = inject<Ref<boolean>>('isLoading')!
const currentPlanIndex = inject<Ref<number>>('currentPlanIndex')!
const currentPhaseIndex = inject<Ref<number>>('currentPhaseIndex')!
const currentTaskIndex = inject<Ref<number>>('currentTaskIndex')!

const canRender = ref(false)
let survey: any = null

async function getTaskDetail() {
  try {
    canRender.value = false
    isLoading.value = true
    const { data } = await getScaleOnTask(props.taskId)
    const answer = data.answer
    survey = new Model(JSON.parse(data.scale.content))

    survey.locale = 'zh-cn'
    survey.showCompletedPage = false
    canRender.value = true
    survey.data = JSON.parse(answer)

    if ((currentTask && currentTask.value && ['START', 'OVERTIME'].includes(currentTask.value!.taskStatus))
        || (props.taskStatus && props.taskStatus !== 'FINISH'))
      survey.mode = 'edit'
    else
      survey.mode = 'display'

    survey.onComplete.add(async (sender: any) => {
      // options.showSaveInProgress('提交中...')
      try {
        const { data } = await saveAnswerAPI({
          answer: JSON.stringify(sender.data),
          taskId: props.taskId,
          writeId: userInfo.id,
          writeType: 'MEDICAL',
        })

        if (data) {
          getTaskDetail()
          if (plans && plans.value)
            plans.value[currentPlanIndex.value].phases[currentPhaseIndex.value].tasks[currentTaskIndex.value].taskStatus = 'FINISH'
          if (props.taskStatus && props.taskStatus !== 'FINISH') {
            // 返回一个已经完成任务的事件
            emit('commitSuccess', true)
          }
          window.$message.success('提交成功')
        }
      }
      catch (error) {
        console.log('提交问卷失败=====', error)
      }
    })

    survey.onValueChanged.add((sender, options) => {
      console.log(`Value changed: ${options.name} - ${options.value}`)
    })

    survey.onCurrentPageChanged.add((sender, options) => {
      console.log(`Page changed: ${options.newCurrentPage.name}`)
    })
  }
  catch (error) {
    console.log(error)
  }
  finally {
    isLoading.value = false
  }
}

watch(() => props.taskId, () => {
  getTaskDetail()
}, {
  immediate: true,
})
</script>

<template>
  <SurveyComponent v-if="canRender" :model="survey" />
</template>
