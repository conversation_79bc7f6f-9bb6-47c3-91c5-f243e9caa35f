<script setup lang="ts">
import dayjs from 'dayjs'
import type { Phase, Status } from '../types'

defineProps<{
  phase: Phase
  isActive: boolean
}>()

const statusMap: Record<Status, {
  label: string
  color: string
}> = {
  OVERTIME: {
    label: '已逾期',
    color: 'bg-#A5B8D1',
  },
  FINISH: {
    label: '已完成',
    color: 'bg-#3AC9A8',
  },
  START: {
    label: '进行中',
    color: 'bg-#FF9B54',
  },
  WAIT: {
    label: '待开始',
    color: 'bg-#ccc',
  },
  CUTOFF: {
    label: '已截止',
    color: 'bg-#ccc',
  },
}
</script>

<template>
  <div class="i-card" :class="{ 'i-card-active': isActive }">
    <div mb-10px flex items-center justify-between>
      <div font="500" text="14px #333">
        V{{ phase.phaseTemplateCode }}
      </div>
      <div :class="statusMap[phase.phaseStatus].color" text="12px #fff center" h-20px w-56px b-rd-10px leading-20px>
        {{ statusMap[phase.phaseStatus].label }}
      </div>
    </div>
    <div text="#666" mb-10px leading-none>
      完成进度：{{ phase?.finishRate ? `${(phase.finishRate * 100).toFixed(2)}%` : '0.00%' }}
    </div>
    <div text="#666" leading-none>
      {{ dayjs(phase.followTimeStart).format('YYYY-MM-DD') }} ~ {{ dayjs(phase.followTimeEnd).format('YYYY-MM-DD') }}
    </div>
  </div>
</template>

<style lang="scss" scoped>
.i-card {
  --uno: h-84px w-183px cursor-pointer b-1 b-rd-3px p-10px bg-#f5f5f5 b-#f5f5f5;

  &:hover {
    --uno: bg-#fff b-1px b-#06AEA6;
  }
}

.i-card-active {
  --uno: bg-#F1FCFB b-1px b-#06AEA6 cursor-not-allowed;

  &:hover {
    --uno: bg-#F1FCFB b-1px b-#06AEA6;
  }
}
</style>
