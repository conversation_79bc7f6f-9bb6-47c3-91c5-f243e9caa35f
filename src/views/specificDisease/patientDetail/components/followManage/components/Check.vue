<script setup lang="ts">
import { getCheckDataAPI, getInspectDataAPI } from '@/api/intelligentFollow/manage'

const props = defineProps<{
  taskId: string
  projectType: string
  checkInspect: 'CHECK' | 'JC'
}>()

const tableData = ref<any[]>([])
const datas = ref<any>()

const isLoading = inject<Ref<boolean>>('isLoading')!

async function getDetail() {
  try {
    isLoading.value = true
    if (props.checkInspect === 'CHECK') {
      const { data } = await getCheckDataAPI(props.taskId)
      // 数据过滤
      tableData.value = data.map((item) => {
        if (!['↓', '↑'].includes(item.hint))
          item.hint = ''

        return {
          ...item,
        }
      })
    }
    else {
      const { data } = await getInspectDataAPI(props.taskId)
      datas.value = data
    }
  }
  catch (error) {

  }
  finally {
    isLoading.value = false
  }
}

watch(() => props.taskId, () => {
  getDetail()
}, {
  immediate: true,
})
</script>

<template>
  <div>
    <el-table v-if="checkInspect === 'CHECK'" :max-height="660" :data="tableData" show-overflow-tooltip stripe>
      <el-table-column label="指标名称" min-width="100" :formatter="(row: any) => row.chineseName || '-' " />
      <el-table-column label="结果" width="100">
        <template #default="{ row }">
          <div v-if="!row?.hint">
            {{ row.result || '-' }}
          </div>
          <div v-else color="#F36969" flex items-center>
            {{ row.result || '-' }}
            {{ row.hint || '-' }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="单位" width="100" :formatter="(row: any) => row.unit?.replaceAll(' ', '') || '-'" />
      <el-table-column label="参考范围" min-width="100" :formatter="(row: any) => row.refValue || '-'" />

      <template #empty>
        <div flex justify-center>
          <DataEmpty />
        </div>
      </template>
    </el-table>
    <div v-else flex="~ col" gap-14px>
      <div flex>
        <div color="#666">
          检查项目：
        </div>
        <div color="#333">
          {{ datas?.examItem || '-' }}
        </div>
      </div>

      <div flex>
        <div flex-shrink-0 color="#666">
          检查描述：
        </div>
        <div bg="#f4f5f6" w-full break-all b-rd-3px p-14px leading-20px>
          {{ datas?.imageSight || '-' }}
        </div>
      </div>

      <div flex>
        <div flex-shrink-0 color="#666">
          检查结论：
        </div>
        <div color="#333" leading-20px>
          {{ datas?.imageDiagnosis || '-' }}
        </div>
      </div>

      <div flex>
        <div flex-shrink-0 color="#666">
          报告医生：
        </div>
        <div color="#333">
          {{ datas?.performer || '-' }}
        </div>
      </div>
    </div>
  </div>
</template>
