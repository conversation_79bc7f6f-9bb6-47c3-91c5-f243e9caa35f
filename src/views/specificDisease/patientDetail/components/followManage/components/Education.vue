<script setup lang="ts">
import { getEduDetailAPI } from '@/api/toolsFollow/education'

const props = defineProps<{
  projectId: string
}>()

const content = ref('')

const isLoading = inject<Ref<boolean>>('isLoading')!

async function getEduDetail() {
  try {
    isLoading.value = true
    const { data } = await getEduDetailAPI(props.projectId)
    content.value = data.content
  }
  catch (error) {

  }
  finally {
    isLoading.value = false
  }
}

watch(() => props.projectId, () => {
  getEduDetail()
}, {
  immediate: true,
})
</script>

<template>
  <div class="editor-content-view" style="white-space: pre-wrap" v-html="content" />
</template>
