export type Status = 'WAIT' | 'START' | 'FINISH' | 'OVERTIME' | 'CUTOFF'

export interface Task {
  projectType: string
  taskId: string
  projectId: string
  projectName: string
  taskStatus: Status
  finishedTime: string
  checkInspect: 'CHECK' | 'JC'
}

export interface Phase {
  phaseStatus: Status
  finishRate: number | null
  phaseTemplateCode: number
  phaseId: string
  followTime: string
  followTimeStart: string
  followTimeEnd: string
  tasks: Task[]
  reachTime: string | null
}

export interface Plan {
  year: number
  phases: Phase[]
}

export const PlansKey: InjectionKey<Ref<Plan[]>> = Symbol('Plans')
export const CurrentTaskKey: InjectionKey<ComputedRef<Task | null>> = Symbol('CurrentTask')
