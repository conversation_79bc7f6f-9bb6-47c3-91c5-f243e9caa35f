<script lang='ts' setup>
import dayjs from 'dayjs'
import { BasicModal } from '@/components/Modal'
import type { BasicTable } from '@/components/Table'
import { addNormaLifeDataApi, deleteTheNormalCheck, getNormaLifeListApi, updateTheNormalCheck } from '@/api/specificDisease'
import { type ECOption, useEcharts } from '@/hooks'

const route = useRoute()
const patientId = route.query?.patientId as string
/// table
const tableRef = ref<InstanceType<typeof BasicTable> | null>(null)

const tableData = ref([])
const columnsData = [{ label: '体温 (°C)', value: 'temperature' }, { label: '脉搏 (次/min)', value: 'pulse' },
  { label: '呼吸 (次/min)`)', value: 'respiratoryRate' }, { label: '血压 (mmHg)', value: 'systolicPressure' },
]
const selectTabIndex = ref(0)
/// y 轴数据(真实数据,hover 要显示的数据啊,脑壳疼哟!!!)
const yAixTrueList = ref<any>([])
/// 趋势图
const lineOptions = ref<any>({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      label: {
        backgroundColor: '#6a7985',
      },
    },
    backgroundColor: 'rgba(0,0,0,0.70)',
    textStyle: {
      color: '#fff',
      fontSize: 12,
    },

  },
  grid: {
    left: '2%',
    right: '4%',
    bottom: '20%',
    containLabel: true,
  },
  xAxis: [
    {
      type: 'category',
      boundaryGap: false,
      axisLine: {
        lineStyle: {
          color: '#ccc',
        },
      },
      axisLabel: {
        color: '#999',
        showMinLabel: true, // 显示最小值标签
        showMaxLabel: true, // 显示最大值标签
        // rotate: 45, // 逆时针旋转 45 度
        formatter(val) {
          // 当字符串长度超过2时
          if (val.length > 2) {
            // 把字符串分割成字符串数组
            const array = val.split('-')
            // 在下标2处删除0个，加上回车
            array.splice(1, 0, '\n')
            array.splice(3, 0, '-')
            return array.join('')
          }
          else {
            return val
          }
        },
      },
      data: [],
    },
  ],
  yAxis: [
    {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#ccc',
        },
      },
      axisLabel: {
        color: '#999',
      },
    },
    {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#ccc',
        },
      },
      axisLabel: {
        color: '#999',
      },
    },
  ],
  series: [
    {
      name: '',
      type: 'line',
      connectNulls: true, // 连接空值
      emphasis: {
        focus: 'series',
      },
      data: [],
    },
  ],
}) as Ref<ECOption>
const { domRef: lineRef } = useEcharts(lineOptions)
const yAixIsNoData = ref(false)

// 分页器
const paginationReactive = reactive({
  page: 1,
  pageSize: 10,
  total: 0,
  pageSizes: [10, 20, 50, 100],
})
/**
 * 展示一般体征的新增框
 */
const showNormalAdd = ref(false)
const normalFormValue = reactive({
  date: Date.now(),
  title: '新增',
  temperature: null,
  pulse: null,
  respiratoryRate: null,
  systolicPressure: null,
  diastolicPressure: null,
  heartRate: null,
})

function loadDatasTable() {
  const params = {
    // TODO
    patientId, // '1680788234808799233',
    size: paginationReactive.pageSize,
    start: paginationReactive.page,
  }
  getNormaLifeListApi(params).then((res: any) => {
    tableData.value = res.data.records
    paginationReactive.total = res.data.total
    const list = res.data.records.map((item: any) => dayjs(item.date).format('YYYY-MM-DD'))
    lineOptions.value.xAxis[0].data = list.reverse()
    headerEvent(selectTabIndex.value)
  })
}

async function handleNormalConfirm() {
  ///  一般体征保存
  const params = {
    date: normalFormValue.date ? dayjs(normalFormValue.date).format('YYYY-MM-DD') : '-',
    diastolicPressure: normalFormValue.diastolicPressure,
    heartRate: normalFormValue.heartRate,
    patientId, // '1680788234808799233',
    pulse: normalFormValue.pulse,
    respiratoryRate: normalFormValue.respiratoryRate,
    source: '手动填写',
    systolicPressure: normalFormValue.systolicPressure,
    temperature: normalFormValue.temperature,
  }

  let res
  if (normalFormValue.id != null) {
    // 是修改
    params.id = normalFormValue.id
    res = await updateTheNormalCheck(params)
  }
  else {
    res = await addNormaLifeDataApi(params)
  }

  // const res = await addNormaLifeDataApi(params)
  if (res) {
    /// 保存成功
    loadDatasTable()
    resetFormValue()
    showNormalAdd.value = false
    // toast 一下
    window.$message.success(normalFormValue.id ? '添加成功' : '修改成功')
  }
}

/**
 * 弹窗打开/关闭的callback
 * @param v true:打开，false:关闭
 */
async function visibleChange(v: boolean) {
  if (!v) {
    resetFormValue()
    showNormalAdd.value = false
  }
}

/// 点击表头
function headerEvent(index: any) {
  selectTabIndex.value = index

  yAixTrueList.value = tableData.value.map((item: any) => item[columnsData[index].value]).reverse()
  lineOptions.value.series[0].data = yAixTrueList.value
  lineOptions.value.series[0].name = columnsData[index].label
  // /// 是否全部为空值
  yAixIsNoData.value = yAixTrueList.value.every((item) => {
    return item === '-' || item === ''
  })

  if (index === 3) {
    lineOptions.value.series[0].name = '收缩压'

    const list = tableData.value.map((item: any) => item.diastolicPressure).reverse()
    lineOptions.value.series = [
      {
        name: '收缩压',
        type: 'line',
        connectNulls: true, // 连接空值

        data: yAixTrueList.value,
      }, {
        name: '舒张压',
        type: 'line',
        connectNulls: true, // 连接空值

        data: list,
      },
    ]
  }
  else if (lineOptions.value.series.length > 1) {
    lineOptions.value.series.splice(1, 1)
  }
}

function editBodyData(row) {
  showNormalAdd.value = true
  normalFormValue.title = '修改'
  normalFormValue.date = dayjs(row.date).valueOf()
  normalFormValue.temperature = row.temperature
  normalFormValue.pulse = row.pulse
  normalFormValue.respiratoryRate = row.respiratoryRate
  normalFormValue.systolicPressure = row.systolicPressure
  normalFormValue.diastolicPressure = row.diastolicPressure
  normalFormValue.heartRate = row.heartRate
  normalFormValue.id = row.id
}

function deleteBodyData(row) {
  window.$dialog.warning({
    title: '确定删除该记录吗？',
    btnGhost: true,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      /// 去删除
      deleteTheNormalCheck(row.id).then(() => {
        /// 刷新
        loadDatasTable()
      })
    },
  })
}

function resetFormValue() {
  normalFormValue.date = null
  normalFormValue.temperature = null
  normalFormValue.pulse = null
  normalFormValue.respiratoryRate = null
  normalFormValue.systolicPressure = null
  normalFormValue.diastolicPressure = null
  normalFormValue.heartRate = null
  normalFormValue.id = null
  normalFormValue.title = '新增'
}

onMounted(() => {
  loadDatasTable()
})
</script>

<template>
  <div>
    <n-button color="#06AEA6" my-14px @click="showNormalAdd = true">
      新增
    </n-button>
    <!-- <BasicTable
      ref="tableRef"
      :columns="columns"
      :action-column="actionCloumns"
      :request="loadDataTable"
      :pagination="paginationReactive"
      striped
    /> -->

    <el-table
      ref="tableRef"
      :data="tableData"
      max-height="320px"
      stripe
      table-layout="auto"
    >
      <el-table-column label="序号" :fixed="columnsData.length > 5">
        <template #default="scope">
          <div>
            {{ scope.$index + 1 }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="日期" :fixed="columnsData.length > 5" min-width="120">
        <template #default="scope">
          {{ scope.row.date }}
        </template>
      </el-table-column>
      <el-table-column v-for="(item, index) in columnsData" :key="index">
        <template #header>
          <div align-items-center flex @click="headerEvent(index)">
            <n-tooltip trigger="hover">
              <template #trigger>
                <SvgIcon v-if="selectTabIndex === index" local-icon="slmc-icon-duoxuanxuanzhong" size="16" mt-3px />
                <SvgIcon v-else local-icon="slmc-icon-fuxuankuang1" size="16" mt-3px />
              </template>
              <span>勾选后，可将{{ item.label }}数据加入下方趋势图查看走势</span>
            </n-tooltip>
            <span w-5px />
            <div class="tabble-header">
              {{ item.label }}
            </div>
          </div>
        </template>
        <template #default="scope">
          <span>{{ item.value !== 'systolicPressure' ? (scope.row[item.value] || '-') : `${scope.row[item.value]}/${scope.row.diastolicPressure}` }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="tableData.length > 0" label="数据来源">
        <template #default="scope">
          {{ scope.row.source }}
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="80" fixed="right">
        <template #default="{ row }">
          <div v-if="row.source !== '住院'" flex items-center gap-5px>
            <span uno-link @click="editBodyData(row)">修改</span>
            <div h-14px w-1px bg="#3B8FD9" />
            <span uno-link @click="deleteBodyData(row)">删除</span>
          </div>
          <div v-else>
            -
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div v-if="tableData.length > 0" mt-14px flex justify-end>
      <n-pagination
        v-model:page="paginationReactive.page" v-model:page-size="paginationReactive.pageSize"
        :to="false" :item-count="paginationReactive.total" :page-sizes="[10, 20, 30]" show-size-picker
        show-quick-jumper @update:page="loadDatasTable" @update:page-size="loadDatasTable"
      />
    </div>

    <div v-if="tableData.length > 0" mb-14px>
      <SectionTitle class="mt-20px -mb-20px">
        {{ columnsData[selectTabIndex]?.label }}趋势图
      </SectionTitle>
      <div v-if="yAixIsNoData" h-260px flex justify-center pt-80px>
        <DataEmpty />
      </div>
      <div v-else ref="lineRef" class="h-270px" />
      <div h-30px />
    </div>

    <BasicModal
      :visible="showNormalAdd"
      :title="normalFormValue.title"
      :height="355"
      width="560"
      :footer-offset="235"
      @ok="handleNormalConfirm"
      @visible-change="visibleChange"
    >
      <n-form
        label-placement="left"
        class="my-24px ml-17px mr-30px"
        require-mark-placement="left"
        :model="normalFormValue"
      >
        <n-form-item label="日期">
          <n-date-picker v-model:value="normalFormValue.date" placeholder="yy-mm-dd" mr-44px w-full />
        </n-form-item>
        <n-form-item label="体温">
          <n-input-number v-model:value="normalFormValue.temperature" placeholder="请输入" :show-button="false" mr-44px w-full>
            <template #suffix>
              <div color="#999">
                °C
              </div>
            </template>
          </n-input-number>
        </n-form-item>
        <n-form-item label="脉搏">
          <n-input-number v-model:value="normalFormValue.pulse" placeholder="请输入" :show-button="false" mr-44px w-full>
            <template #suffix>
              <div color="#999">
                次/min
              </div>
            </template>
          </n-input-number>
        </n-form-item>
        <n-form-item label="呼吸">
          <n-input-number v-model:value="normalFormValue.respiratoryRate" placeholder="请输入" :show-button="false" mr-44px w-full>
            <template #suffix>
              <div color="#999">
                次/min
              </div>
            </template>
          </n-input-number>
        </n-form-item>
        <n-form-item label="血压">
          <div mr-44px w-full>
            <n-input-number v-model:value="normalFormValue.systolicPressure" :show-button="false">
              <template #prefix>
                <div
                  color="#666" style="background-color:#f5f5f5; text-align: center; margin-left: -20px;
    border-right: 1px solid #d1d1d1;" font-14px w-84px
                >
                  收缩压
                </div>
              </template>
              <template #suffix>
                <div color="#999">
                  mmHg
                </div>
              </template>
            </n-input-number>
            <n-input-number v-model:value="normalFormValue.diastolicPressure" :show-button="false" mt-10px>
              <template #prefix>
                <div
                  color="#666" style="background-color:#f5f5f5; text-align: center; margin-left: -20px;
    border-right: 1px solid #d1d1d1;" font-14px w-84px
                >
                  舒张压
                </div>
              </template>
              <template #suffix>
                <div color="#999">
                  mmHg
                </div>
              </template>
            </n-input-number>
          </div>
        </n-form-item>
        <n-form-item label="心率">
          <n-input-number v-model:value="normalFormValue.heartRate" placeholder="请输入" :show-button="false" mr-44px w-full>
            <template #suffix>
              <div color="#999">
                次/min
              </div>
            </template>
          </n-input-number>
        </n-form-item>
      </n-form>
    </BasicModal>
  </div>
</template>

<style>
</style>
