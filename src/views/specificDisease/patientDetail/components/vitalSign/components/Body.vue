<script lang='ts' setup>
import dayjs from 'dayjs'
import type { FormInst } from 'wowjoy-vui'
import { addBodyDataApi, deleteBodyCheck, getBodyListApi, updateBodyCheck } from '@/api/specificDisease'
import { BasicModal } from '@/components/Modal'
import { type ECOption, useEcharts } from '@/hooks'

const route = useRoute()
const patientId = route.query?.patientId as string

/// table
const tableRef = ref<any>(null)

const tableData = ref([])
const columnsData = [{ label: '身高(cm)', value: 'height' }, { label: '体重(kg)', value: 'weight' }, { label: 'BMI(kg/m²`)', value: 'bmi' }, { label: '腰围', value: 'waist' },
  { label: '臀围', value: 'hip' }, { label: '腰臀比', value: 'whr' },
]
const selectTabIndex = ref(0)

/// y 轴数据(真实数据,hover 要显示的数据啊,脑壳疼哟!!!)
const yAixTrueList = ref<any>([])
/// 趋势图
const lineOptions = ref<any>({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      label: {
        backgroundColor: '#6a7985',
      },
    },
    backgroundColor: 'rgba(0,0,0,0.70)',
    textStyle: {
      color: '#fff',
      fontSize: 12,
    },
    formatter(params: any) {
      console.log(params)
      const data = params[0]
      const index = data.dataIndex
      const trueData = yAixTrueList.value[index]
      return `<div px-8px pt-8px >
                <div>${data.axisValue}</div>
                <div flex items-center my-8px >
                      <div style=background-color:#5B96FD;width:10px;height:10px;display:inline-block;border-radius:50%;margin-right:6px;" ></div>
                      <div>
                        <span>${data.seriesName}:</span>
                        <span>${trueData}</span>
                      </div>
                </div>
              </div>
            `
    },
  },
  grid: {
    left: '2%',
    right: '4%',
    bottom: '20%',
    containLabel: true,
  },
  xAxis: [
    {
      type: 'category',
      boundaryGap: false,
      axisLine: {
        lineStyle: {
          color: '#ccc',
        },
      },
      axisLabel: {
        color: '#999',
        showMinLabel: true, // 显示最小值标签
        showMaxLabel: true, // 显示最大值标签
        // rotate: 45, // 逆时针旋转 45 度
        formatter(val) {
          // 当字符串长度超过2时
          if (val.length > 2) {
            // 把字符串分割成字符串数组
            const array = val.split('-')
            // 在下标2处删除0个，加上回车
            array.splice(1, 0, '\n')
            array.splice(3, 0, '-')
            return array.join('')
          }
          else {
            return val
          }
        },
      },
      data: [],
    },
  ],
  yAxis: [
    {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#ccc',
        },
      },
      axisLabel: {
        color: '#999',
      },
    },
  ],
  series: [
    {
      color: '#5B96FD',
      name: '',
      type: 'line',
      connectNulls: true, // 连接空值
      emphasis: {
        focus: 'series',
      },
      data: [],
    },
  ],
}) as Ref<ECOption>
const { domRef: lineRef } = useEcharts(lineOptions)
const yAixIsNoData = ref(false)
// 分页器
const paginationReactive = reactive({
  page: 1,
  pageSize: 10,
  total: 0,
  pageSizes: [10, 20, 50, 100],
})

/// 弹框
/**
 * 身高体重
 */
const showBodyAdd = ref(false)
const formRef = ref<FormInst | null>(null)
const bodyFormValue = reactive({
  date: Date.now(),
  height: null,
  weight: null,
  BMI: null,
  hip: null,
  waist: null,
  title: '新增',
  id: null,
  WHR: null,
})
/**
 * 计算 BMI
*/
function handleChange() {
  bodyFormValue.BMI = (bodyFormValue.height && bodyFormValue.weight) ? (bodyFormValue.weight / (bodyFormValue.height / 100) / (bodyFormValue.height / 100)).toFixed(2) : null
}

function loadDatasTable() {
  getBodyListApi({
    patientId,
    size: paginationReactive.pageSize,
    start: paginationReactive.page,
  }).then((res: any) => {
    tableData.value = res.data.records
    paginationReactive.total = res.data.total
    const list = res.data.records.map((item: any) => dayjs(item.date).format('YYYY-MM-DD'))
    lineOptions.value.xAxis[0].data = list.reverse()
    headerEvent(selectTabIndex.value)
  })
}

function handleYtb() {
  if (bodyFormValue.hip == null || bodyFormValue.hip === 0)
    bodyFormValue.WHR = null
  else
    bodyFormValue.WHR = (bodyFormValue.waist / bodyFormValue.hip).toFixed(2)
}
const bodyRules = {
  date: {
    type: 'number',
    required: true,
    message: '请选择日期',
    trigger: ['blur', 'change'],
  },
  weight: {
    type: 'number',
    required: true,
    message: '请输入体重',
    trigger: ['blur', 'input'],
  },
  height: {
    required: true,
    type: 'number',
    message: '请输入身高',
    trigger: ['blur', 'input'],
  },
}
async function handleBodyConfirm() {
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      /// 身高体重保存
      const params = {
        date: dayjs(bodyFormValue.date).format('YYYY-MM-DD'),
        weight: bodyFormValue.weight,
        height: bodyFormValue.height,
        hip: bodyFormValue.hip,
        waist: bodyFormValue.waist,
        patientId, // '1680788234808799233',
        source: '手动填写',
        bmi: bodyFormValue.BMI,
        whr: bodyFormValue.WHR,
      }

      let res
      if (bodyFormValue.id != null) {
        // 是修改
        params.id = bodyFormValue.id
        res = await updateBodyCheck(params)
      }
      else {
        res = await addBodyDataApi(params)
      }

      if (res) {
        /// 保存成功
        // tableRef.value?.reload()
        resetFormValue()
        showBodyAdd.value = false
        loadDatasTable()
        // toast 一下
      }
    }
    else {
      window.$message.error('请输入信息 ')
    }
  })
}

/**
 * 弹窗打开/关闭的callback
 * @param v true:打开，false:关闭
 */
async function visibleChange(v: boolean) {
  if (!v) {
    resetFormValue()
    showBodyAdd.value = false
  }
}

function resetFormValue() {
  bodyFormValue.date = null
  bodyFormValue.height = null
  bodyFormValue.weight = null
  bodyFormValue.BMI = null
  bodyFormValue.hip = null
  bodyFormValue.waist = null
  bodyFormValue.title = '新增'
  bodyFormValue.id = null
  bodyFormValue.WHR = null
}

function editBodyData(row) {
/// 去修改
  showBodyAdd.value = true
  bodyFormValue.title = '修改'
  bodyFormValue.date = dayjs(row.date).valueOf()
  bodyFormValue.height = row.height
  bodyFormValue.weight = row.weight
  bodyFormValue.BMI = row.bmi
  bodyFormValue.waist = row.waist
  bodyFormValue.hip = row.hip
  bodyFormValue.id = row.id
  bodyFormValue.WHR = row.whr
}

function deleteBodyData(row) {
  window.$dialog.warning({
    title: '确定删除该记录吗？',
    btnGhost: true,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      /// 去删除
      deleteBodyCheck(row.id).then(() => {
        /// 刷新
        loadDatasTable()
      })
    },
  })
}

/// 点击表头
function headerEvent(index: any) {
  selectTabIndex.value = index

  yAixTrueList.value = tableData.value.map((item: any) => item[columnsData[index].value]).reverse()
  lineOptions.value.series[0].data = yAixTrueList.value
  lineOptions.value.series[0].name = columnsData[index].label
  // /// 是否全部为空值
  yAixIsNoData.value = yAixTrueList.value.every((item) => {
    return item === '-' || item === ''
  })
}

onMounted(() => {
  loadDatasTable()
})
</script>

<template>
  <div>
    <n-button color="#06AEA6" my-14px @click="showBodyAdd = true;bodyFormValue.date = Date.now()">
      新增
    </n-button>
    <!-- <BasicTable
      ref="tableRef"
      :columns="columns"
      :action-column="actionCloumns"
      :request="loadDataTable"
      :pagination="paginationReactive"
      striped
    /> -->

    <el-table
      ref="tableRef"
      :data="tableData"
      max-height="320px"
      stripe
      table-layout="auto"
    >
      <el-table-column label="序号" :fixed="columnsData.length > 5">
        <template #default="scope">
          <div>
            {{ scope.$index + 1 }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="日期" :fixed="columnsData.length > 5" min-width="120">
        <template #default="scope">
          {{ scope.row.date }}
        </template>
      </el-table-column>
      <el-table-column v-for="(item, index) in columnsData" :key="index">
        <template #header>
          <div align-items-center flex @click="headerEvent(index)">
            <n-tooltip trigger="hover">
              <template #trigger>
                <SvgIcon v-if="selectTabIndex === index" local-icon="slmc-icon-duoxuanxuanzhong" size="16" mt-3px />
                <SvgIcon v-else local-icon="slmc-icon-fuxuankuang1" size="16" mt-3px />
              </template>
              <span>勾选后，可将{{ item.label }}数据加入下方趋势图查看走势</span>
            </n-tooltip>
            <span w-5px />
            <div class="tabble-header">
              {{ item.label }}
            </div>
          </div>
        </template>
        <template #default="scope">
          <span>{{ scope.row[item.value] || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column v-if="columnsData.length > 5" label="数据来源">
        <template #default="scope">
          {{ scope.row.source }}
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="80" fixed="right">
        <template #default="{ row }">
          <div v-if="row.source !== '住院'" flex items-center gap-5px>
            <span uno-link @click="editBodyData(row)">修改</span>
            <div h-14px w-1px bg="#3B8FD9" />
            <span uno-link @click="deleteBodyData(row)">删除</span>
          </div>
          <div v-else>
            -
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div v-if="tableData.length > 0" mt-14px flex justify-end>
      <n-pagination
        v-model:page="paginationReactive.page" v-model:page-size="paginationReactive.pageSize"
        :to="false" :item-count="paginationReactive.total" :page-sizes="[10, 20, 30]" show-size-picker
        show-quick-jumper @update:page="loadDatasTable" @update:page-size="loadDatasTable"
      />
    </div>

    <div v-if="tableData.length > 0" mb-14px>
      <SectionTitle class="mt-20px -mb-20px">
        {{ columnsData[selectTabIndex]?.label }}趋势图
      </SectionTitle>
      <div v-if="yAixIsNoData" h-260px flex justify-center pt-80px>
        <DataEmpty />
      </div>
      <div v-else ref="lineRef" class="h-270px" />
      <div h-30px />
    </div>

    <BasicModal
      :visible="showBodyAdd"
      :title="bodyFormValue.title"
      :height="370"
      width="560"
      :footer-offset="200"
      @ok="handleBodyConfirm"
      @visible-change="visibleChange"
    >
      <div pr-120px>
        <n-form
          ref="formRef"
          label-placement="left"
          :rules="bodyRules"
          :model="bodyFormValue"
          class="my-24px ml-17px mr-30px"
          require-mark-placement="left"
        >
          <n-form-item path="date" label="日期">
            <n-date-picker v-model:value="bodyFormValue.date" placeholder="yy-mm-dd" type="date" w-full />
          </n-form-item>
          <n-form-item label="身高" ml-20px>
            <n-input-number v-model:value="bodyFormValue.height" w-full placeholder="请输入" :show-button="false" @update:value="handleChange">
              <template #suffix>
                <div color="#999">
                  cm
                </div>
              </template>
            </n-input-number>
          </n-form-item>
          <n-form-item label="体重" ml-20px>
            <n-input-number v-model:value="bodyFormValue.weight" w-full placeholder="请输入" :show-button="false" @update:value="handleChange">
              <template #suffix>
                <div color="#999">
                  kg
                </div>
              </template>
            </n-input-number>
          </n-form-item>
          <n-form-item label="BMI" ml-20px color="#666">
            <n-input-number v-model:value="bodyFormValue.BMI" w-full :show-button="false" :disabled="true" placeholder="根据身高体重自动计算">
              <template #suffix>
                <div color="#999">
                  kg/m²
                </div>
              </template>
            </n-input-number>
          </n-form-item>
          <n-form-item ml-20px label="腰围">
            <n-input-number v-model:value="bodyFormValue.waist" w-full placeholder="请输入" :show-button="false" @update:value="handleYtb">
              <template #suffix>
                <div color="#999">
                  cm
                </div>
              </template>
            </n-input-number>
          </n-form-item>
          <n-form-item ml-20px label="臀围">
            <n-input-number v-model:value="bodyFormValue.hip" w-full placeholder="请输入" :show-button="false" @update:value="handleYtb">
              <template #suffix>
                <div color="#999">
                  cm
                </div>
              </template>
            </n-input-number>
          </n-form-item>
          <n-form-item ml-8px label="腰臀比">
            <n-input-number v-model:value="bodyFormValue.WHR" w-full placeholder="根据腰围臀围自动计算" :disabled="true" :show-button="false" />
          </n-form-item>
        </n-form>
      </div>
    </BasicModal>
  </div>
</template>

<style lang="css">

</style>
