<script lang='ts' setup>
import { Body, Normal } from './components'

const activeSelect = ref('body')
</script>

<template>
  <div class="bg-view" mx-14px h-full>
    <n-tabs animated :width="120" :default-value="activeSelect">
      <n-tab-pane label="一般检查" name="body">
        <Body />
      </n-tab-pane>
      <n-tab-pane label="一般体征" name="nomal">
        <Normal />
      </n-tab-pane>
    </n-tabs>
  </div>
</template>

<style scoped lang="scss">
</style>
