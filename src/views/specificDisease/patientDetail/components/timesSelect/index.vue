<script setup lang='ts'>
import dayjs from 'dayjs'

const props = defineProps({
  placeholder: {
    type: String,
    default: 'YY-MM',
  },
  width: {
    type: Number,
    default: 200,
  },
  date: {
    type: Date,
  },
})

const emits = defineEmits(['update:value'])

const input = ref()
const selectYear = ref()
const selectMonth = ref()
const popoverRef = ref()
const scrollYear = ref()
const scrollMonth = ref()

/// pop 消失
function popSelectDissmiss() {
  //
  // visible.value = false
  // 查看输入的内容
  if (input.value?.length === 0 || input.value == null) {
    selectYear.value = null
    selectMonth.value = null
  }
  else {
    setInputText()
  }
}

function popSelectShow() {

}

function yearClick(index) {
  selectYear.value = index
  setInputText()
}

function monthClick(index) {
  selectMonth.value = index
  setInputText()
  if (selectYear.value !== null)
    popoverRef.value.hide()
}

function setInputText() {
  input.value = `${selectYear.value > 0 ? `${selectYear.value}年` : ''}${selectMonth.value > 0 ? `${selectMonth.value}个月` : ''}`
}

function clearInput() {
  selectMonth.value = null
  selectYear.value = null
}
/// 隐藏
function popHide() {
  // emits('update:value', 2)
  // console.log('嘻嘻嘻')
  /// 根据结果计算时间
  let time = dayjs()
  if (selectYear.value > 0)
    time = time.subtract(selectYear.value, 'year')

  if (selectMonth.value > 0)
    time = time.subtract(selectMonth.value, 'month')

  if (selectYear.value > 0 || selectMonth.value > 0) {
    emits('update:value', time)
    console.log(time)
  }

  if (selectYear.value == null && selectMonth.value == null) {
    emits('update:value', null)
    console.log('kong')
  }
}

watch(() => props.date, (o, n) => {
  /// 监听赋值, 计算间隔, 赋值
  if (props.date) {
    const MM = dayjs().diff(props.date, 'month')
    selectYear.value = Math.floor(MM / 12)
    selectMonth.value = MM % 12
    setInputText()
  }
}, { immediate: true })

function scrolllTheView() {
  // document.getElementById(`year${selectYear.value}`)?.scrollIntoView({ behavior: 'smooth' })
  // document.getElementById(`month${selectMonth.value}`)?.scrollIntoView({ behavior: 'smooth' })
  scrollYear.value?.scrollTo({ top: (selectYear.value - 2) * 26, behavior: 'smooth' })
  scrollMonth.value?.scrollTo({ top: (selectMonth.value - 2) * 26, behavior: 'smooth' })
}

function popShow() {
  scrolllTheView()
}
</script>

<template>
  <div>
    <el-popover
      ref="popoverRef"
      placement="top-start"
      :width="width"
      trigger="click"
      @hide="popHide"
      @show="popShow"
    >
      <template #reference>
        <el-input v-model="input" :style="{ width: `${width}px` }" :placeholder="placeholder" clearable @blur="popSelectDissmiss" @focus="popSelectShow" @clear="clearInput" />
      </template>
      <div flex-col items-center>
        <div>{{ `${selectYear == null ? '-' : selectYear} 年 ${selectMonth == null ? '-' : selectMonth} 月` }}</div>
        <el-divider style="margin: 6px 0 0 0;" />
        <div mt-14px h-150px w-full flex items-center>
          <div ref="scrollYear" class="scroll-select" flex-1 overflow-y-auto text-center style="height: 100%;">
            <div v-for="index in 100" :id="`year${index}`" :key="index" :class="selectYear === index - 1 ? 'select-item' : ''" h-26px flex items-center justify-center @click="yearClick(index - 1)">
              {{ index - 1 }}
            </div>
          </div>
          <div h-26px w-30px flex items-center justify-center>
            年
          </div>
          <div ref="scrollMonth" class="scroll-select" flex-1 overflow-y-auto text-center style="height: 100%;">
            <div v-for="index in 12" :id="`month${index}`" :key="index" :class="selectMonth === index - 1 ? 'select-item' : ''" h-26px flex items-center justify-center @click="monthClick(index - 1)">
              {{ index - 1 }}
            </div>
          </div>
          <div h-26px w-30px flex items-center justify-center>
            月
          </div>
        </div>
      </div>
    </el-popover>
  </div>
</template>

<style lang="scss" scoped>
::-webkit-scrollbar {
    width: 8px;               /* 滚动条的宽度 */
    height: 8px;              /* 滚动条的高度，对水平滚动条有效 */
    background-color: #f9f9fd; /* 滚动条的背景颜色 */
}

/* 滚动条轨道 */
::-webkit-scrollbar-track {
    border-radius: 10px;
    background: #e1e1e1; /* 轨道的背景颜色 */
}

/* 滚动条滑块 */
::-webkit-scrollbar-thumb {
    border-radius: 0px;
    background-color: #ccc; /* 滑块的背景颜色 */
    border: 2px solid #ccc; /* 滑块的边框和轨道相同的颜色，可以制造“边距”的效果 */
}

/* 滚动条滑块：悬停效果 */
::-webkit-scrollbar-thumb:hover {
    background-color: #a8a8a8; /* 滑块的悬停颜色 */
}

/* 滚动条滑块：激活时的效果 */
::-webkit-scrollbar-thumb:active {
    background-color: #888888; /* 滑块的激活颜色 */
}

/* 滚动条按钮（上下箭头） */
::-webkit-scrollbar-button {
    display: none; /* 通常情况下不显示滚动条按钮 */
}

.select-item{
  color: #409eff;
  background-color: #f5f7fa;
}
</style>
