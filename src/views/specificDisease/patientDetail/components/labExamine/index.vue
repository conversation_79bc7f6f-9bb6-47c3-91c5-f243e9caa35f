<script lang='ts' setup>
import BScroll from '@better-scroll/core'
import { LabProject } from './components'

const route = useRoute()

const labTab = route.query?.labTab

const tagsRef = ref<any>(null)
const scrollWrap = ref<any>(null)
const selectIndex = ref(labTab ? Number(labTab) : 0)
const tableHeadList = [
  '肝功能',
  '肾功能',
  '血糖检测',
  '血脂检测',
  '血常规',
  '乙肝DNA检测',
  '乙肝三系定量检测',
  '自身免疫性肝病抗体检测',
  '肠道微生态检测',
  '电解质检测',
  '肿瘤标志物',
  '肝纤维化血清学检测',
  '凝血功能',
  '尿微量蛋白',
  '铜蓝蛋白检测',
  '抗核抗体检测',
  '血浆氨',
  '壳多糖酶3样蛋白测定',
  '免疫球蛋白检测',
  '丙肝病毒学检测',
  'HBV-pgRNA',
]
const bs = ref()

onMounted(() => {
  bs.value = new BScroll(tagsRef.value, {
    // ...... 详见配置项
    scrollX: true,
    scrollY: false,
    probeType: 3, // 开启滚动监听
  })
})

/// 按钮点击
function titleTagEvent(e: any) {
  if (selectIndex.value > e)
    document.getElementById(`tag${e - 1}`)?.scrollIntoView(false)
  else
    document.getElementById(`tag${e + 1}`)?.scrollIntoView(false)
  selectIndex.value = e
}

/// /左移按钮
function leftMoveEvent() {
  bs.value.scrollBy(300, 0, 100)
  /// 滚动到可见范围
  // document.getElementById(`tag${selectIndex.value}`)?.scrollIntoView(false)
  // if (selectIndex.value !== 0) {
  //   selectIndex.value -= 1
  //   /// 滚动到可见范围
  //   document.getElementById(`tag${selectIndex.value - 1}`)?.scrollIntoView(false)
  // }
}

/// 右移按钮
function rightMoveEvent() {
  bs.value.scrollBy(-300, 0, 100) // 向右滚动，过渡时间为500ms
  // bs.value.refresh()
  // tagsRef.value.scrollBy(-300, 0)
  // document.getElementById(`tag${selectIndex.value}`)?.scrollIntoView(false)
  // if (selectIndex.value !== tableHeadList.length - 1) {
  //   selectIndex.value += 1
  //   /// 滚动到可见范围
  //   document.getElementById(`tag${selectIndex.value + 1}`)?.scrollIntoView(false)
  // }
}
</script>

<template>
  <div class="wrap" mx-14px my-20px>
    <div ref="scrollWrap" class="headerWrap">
      <div flex>
        <n-button class="leftArrow" min-width="42px" @click="leftMoveEvent">
          <template #icon>
            <SvgIcon local-icon="slmc-icon-crumbs1 " class="rotate-180" size="10" />
          </template>
        </n-button>

        <div ref="tagsRef" class="tagsWrap" pl-10px pr-72px>
          <div inline-block>
            <!-- <span w-6px color="#f5f5f5">1</span> -->
            <span
              v-for="(tag, index) in tableHeadList" :id="`tag${index}`"
              :key="index"
              class="tag"
              :color="selectIndex === index ? '#06AEA6' : '#666'"
              px-20px py-8px
              :style="{ backgroundColor: selectIndex === index ? '#fff' : '#f5f5f5' }"
              @click="titleTagEvent(index)"
            >
              {{ tag }}
            </span>
            <span w-6px color="#f5f5f5">1</span>
          </div>
        </div>

        <n-button class="rightArrow" min-width="42px" @click="rightMoveEvent">
          <template #icon>
            <SvgIcon local-icon="slmc-icon-crumbs1" class="rotate-0" size="10" />
          </template>
        </n-button>
      </div>
    </div>

    <LabProject :lab-project-name="tableHeadList[selectIndex]" />
  </div>
</template>

<style scoped lang="scss">
.wrap{
  height: calc(100vh - 253px);
}
.headerWrap {
  background-color: #f5f5f5;
  height: 44px;
  line-height: 44px;
  max-width:100%;

  .leftArrow{
    width: 42px;
    height: 42px;
    margin: 1px 0 1px 1px;
    background-color: #fff;
    text-align: center;
  }
  .rightArrow{
    width: 42px;
    height: 42px;
    margin: 1px 1px 1px 0;
    background-color: #fff;
    text-align: center;
  }

  .tagsWrap{
    white-space: nowrap;
    width:100%;
    overflow:hidden;
    cursor: pointer;

    .tag:hover {
      color: #06AEA6;
    }
    .tag {
      border-radius: 3px;
    }

  }
}
</style>
