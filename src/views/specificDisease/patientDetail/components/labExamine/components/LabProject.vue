<script lang="ts" setup>
import { cloneDeep } from 'lodash'
import { getAssayDetailList, getAssayDetailResultList, getPatientInfoApi } from '@/api/specificDisease'
import { type ECOption, useEcharts } from '@/hooks'
import { getCheckDataValue, getEmptyIndex, isOnlyYAxis } from '@/utils/common'

const props = defineProps<Props>()
const route = useRoute()
const patientId = route.query?.patientId as string

interface Props {
  labProjectName: string
}
const patientInfo = ref<any>(null)
const labName = ref('')
const selectTabIndex = ref(0)
const tableRef = ref<any>(null)
/// 列的表头数据
const columnsData = ref<any>([])
/// 实验室具体数据
const tableData = ref<any>([])

/// 日期列表数据
const xAixList = ref<any>([])
/// y 轴数据(真实数据,hover 要显示的数据啊,脑壳疼哟!!!)
const yAixTrueList = ref<any>([])
/// y 轴是否全无数据
const yAixIsNoData = ref(false)

const projectTitles = ref([])

/// 趋势图
const lineOptions = ref<any>({
  dataZoom: [
    {
      type: 'slider',
      xAxisIndex: 0, // 对应 x 轴的索引
      start: 0,
      end: 100,
      left: '10%',
      right: '10%',
    }],
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      label: {
        backgroundColor: '#6a7985',
      },
    },
    backgroundColor: 'rgba(0,0,0,0.70)',
    textStyle: {
      color: '#fff',
      fontSize: 12,
    },
    formatter(params: any) {
      console.log(params)
      const data = params[0]
      const index = data.dataIndex
      const trueData = yAixTrueList.value[index]
      return `<div px-8px pt-8px >
                <div>${data.axisValue}</div>
                <div flex items-center my-8px >
                      <div style=background-color:#5B96FD;width:10px;height:10px;display:inline-block;border-radius:50%;margin-right:6px;" ></div>
                      <div>
                        <span>${data.seriesName}:</span>
                        <span>${trueData}</span>
                      </div>
                </div>
              </div>
            `
    },
  },
  grid: {
    left: '2%',
    right: '4%',
    bottom: '20%',
    containLabel: true,
  },
  xAxis: [
    {
      type: 'category',
      boundaryGap: false,
      axisLine: {
        lineStyle: {
          color: '#ccc',
        },
      },
      axisLabel: {
        color: '#999',
        showMinLabel: true, // 显示最小值标签
        showMaxLabel: true, // 显示最大值标签
        // rotate: 45, // 逆时针旋转 45 度
        formatter(val) {
          // 当字符串长度超过2时
          if (val.length > 2) {
            // 把字符串分割成字符串数组
            const array = val.split('-')
            // 在下标2处删除0个，加上回车
            array.splice(1, 0, '\n')
            array.splice(3, 0, '-')
            return array.join('')
          }
          else {
            return val
          }
        },
      },
      data: [],
    },
  ],
  yAxis: [
    {
      type: 'value',
      axisLine: {
        lineStyle: {
          color: '#ccc',
        },
      },
      axisLabel: {
        color: '#999',
      },
    },
  ],
  series: [
    {
      color: '#5B96FD',
      name: '',
      type: 'line',
      connectNulls: true, // 连接空值
      emphasis: {
        focus: 'series',
      },
      data: [],
    },
  ],
}) as Ref<ECOption>
const { domRef: lineRef } = useEcharts(lineOptions)

onMounted(() => {
  getPatientInfo()
  initData()
})
watch(() => props.labProjectName, (o, _n) => {
  if (labName.value !== o) {
    labName.value = o
    selectTabIndex.value = 0
    columnsData.value = []
    initData()
  }
})

async function getPatientInfo() {
  const res = await getPatientInfoApi(patientId)
  patientInfo.value = res.data
}

async function initData() {
  labName.value = props.labProjectName

  const params = {
    assayProjectName: labName.value,
    patientId, // '1719916747623100418',
    sex: patientInfo.value?.sexName,
  }

  /**
   * 处理表头数据
   */
  const result = await getAssayDetailList(params)
  columnsData.value = result.data
  const temp: any = []
  for (let index = 0; index < columnsData.value.length; index++) {
    const element = columnsData.value[index]
    if (element.assayProjectDetailName === 'B/E值')
      selectTabIndex.value = index

    // break
    temp.push(element.assayProjectDetailName)
  }
  projectTitles.value = temp

  /**
   * 处理表格内容数据
   * 将该接口的数据的englishName和表头hisAssayDetailCode对比
   * (用chineseName和hisAssayDetailName对比),取得 key 值
   */
  const assayRes = await getAssayDetailResultList<any>(params)
  const res = assayRes.data
  const dateList = Object.keys(res)
  dateList.sort((a, b) => new Date(b) - new Date(a))
  xAixList.value = cloneDeep(dateList)
  xAixList.value.reverse()
  const list = []
  dateList.forEach((dateItem) => {
    const item = {
      date: dateItem,
    }
    columnsData.value.forEach((columns) => {
      let find = false
      if (columns.hisAssayDetailCode) {
        res[dateItem].forEach((data) => {
          if (columns.hisAssayDetailCode.toUpperCase() === data.englishName.toUpperCase()) {
            const str = columns.assayProjectDetailName
            columns.unit = data.unit
            const strArrow = `${str}Arrow`
            item[str] = data.result
            item[strArrow] = data.hint
            find = true
          }
        })
      }
      if (columns.hisAssayDetailName && !find) {
        res[dateItem].forEach((data) => {
          if (columns.hisAssayDetailName === data.chineseName) {
            columns.unit = data.unit
            const str = columns.assayProjectDetailName
            const strArrow = `${str}Arrow`
            item[str] = data.result
            item[strArrow] = data.hint
          }
        })
      }
    })
    list.push(item)
  })

  /// 有时候表格出不来数据
  tableData.value = list
  handleOptionsData()
  nextTick(() => {
    tableRef.value.setCurrentRow(columnsData.value[selectTabIndex.value])
  })
}

/// 处理趋势图的数据呀呀呀呀
function handleOptionsData() {
  /**
   * 处理趋势图的数据
   * 1.最正常的情况,嘿嘿,只有数字,跟着数字显示就好了啊
   * 2.所有值有大于小于号,阴性阳性等符号,直接正常取出数字即可
   * 3.若只有阴性,阳性汉字情况下,则纵轴分阴阳俩块,阴阳取值
   * 4.低于检测值时,取值refValue,是否包含10,是则取值 9,否则取值 29(取值范围最低,不是 10 就是 30,低于检测值则取最低值-1)
   */
  const column = columnsData.value[selectTabIndex.value]
  const selectName = column?.assayProjectDetailName
  const selectUnit = column?.unit
  lineOptions.value.yAxis[0].name = selectUnit
  lineOptions.value.series[0].name = selectName
  lineOptions.value.xAxis[0].data = xAixList.value

  const yList: any[] = []
  /// 先判断是不是只有y 轴阴阳
  if (isOnlyYAxis(tableData.value, selectName)) {
    tableData.value.forEach((element) => {
      yAixTrueList.value.push(element[selectName] || '-')
      if (element[selectName] === '阴性')
        yList.push('阴性')

      else if (element[selectName] === '阳性' || element[selectName]?.includes('+1:'))
        yList.push('阳性')

      else
        yList.push('-')
    })
    lineOptions.value.yAxis[0].type = 'category'
    lineOptions.value.yAxis[0].data = ['阴性', '阳性']
  }
  else {
    tableData.value.forEach((element) => {
      yAixTrueList.value.push(element[selectName] || '-')
      if (element[selectName]) {
        const num = getCheckDataValue(element[selectName], columnsData.value[selectTabIndex.value].refValue)
        yList.push(num)
      }
      else { yList.push('-') }
    })
    lineOptions.value.yAxis[0].type = 'value'
    lineOptions.value.yAxis[0].data = []
  }

  /// 是否全部为空值
  yAixIsNoData.value = yList.every((item) => {
    return item === '-' || item === ''
  })

  lineOptions.value.series[0].data = yList.reverse()
  yAixTrueList.value = yAixTrueList.value.reverse()

  /// 识别长数组中有值的一段
  const valueRes = getEmptyIndex(yList)
  lineOptions.value.dataZoom[0].start = valueRes[0] / yList.length * 100
  lineOptions.value.dataZoom[0].end = valueRes[1] / yList.length * 100
}

/// 点击表头
function headerEvent(index: any) {
  selectTabIndex.value = index
  yAixTrueList.value = []
  handleOptionsData()
}

/// 处理时间表头
function handleTheDateHeader(date) {
  const sb = date.split('-')
  return `${sb[0]}\n${sb[1]}-${sb[2]}`
}

function handleTheFwText(item) {
  const result = item?.refValue || '-'
  return `( ${result} ) ${item.unit || ''}`
}

function showHintWarn(str: string) {
  return str === '↓' || str === '↑' || str === '*'
}

/// 选中当前行, 处理那个指标趋势图
function handleCurrentRowSelect(row: any) {
  /// 选中
  // headerEvent(row.index)
  console.log(row)
  if (row) {
    const index = projectTitles.value.indexOf(row?.assayProjectDetailName)
    headerEvent(index)
  }
}
</script>

<template>
  <div class="labWrapper" h-full overflow-auto py-14px>
    <el-table
      ref="tableRef"
      max-height="320px"
      stripe
      flexible
      :data="columnsData"
      table-layout="auto"
      highlight-current-row
      @current-change="handleCurrentRowSelect"
    >
      <el-table-column show-overflow-tooltip fixed prop="assayProjectDetailName" label="检查项目" width="210" />
      <el-table-column v-for="(item, index) in tableData" :key="item.date" min-width="100" show-overflow-tooltip>
        <template #header>
          <div
            style="text-align: center;
              white-space: break-spaces;line-height: normal;font-weight: 500;"
          >
            {{ handleTheDateHeader(item.date) }}
          </div>
        </template>
        <template #default="scope">
          <div text-center>
            <span
              :style="{
                color: showHintWarn(item[`${scope.row?.assayProjectDetailName}Arrow`]) ? '#F36969' : '#333',
              }"
            >
              {{ item[scope.row?.assayProjectDetailName] || '-' }}
            </span>
            <span v-if="showHintWarn(item[`${scope.row?.assayProjectDetailName}Arrow`])" style="color: #F36969">{{ item[`${scope.row?.assayProjectDetailName}Arrow`] }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column v-if="tableData?.length === 0">
        <template #header>
          <div
            style="text-align: center;
              white-space: break-spaces;line-height: normal;font-weight: 500;"
          >
            日期
          </div>
        </template>
        <template #default="scope">
          <div text-center>
            -
          </div>
        </template>
      </el-table-column>
      <el-table-column fixed="right" show-overflow-tooltip prop="assayProjectDetailName" label="正常值范围" width="200">
        <template #default="scope">
          <el-text color="#333" truncated>
            {{ handleTheFwText(scope.row) }}
          </el-text>
        </template>
      </el-table-column>
      <template #empty>
        <div h-300px flex items-center justify-center>
          <DataEmpty />
        </div>
      </template>
    </el-table>
    <!-- <el-table
      ref="tableRef"
      :data="tableData"
      max-height="320px"
      stripe
      table-layout="auto"
    >
      <el-table-column label="序号" :fixed="columnsData.length > 5">
        <template #default="scope">
          <div>
            {{ scope.$index + 1 }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="日期" :fixed="columnsData.length > 5" min-width="120">
        <template #default="scope">
          {{ scope.row.date }}
        </template>
      </el-table-column>
      <el-table-column v-for="(item, index) in columnsData" :key="index" label="表头" :min-width="90 + (item.assayProjectDetailName.length * 12)">
        <template #header>
          <div align-items-center flex @click="headerEvent(index)">
            <div class="tabble-header">
              {{ item.assayProjectDetailName }}
            </div>
            <span w-5px />
            <SvgIcon v-if="selectTabIndex === index" local-icon="slmc-icon-duoxuanxuanzhong" size="16" mt-3px />
            <SvgIcon v-else local-icon="slmc-icon-fuxuankuang1" size="16" mt-3px />
            <span w-3px />
            <n-tooltip trigger="hover">
              <template #trigger>
                <SvgIcon class="hint_svg text-#000" local-icon="slmc-icon-information" size="16" mt-3px opacity-30 />
              </template>
              <span>勾选后，可将{{ item.assayProjectDetailName }}数据加入下方趋势图查看走势</span>
            </n-tooltip>
          </div>
        </template>
        <template #default="scope">
          <span>{{ scope.row[item.assayProjectDetailName] || '-' }}</span>
          <span v-if="scope.row[`${item.assayProjectDetailName}Arrow`] === '↓' || scope.row[`${item.assayProjectDetailName}Arrow`] === '↑'" style="color: #F36969">{{ scope.row[`${item.assayProjectDetailName}Arrow`] }}</span>
        </template>
      </el-table-column>

      <template #empty>
        <div h-300px flex items-center justify-center>
          <DataEmpty />
        </div>
      </template>
    </el-table> -->

    <div v-if="tableData.length > 0" mb-14px>
      <SectionTitle class="mt-20px -mb-20px">
        {{ columnsData[selectTabIndex]?.assayProjectDetailName }}趋势图
      </SectionTitle>
      <div v-if="yAixIsNoData" h-260px flex justify-center pt-80px>
        <DataEmpty />
      </div>
      <div v-else ref="lineRef" class="h-270px" />
      <div h-30px />
    </div>
  </div>
</template>

<style  scoped lang="scss">
.hint_svg:hover {
  opacity: 50%;
}

:deep(.current-row ){
  td.el-table__cell{
    background-color: #FFE1CB !important;
  }
}
:deep(.el-table__body){
  padding-bottom: 10px;
}
</style>
