export type TagName = '关键性指标' | '病历记录' | '专科评分'
export type TagValue = 'KeyIndicator' | 'MedicalRecord' | 'SpecialtyScore'

export interface Tag {
  label: string
  value: number | string
  children?: Tag[]
}

export interface PointModal {
  id: string
  recordId: string
  patientId: string
  assayProjectId: string
  itemId: string
  englishName: string
  chineseName: string
  result: string
  unit: string
  refValue: string
  negativePositive?: null | number
  hint: string
  emergency: number
  reportTime: string
  defaultStatus: null | string
}
