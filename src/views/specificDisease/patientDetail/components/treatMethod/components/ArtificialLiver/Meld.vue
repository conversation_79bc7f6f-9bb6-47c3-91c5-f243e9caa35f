<script lang='ts' setup>
import type { FormInst } from 'wowjoy-vui'
import { watchEffect } from 'vue'
import dayjs from 'dayjs'
import type { PointModal } from '../type'
import { getAssayDetailApi, getScoreDetailApi, updateScoreApi, updateScoreDetailApi } from '@/api/specificDisease'
import type { GetResultDetailModel, UpdateResultDetailParams, UpdateSpecialScoreParams } from '@/api/specificDisease'
import { etiologyList } from '@/constants'
import { getMeldScore } from '@/utils/common'

const props = defineProps<Props>()

const emit = defineEmits(['success', 'cancel'])

const route = useRoute()

interface MeldModel {
  etiology: null | string
  TBIL: {
    id: null | string
    result: string
    unit: null | string
  }
  INR: {
    id: null | string
    result: string
  }
  CR: {
    id: null | string
    result: string
    unit: null | string
  }

  score: number

}

interface Props {
  calTime: string
  /** 1 前 2 后 */
  scoreOrder: string
  /** 1 child-pugh评分 2 meld评分 */
  scoreType: string
  patientId: string
  recordId: string
  recordTime: string
  id: string
  /** 浮层开关 */
  popoverShow: boolean
}

const PATIENT_ID = route.query?.patientId as string

enum KEY_POINT {
  TBIL = 'TBIL',
  INR = 'INR',
  CR = 'CR',
}
/** 时间阶段：前/后 */
enum TIME_PHASE {
  Before = '1',
  After = '2',
}
/** 本次编辑的id */
let _dataId: string | undefined

watchEffect(async () => {
  if (props.popoverShow)
    await Promise.all([await getAssayDetail(), await getResultDetail()])
})

/** meld评分指标枚举 */
const meldOptions = ref({
  /** 总胆红素 */
  TBIL: [],
  INR: [],
  /** 肌酐 */
  CR: [],
})
const formRef = ref<FormInst | null>(null)
const meldModel = ref<MeldModel>({
  etiology: null,
  TBIL: {
    id: null,
    result: '',
    unit: null,
  },
  INR: {
    id: null,
    result: '',
  },
  CR: {
    id: null,
    result: '',
    unit: null,
  },
  score: 0,
})

const meldRules = {
  etiology: {
    required: true,
    trigger: ['blur'],
    message: '内容不能为空',

  },
  TBIL: {
    required: true,
    message: '内容不能为空',
    trigger: ['blur', 'change'],
  },
  INR: {
    required: true,
    message: '内容不能为空',
    trigger: ['blur', 'change'],
  },
  CR: {
    required: true,
    message: '内容不能为空',
    trigger: ['blur', 'change'],
  },
}

/**
 * 编辑时获取上次填写的结果和id
 * @param key 指标名称
 * @param value 指标id
 */
function getPointResult(key: string, value: string) {
  const id = value
  let result = ''
  let unit = null

  const findIndex = meldOptions.value[key]
    .findIndex(item => item.value === value)

  if (findIndex > -1) {
    result = meldOptions.value[key][findIndex].result
    unit = meldOptions.value[key][findIndex].unit
  }

  return {
    id, result, unit,
  }
}

/** 获取各项标准的结果值 */
async function getResultDetail() {
  try {
    const params = {
      calTime: props.calTime,
      scoreId: props.id,
      scoreOrder: props.scoreOrder,
      scoreType: props.scoreType,

    }
    const res = await getScoreDetailApi<GetResultDetailModel>(params)
    if (res?.data) {
      _dataId = res.data?.id
      meldModel.value.etiology = res.data?.etiology

      meldModel.value.TBIL.id = getPointResult('TBIL', res.data.tbil).id
      meldModel.value.TBIL.result = getPointResult('TBIL', res.data.tbil).result
      meldModel.value.TBIL.unit = getPointResult('TBIL', res.data.tbil).unit

      meldModel.value.INR.id = getPointResult('INR', res.data.inr).id
      meldModel.value.INR.result = getPointResult('INR', res.data.inr).result

      meldModel.value.CR.id = getPointResult('CR', res.data.scr).id
      meldModel.value.CR.result = getPointResult('CR', res.data.scr).result
      meldModel.value.CR.unit = getPointResult('CR', res.data.scr).unit
    }
  }
  catch (error) {

  }
}
/** 获取各项指标下拉枚举 */
async function getAssayDetail() {
  const params = {
    calTime: props.calTime,
    patientId: PATIENT_ID,
    scoreOrder: props.scoreOrder,
    scoreType: props.scoreType,
  }
  try {
    const res = await getAssayDetailApi(params)

    if (res.data) {
      const DEFAULT_STATUS = '1'

      const { INR = [], CR = [] } = res.data as unknown as any
      const INRArray = INR.map((item) => {
        const label = `${dayjs(item.reportTime).format('YYYY-MM-DD HH:mm')} ${item.result} ${item.unit}`
        const value = item.id
        if (item.defaultStatus === DEFAULT_STATUS && !_dataId) {
          meldModel.value.INR.id = item.id
          meldModel.value.INR.result = item.result
        }

        return {
          label,
          value,
          ...item,
        }
      })

      const CRArray = CR.map((item) => {
        const label = `${dayjs(item.reportTime).format('YYYY-MM-DD HH:mm')} ${item.result} ${item.unit}`
        const value = item.id
        if (item.defaultStatus === DEFAULT_STATUS && !_dataId) {
          meldModel.value.CR.id = item.id
          meldModel.value.CR.result = item.result
          meldModel.value.CR.unit = item.unit
        }

        return {
          label,
          value,
          ...item,
        }
      })
      const TBILArray = res.data['T-BIL'].map((item) => {
        const label = `${dayjs(item.reportTime).format('YYYY-MM-DD HH:mm')} ${item.result} ${item.unit}`
        const value = item.id
        if (item.defaultStatus === DEFAULT_STATUS && !_dataId) {
          meldModel.value.TBIL.id = item.id
          meldModel.value.TBIL.result = item.result
          meldModel.value.TBIL.unit = item.unit
        }

        return {
          label,
          value,
          ...item,
        }
      })

      meldOptions.value.INR = INRArray
      meldOptions.value.CR = CRArray
      meldOptions.value.TBIL = TBILArray
    }
  }
  catch (error) {
    console.error(error)
  }
}
/** 是否全部填写 */
function isFillIn() {
  const isNoResult = (value: string | null | undefined | number) => {
    if (value === null || value === undefined || value === undefined)
      return true
    else
      return false
  }
  const { TBIL, CR, INR, etiology } = meldModel.value

  if (isNoResult(TBIL.id) || isNoResult(CR.id) || isNoResult(INR.id)
        || isNoResult(etiology))
    return false

  return true
}

/** meld最终评分 */
const computedMeldScore = computed(() => {
  /** 是否全部填写 */
  const isAllHaveValue = isFillIn()

  if (isAllHaveValue) {
    const { etiology, TBIL, INR, CR } = meldModel.value
    const etiologyScore = Number.parseFloat(etiology!)

    const meldPayload = {
      etiology: etiologyScore,
      TBIL: {
        result: TBIL.result,
        unit: TBIL.unit,
      },
      INR: {
        result: INR.result,
      },
      CR: {
        result: CR.result,
        unit: CR.unit,
      },
    }
    const meldScore = getMeldScore(meldPayload)
    meldModel.value.score = meldScore
    return `${meldScore}分`
  }
  else {
    return '-'
  }
})
/**
 * 更新指标时回调函数
 * @param pointName 指标名称
 * @param value 指标id
 * @param option 指标的对象
 */
function onUpdateKeyPoint(pointName: string, value: string, option: PointModal) {
  const { result, unit } = option
  if (pointName === KEY_POINT.TBIL) {
    meldModel.value.TBIL.id = value
    meldModel.value.TBIL.result = result
    meldModel.value.TBIL.unit = unit
  }
  else if (pointName === KEY_POINT.CR) {
    meldModel.value.CR.id = value
    meldModel.value.CR.result = result
    meldModel.value.CR.unit = unit
  }
  else if (pointName === KEY_POINT.INR) {
    meldModel.value.INR.id = value
    meldModel.value.INR.result = result
  }
}

/**
 * 保存最终评分
 */
async function confirmFinallyScore(): Promise<boolean | null> {
  try {
    const params: UpdateSpecialScoreParams = {
      calTime: props.calTime,
      id: props.id,
      patientId: props.patientId,
      recordId: props.recordId,
      recordTime: props.recordTime,
      meldScoreBefore: undefined,
      meldScoreAfter: undefined,
    }

    if (props.scoreOrder === TIME_PHASE.Before) // 人工肝前
      params.meldScoreBefore = `${meldModel.value.score}分`
    else if (props.scoreOrder === TIME_PHASE.After) // 人工肝后
      params.meldScoreAfter = `${meldModel.value.score}分`

    const res = await updateScoreApi<boolean>(params)

    return res.data
  }
  catch (error) {
    console.error(error)
    return false
  }
}/** 保存各项数据 */
async function confirmFinallyResult(): Promise<boolean | null> {
  try {
    const params: UpdateResultDetailParams = {
      calTime: props.calTime,
      scoreId: props.id,
      id: _dataId || undefined,
      etiology: meldModel.value.etiology!,
      scr: meldModel.value.CR.id!,
      tbil: meldModel.value.TBIL.id!,
      inr: meldModel.value.INR.id!,
      scoreType: '2', // 1 child-pugh评分 2 meld评分
      scoreOrder: props.scoreOrder, // 1 前 2 后
    }

    const res = await updateScoreDetailApi<boolean>(params)

    return res.data
  }
  catch (error) {
    console.error(error)
    return false
  }
}
/** 确定 */
function onConfirm() {
  formRef.value?.validate(async (errors) => {
    if (!errors) {
      const resFinallyScore = await confirmFinallyScore()
      const resFinallyResult = await confirmFinallyResult()
      if (resFinallyScore && resFinallyResult) {
        window.$message.success('保存成功')
        emit('success')
        resetModel()
      }
    }
    else {
      console.log(errors)
    }
  })
}

function onCancel() {
  emit('cancel')
  resetModel()
}

function resetModel() {
  meldModel.value = {
    etiology: null,
    TBIL: {
      id: null,
      result: '',
      unit: null,
    },
    INR: {
      id: null,
      result: '',
    },
    CR: {
      id: null,
      result: '',
      unit: null,
    },
    score: 0,
  }
}
</script>

<template>
  <div class="bg-#fff">
    <div class="px-20px pt-20px">
      <n-form
        ref="formRef"
        :label-width="166"
        :item-margin="10"
        :model="meldModel"
        :rules="meldRules"
        label-placement="left"
        require-mark-placement="left"
      >
        <n-form-item
          label="病因" path="etiology"
          style="--n-feedback-height: 14px;"
        >
          <n-radio-group v-model:value="meldModel.etiology" name="etiology">
            <n-space>
              <n-radio v-for="item in etiologyList" :key="item.value" :value="item.value">
                {{ item.label }}
              </n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item>
        <n-form-item
          label="总胆红素 (umol/L)" path="TBIL.id"
          style="--n-feedback-height: 14px;"
        >
          <n-select
            v-model:value="meldModel.TBIL.id"
            placeholder="请选择"
            :options="meldOptions.TBIL"
            style="width:258px"
            @update-value="(value, option) => onUpdateKeyPoint('TBIL', value, option)"
          />
        </n-form-item>
        <n-form-item
          label="INR" path="INR.id"
          style="--n-feedback-height: 14px;"
        >
          <n-select
            v-model:value="meldModel.INR.id"
            placeholder="请选择"
            :options="meldOptions.INR"
            style="width:258px"
            @update-value="(value, option) => onUpdateKeyPoint('INR', value, option)"
          />
        </n-form-item>
        <n-form-item
          label="肌酐 (umol/l)" path="CR.id"
          style="--n-feedback-height: 14px;"
        >
          <n-select
            v-model:value="meldModel.CR.id"
            placeholder="请选择"
            :options="meldOptions.CR"
            style="width:258px"
            @update-value="(value, option) => onUpdateKeyPoint('CR', value, option)"
          />
        </n-form-item>
      </n-form>
    </div>
    <n-divider dashed margin="0px 0px 0px 0px" />
    <div class="m-20px text-14px">
      根据MELD评分计算规则，该患者评分为：<span class="text-#FF9B54">{{ computedMeldScore }}</span>
    </div>
    <div class="w-full pb-18px">
      <n-space justify="center">
        <n-button type="primary" @click="onConfirm">
          确&nbsp;&nbsp;定
        </n-button>
        <n-button @click="onCancel">
          取&nbsp;&nbsp;消
        </n-button>
      </n-space>
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>
