<script lang='ts' setup>
import ChildPugh from './ChildPugh.vue'
import Meld from './Meld.vue'

const props = defineProps<Props>()

const emit = defineEmits(['refresh', 'hiddenEditIcon'])

enum SCORE_TYPE {
  ChildPugh = '1',
  Meld = '2',
}

interface Props {
  calTime: string
  /** 1 前 2 后 */
  scoreOrder: string
  /** 1 child-pugh评分 2 meld评分 */
  scoreType: string
  patientId: string
  recordId: string
  recordTime: string
  id: string
}

const popoverShow = ref(false)

/** 浮层打开回调 */
function onShowPopover() {
  popoverShow.value = true
}
/** 点击外部区域，浮层触发回调  */
function clickOutside() {
  popoverShow.value = false
  emit('hiddenEditIcon')
}
/** 浮层确认 回调 */
function onConfirmSuccess() {
  popoverShow.value = false
  emit('refresh')
  emit('hiddenEditIcon')
}
/** 浮层取消回调 */
function onCancel() {
  popoverShow.value = false
  emit('hiddenEditIcon')
}
</script>

<template>
  <div class="renderEdit">
    <n-popover
      trigger="click" placement="bottom-end"
      style="left: 16px;width:460px" raw :show="popoverShow" @clickoutside="clickOutside"
    >
      <template #trigger>
        <SvgIcon
          local-icon="slmc-icon-edit1"
          size="16"
          style="cursor: pointer;"
          @click="onShowPopover"
        />
      </template>

      <ChildPugh
        v-if="scoreType === SCORE_TYPE.ChildPugh"
        v-bind="props"
        :popover-show="popoverShow"
        @success="onConfirmSuccess" @cancel="onCancel"
      />
      <Meld
        v-if="scoreType === SCORE_TYPE.Meld"
        v-bind="props"
        :popover-show="popoverShow"
        @success="onConfirmSuccess" @cancel="onCancel"
      />
    </n-popover>
  </div>
</template>

<style scoped lang="scss">
.renderEdit {
    :deep(.n-form-item ) {
    --n-feedback-height: 14px;
  }
}
</style>
