<script lang='ts' setup>
import { NPopover } from 'wowjoy-vui'
import dayjs from 'dayjs'
import RenderEdit from './RenderEdit.vue'
import { getSpecialtyScoreApi } from '@/api/specificDisease'
import { convertToChinaNum } from '@/utils'
import { SvgIcon } from '@/components/Icon'

const route = useRoute()
const PATIENT_ID = route.query?.patientId as string

/** 渲染表格头部 */
function renderHeaderCell(leftName: string, rightName: string) {
  return h('div', {
    class: 'diagonalCell',
  },
  [
    h('span', { class: 'table-left' }, leftName),
    h('span', { class: 'table-right ' }, rightName),
  ])
}
/** 渲染人工肝次数头部 */
function renderLiverCountCell(frequency: string, time: string) {
  return h('div', {
    class: 'flex  items-center gap-4px justify-center',
  },
  [
    h('span', { class: '' }, `第${frequency}次人工肝`),
    h(
      NPopover,
      {
        showArrow: true,
        arrowStyle: 'background-color:rgba(0,0,0,0.7);',
        style: 'background-color:rgba(0,0,0,0.7);',
        // placement: 'bottom',
        class: ' text-#fff px-14px py-12px w-210px h-42px text-12px',
        raw: true,
        trigger: 'hover',
      },
      {
        trigger: () => h(SvgIcon, {
          size: 16,
          class: 'infoIcon',
          localIcon: 'slmc-icon-information',

        }),
        default: () => `第${frequency}次人工肝时间：${dayjs(time).format('YYYY-MM-DD')}`,

      },

    ),

  ])
}

/** 渲染第一列数据头部 */
function renderLiverFirstCell(keyPoints: string, time: string) {
  return h('div', {
    class: 'flex  items-center gap-4px justify-center',
  },
  [
    h('span', { class: '' }, keyPoints),
    // 暂时取除，后期会加上去
    /*  h(
      NPopover,
      {
        showArrow: true,
        arrowStyle: 'background-color:rgba(0,0,0,0.7);',
        style: 'background-color:rgba(0,0,0,0.7);',
        // placement: 'bottom',
        class: ' text-#fff px-14px py-12px w-210px h-42px text-12px',
        raw: true,
        trigger: 'hover',
      },
      {
        trigger: () => h(SvgIcon, {
          size: 16,
          class: 'infoIcon ',
          localIcon: 'slmc-icon-information',

        }),
        default: () => `${keyPoints}时间：${dayjs(time).format('YYYY-MM-DD')}`,

      },

    ), */

  ])
}
function onRefresh() {
  init()
}
/** 渲染第一列数据头部 */
function renderLiverScoreCell(row: unknown, index: number) {
  const { scoreType, scoreOrder, score } = row

  let currentEvent = null

  return h('div', {
    class: 'flex  items-center gap-6px justify-center h-full',
    onMouseenter: (e) => {
      e.currentTarget.children[1].style.visibility = 'visible'
    },
    onMouseleave: (e) => {
      e.currentTarget.children[1].style.visibility = 'hidden'
    },
  },
  [
    h('span', { }, score),
    h(RenderEdit, {
      class: 'renderEdit',
      calTime: row[`frequency-${index + 1}-calTime`],
      scoreOrder,
      scoreType,
      patientId: row[`frequency-${index + 1}-patientId`],
      recordId: row[`frequency-${index + 1}-recordId`],
      recordTime: row[`frequency-${index + 1}-recordTime`],
      id: row[`frequency-${index + 1}-id`],
      onRefresh: () => {
        onRefresh()
      },
      onHiddenEditIcon: () => {
        currentEvent.style.visibility = ''
      },
      onClick: (e) => {
        currentEvent = e.currentTarget
        e.currentTarget.style.visibility = 'visible'
      },
    }),

  ])
}

const columns = ref([])
const data = ref([])
/** 创建表格列 */
function createColumns(result: unknown) {
  const timeArr = result.map(item => item.calTime)

  const createHead = timeArr.map((item, index) => {
    const frequency = convertToChinaNum(index + 1)
    return {

      key: `frequency-${index + 1}`,
      width: 200,
      className: 'countHead',
      title() {
        return renderLiverCountCell(frequency, item)
      },
      children: [
        {
          title: '前',
          key: `frequency-${index + 1}_prev`,
          render(row) {
            const score = row[`frequency-${index + 1}_prev`]
            const scoreType = row.keyPoints === 'MELD评分' ? '2' : '1'

            const rowPayload = {
              score,
              scoreOrder: '1',
              scoreType,
              ...row,

            }
            return renderLiverScoreCell(rowPayload, index)
          },

        },
        {
          title: '后',
          key: `frequency-${index + 1}_next`,
          render(row) {
            const score = row[`frequency-${index + 1}_next`]
            const scoreType = row.keyPoints === 'MELD评分' ? '2' : '1'
            const rowPayload = {
              score,
              scoreOrder: '2',
              scoreType,
              ...row,

            }
            return renderLiverScoreCell(rowPayload, index)
          },
        },
      ],
    }
  })
  return [
    {
      key: 'keyPoints',
      className: 'keyPoints',
      width: 235,
      fixed: 'left',
      title() {
        return renderHeaderCell('评分类型', '治疗次数')
      },
      render(row) {
        const { calTime, keyPoints } = row
        return renderLiverFirstCell(keyPoints, calTime)
      },
    },

    ...createHead,
  ]
}

function createData(pointDataRes: unknown) {
//   console.log(pointDataRes)

  const keyList = ['CHild-pugh评分', 'MELD评分']
  const columnsLength = columns.value.length - 1

  const data = keyList.map((item) => {
    const createRowData: unknown = {

    }
    for (let i = 0; i < columnsLength; i++) {
      createRowData[`frequency-${i + 1}_next`] = '-'
      createRowData[`frequency-${i + 1}_prev`] = '-'
    }

    return {
      keyPoints: item,

      ...createRowData,
    }
  })

  pointDataRes.forEach((element, index) => {
    const {
      childPughScoreAfter = '', childPughScoreBefore = '',
      meldScoreAfter = '', meldScoreBefore = '', recordId = '',
      patientId = '', recordTime = '', calTime = '', id,
    } = element

    data[0][`frequency-${index + 1}_prev`] = childPughScoreBefore
    data[0][`frequency-${index + 1}_next`] = childPughScoreAfter

    data[0][`frequency-${index + 1}-recordId`] = recordId
    data[0][`frequency-${index + 1}-patientId`] = patientId
    data[0][`frequency-${index + 1}-recordTime`] = recordTime
    data[0][`frequency-${index + 1}-calTime`] = calTime
    data[0][`frequency-${index + 1}-id`] = id

    data[1][`frequency-${index + 1}_prev`] = meldScoreBefore
    data[1][`frequency-${index + 1}_next`] = meldScoreAfter

    data[1][`frequency-${index + 1}-recordId`] = recordId
    data[1][`frequency-${index + 1}-patientId`] = patientId
    data[1][`frequency-${index + 1}-recordTime`] = recordTime
    data[1][`frequency-${index + 1}-calTime`] = calTime
    data[1][`frequency-${index + 1}-id`] = id
  })
  //   console.log(data)
  return data
}
async function getSpecialtyScore(patientId: string) {
  try {
    const res = await getSpecialtyScoreApi(patientId)
    if (res?.data) {
      columns.value = createColumns(res.data)
      data.value = createData(res.data)
    }
  }
  catch (error) {
    console.error(error)
  }
}

function init() {
  getSpecialtyScore(PATIENT_ID)
}

onMounted(() => {
  init()
})
</script>

<template>
  <div class="specialtyScore">
    <SectionTitle class="mb-14px">
      专科评分
    </SectionTitle>
    <n-data-table
      :data="data"
      :columns="columns"
      :single-line="false"
      :pagination="false"
      class="tableClass"
      style="--n-merged-border-color:#d1d1d1"
    />
  </div>
</template>

<style scoped lang="scss">
.specialtyScore {
    flex:1;
    padding: 0 14px;
    overflow-y: auto;
    :deep(.renderEdit) {
        visibility: hidden;

    }
 /*    :deep(.n-data-table .n-data-table-tr:not(.n-data-table-tr--summary):hover){
        .renderEdit{
            visibility: visible;

        }
    } */
    :deep(.n-data-table-thead){
        .n-data-table-th {
            text-align: center;
            height: 25px;
            line-height: 25px;
        }
        .keyPoints{
            padding: 0;

        .diagonalCell{
            background: linear-gradient(
                to top right,
                transparent 0%,
                transparent calc(50% - 1px),
                #DCDCDC 50%,
                transparent calc(50% + 1px),
                transparent 100%
            );
            width: 100%;
            height:50px;

            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-around;

            .table-left {

                transform: translateX(-20px) translateY(10px);
            }

            .table-right {

                transform: translateX(-30px) translateY(-10px);
            }
        }
    }
    }
    :deep(.infoIcon ){
        cursor: pointer;
        color: #000000;
        opacity: 0.3;
        &:hover{
            color:#666666;
            opacity: 1;
        }
    }
}
</style>
