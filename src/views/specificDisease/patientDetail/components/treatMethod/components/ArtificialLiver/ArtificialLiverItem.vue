<script lang='ts' setup>
import { ref } from 'vue'
import type { Tag } from '../type'

const emit = defineEmits(['emitTag'])

const projectList: Tag[] = [
  { label: '关键性指标', value: 'KeyIndicator' },
  { label: '病历记录', value: 'MedicalRecord' },
  { label: '专科评分', value: 'SpecialtyScore' },
]

const currentTag = ref('KeyIndicator')

function onChangeItem(item: Tag) {
  currentTag.value = item.value
  emit('emitTag', item)
}
</script>

<template>
  <ul class="artificialLiverItem">
    <li v-for="item in projectList" :key="item.value" :class="{ activeTag: currentTag === item.value }" class="tag" @click="onChangeItem(item)">
      {{ item.label }}
    </li>
  </ul>
</template>

<style scoped lang="scss">
.artificialLiverItem {
    list-style: none;
    width: 180px;
    min-width: 180px;
    height: 100%;
    border-right: 1px solid #DCDCDC ;
    font-size: 12px;
    padding-left: 0;
    margin: 0;
    .tag {
        width: 100%;
        height: 32px;
        line-height: 32px;
        padding-left: 16px;
        cursor: pointer;
        &:hover {
            background-color: #fffbe0;
        }
    }
    .activeTag {
        background: #fffbe0;
        color:#06AEA6 ;
    }
}
</style>
