<script lang='ts' setup>
import { onMounted } from 'vue'
import { NPopover } from 'wowjoy-vui'
import dayjs from 'dayjs'
import { LIVER_ICON } from './constant'
import { SectionTitle } from '@/components/Title'
import { getAssayDetailListApi, getAssayDetailResultListApi, getAssayDetailTableApi, getHaveDataApi } from '@/api/specificDisease'
import type { AssayDetailItem, AssayDetailResultModal, KeyPointModel, KeyPointOption } from '@/api/specificDisease'
import { type ECOption, useEcharts } from '@/hooks'
import 'echarts/lib/component/dataZoom'
import { isArray } from '@/utils/common/is'

import { convertToChinaNum } from '@/utils'
import { SvgIcon } from '@/components/Icon'

const route = useRoute()

const PATIENT_ID = route.query?.patientId as string
const SEX = route.query?.sex as string

const showChart = ref<boolean>(true)

/** 入院时间 */
const admissionTime = ref('')
/** 关键指标项列表 */
const keyPointsOptions = ref<KeyPointOption[]>([])
/** 关键指标选中项 */
const keyPoints = ref<string[]>(['T-BIL', 'PT'])
/** 最大指标数 */
const MAX_KEY_POINT_COUNT = 3
// assayProjectDetailName和code的map
const nameCodeMap = new Map()
// 缓存所有英文名
const englishName = new Set()
enum NAME_TYPE {
  Chinese = 'CHINESE',
  English = 'ENGLISH',
}
const artificialLiverTime = ref([])
const LINE_COLOR = ['#5B96FD', '#FFC569', '#9EC97F']
const Y_POSITION = [
  {
    position: 'left',
    offset: 0,
  },
  {
    position: 'right',
    offset: 0,
  },
  {
    position: 'right',
    offset: 50,
  },
]

/** 指标更新回调 */
function onUpdateKeyPoint() {
  // 是否达到最大数量限制
  const isReachMaxCount = keyPoints.value.length >= MAX_KEY_POINT_COUNT
  if (isReachMaxCount) {
    keyPointsOptions.value.forEach((point) => {
      point.disabled = !keyPoints.value.includes(point.value)
    })
  }
  else {
    keyPointsOptions.value.forEach((point) => {
      point.disabled = false
    })
  }
  // 每次更新都要去请求新的数据
  getAssayDetailResultList()
}
/**
 * 获取设置namecodemap的key
 * @param hisAssayDetailCode his指标code
 * @param hisAssayDetailName his指标name
 */
function getNameCodeMapKey(hisAssayDetailCode: string | null, hisAssayDetailName: string | null) {
  return hisAssayDetailCode?.toUpperCase() || hisAssayDetailName
}

/** 获取关键性指标枚举 */
async function getAssayDetailList() {
  try {
    const res = await getAssayDetailListApi<KeyPointModel[]>()

    if (res?.data) {
      nameCodeMap.clear()
      englishName.clear()
      keyPointsOptions.value = res.data?.map((item) => {
        // 以his的code或者name 对渲染显示的名称 做一个缓存，后续主要用来渲染
        const getMapKey = getNameCodeMapKey(item.hisAssayDetailCode, item.hisAssayDetailName)
        const getMapValue = {
          name: item.assayProjectDetailName,
          type: item.hisAssayDetailCode ? NAME_TYPE.English : NAME_TYPE.Chinese,
        }
        nameCodeMap.set(getMapKey, getMapValue)
        englishName.add(item.hisAssayDetailCode)

        return {
          ...item,
          label: item.assayProjectDetailName,
          value: item.hisAssayDetailCode || item.hisAssayDetailName,
          hisAssayDetailCode: item.hisAssayDetailCode,
          hisAssayDetailName: item.hisAssayDetailName,
          disabled: false,
        }
      })
    }
  }
  catch (error) {
    console.error(error)
  }
}

function getCodeName(englishName: string | null, chineseName: string | null) {
  const isConformToEnglishName = englishName && nameCodeMap.get(englishName)?.type === NAME_TYPE.English

  if (isConformToEnglishName)
    return nameCodeMap.get(englishName).name
  else
    return nameCodeMap.get(chineseName).name
}
/** 获取详细指标数据 */
async function getAssayDetailResultList() {
  try {
    const params = {
      patientId: PATIENT_ID,
      assayDetailName: toRaw(keyPoints.value),
      sex: SEX,
    }

    const res = await getAssayDetailResultListApi<AssayDetailResultModal>(params)
    const assayDetailName: string[] = []

    // 为了只显示图例 加下面这些逻辑
    // 获取参数中的key,找到对应的name
    params.assayDetailName.forEach((item) => {
      const isEnglishCode = englishName.has(item)
      const getKey = isEnglishCode ? item?.toUpperCase() : item

      assayDetailName.push(nameCodeMap.get(getKey).name)
    })

    if (res?.data) {
      const dataSource = {}

      for (const [key, value] of Object.entries(res.data)) {
        const englishName = res.data[key][0]?.englishName?.toUpperCase()
        const chineseName = res.data[key][0]?.chineseName

        const name = getCodeName(englishName, chineseName)

        dataSource[name] = value
      }

      const dataKeys = Object.keys(dataSource)

      assayDetailName.forEach((item) => {
        if (!dataKeys.includes(item))
          dataSource[item] = []
      })
      updateCharDataSource(dataSource)
    }
  }
  catch (error) {
    console.error(error)
  }
}
const lineOptions = ref<ECOption>({
  tooltip: {
    trigger: 'axis',
    borderColor: '#333',
    borderRadius: 4,
    borderWidth: 0,
    padding: 5,
    axisPointer: {
      type: 'line',

    },
    backgroundColor: 'rgba(0,0,0,0.70)',
    textStyle: {
      color: '#fff',
      fontSize: 12,
    },
    formatter(params: any) {
      if (!admissionTime.value)
        return

      const date1 = dayjs(admissionTime.value).format('YYYY-MM-DD')
      const date2 = dayjs(params[0].axisValue)

      const hospitalDay = date2.diff(date1, 'day')

      const hospitalDayStr = `
        <div flex items-center my-8px >
              <div style=background-color:#FA8383;width:10px;height:10px;display:inline-block;border-radius:50%;margin-right:6px;" ></div>
              <div>
                <span>已住院：</span>
                <span>${hospitalDay}</span>
                <span>天</span>
              </div>
        </div>
      `
      const seriesName = new Map()

      params.forEach((element) => {
        let mapValueArr = seriesName.has(element.seriesName)
          ? seriesName.get(element.seriesName)
          : []

        mapValueArr.push(element)
        mapValueArr = mapValueArr.sort((a: any, b: any) => {
          const timeA: any = new Date(a.hoverTime)
          const timeB: any = new Date(b.hoverTime)
          return timeA - timeB
        })
        seriesName.set(element.seriesName, mapValueArr)
      })

      let itemStr = ''

      seriesName.forEach((value, key) => {
        let groupStr = ''
        isArray(value) && value.forEach((element, index) => {
          const pointStr = `
              <div style=background-color:${element.color};width:10px;height:10px;display:inline-block;border-radius:50%;margin-right:6px;" ></div>`

          groupStr += `
             <div class='flex items-center'>
                ${index === 0 ? pointStr : ''}
                <div class='${index > 0 ? 'pl-15px ' : ''}'>
                    <span>${key}：</span>
                    <span>${element.data[1]}</span>
                    <span>${element.data[2]?.unit}</span>
                    <span>${dayjs(element.data[2]?.hoverTime).format('YYYY-MM-DD HH:mm')}</span>
                </div>
            </div>
            `
        })

        itemStr += `<section  class=" gap-8px mb-8px flex flex-col justify-center items-start">
            ${groupStr}
        </section>
            `
      })

      return `
      <div px-8px pt-8px >
          ${itemStr}
          ${hospitalDay >= 0 ? hospitalDayStr : ''}
          </div>
        `
    },
  },
  legend: {
    data: [],
    right: 14,
    itemWidth: 16,
    itemHeight: 8,
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '13%',
    containLabel: true,
  },
  dataZoom: [{
    type: 'inside',
    start: 1,
    zoomOnMouseWheel: true, // 关闭滚轮缩放
    moveOnMouseWheel: true, // 开启滚轮平移
    moveOnMouseMove: true, // 鼠标移动能触发数据窗口平移
  },
  {
    height: 20,
    type: 'slider',
    show: true,
    xAxisIndex: [0],

    bottom: 10,

  }],
  xAxis: {
    type: 'category',
    // boundaryGap: false,

    axisLine: {
      lineStyle: {
        color: '#cccccc',
      },
    },
    axisLabel: {
      formatter(val) {
        // 当字符串长度超过2时
        if (val.length > 4) {
          // 把字符串分割成字符串数组
          let array = val.split('-')
          const year = array.slice(0, 1)
          const month = array.slice(1)

          array = [...year, '\n', month.join('-')]

          return array.join('')
        }
        else {
          return val
        }
      },
      color: '#999999',
    },

    data: [],

  },
  yAxis: [{
    type: 'value',
  }],
  series: [

  ],
}) as unknown as Ref<ECOption>
const { domRef: lineRef } = useEcharts(lineOptions)

/**
 * 转换数据源到Y轴的数据
 * @param pointData 单个指标数据源
 */
function formatChartYData(pointData: AssayDetailItem[]) {
  if (!isArray(pointData))
    return []

  const results = pointData.map((item: AssayDetailItem) => [item.reportTime, item.result, { ...item }]).reverse()

  return results
}

/**
 * 更新图表数据
 * @param dataSource 指标数据源
 */
function updateCharDataSource(dataSource: AssayDetailResultModal) {
  const YArray: any = []
  const legendArray: string[] = []
  const seriesArray: any = []
  let xAxisTime: string[] = []
  let index = 0

  if (Object.keys(dataSource).length <= 0) {
    showChart.value = false
    return
  }

  showChart.value = true

  const getMarkLineData = () => {
    const markLineData: unknown[] = []
    for (let i = 0; i < artificialLiverTime.value.length; i++) {
      markLineData.push({
        lineStyle: { // 标线样式
          type: 'solid',
          color: '#A5B8D1',
          width: 1,
        },
        label: {
          show: false,
          color: '#999',
          distance: [10, -2],
          formatter() {
            return `
            人工肝时间
            ${artificialLiverTime.value[i]}
            `
          },
        },
        xAxis: artificialLiverTime.value[i], // 标线的x值,
      })
    }

    return markLineData
  }

  // 遍历数据 去生成相关的图表数据
  for (const [key, value] of Object.entries(dataSource)) {
    const formatData = value.map((item) => {
      return {
        ...item,
        reportTime: dayjs(item.reportTime).format('YYYY-MM-DD'),
        hoverTime: dayjs(item.reportTime).format('YYYY-MM-DD HH:mm:ss'),
      }
    }).sort((a: AssayDetailItem, b: AssayDetailItem) => {
      const timeA: any = new Date(a.reportTime)
      const timeB: any = new Date(b.reportTime)
      return timeA - timeB
    })
      .filter((value: any) => value !== undefined)

    const unit = value[0]?.unit
    const seriesObj = {
      color: LINE_COLOR[index],
      name: key,
      type: 'line',
      emphasis: {
        focus: 'series',
      },
      // 指示线
      markLine: { // 标线
        symbol: ['none', LIVER_ICON], // 去掉箭头
        symbolSize: [39, 16],
        symbolOffset: [0, [0, -14]],
        silent: true, // 图形是否不响应和触发鼠标事件，默认为 false，即响应和触发鼠标事件。
        data: getMarkLineData(),
      },
      yAxisIndex: index,
      data: formatChartYData(formatData),
    }

    const YObj = {
      type: 'value',
      name: unit,
      show: value.length > 0,
      position: Y_POSITION[index]?.position,
      alignTicks: true,
      offset: Y_POSITION[index]?.offset,
      axisLine: {
        show: true,
        lineStyle: {
          color: LINE_COLOR[index],
        },
      },

    }

    const legendName = key
    const xTime = formatData.map(item => item.reportTime)

    seriesArray.push(seriesObj)
    YArray.push(YObj)
    legendArray.push(legendName)
    xAxisTime = xAxisTime.concat(xTime)
    index = index + 1
  }

  lineOptions.value.series = seriesArray
  lineOptions.value.yAxis = YArray
  lineOptions.value.legend.data = legendArray

  // 为了让标注线都显示,x轴加上人工肝时间
  const addArtificialLiverTime = toRaw(artificialLiverTime.value)

  const sortXTime = xAxisTime.concat(addArtificialLiverTime).sort((a: any, b: any) => {
    const timeA: any = new Date(a)
    const timeB: any = new Date(b)
    return timeA - timeB
  })
    .filter((value: any) => value !== undefined)
  const removeDuplication = [...new Set(sortXTime)]

  // 构造入院时间往前推7天的数组
  const admissionBefore7DaysData: any[] = []
  for (let i = 7; i > 0; i--) {
    const admissionTimeBeforeDays = dayjs(admissionTime.value).subtract(i, 'day').format('YYYY-MM-DD')
    admissionBefore7DaysData.push(
      admissionTimeBeforeDays,
    )
  }

  lineOptions.value.xAxis.data = admissionBefore7DaysData.concat(removeDuplication)
}

/** 患者是否做过人工肝 */
async function getHaveData() {
  try {
    const patientId = PATIENT_ID
    const res = await getHaveDataApi<string>(patientId)
    if (res?.data) {
      admissionTime.value = res.data
      await getAssayDetailList()
      await getAssayDetailTable()
      await getAssayDetailResultList()
    }
  }
  catch (error) {
    console.log(error)
  }
}

/** 渲染表格头部 */
function renderHeaderCell(leftName: string, rightName: string) {
  return h('div', {
    class: 'diagonalCell',
  },
  [
    h('span', { class: 'table-left' }, leftName),
    h('span', { class: 'table-right ' }, rightName),
  ])
}
/** 渲染人工肝次数头部 */
function renderLiverCountCell(frequency: string, time: string) {
  return h('div', {
    class: 'flex  items-center gap-4px justify-center',
  },
  [
    h('span', { class: '' }, `第${frequency}次人工肝`),
    h(
      NPopover,
      {
        showArrow: true,
        arrowStyle: 'background-color:rgba(0,0,0,0.7);',
        style: 'background-color:rgba(0,0,0,0.7);',
        class: ' text-#fff px-14px py-12px w-210px h-42px text-12px',
        raw: true,
        trigger: 'hover',
      },
      {
        trigger: () => h(SvgIcon, {
          size: 16,
          class: 'infoIcon',
          localIcon: 'slmc-icon-information',

        }),
        default: () => `第${frequency}次人工肝时间：${dayjs(time).format('YYYY-MM-DD')}`,

      },

    ),

  ])
}

const columns = ref([])
const data = ref([])
/** 创建表格列 */
function createColumns(result: unknown) {
  const timeArr = result.map(item => item.time)

  const createHead = timeArr.map((item, index) => {
    const frequency = convertToChinaNum(index + 1)
    return {

      key: `frequency-${index + 1}`,
      width: 200,
      className: 'countHead',
      title() {
        return renderLiverCountCell(frequency, item)
      },
      children: [
        {
          title: '前',
          key: `frequency-${index + 1}_prev`,
        },
        {
          title: '后',
          key: `frequency-${index + 1}_next`,
        },
      ],
    }
  })
  return [
    {
      key: 'keyPoints',
      className: 'keyPoints',
      width: 235,
      title() {
        return renderHeaderCell('检验指标', '治疗次数')
      },
    },

    ...createHead,
  ]
}
/**
 * 创建表格data数据
 * @param pointDataRes
 */

function createData(pointDataRes: unknown) {
  const keyList = toRaw(keyPointsOptions.value)
  const columnsLength = columns.value.length - 1
  const formatKetPointStr = (item) => {
    if (item?.unit)
      return `${item.label}-${item.unit}`

    else
      return `${item.label}`
  }

  const data = keyList.map((item) => {
    const createRowData: unknown = {}
    for (let i = 0; i < columnsLength; i++) {
      createRowData[`frequency-${i + 1}_next`] = '-'
      createRowData[`frequency-${i + 1}_prev`] = '-'
    }

    return {
      keyPoints: formatKetPointStr(item),
      code: item?.value,
      hisAssayDetailCode: item.hisAssayDetailCode,
      hisAssayDetailName: item.hisAssayDetailName,
      ...createRowData,
    }
  })

  pointDataRes.forEach((element, index) => {
    const prevArr = element['前']
    const nextArr = element['后']
    prevArr.forEach((prevElement) => {
      const { englishName = '', chineseName = '' } = prevElement

      const findIndex = data.findIndex((item) => {
        if (englishName && item.hisAssayDetailCode)
          return englishName.toUpperCase() === item.hisAssayDetailCode.toUpperCase()

        else if (chineseName && item.hisAssayDetailName)
          return chineseName === item.hisAssayDetailName
      })

      if (findIndex > -1)
        data[findIndex][`frequency-${index + 1}_prev`] = prevElement.result
    })
    nextArr.forEach((nextElement) => {
      const { englishName = '', chineseName = '' } = nextElement

      const findIndex = data.findIndex((item) => {
        if (englishName && item.hisAssayDetailCode)
          return englishName.toUpperCase() === item.hisAssayDetailCode.toUpperCase()

        else if (chineseName && item.hisAssayDetailName)
          return chineseName === item.hisAssayDetailName
      })
      if (findIndex > -1)
        data[findIndex][`frequency-${index + 1}_next`] = nextElement.result
    })
  })

  return data
}
/** 获取表格数据 */
async function getAssayDetailTable() {
  try {
    const patientId = PATIENT_ID
    const res = await getAssayDetailTableApi<unknown[]>(patientId)
    if (res?.data) {
      artificialLiverTime.value = res.data.map(item => dayjs(item.time).format('YYYY-MM-DD'))

      columns.value = createColumns(res.data)
      data.value = createData(res.data)
    }
  }
  catch (error) {
    console.error(error)
  }
}
/** 初始化函数 */
function init() {
  getHaveData()
}

onMounted(() => {
  init()
})
</script>

<template>
  <div class="keyIndicator">
    <div v-if="admissionTime">
      <SectionTitle>关键指标趋势图</SectionTitle>
      <section class="mb-14px mt-14px flex items-center justify-start gap-10px">
        <div class="ml15px flex-shrink-0">
          重点指标
        </div>
        <n-select
          v-model:value="keyPoints" multiple

          :options="keyPointsOptions" style="width:560px"
          @update:value="onUpdateKeyPoint"
        />
      </section>
      <!-- 指标趋势图 -->
      <div v-show="showChart" ref="lineRef" class="h-290px" />

      <div v-show="!showChart" class="h-full flex items-center justify-center">
        <n-empty description="无数据" />
      </div>
      <section class="mt-20px pb-28px">
        <SectionTitle class="mb-14px">
          关键指标结果
        </SectionTitle>
        <n-data-table
          :data="data"
          :columns="columns"
          :single-line="false"
          :pagination="false"
          class="tableClass"
          style="--n-merged-border-color:#d1d1d1"
        />
      </section>
    </div>
    <!-- 无人工肝ui -->
    <div v-else class="h-full flex items-center justify-center">
      <n-empty description="无数据" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.keyIndicator {
    flex:1;
    padding: 0 14px;
    overflow-y: auto;
    :deep(.n-data-table-thead){
        .n-data-table-th {
            text-align: center;
            height: 25px;
            line-height: 25px;
        }
        .keyPoints{
            padding: 0;

        .diagonalCell{
            background: linear-gradient(
                to top right,
                transparent 0%,
                transparent calc(50% - 1px),
                #DCDCDC 50%,
                transparent calc(50% + 1px),
                transparent 100%
            );
            width: 100%;
            height:50px;

            position: relative;
            display: flex;
            align-items: center;
            justify-content: space-around;

            .table-left {

                transform: translateX(-20px) translateY(10px);
            }

            .table-right {

                transform: translateX(-30px) translateY(-10px);
            }
        }
    }
    }
    :deep(.infoIcon ){
        cursor: pointer;
        color: #000000;
        opacity: 0.3;
        &:hover{
            color:#666666;
            opacity: 1;
        }
    }

}
</style>
