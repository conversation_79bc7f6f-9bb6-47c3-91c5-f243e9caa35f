<script lang='ts' setup>
import { ref, shallowRef } from 'vue'
import type { Tag, TagValue } from '../type'
import { ArtificialLiverItem, KeyIndicator, MedicalRecord, SpecialtyScore } from './index'

const receiveTag = ref<TagValue>('KeyIndicator')
function onReceiveTag(tag: Tag) {
  receiveTag.value = tag.value
}
const tagComponent = shallowRef({
  KeyIndicator,
  MedicalRecord,
  SpecialtyScore,
})
</script>

<template>
  <div class="artificialLiver">
    <ArtificialLiverItem @emit-tag="onReceiveTag" />

    <component :is="tagComponent[receiveTag]" :current-tag="receiveTag" />
  </div>
</template>

<style scoped lang="scss">
.artificialLiver {
    display: flex;
    justify-content: flex-start;
    width: 100%;
    height: 100%;
    padding-top: 14px;
}
</style>
