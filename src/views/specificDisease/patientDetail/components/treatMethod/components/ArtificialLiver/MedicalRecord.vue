<script lang='ts' setup>
import BaseMedicalRecord from './BaseMedicalRecord.vue'
import EditMedicalRecord from './EditMedicalRecord.vue'

enum COMPONENT_NAME {
  BASE = 'BASE',
  EDIT = 'EDIT',
}

type TYPES = keyof typeof COMPONENT_NAME

type BaseMedicalRecordComp = typeof BaseMedicalRecord
type EditMedicalRecordComp = typeof EditMedicalRecord

/** 组件列表 */
const componentList: Record<TYPES, BaseMedicalRecordComp | EditMedicalRecordComp> = {
  BASE: BaseMedicalRecord,
  EDIT: EditMedicalRecord,
}

/** 目前激活的组件 */
const activeComponent = ref<TYPES>(COMPONENT_NAME.BASE)

/**
 * 点击切换组件
 * @param compName 组件名称
 */
function onToggleComp(compName: TYPES) {
  activeComponent.value = compName
}
</script>

<template>
  <div class="w-full">
    <Transition name="fade" mode="out-in">
      <component :is="componentList[activeComponent]" @toggle-comp="onToggleComp" />
    </Transition>
  </div>
</template>

<style scoped lang="scss">

</style>
