<script lang='ts' setup>
import dayjs from 'dayjs'
import { formatNullValueToShortBar } from '@/utils/common/format'
import { SectionTitle } from '@/components/Title'
import { getMedicalRecordApi } from '@/api/specificDisease'
import type { MedicalRecordModal } from '@/api/specificDisease'
import { isArray } from '@/utils/common/is'

const emit = defineEmits(['toggleComp'])

const route = useRoute()
const PATIENT_ID = route.query?.patientId as string

/** 是否肝硬化 */
const LIVER_CIRRHOSIS = {
  GET_CIRRHOSIS: 1,
  NOT_CIRRHOSIS: 2,
}
/** 家族史 */
const FAMILY_HISTORY = {
  GET: 1,
  NOT: 0,
}

/** 病历记录 */
const medicalInfo = ref<MedicalRecordModal | null>(null)

/** 去编辑 */
function goEdit() {
  emit('toggleComp', 'EDIT')
}

/**
* 回显数据格式化
* @param receiveStr 复选框list
* @param flagName 字段名称
*/
function getStringToArray(receiveStr: string | null, flagName: string) {
  if (!receiveStr)
    return '-'

  try {
    const receiveArr = JSON.parse(receiveStr)

    const list = receiveArr[flagName].filter(item => item !== '其他')
    const other = receiveArr[`${flagName}Other`]
    let str = ''
    if (isArray(list)) {
      list.forEach((item, index) => {
        str += `${index + 1}、${item}${index !== list?.length && '；'}`
      })
    }
    if (other)
      str += `${list?.length + 1}、${other}`

    return str
  }
  catch (error) {
    console.error(error)
    return ''
  }
}

/**
 * 获取病历信息
 * @param patientId 病人id
 */
async function getMedicalRecord(patientId: string) {
  if (!patientId)
    return
  try {
    const res = await getMedicalRecordApi<MedicalRecordModal>(patientId)
    if (res?.data) {
      const {
        firstArtificialLiverTime, inHospitalTime,
        complication, currentEtiology, basicEtiology,
        id, cirrhosis, outHospitalDiagnose, inHospitalDiagnose,
        patientId, timeInterval, basicDiagnose, historyA, historyB, historyC,
      } = res.data

      medicalInfo.value = {
        basicEtiology: getStringToArray(basicEtiology, 'basicEtiology'),
        complication: getStringToArray(complication, 'complication'),
        currentEtiology: getStringToArray(currentEtiology, 'currentEtiology'),
        basicDiagnose: getStringToArray(basicDiagnose, 'basicDiagnose'),
        inHospitalTime,
        firstArtificialLiverTime,
        cirrhosis,
        id,
        inHospitalDiagnose,
        outHospitalDiagnose,
        patientId,
        timeInterval,
        historyA,
        historyB,
        historyC,
      }
    }
  }
  catch (error) {
    console.error(error)
  }
}

function init() {
  getMedicalRecord(PATIENT_ID)
}

onMounted(() => {
  init()
})
</script>

<template>
  <div class="baseMedicalRecord">
    <div class="baseMedicalRecord-body">
      <n-button ghost type="primary" class="mb-14px" @click="goEdit">
        编辑
      </n-button>
      <section>
        <SectionTitle class="mb-14px">
          治疗信息
        </SectionTitle>
        <n-descriptions
          class="desc" bordered label-placement="left"
          :column="2"
          style="--n-border-color:#D1D1D1"
          :content-style="{ boxSizing: 'border-box', padding: '10px', lineHeight: '1.4', color: '#333333' }"
          :label-style="{ textAlign: 'right', boxSizing: 'border-box', width: '170px', padding: '10px', lineHeight: '1.4', color: '#666666' }"
        >
          <n-descriptions-item label="入院时间">
            {{ medicalInfo?.inHospitalTime ? dayjs(medicalInfo.inHospitalTime).format('YYYY-MM-DD HH:mm') : '-' }}
          </n-descriptions-item>
          <n-descriptions-item label="上机时间">
            {{ medicalInfo?.firstArtificialLiverTime ? dayjs(medicalInfo.firstArtificialLiverTime).format('YYYY-MM-DD HH:mm') : '-' }}
          </n-descriptions-item>
          <n-descriptions-item label="入院-首次上机间隔时间">
            {{ medicalInfo?.timeInterval || '-' }}
          </n-descriptions-item>
          <n-descriptions-item label="人工肝方法">
            李氏人工肝 (Li-ALS)
          </n-descriptions-item>
        </n-descriptions>
      </section>
      <section class="mt-20px">
        <SectionTitle class="mb-14px">
          疾病信息
        </SectionTitle>
        <n-descriptions
          class="desc" bordered label-placement="left"
          :column="1"
          style="--n-border-color:#D1D1D1"
          :content-style="{ boxSizing: 'border-box', padding: '10px', lineHeight: '1.4', color: '#333333' }"
          :label-style="{ textAlign: 'right', boxSizing: 'border-box', width: '170px', padding: '10px', lineHeight: '1.4', color: '#666666' }"
        >
          <n-descriptions-item label="入院诊断">
            {{ formatNullValueToShortBar(medicalInfo?.inHospitalDiagnose) }}
          </n-descriptions-item>
          <n-descriptions-item label="出院诊断">
            {{ formatNullValueToShortBar(medicalInfo?.outHospitalDiagnose) }}
          </n-descriptions-item>
          <n-descriptions-item label="并发症">
            {{ formatNullValueToShortBar(medicalInfo?.complication) }}
          </n-descriptions-item>
          <n-descriptions-item label="基础病因">
            {{ formatNullValueToShortBar(medicalInfo?.basicEtiology) }}
          </n-descriptions-item>
          <n-descriptions-item label="现症病因">
            {{ formatNullValueToShortBar(medicalInfo?.currentEtiology) }}
          </n-descriptions-item>
          <n-descriptions-item label="基础疾病">
            {{ formatNullValueToShortBar(medicalInfo?.basicDiagnose) }}
          </n-descriptions-item>
          <n-descriptions-item label="肝硬化">
            {{ medicalInfo?.cirrhosis !== null
              ? medicalInfo?.cirrhosis === LIVER_CIRRHOSIS.GET_CIRRHOSIS ? '是' : '否'
              : '-' }}
          </n-descriptions-item>
          <n-descriptions-item label="家族史">
            {{
              `家族史-乙肝：${medicalInfo?.historyA !== null
                ? medicalInfo?.historyA === FAMILY_HISTORY.GET ? '有' : '无'
                : '-'}；`
            }}
            {{
              `家族史-肝硬化：${medicalInfo?.historyB !== null
                ? medicalInfo?.historyB === FAMILY_HISTORY.GET ? '有' : '无'
                : '-'}；`
            }}
            {{
              `家族史--肝癌：${medicalInfo?.historyC !== null
                ? medicalInfo?.historyC === FAMILY_HISTORY.GET ? '有' : '无'
                : '-'}；`
            }}
          </n-descriptions-item>
        </n-descriptions>
      </section>
    </div>
  </div>
</template>

<style scoped lang="scss">
.baseMedicalRecord {
    position: relative;
    padding: 0px 0px 14px 0px;
    height: 100%;
    flex:1;

    &-body {
        height: 100%;
        padding: 14px;
        overflow-y: auto;
    }

    :deep(.n-descriptions.n-descriptions--bordered .n-descriptions-table-wrapper .n-descriptions-table .n-descriptions-table-row .n-descriptions-table-header){
        background-color: #F5F5F5;
    }
    :deep(.n-descriptions .n-descriptions-table-wrapper .n-descriptions-table .n-descriptions-table-row .n-descriptions-table-header ){
        padding: 0px;

    }
    :deep(.n-descriptions .n-descriptions-table-wrapper .n-descriptions-table .n-descriptions-table-row .n-descriptions-table-content){
        padding: 0px;
    }
    :deep(.n-descriptions .n-descriptions-table-wrapper .n-descriptions-table .n-descriptions-table-row .n-descriptions-table-content){
        vertical-align: middle;
    }
}
</style>
