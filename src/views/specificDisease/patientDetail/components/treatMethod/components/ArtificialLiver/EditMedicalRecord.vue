<script lang='ts' setup>
import dayjs from 'dayjs'
import { toRaw } from 'vue'
import { isArray } from '@/utils/common/is'
import { SectionTitle } from '@/components/Title'
import { getMedicalRecordApi, updateMedicalRecordApi } from '@/api/specificDisease'
import type { MedicalRecordModal } from '@/api/specificDisease'

interface MedicalRecord {
  basicEtiology: string[]
  cirrhosis: number | null
  complication: string[]
  currentEtiology: string[]
  id: string
  inHospitalDiagnose: string
  outHospitalDiagnose: string
  timeInterval: string
  patientId: string
  firstArtificialLiverTime: number | null
  inHospitalTime: number | null
  complicationOther: string
  currentEtiologyOther: string
  basicEtiologyOther: string
  basicDiagnose: string[]
  basicDiagnoseOther: string
  familyHistory: string[]
}

const emit = defineEmits(['toggleComp'])
const route = useRoute()
const PATIENT_ID = route.query?.patientId as string

/** 并发症复选 */
const complicationList = [
  { label: '消化道出血', value: '消化道出血' },
  { label: '腹水', value: '腹水' },
  { label: '肝性脑病', value: '肝性脑病' },
  { label: '肝肾综合征', value: '肝肾综合征' },
  { label: '其他', value: '其他' },
]
/** 基础病因复选 */
const basicEtiologyList = [
  { label: '肝衰竭', value: '肝衰竭' },
  { label: '肝硬化', value: '肝硬化' },
  { label: '高胆红素血症', value: '高胆红素血症' },
  { label: '其他', value: '其他' },
]
/** 现症病因复选 */
const currentEtiologyList = [
  { label: '消化道出血', value: '消化道出血' },
  { label: '感染', value: '感染' },
  { label: '肝性脑病', value: '肝性脑病' },
  { label: '其他', value: '其他' },
]
/** 肝硬化复选框 */
const liverCirrhosisList = [
  { label: '是', value: 1 },
  { label: '否', value: 2 },
]
/** 基础疾病复选 */
const basicDiagnoseList = [
  { label: '高血压', value: '高血压' },
  { label: '糖尿病', value: '糖尿病' },
  { label: '冠心病', value: '冠心病' },
  { label: '其他', value: '其他' },
]
/** 家族史复选 */
const familyHistoryList = [
  { label: '家族史-乙肝', value: 'historyA' },
  { label: '家族史-肝硬化', value: 'historyB' },
  { label: '家族史-肝癌', value: 'historyC' },

]

/** 病历信息 */
const medicalInfo = ref<MedicalRecord>({
  basicEtiology: [],
  cirrhosis: null,
  complication: [],
  currentEtiology: [],
  basicDiagnose: [],
  id: '',
  inHospitalDiagnose: '',
  outHospitalDiagnose: '',
  timeInterval: '',
  patientId: '',
  firstArtificialLiverTime: null,
  inHospitalTime: null,
  complicationOther: '',
  currentEtiologyOther: '',
  basicEtiologyOther: '',
  basicDiagnoseOther: '',
  familyHistory: [],
})
/**
* 回显数据格式化
* @param receiveStr 复选框list
* @param flagName 字段名称
*/
function getStringToArray(receiveStr: string | null, flagName: string) {
  if (!receiveStr) {
    return {
      list: [],
      other: null,
    }
  }

  try {
    const receiveArr = JSON.parse(receiveStr)

    return {
      list: receiveArr[flagName],
      other: receiveArr[`${flagName}Other`],
    }
  }
  catch (error) {
    console.error(error)
  }
}

function getFamilyHistoryStringToArray(historyA: number | null, historyB: number | null, historyC: number | null) {
  const family: string[] = []
  if (historyA && historyA === 1)
    family.push('historyA')

  if (historyB && historyB === 1)
    family.push('historyB')

  if (historyC && historyC === 1)
    family.push('historyC')

  return family
}

/**
 * 获取病历信息
 * @param patientId 病人id
 */
async function getMedicalRecord(patientId: string) {
  if (!patientId)
    return
  try {
    const res = await getMedicalRecordApi<MedicalRecordModal>(patientId)

    if (res?.data) {
      const {
        firstArtificialLiverTime, inHospitalTime,
        complication, currentEtiology, basicEtiology,
        id, cirrhosis, outHospitalDiagnose, inHospitalDiagnose,
        patientId, timeInterval, basicDiagnose, historyA, historyB, historyC,
      } = res.data

      medicalInfo.value = {
        complicationOther: getStringToArray(complication, 'complication')?.other,
        currentEtiologyOther: getStringToArray(currentEtiology, 'currentEtiology')?.other,
        basicEtiologyOther: getStringToArray(basicEtiology, 'basicEtiology')?.other,
        basicDiagnoseOther: getStringToArray(basicDiagnose, 'basicDiagnose')?.other,
        complication: getStringToArray(complication, 'complication')?.list,
        basicEtiology: getStringToArray(basicEtiology, 'basicEtiology')?.list,
        currentEtiology: getStringToArray(currentEtiology, 'currentEtiology')?.list,
        basicDiagnose: getStringToArray(basicDiagnose, 'basicDiagnose')?.list,
        familyHistory: getFamilyHistoryStringToArray(historyA, historyB, historyC),
        inHospitalTime: inHospitalTime ? dayjs(inHospitalTime).valueOf() : null,
        firstArtificialLiverTime: firstArtificialLiverTime ? dayjs(firstArtificialLiverTime).valueOf() : null,
        cirrhosis,
        id,
        inHospitalDiagnose,
        outHospitalDiagnose,
        patientId,
        timeInterval,
      }
    }
  }
  catch (error) {
    console.error(error)
  }
}

function formatSubmitCheckboxParams(key: 'complication' | 'basicEtiology' | 'currentEtiology' | 'basicDiagnose') {
  const list = toRaw(medicalInfo.value[key])
  const other = list.includes('其他') ? toRaw(medicalInfo.value[`${key}Other`]) : null

  if (isArray(list) && list.length > 0) {
    return {
      [key]: list,
      [`${key}Other`]: other,
    }
  }
  else {
    return {
      [key]: [],
      [`${key}Other`]: null,
    }
  }
}

/** 确认 */
async function onConfirm() {
  try {
    // 并发症
    const formatComplication = formatSubmitCheckboxParams('complication')
    // 基础病因
    const formatBasicEtiology = formatSubmitCheckboxParams('basicEtiology')
    // 现症病因
    const formatCurrentEtiology = formatSubmitCheckboxParams('currentEtiology')
    // 基础疾病
    const formatBasicDiagnose = formatSubmitCheckboxParams('basicDiagnose')
    // 入院时间
    const inHospitalTime = medicalInfo.value.inHospitalTime
      ? dayjs(medicalInfo.value.inHospitalTime).format('YYYY-MM-DD HH:mm:ss')
      : null

    // 上机时间
    const firstArtificialLiverTime = medicalInfo.value.firstArtificialLiverTime
      ? dayjs(medicalInfo.value.firstArtificialLiverTime).format('YYYY-MM-DD HH:mm:ss')
      : null

    const params = {
      basicEtiology: JSON.stringify(formatBasicEtiology),
      basicDiagnose: JSON.stringify(formatBasicDiagnose),
      cirrhosis: medicalInfo.value.cirrhosis,
      complication: JSON.stringify(formatComplication),
      currentEtiology: JSON.stringify(formatCurrentEtiology),
      firstArtificialLiverTime,
      id: medicalInfo.value.id,
      inHospitalDiagnose: medicalInfo.value.inHospitalDiagnose,
      inHospitalTime,
      outHospitalDiagnose: medicalInfo.value.outHospitalDiagnose,
      patientId: medicalInfo.value.patientId,
      historyA: medicalInfo.value.familyHistory.includes('historyA') ? '1' : '0',
      historyB: medicalInfo.value.familyHistory.includes('historyB') ? '1' : '0',
      historyC: medicalInfo.value.familyHistory.includes('historyC') ? '1' : '0',
    }

    const res = await updateMedicalRecordApi(params)

    if (res?.data)
      onCancel()
  }
  catch (error) {

  }
}

/** 返回 */
function onCancel() {
  emit('toggleComp', 'BASE')
}

function init() {
  getMedicalRecord(PATIENT_ID)
}

onMounted(() => {
  init()
})
</script>

<template>
  <div class="editMedicalRecord">
    <div class="editMedicalRecord-body">
      <section>
        <SectionTitle class="mb-14px">
          治疗信息
        </SectionTitle>
        <n-descriptions
          class="desc" bordered label-placement="left"
          :column="2"
          style="--n-border-color:#D1D1D1"
          :content-style="{ color: '#333333', boxSizing: 'border-box', padding: '2px 10px', lineHeight: '1.4' }"
          :label-style="{ color: '#666666', textAlign: 'right', boxSizing: 'border-box', width: '170px', padding: '10px', lineHeight: '1.4' }"
        >
          <n-descriptions-item label="入院时间">
            <n-date-picker v-model:value="medicalInfo.inHospitalTime" type="datetime" clearable />
          </n-descriptions-item>
          <n-descriptions-item label="上机时间">
            <n-date-picker v-model:value="medicalInfo.firstArtificialLiverTime" type="datetime" clearable />
          </n-descriptions-item>
          <n-descriptions-item label="入院-首次上机间隔时间">
            {{ medicalInfo?.timeInterval || '-' }}
          </n-descriptions-item>
          <n-descriptions-item label="人工肝方法">
            李氏人工肝 (Li-ALS)
          </n-descriptions-item>
        </n-descriptions>
      </section>
      <section class="mt-20px">
        <SectionTitle class="mb-14px">
          疾病信息
        </SectionTitle>
        <n-descriptions
          class="desc" bordered label-placement="left"
          :column="1"
          style="--n-border-color:#D1D1D1"
          :content-style="{ color: '#333333', boxSizing: 'border-box', padding: '2px 10px', lineHeight: '1.4' }"
          :label-style="{ color: '#666666', textAlign: 'right', boxSizing: 'border-box', width: '170px', padding: '10px', lineHeight: '1.4' }"
        >
          <n-descriptions-item label="入院诊断">
            <n-input
              v-model:value="medicalInfo.inHospitalDiagnose"
              type="textarea"
              placeholder="请输入"
              :rows="1"
              autosize
              :maxlength="5000"
              style="min-height: 32px;"
            />
          </n-descriptions-item>
          <n-descriptions-item label="出院诊断">
            <n-input
              v-model:value="medicalInfo.outHospitalDiagnose"
              type="textarea"
              placeholder="请输入"
              :rows="1"
              autosize
              :maxlength="5000"
              style="min-height: 32px;"
            />
          </n-descriptions-item>
          <n-descriptions-item label="并发症">
            <n-checkbox-group v-model:value="medicalInfo.complication">
              <n-space item-style="display: flex;" :size="[40, 0]">
                <n-checkbox
                  v-for="complication in complicationList"
                  :key="complication.value"
                  :value="complication.value"
                >
                  <div class="w-full flex flex-1 items-center justify-start">
                    <span class="ml-10px" :class="complication.value === '其他' ? '-mr-30px' : ''">{{ complication.label }}</span>
                  </div>
                </n-checkbox>
                <n-input
                  v-show="medicalInfo.complication.includes('其他') "
                  v-model:value="medicalInfo.complicationOther"
                  style="minWidth:200px"
                  maxlength="30"
                />
              </n-space>
            </n-checkbox-group>
          </n-descriptions-item>
          <n-descriptions-item label="基础病因">
            <n-checkbox-group v-model:value="medicalInfo.basicEtiology">
              <n-space item-style="display: flex;" :size="[40, 0]">
                <n-checkbox
                  v-for="basicEtiology in basicEtiologyList"
                  :key="basicEtiology.value"
                  :value="basicEtiology.value"
                >
                  <div class="w-full flex flex-1 items-center justify-start">
                    <span class="ml-10px" :class="basicEtiology.value === '其他' ? '-mr-30px' : ''">{{ basicEtiology.label }}</span>
                  </div>
                </n-checkbox>
                <n-input
                  v-show="medicalInfo.basicEtiology.includes('其他') "
                  v-model:value="medicalInfo.basicEtiologyOther"
                  style="minWidth:200px"
                  maxlength="30"
                />
              </n-space>
            </n-checkbox-group>
          </n-descriptions-item>
          <n-descriptions-item label="现症病因">
            <n-checkbox-group v-model:value="medicalInfo.currentEtiology">
              <n-space item-style="display: flex;" :size="[40, 0]">
                <n-checkbox
                  v-for="currentEtiology in currentEtiologyList"
                  :key="currentEtiology.value"
                  :value="currentEtiology.value"
                >
                  <div class="w-full flex flex-1 items-center justify-start">
                    <span class="ml-10px" :class="currentEtiology.value === '其他' ? '-mr-30px' : ''">{{ currentEtiology.label }}</span>
                  </div>
                </n-checkbox>
                <n-input
                  v-show="medicalInfo.currentEtiology.includes('其他') "
                  v-model:value="medicalInfo.currentEtiologyOther"
                  style="minWidth:200px"
                  maxlength="30"
                />
              </n-space>
            </n-checkbox-group>
          </n-descriptions-item>
          <n-descriptions-item label="基础疾病">
            <n-checkbox-group v-model:value="medicalInfo.basicDiagnose">
              <n-space item-style="display: flex;" :size="[40, 0]">
                <n-checkbox
                  v-for="basicDiagnose in basicDiagnoseList"
                  :key="basicDiagnose.value"
                  :value="basicDiagnose.value"
                >
                  <div class="w-full flex flex-1 items-center justify-start">
                    <span class="ml-10px" :class="basicDiagnose.value === '其他' ? '-mr-30px' : ''">{{ basicDiagnose.label }}</span>
                  </div>
                </n-checkbox>
                <n-input
                  v-show="medicalInfo.basicDiagnose.includes('其他') "
                  v-model:value="medicalInfo.basicDiagnoseOther"
                  style="minWidth:200px"
                  maxlength="30"
                />
              </n-space>
            </n-checkbox-group>
          </n-descriptions-item>
          <n-descriptions-item label="肝硬化">
            <n-radio-group v-model:value="medicalInfo.cirrhosis" name="cirrhosis">
              <n-space item-style="display: flex;" :size="[40, 0]">
                <n-radio v-for="cirrhosis in liverCirrhosisList" :key="cirrhosis.value" :value="cirrhosis.value">
                  {{ cirrhosis.label }}
                </n-radio>
              </n-space>
            </n-radio-group>
          </n-descriptions-item>
          <n-descriptions-item label="家族史">
            <n-checkbox-group v-model:value="medicalInfo.familyHistory ">
              <n-space item-style="display: flex;" :size="[40, 0]">
                <n-checkbox
                  v-for="familyHistory in familyHistoryList"
                  :key="familyHistory.value"
                  :value="familyHistory.value"
                >
                  <span class="ml-10px">
                    {{ familyHistory.label }}
                  </span>
                </n-checkbox>
              </n-space>
            </n-checkbox-group>
          </n-descriptions-item>
        </n-descriptions>
      </section>
    </div>
    <footer class="footer">
      <n-space justify="center">
        <n-button type="primary" @click="onConfirm">
          保&nbsp;&nbsp;存
        </n-button>
        <n-button @click="onCancel">
          取&nbsp;&nbsp;消
        </n-button>
      </n-space>
    </footer>
  </div>
</template>

<style scoped lang="scss">
.editMedicalRecord {
    position: relative;
    padding: 0px 0px 60px 0px;
    height: 100%;
    flex:1;

    &-body {
        height: 100%;
        padding: 14px;
        overflow-y: auto;
    }

    :deep(.n-descriptions.n-descriptions--bordered .n-descriptions-table-wrapper .n-descriptions-table .n-descriptions-table-row .n-descriptions-table-header){
        background-color: #F5F5F5;
    }
    :deep(.n-descriptions .n-descriptions-table-wrapper .n-descriptions-table .n-descriptions-table-row .n-descriptions-table-header ){
        padding: 0px;
    }
    :deep(.n-descriptions .n-descriptions-table-wrapper .n-descriptions-table .n-descriptions-table-row .n-descriptions-table-content){
        padding: 0px;
    }
    :deep(.n-descriptions .n-descriptions-table-wrapper .n-descriptions-table .n-descriptions-table-row .n-descriptions-table-content){
        vertical-align: middle;
    }

    .footer {
        display:flex;
        align-items: center;
        justify-content: center;
        height: 60px;
        background-color: #F4F5F6;
        position: absolute;
        left:0;
        bottom:0;
        border-top: 1px solid #E8E8E8;
        width:100%;
}
}
</style>
