<script lang='ts' setup>
import type { FormInst } from 'wowjoy-vui'
import { watchEffect } from 'vue'
import dayjs from 'dayjs'
import type { PointModal } from '../type'
import { ascitesList, hepaticEncephalopathyList } from '@/constants'
import { getALBScore, getPTScore, getScoreRate, getTBILScore } from '@/utils/common'
import { getAssayDetailApi, getScoreDetailApi, updateScoreApi, updateScoreDetailApi } from '@/api/specificDisease'
import type { GetResultDetailModel, UpdateResultDetailParams, UpdateSpecialScoreParams } from '@/api/specificDisease'

const props = defineProps<Props>()

const emit = defineEmits(['success', 'cancel'])

const route = useRoute()

interface ChildPughModel {
  hepaticEncephalopathy: null | string
  ascites: null | string
  TBIL: {
    id: null | string
    result: null | string
  }
  PT: {
    id: null | string
    result: null | string
  }
  ALB: {
    id: null | string
    result: null | string
  }
  rate: string
  score: number

}

interface Props {
  calTime: string
  /** 1 前 2 后 */
  scoreOrder: string
  /** 1 child-pugh评分 2 meld评分 */
  scoreType: string
  patientId: string
  recordId: string
  recordTime: string
  id: string
  /** 浮层开关 */
  popoverShow: boolean
}

const PATIENT_ID = route.query?.patientId as string

enum KEY_POINT {
  TBIL = 'TBIL',
  PT = 'PT',
  ALB = 'ALB',
}
/** 时间阶段：前/后 */
enum TIME_PHASE {
  Before = '1',
  After = '2',
}

/** 本次编辑的id */
let _dataId: string | undefined

watchEffect(async () => {
  if (props.popoverShow)
    await Promise.all([await getAssayDetail(), await getResultDetail()])
})

/** child评分指标枚举 */
const childPughOptions = ref({
  /** 总胆红素 */
  TBIL: [],
  /** 凝血酶原时间延长 */
  PT: [],
  /** 白蛋白 */
  ALB: [],
})
const formRef = ref<FormInst | null>(null)
const childPughModel = ref<ChildPughModel>({
  hepaticEncephalopathy: null,
  ascites: null,
  TBIL: {
    id: null,
    result: null,
  },
  PT: {
    id: null,
    result: null,
  },
  ALB: {
    id: null,
    result: null,
  },
  score: 0,
  rate: '',

})

const childPughRules = {
  hepaticEncephalopathy: {
    required: true,
    trigger: ['blur'],
    message: '内容不能为空',

  },
  ascites: {
    required: true,
    trigger: ['blur'],
    message: '内容不能为空',

  },
  TBIL: {
    required: true,
    message: '内容不能为空',
    trigger: ['blur', 'change'],
  },
  PT: {
    required: true,
    message: '内容不能为空',
    trigger: ['blur', 'change'],
  },
  ALB: {
    required: true,
    message: '内容不能为空',
    trigger: ['blur', 'change'],
  },
}
/**
 * 编辑时获取上次填写的结果和id
 * @param key 指标名称
 * @param value 指标id
 */
function getPointResult(key: string, value: string) {
  const id = value
  let result: string | null = null

  const findIndex = childPughOptions.value[key]
    .findIndex(item => item.value === value)

  if (findIndex > -1) {
    const resultValue = childPughOptions.value[key][findIndex].result
    if (key === 'PT')
      result = `${(Number.parseFloat(resultValue) - 11.2).toFixed(2)}`
    else
      result = resultValue
  }
  return {
    id, result,
  }
}

/** 获取各项标准的结果值 */
async function getResultDetail() {
  try {
    const params = {
      calTime: props.calTime,
      scoreId: props.id,
      scoreOrder: props.scoreOrder,
      scoreType: props.scoreType,

    }
    const res = await getScoreDetailApi<GetResultDetailModel>(params)
    if (res?.data) {
      _dataId = res.data?.id
      childPughModel.value.hepaticEncephalopathy = res.data?.hepaticComa
      childPughModel.value.ascites = res.data?.ascites

      childPughModel.value.TBIL.id = getPointResult('TBIL', res.data.tbil).id
      childPughModel.value.TBIL.result = getPointResult('TBIL', res.data.tbil).result

      childPughModel.value.PT.id = getPointResult('PT', res.data.pt).id
      childPughModel.value.PT.result = getPointResult('PT', res.data.pt).result

      childPughModel.value.ALB.id = getPointResult('ALB', res.data.alb).id
      childPughModel.value.ALB.result = getPointResult('ALB', res.data.alb).result

      console.log(childPughModel)
    }
  }
  catch (error) {

  }
}
/** 获取各项指标下拉枚举 */
async function getAssayDetail() {
  const params = {
    calTime: props.calTime,
    patientId: PATIENT_ID,
    scoreOrder: props.scoreOrder,
    scoreType: props.scoreType,
  }
  try {
    const res = await getAssayDetailApi(params)

    if (res.data) {
      const DEFAULT_STATUS = '1'
      const { ALB = [], PT = [] } = res.data as unknown as any
      const ALBArray = ALB.map((item) => {
        const label = `${dayjs(item.reportTime).format('YYYY-MM-DD HH:mm')} ${item.result} ${item.unit}`
        const value = item.id
        if (item.defaultStatus === DEFAULT_STATUS && !_dataId) {
          childPughModel.value.ALB.id = item.id
          childPughModel.value.ALB.result = item.result
        }

        return {
          label,
          value,
          ...item,
        }
      })
      /*
        这个值等于凝血酶原时间减去凝血酶原时间正常对照(RPT)，rpt按11.2s算，也就是说=pt-11.2
        */
      const PTArray = PT.map((item) => {
        const resultValue = `${(Number.parseFloat(item.result) - 11.2).toFixed(2)}`
        const label = `${dayjs(item.reportTime).format('YYYY-MM-DD HH:mm')} ${resultValue} ${item.unit}`
        const value = item.id
        if (item.defaultStatus === DEFAULT_STATUS && !_dataId) {
          childPughModel.value.PT.id = item.id
          childPughModel.value.PT.result = resultValue
        }

        return {
          label,
          value,
          ...item,
        }
      })
      const TBILArray = res.data['T-BIL'].map((item) => {
        const label = `${dayjs(item.reportTime).format('YYYY-MM-DD HH:mm')} ${item.result} ${item.unit}`
        const value = item.id
        if (item.defaultStatus === DEFAULT_STATUS && !_dataId) {
          childPughModel.value.TBIL.id = item.id
          childPughModel.value.TBIL.result = item.result
        }

        return {
          label,
          value,
          ...item,
        }
      })

      childPughOptions.value.ALB = ALBArray
      childPughOptions.value.PT = PTArray
      childPughOptions.value.TBIL = TBILArray
    }
  }
  catch (error) {
    console.error(error)
  }
}
/** 是否全部填写 */
function isFillIn() {
  const isNoResult = (value: string | null | undefined | number) => {
    if (value === null || value === undefined || value === undefined)
      return true
    else
      return false
  }
  const { TBIL, PT, ALB, hepaticEncephalopathy, ascites } = childPughModel.value

  if (isNoResult(TBIL.id) || isNoResult(PT.id) || isNoResult(ALB.id)
        || isNoResult(ascites) || isNoResult(hepaticEncephalopathy))
    return false

  return true
}

/** child-pugh最终评分 */
const computedCHildPughScore = computed(() => {
  /** 是否全部填写 */
  const isAllHaveValue = isFillIn()

  if (isAllHaveValue) {
    const { hepaticEncephalopathy, ascites, TBIL, PT, ALB } = childPughModel.value
    const hepaticEncephalopathyScore = Number.parseFloat(hepaticEncephalopathy!)
    const ascitesScore = Number.parseFloat(ascites!)

    const TBILScore = getTBILScore(Number.parseFloat(TBIL.result!))

    const PTScore = getPTScore(Number.parseFloat(PT.result!))
    const ALBScore = getALBScore(Number.parseFloat(ALB.result!))

    const totalScore = hepaticEncephalopathyScore + ascitesScore + TBILScore + PTScore + ALBScore
    const scoreRate = getScoreRate(totalScore)
    childPughModel.value.score = totalScore
    childPughModel.value.rate = scoreRate
    console.log(childPughModel)

    return `${scoreRate}级 (${totalScore}分)`
  }
  else {
    return '-'
  }
})
/**
 * 更新指标时回调函数
 * @param pointName 指标名称
 * @param value 指标id
 * @param option 指标的对象
 */
function onUpdateKeyPoint(pointName: string, value: string, option: PointModal) {
  const { result } = option
  if (pointName === KEY_POINT.TBIL) {
    childPughModel.value.TBIL.id = value
    childPughModel.value.TBIL.result = result
  }
  else if (pointName === KEY_POINT.PT) {
    /* 这个值等于凝血酶原时间减去凝血酶原时间正常对照(RPT)，rpt按11.2s算，也就是说=pt-11.2 */
    const resultValue = `${(Number.parseFloat(result) - 11.2).toFixed(2)}`
    childPughModel.value.PT.id = value
    childPughModel.value.PT.result = resultValue
  }
  else if (pointName === KEY_POINT.ALB) {
    childPughModel.value.ALB.id = value
    childPughModel.value.ALB.result = result
  }
}
/**
 * 保存最终评分
 */
async function confirmFinallyScore(): Promise<boolean | null> {
  try {
    const params: UpdateSpecialScoreParams = {
      calTime: props.calTime,
      id: props.id,
      patientId: props.patientId,
      recordId: props.recordId,
      recordTime: props.recordTime,
      childPughScoreBefore: undefined,
      childPughScoreAfter: undefined,
    }

    if (props.scoreOrder === TIME_PHASE.Before) // 人工肝前
      params.childPughScoreBefore = `${childPughModel.value.rate}级 (${childPughModel.value.score}分)`
    else if (props.scoreOrder === TIME_PHASE.After) // 人工肝后
      params.childPughScoreAfter = `${childPughModel.value.rate}级 (${childPughModel.value.score}分)`

    const res = await updateScoreApi<boolean>(params)

    return res.data
  }
  catch (error) {
    console.error(error)
    return false
  }
}
/** 保存详细指标的结果值 */
async function confirmFinallyResult(): Promise<boolean | null> {
  try {
    const params: UpdateResultDetailParams = {
      calTime: props.calTime,
      scoreId: props.id,
      id: _dataId || undefined,
      hepaticComa: childPughModel.value.hepaticEncephalopathy!,
      ascites: childPughModel.value.ascites!,
      alb: childPughModel.value.ALB.id!,
      tbil: childPughModel.value.TBIL.id!,
      pt: childPughModel.value.PT.id!,
      scoreType: '1', // 1 child-pugh评分 2 meld评分
      scoreOrder: props.scoreOrder, // 1 前 2 后
    }

    const res = await updateScoreDetailApi<boolean>(params)

    return res.data
  }
  catch (error) {
    console.error(error)
    return false
  }
}
/** 确定 */
function onConfirm() {
  formRef.value?.validate(async (errors) => {
    if (!errors) {
      const resFinallyScore = await confirmFinallyScore()
      const resFinallyResult = await confirmFinallyResult()

      if (resFinallyScore && resFinallyResult) {
        window.$message.success('保存成功')
        emit('success')
        resetModel()
      }
    }
    else {
      console.log(errors)
      window.$message.error('验证失败')
    }
  })
}

function onCancel() {
  emit('cancel')
  resetModel()
}

function resetModel() {
  childPughModel.value = {
    hepaticEncephalopathy: null,
    ascites: null,
    TBIL: {
      id: null,
      result: null,
    },
    PT: {
      id: null,
      result: null,
    },
    ALB: {
      id: null,
      result: null,
    },
    score: 0,
    rate: '',
  }
}
</script>

<template>
  <div class="bg-#fff">
    <div class="px-20px pt-20px">
      <n-form
        ref="formRef"
        :label-width="166"
        :item-margin="10"
        :model="childPughModel"
        :rules="childPughRules"
        label-placement="left"
        require-mark-placement="left"
      >
        <n-form-item
          label="肝性脑病 (期)" path="hepaticEncephalopathy"
          style="--n-feedback-height: 14px;"
        >
          <n-radio-group v-model:value="childPughModel.hepaticEncephalopathy" name="hepaticEncephalopathy">
            <n-space>
              <n-radio v-for="item in hepaticEncephalopathyList" :key="item.value" :value="item.value">
                {{ item.label }}
              </n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item>
        <n-form-item
          label="腹水" path="ascites"
          style="--n-feedback-height: 14px;"
        >
          <n-radio-group v-model:value="childPughModel.ascites" name="ascites">
            <n-space>
              <n-radio v-for="item in ascitesList" :key="item.value" :value="item.value">
                {{ item.label }}
              </n-radio>
            </n-space>
          </n-radio-group>
        </n-form-item>
        <n-form-item
          label="总胆红素 (umol/L)" path="TBIL.id"
          style="--n-feedback-height: 14px;"
        >
          <n-select
            v-model:value="childPughModel.TBIL.id"
            placeholder="请选择"
            :options="childPughOptions.TBIL"
            style="width:258px"
            @update-value="(value, option) => onUpdateKeyPoint('TBIL', value, option)"
          />
        </n-form-item>
        <n-form-item
          label="凝血酶原时间延长 (秒)" path="PT.id"
          style="--n-feedback-height: 14px;"
        >
          <n-select
            v-model:value="childPughModel.PT.id"
            placeholder="请选择"
            :options="childPughOptions.PT"
            style="width:258px"
            @update-value="(value, option) => onUpdateKeyPoint('PT', value, option)"
          />
        </n-form-item>
        <n-form-item
          label="白蛋白 (g/L)" path="ALB.id"
          style="--n-feedback-height: 14px;"
        >
          <n-select
            v-model:value="childPughModel.ALB.id"
            placeholder="请选择"
            :options="childPughOptions.ALB"
            style="width:258px"
            @update-value="(value, option) => onUpdateKeyPoint('ALB', value, option)"
          />
        </n-form-item>
      </n-form>
    </div>
    <n-divider dashed margin="0px 0px 0px 0px" />
    <div class="m-20px text-14px">
      根据CHild-pugh评分计算规则，该患者评分为：<span class="text-#FF9B54">{{ computedCHildPughScore }}</span>
    </div>

    <div class="w-full pb-18px">
      <n-space justify="center">
        <n-button type="primary" @click="onConfirm">
          确&nbsp;&nbsp;定
        </n-button>
        <n-button @click="onCancel">
          取&nbsp;&nbsp;消
        </n-button>
      </n-space>
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>
