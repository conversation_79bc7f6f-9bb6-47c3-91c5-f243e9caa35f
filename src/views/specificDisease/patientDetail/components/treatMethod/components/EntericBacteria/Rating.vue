<script setup lang="ts">
import { cloneDeep } from 'lodash-es'
import dayjs from 'dayjs'
import Pugh from './Pugh.vue'
import Meld from './Meld.vue'
import Fib from './Fib.vue'
import { addScoreAPI, deleteScoreAPI, getScoreAPI, getSelectEnumsAPI, updateBacteriaScoreDetailApi, updateScoreAPI } from '@/api/specificDisease'

const route = useRoute()
const isModalShow = ref(false)
const isLoading = inject<Ref<boolean>>('isLoading')!

interface FixedForm {
  id: null | string | undefined
  date: null | string
  phesScore: null | string
  westHavenScore: null | string
  patientId: null | string

}

const PATIENT_ID = route.query?.patientId as string

const fixedForm = ref<FixedForm>({
  id: null,
  date: null,
  phesScore: null,
  westHavenScore: null,
  patientId: PATIENT_ID,

})

const actionType = computed(() => {
  return fixedForm.value.id ? 'EDIT' : 'CREATE'
})

const page = ref({
  page: 1,
  size: 10,
  total: 0,
})

const pughRef = ref<InstanceType<typeof Pugh>>()
const meldRef = ref<InstanceType<typeof Meld>>()
const fibRef = ref<InstanceType<typeof Fib>>()

const tableData = ref<any[]>([])
/** 获取表格数据 */
async function getTableData() {
  try {
    isLoading.value = true
    const { data } = await getScoreAPI({
      patientId: route.query.patientId,
      size: page.value.size,
      start: page.value.page,
    })

    tableData.value = data.records
    page.value.total = data.total
  }
  catch (error) {
    console.log(error)
  }
  finally {
    isLoading.value = false
  }
}

/** 保存child-pugh指标最终结果 */
async function confirmChildPughResult(scoreId?: string): Promise<boolean> {
  try {
    const { formData = {}, _dataId = '' } = pughRef.value

    const { hepaticEncephalopathy, ascites, TBIL, PT, ALB } = formData
    const params = {
      scoreType: '1',
      calTime: fixedForm.value.date,
      scoreId: scoreId || (fixedForm.value.id || undefined),
      id: _dataId || undefined,
      hepaticComa: hepaticEncephalopathy,
      ascites: ascites!,
      alb: ALB.value!,
      tbil: TBIL.value!,
      pt: PT.value!,
    }
    const res = await updateBacteriaScoreDetailApi<boolean>(params)

    if (res?.data)
      return true

    else
      return false
  }
  catch (error) {
    console.error(error)
    return false
  }
}
/** 保存meld指标最终结果 */
async function confirmMeldResult(scoreId?: string): Promise<boolean> {
  try {
    const { formData = {}, _dataId = '' } = meldRef.value

    const { etiology, TBIL, CR, INR } = formData
    const params = {
      scoreType: '2',
      calTime: fixedForm.value.date,
      scoreId: scoreId || (fixedForm.value.id || undefined),
      id: _dataId || undefined,
      etiology,
      inr: INR.value!,
      tbil: TBIL.value!,
      scr: CR.value!,
    }
    const res = await updateBacteriaScoreDetailApi<boolean>(params)

    if (res?.data)
      return true

    else
      return false
  }
  catch (error) {
    console.error(error)
    return false
  }
}
/** 保存FIB指标最终结果 */
async function confirmFibResult(scoreId?: string): Promise<boolean> {
  try {
    const { formData = {}, _dataId = '' } = fibRef.value

    const { ast, alt, plt } = formData
    const params = {
      scoreType: '3',
      calTime: fixedForm.value.date,
      scoreId: scoreId || (fixedForm.value.id || undefined),
      id: _dataId || undefined,
      ast: ast.value!,
      alt: alt.value!,
      plt: plt.value!,
    }
    const res = await updateBacteriaScoreDetailApi<boolean>(params)

    if (res?.data)
      return true

    else
      return false
  }
  catch (error) {
    console.error(error)
    return false
  }
}
/** 确认保存 */
async function addOrUpdateRecord() {
  try {
    if (pughRef.value!.validate() && meldRef.value!.validate() && fibRef.value!.validate()) {
      const _fixedForm = cloneDeep(fixedForm.value)
      const formData = {
        ..._fixedForm,
        childPughScore: pughRef.value!.score?.rate ? `${pughRef.value!.score?.rate}级(${pughRef.value!.score?.total}分)` : null,
        meldScore: meldRef.value!.score,
        fib4Score: fibRef.value!.score,
      }

      if (_fixedForm.id) {
        const { data } = await updateScoreAPI(formData)
        const childPughRes = await confirmChildPughResult()
        const meldRes = await confirmMeldResult()
        const fibRes = await confirmFibResult()

        if (data && childPughRes && meldRes && fibRes) {
          window.$message.success('评分成功')
          isModalShow.value = false
          getTableData()
        }
      }
      else {
        const { data } = await addScoreAPI(formData)
        const childPughRes = await confirmChildPughResult(data as string)
        const meldRes = await confirmMeldResult(data as string)
        const fibRes = await confirmFibResult(data as string)

        if (data && childPughRes && meldRes && fibRes) {
          window.$message.success('评分成功')
          isModalShow.value = false
          getTableData()
        }
      }
    }
  }
  catch (error) {
    console.log(error)
  }
}

function handleAddClick() {
  isModalShow.value = true
  fixedForm.value.id = null
  fixedForm.value.date = dayjs().format('YYYY-MM-DD')
  fixedForm.value.phesScore = null
  fixedForm.value.westHavenScore = null
  console.log(fixedForm.value.date)
}

function handleEditClick(row: any) {
  isModalShow.value = true
  fixedForm.value.id = row.id
  fixedForm.value.date = row.date
  fixedForm.value.phesScore = row.phesScore
  fixedForm.value.westHavenScore = row.westHavenScore
}

function handleDeleteClick(row: any) {
  window.$dialog.warning({
    title: '确定删除该数据吗？',
    negativeText: '取消',
    positiveText: '确定',
    positiveButtonProps: {
      type: 'primary',
      ghost: true,
    },
    negativeButtonProps: {
      type: 'primary',
      ghost: true,
    },
    onPositiveClick: async () => {
      try {
        const { data } = await deleteScoreAPI(row.id)
        if (data) {
          window.$message.success('删除成功')
          getTableData()
        }
      }
      catch (error) {
        console.log(error)
      }
    },

  })
}

const enums = ref<Record<string, any>>({})

provide('enums', enums)

async function getEnums() {
  try {
    const requests = [
      getSelectEnumsAPI({
        patientId: route.query.patientId,
        scoreType: '1',
        page: 0,
        size: 0,
      }),
      getSelectEnumsAPI({
        patientId: route.query.patientId,
        scoreType: '2',
        page: 0,
        size: 0,
      }),
      getSelectEnumsAPI({
        patientId: route.query.patientId,
        scoreType: '3',
        page: 0,
        size: 0,
      }),
    ]

    const data = await Promise.all(requests)
    data.forEach((item) => {
      Object.entries(item.data).forEach(([key, value]) => {
        (value as any).forEach((item2) => {
          if (key === 'PT')
            item2.label = `${item2.reportTime} ${(Number.parseFloat(item2.result) - 11.2).toFixed(2)} ${item2.unit}`

          else
            item2.label = `${item2.reportTime} ${item2.result} ${item2.unit}`
        })
        enums.value[key] = value
      })
    })
  }
  catch (error) {
    console.log(error)
  }
}

onMounted(() => {
  getTableData()
  getEnums()
})
</script>

<template>
  <div w-full>
    <n-button type="primary" mb-20px @click="handleAddClick">
      新增
    </n-button>

    <el-table :data="tableData" stripe w-full>
      <el-table-column type="index" label="序号" width="80" />
      <el-table-column label="日期" min-width="100" :formatter="({ date }) => date || '-'" />
      <el-table-column prop="" min-width="212" :formatter="({ phesScore }) => phesScore ? `${phesScore}分` : '-'">
        <template #header>
          <div leading-none>
            心理测量肝性脑病评分(PHES)
          </div>
        </template>
      </el-table-column>
      <el-table-column label="MELD评分" min-width="120" :formatter="({ meldScore }) => meldScore ? `${meldScore}分` : '-'" />
      <el-table-column label="Child-Pugh评分" min-width="130" :formatter="({ childPughScore }) => childPughScore ? childPughScore : '-'" />
      <el-table-column label="Fib-4评分" min-width="120" :formatter="({ fib4Score }) => fib4Score ? `${fib4Score}分` : '-'" />
      <el-table-column label="West Haven分级" min-width="150" :formatter="({ westHavenScore }) => westHavenScore ? `${westHavenScore}分` : '-'" />
      <el-table-column label="操作" width="100" fixed="right">
        <template #default="{ row }">
          <div flex items-center gap-5px>
            <span uno-link @click="handleEditClick(row)">编辑</span>
            <div h-14px w-1px bg="#3B8FD9" />
            <span uno-link @click="handleDeleteClick(row)">删除</span>
          </div>
        </template>
      </el-table-column>
      <template #empty>
        <div h-300px flex items-center justify-center>
          <DataEmpty />
        </div>
      </template>
    </el-table>

    <div v-if="page.total > 0" mt-20px w-full flex justify-end>
      <n-pagination
        v-model:page="page.page"
        v-model:page-size="page.size"
        :item-count="page.total"
        :page-sizes="[5, 10, 20, 30]"
        show-size-picker
        show-quick-jumper
        @update:page="getTableData"
        @update:page-size="() => {
          page.page = 1
          getTableData()
        }"
      />
    </div>

    <n-modal
      v-model:show="isModalShow"
      preset="card"
      :title="`${fixedForm.id ? '编辑' : '新增'}专科评分`"
      w-1000px
      class="h-600px"
      head-style="divide"
    >
      <div>
        <div flex="~ col" gap-14px px-10px pt-20px class="h-482px overflow-y-auto">
          <div flex justify-between>
            <div flex items-center gap-10px>
              <div text="#666" text-right>
                日期
              </div>
              <n-date-picker
                v-model:formatted-value="fixedForm.date"
                type="date"
                value-format="yyyy-MM-dd"
                class="!w-234px"
              />
            </div>

            <div flex items-center gap-10px>
              <div w-88px text="12px right #666">
                心理测量肝性脑病评分(PHES)
              </div>
              <n-input
                v-model:value="fixedForm.phesScore"
                class="!w-234px"
              >
                <template #suffix>
                  <span class="text-#999">分</span>
                </template>
              </n-input>
            </div>

            <div flex items-center gap-10px>
              <div w-72px text="12px right #666">
                West Haven分级
              </div>
              <n-input
                v-model:value="fixedForm.westHavenScore"
                class="!w-234px"
              >
                <template #suffix>
                  <span class="text-#999">级</span>
                </template>
              </n-input>
            </div>
          </div>

          <Pugh ref="pughRef" :action-type="actionType" :score-id="fixedForm.id" :popover-show="isModalShow" />

          <Meld ref="meldRef" :action-type="actionType" :score-id="fixedForm.id" :popover-show="isModalShow" />

          <Fib ref="fibRef" :action-type="actionType" :score-id="fixedForm.id" :popover-show="isModalShow" />
        </div>
        <div mt-24px w-full flex justify-center>
          <n-button mr-16px type="primary" w-100px @click="addOrUpdateRecord">
            保 存
          </n-button>
          <n-button w-100px @click="isModalShow = false">
            取 消
          </n-button>
        </div>
      </div>
    </n-modal>
  </div>
</template>
