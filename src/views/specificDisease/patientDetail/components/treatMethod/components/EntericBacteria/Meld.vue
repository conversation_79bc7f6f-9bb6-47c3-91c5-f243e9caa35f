<script setup lang="ts">
import { getMeldScore } from '@/utils'
import { getBacteriaSBcoreDetailApi } from '@/api/specificDisease'

interface Props {
  actionType: 'EDIT' | 'CREATE'
  /** 浮层开关 */
  popoverShow: boolean
  scoreId: string | null | undefined
}
const props = defineProps<Props>()

const enums = inject<Ref<Record<string, any>>>('enums')!

const isLogicShow = ref(false)
const _dataId = ref('')

const formData = ref({
  etiology: null,
  TBIL: {
    value: null,
    result: null,
    unit: null,
  },
  INR: {
    value: null,
    result: null,
  },
  CR: {
    value: null,
    result: null,
    unit: null,
  },
})

watchEffect(() => {
  if (props.popoverShow && props.actionType === 'EDIT')
    getBacteriaSBcoreDetail()
})
/**
 * 编辑时获取上次填写的结果和id
 * @param key 指标名称
 * @param value 指标id
 */
function getPointResult(key: string, value: string) {
  const id = value
  let result = null
  let unit = null
  const findIndex = enums.value[key]
    .findIndex(item => item.id === value)

  if (findIndex > -1) {
    result = enums.value[key][findIndex].result
    unit = enums.value[key][findIndex].unit
  }

  return {
    id, result, unit,
  }
}
/** 获取指标详细结果 */
async function getBacteriaSBcoreDetail() {
  try {
    const params = {
      scoreId: props.scoreId!,
      scoreType: '2',
    }
    const res = await getBacteriaSBcoreDetailApi(params)
    if (res?.data) {
      _dataId.value = res.data?.id
      formData.value.etiology = res.data?.etiology

      formData.value.TBIL.value = getPointResult('T-BIL', res.data.tbil).id
      formData.value.TBIL.result = getPointResult('T-BIL', res.data.tbil).result
      formData.value.TBIL.unit = getPointResult('T-BIL', res.data.tbil).unit

      formData.value.INR.value = getPointResult('INR', res.data.inr).id
      formData.value.INR.result = getPointResult('INR', res.data.inr).result

      formData.value.CR.value = getPointResult('CR', res.data.scr).id
      formData.value.CR.result = getPointResult('CR', res.data.scr).result
      formData.value.CR.unit = getPointResult('CR', res.data.scr).unit
    }
  }
  catch (error) {
    console.log(error)
  }
}
const score = computed(() => {
  const TBIL = formData.value.TBIL
  const INR = formData.value.INR
  const CR = formData.value.CR
  const etiology = formData.value.etiology

  if (etiology && TBIL.value && INR.value && CR.value) {
    const meldPayload = {
      etiology,
      TBIL: {
        result: TBIL.result,
        unit: TBIL.unit,
      },
      INR: {
        result: INR.result,
      },
      CR: {
        result: CR.result,
        unit: CR.unit,
      },
    }
    return getMeldScore(meldPayload)
  }

  else { return null }
})

function validate() {
  if (isLogicShow.value) {
    const TBIL = formData.value.TBIL.result
    const INR = formData.value.INR.result
    const CR = formData.value.CR.result
    const etiology = formData.value.etiology

    if (!etiology || !TBIL || !INR || !CR) {
      window.$message.warning('请填写完整MELD评分数据')
      return false
    }

    return true
  }

  return true
}

defineExpose({
  validate,
  score,
  formData,
  _dataId,
})
</script>

<template>
  <div flex gap-10px>
    <div text="#666">
      MELD评分
    </div>
    <n-checkbox v-model:checked="isLogicShow">
      <span ml-10px text="#333">填写并计算</span>
    </n-checkbox>
  </div>

  <div v-if="isLogicShow" w-full bg="#A5B8D1/10" p-15px>
    <div flex="~ wrap" justify-between gap-y-14px>
      <div flex items-center>
        <SvgIcon
          local-icon="slmc-icon-app_required1"
          size="14"
          class="text-#F36969"
        />
        <div mr-10px>
          病因
        </div>
        <n-radio-group v-model:value="formData.etiology">
          <n-radio mr-145px value="0">
            <span text="#333" mr-10px>胆汁性或酒精性</span>
          </n-radio>
          <n-radio value="1">
            <span text="#333" mr-10px>其他</span>
          </n-radio>
        </n-radio-group>
      </div>

      <div flex items-center>
        <SvgIcon
          local-icon="slmc-icon-app_required1"
          size="14"
          class="text-#F36969"
        />
        <div mr-10px>
          总胆红素 (umol/L)
        </div>
        <n-select
          v-model:value="formData.TBIL.value"
          :options="enums['T-BIL']"
          class="!w-338px"
          value-field="id"
          @update:value="(_v:any, option: any) => {
            formData.TBIL.result = option.result
            formData.TBIL.unit = option.unit
          }"
        />
      </div>

      <div flex items-center>
        <SvgIcon
          local-icon="slmc-icon-app_required1"
          size="14"
          class="text-#F36969"
        />
        <div mr-10px>
          INR
        </div>
        <n-select
          v-model:value="formData.INR.value"
          :options="enums.INR"
          class="!w-338px"
          value-field="id"
          @update:value="(_v:any, option: any) => {
            formData.INR.result = option.result
          }"
        />
      </div>

      <div flex items-center>
        <div w-135px flex items-center justify-end>
          <SvgIcon
            local-icon="slmc-icon-app_required1"
            size="14"
            class="text-#F36969"
          />
          <div mr-10px>
            肌酐 (umol/l)
          </div>
        </div>

        <n-select
          v-model:value="formData.CR.value"
          :options="enums.CR"
          class="!w-338px"
          value-field="id"
          @update:value="(_v:any, option: any) => {
            formData.CR.result = option.result
            formData.CR.unit = option.unit
          }"
        />
      </div>
    </div>
    <div my-14px h-1px w-full style="border-top: 1px dashed #ccc;" />
    <div>
      根据MELD评分计算规则，该患者评分为：<span text="#FF9B54">{{ score ? `(${score}分)` : '-' }}</span>
    </div>
  </div>
</template>
