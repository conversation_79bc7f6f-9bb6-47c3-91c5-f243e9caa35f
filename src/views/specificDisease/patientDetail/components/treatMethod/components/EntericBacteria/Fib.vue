<script setup lang="ts">
import { getBacteriaSBcoreDetailApi } from '@/api/specificDisease'

const props = defineProps<Props>()
const enums = inject<Ref<Record<string, any>>>('enums')!
const patientInfo = inject<Ref<Record<string, any>>>('patientInfo')!

interface Props {
  actionType: 'EDIT' | 'CREATE'
  /** 浮层开关 */
  popoverShow: boolean
  scoreId: string | null | undefined
}
const age = patientInfo?.value?.age
// const age = '22'
const isLogicShow = ref(false)
const _dataId = ref('')

const formData = ref({
  ast: {
    value: null,
    result: null,
  },
  plt: {
    value: null,
    result: null,
  },
  alt: {
    value: null,
    result: null,
  },
})
watchEffect(() => {
  if (props.popoverShow && props.actionType === 'EDIT')
    getBacteriaSBcoreDetail()
})

/**
 * 编辑时获取上次填写的结果和id
 * @param key 指标名称
 * @param value 指标id
 */
function getPointResult(key: string, value: string) {
  const id = value
  let result = null

  const findIndex = enums.value[key]
    .findIndex(item => item.id === value)

  if (findIndex > -1)
    result = enums.value[key][findIndex].result

  return {
    id, result,
  }
}
/** 获取指标详细结果 */
async function getBacteriaSBcoreDetail() {
  try {
    const params = {
      scoreId: props.scoreId!,
      scoreType: '3',
    }

    const res = await getBacteriaSBcoreDetailApi(params)
    if (res?.data) {
      _dataId.value = res.data?.id

      formData.value.ast.value = getPointResult('AST', res.data.ast).id
      formData.value.ast.result = getPointResult('AST', res.data.ast).result

      formData.value.alt.value = getPointResult('ALT', res.data.alt).id
      formData.value.alt.result = getPointResult('ALT', res.data.alt).result

      formData.value.plt.value = getPointResult('PLT', res.data.plt).id
      formData.value.plt.result = getPointResult('PLT', res.data.plt).result
    }
  }
  catch (error) {
    console.log(error)
  }
}
const score = computed(() => {
  const ast = Number(formData.value.ast.result)
  const plt = Number(formData.value.plt.result)
  const alt = Number(formData.value.alt.result)
  const _age = Number(age)

  if (ast && plt && alt && _age)
    return ((age * ast) / (plt * Math.sqrt(alt))).toFixed(1)
  else
    return null
})

function validate() {
  if (isLogicShow.value) {
    const ast = Number(formData.value.ast.result)
    const plt = Number(formData.value.plt.result)
    const alt = Number(formData.value.alt.result)
    const _age = Number(age)

    if (!ast || !plt || !alt || !_age) {
      window.$message.warning('请填写完整Fib-4评分评分数据')
      return false
    }

    return true
  }

  return true
}

defineExpose({
  validate,
  score,
  formData,
  _dataId,
})
</script>

<template>
  <div flex gap-10px>
    <div text="#666">
      Fib-4评分
    </div>
    <n-checkbox v-model:checked="isLogicShow">
      <span ml-10px text="#333">填写并计算</span>
    </n-checkbox>
  </div>

  <div v-if="isLogicShow" w-full bg="#A5B8D1/10" p-15px>
    <div flex="~ wrap" justify-between gap-y-14px>
      <div flex items-center>
        <SvgIcon
          local-icon="slmc-icon-app_required1"
          size="14"
          class="text-#F36969"
        />
        <div mr-10px>
          AST
        </div>
        <n-select
          v-model:value="formData.ast.value"
          :options="enums.AST"
          class="!w-338px"
          value-field="id"
          @update:value="(_v:any, option: any) => {
            formData.ast.result = option.result
          }"
        />
      </div>

      <div flex items-center>
        <SvgIcon
          local-icon="slmc-icon-app_required1"
          size="14"
          class="text-#F36969"
        />
        <div mr-10px>
          PLT计数
        </div>
        <n-select
          v-model:value="formData.plt.value"
          :options="enums.PLT"
          class="!w-338px"
          value-field="id"
          @update:value="(_v:any, option: any) => {
            formData.plt.result = option.result
          }"
        />
      </div>

      <div flex items-center>
        <SvgIcon
          local-icon="slmc-icon-app_required1"
          size="14"
          class="text-#F36969"
        />
        <div mr-13px>
          ALT
        </div>
        <n-select
          v-model:value="formData.alt.value"
          :options="enums.ALT"
          class="!w-338px"
          value-field="id"
          @update:value="(_v:any, option: any) => {
            formData.alt.result = option.result
          }"
        />
      </div>
    </div>
    <div my-14px h-1px w-full style="border-top: 1px dashed #ccc;" />
    <div>
      根据Fib-4评分计算规则，该患者评分为：<span text="#FF9B54">{{ score ? `(${score}分)` : '-' }}</span>
    </div>
  </div>
</template>
