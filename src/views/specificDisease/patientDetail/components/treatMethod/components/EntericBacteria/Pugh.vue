<script setup lang="ts">
import { ascitesList, hepaticEncephalopathyList } from '@/constants'
import { getALBScore, getPTScore, getScoreRate, getTBILScore } from '@/utils'
import { getBacteriaSBcoreDetailApi } from '@/api/specificDisease'

interface Props {
  actionType: 'EDIT' | 'CREATE'
  /** 浮层开关 */
  popoverShow: boolean
  scoreId: string | null | undefined
}
const props = defineProps<Props>()

const enums = inject<Ref<Record<string, any>>>('enums')!
const isLogicShow = ref(false)
const _dataId = ref('')

const formData = ref({
  hepaticEncephalopathy: null,
  ascites: null,
  TBIL: {
    value: null,
    result: null,
  },
  PT: {
    value: null,
    result: null,
  },
  ALB: {
    value: null,
    result: null,
  },
})
watchEffect(() => {
  if (props.popoverShow && props.actionType === 'EDIT')
    getBacteriaSBcoreDetail()
})

/**
 * 编辑时获取上次填写的结果和id
 * @param key 指标名称
 * @param value 指标id
 */
function getPointResult(key: string, value: string) {
  const id = value
  let result = null

  const findIndex = enums.value[key]
    .findIndex(item => item.id === value)

  if (findIndex > -1)
    result = enums.value[key][findIndex].result

  return {
    id, result,
  }
}
/** 获取指标详细结果 */
async function getBacteriaSBcoreDetail() {
  try {
    const params = {
      scoreId: props.scoreId!,
      scoreType: '1',
    }
    const res = await getBacteriaSBcoreDetailApi(params)
    if (res?.data) {
      _dataId.value = res.data?.id
      formData.value.hepaticEncephalopathy = res.data?.hepaticComa
      formData.value.ascites = res.data?.ascites

      formData.value.TBIL.value = getPointResult('T-BIL', res.data.tbil).id
      formData.value.TBIL.result = getPointResult('T-BIL', res.data.tbil).result

      formData.value.PT.value = getPointResult('PT', res.data.pt).id
      formData.value.PT.result = getPointResult('PT', res.data.pt).result

      formData.value.ALB.value = getPointResult('ALB', res.data.alb).id
      formData.value.ALB.result = getPointResult('ALB', res.data.alb).result
    }
  }
  catch (error) {
    console.log(error)
  }
}

const score = computed(() => {
  const hepaticEncephalopathy = Number(formData.value.hepaticEncephalopathy)
  const ascites = Number(formData.value.ascites)
  const TBIL = Number(formData.value.TBIL.result)
  const PT = Number(formData.value.PT.result)
  const ALB = Number(formData.value.ALB.result)

  if (hepaticEncephalopathy && ascites && TBIL && PT && ALB) {
    const total = getTBILScore(TBIL!)! + getPTScore(PT! - 11.2)! + getALBScore(ALB!)! + hepaticEncephalopathy + ascites
    const rate = getScoreRate(total)

    return {
      total,
      rate,
    }
  }

  return null
})

function validate() {
  if (isLogicShow.value) {
    const hepaticEncephalopathy = Number(formData.value.hepaticEncephalopathy)
    const ascites = Number(formData.value.ascites)
    const TBIL = Number(formData.value.TBIL.result)
    const PT = Number(formData.value.PT.result)
    const ALB = Number(formData.value.ALB.result)

    if (!hepaticEncephalopathy || !TBIL || !ascites || !PT || !ALB) {
      window.$message.warning('请填写完整Child-Pugh评分数据')
      return false
    }

    return true
  }

  return true
}

defineExpose({
  validate,
  score,
  formData,
  _dataId,
})
</script>

<template>
  <div flex gap-10px>
    <div text="#666">
      Child-Pugh评分
    </div>
    <n-checkbox v-model:checked="isLogicShow">
      <span ml-10px text="#333">填写并计算</span>
    </n-checkbox>
  </div>
  <div v-if="isLogicShow" w-full bg="#A5B8D1/10" p-15px>
    <div flex="~ wrap" justify-between gap-y-14px>
      <div flex items-center justify-end>
        <div w-135px flex items-center justify-end>
          <SvgIcon
            local-icon="slmc-icon-app_required1"
            size="14"
            class="text-#F36969"
          />
          <div mr-10px>
            肝性脑病 (期)
          </div>
        </div>
        <n-radio-group v-model:value="formData.hepaticEncephalopathy">
          <div flex gap-40px>
            <n-radio v-for="(item, index) in hepaticEncephalopathyList" :key="index" :value="item.value">
              <span text="#333">{{ item.label }}</span>
            </n-radio>
          </div>
        </n-radio-group>
      </div>
      <div flex items-center>
        <SvgIcon
          local-icon="slmc-icon-app_required1"
          size="14"
          class="text-#F36969"
        />
        <div mr-10px>
          腹水
        </div>
        <n-radio-group v-model:value="formData.ascites" class="!w-341px">
          <div flex gap-40px>
            <n-radio v-for="(item, index) in ascitesList" :key="index" :value="item.value">
              <span text="#333">{{ item.label }}</span>
            </n-radio>
          </div>
        </n-radio-group>
      </div>
      <div flex items-center>
        <SvgIcon
          local-icon="slmc-icon-app_required1"
          size="14"
          class="text-#F36969"
        />
        <div mr-10px>
          总胆红素 (umol/L)
        </div>
        <n-select
          v-model:value="formData.TBIL.value"
          :options="enums['T-BIL']"
          class="!w-338px"
          value-field="id"
          @update:value="(_v:any, option: any) => {
            formData.TBIL.result = option.result
          }"
        />
      </div>
      <div flex items-center>
        <div w-100px flex items-center>
          <SvgIcon
            local-icon="slmc-icon-app_required1"
            size="14"
            flex-shrink-0
            class="text-#F36969"
          />
          <div mr-10px text="12px right">
            凝血酶原时间延长 (秒)
          </div>
        </div>

        <n-select
          v-model:value="formData.PT.value"
          :options="enums.PT"
          class="!w-338px"
          value-field="id"
          @update:value="(_v:any, option: any) => {
            formData.PT.result = option.result
          }"
        />
      </div>
      <div flex items-center justify-end>
        <div w-135px flex items-center justify-end>
          <SvgIcon
            local-icon="slmc-icon-app_required1"
            size="14"
            class="text-#F36969"
          />
          <div mr-10px>
            白蛋白 (g/L)
          </div>
        </div>
        <n-select
          v-model:value="formData.ALB.value"
          :options="enums.ALB"
          class="!w-338px"
          value-field="id"
          @update:value="(_v:any, option: any) => {
            formData.ALB.result = option.result
          }"
        />
      </div>
    </div>
    <div my-14px h-1px w-full style="border-top: 1px dashed #ccc;" />
    <div>
      根据CHild-pugh评分计算规则，该患者评分为：<span text="#FF9B54">
        {{ score ? `${score.rate}级(${score.total}分)` : '-' }}
      </span>
    </div>
  </div>
</template>
