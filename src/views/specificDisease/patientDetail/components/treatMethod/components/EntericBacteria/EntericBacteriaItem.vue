<script lang='ts' setup>
import type { Tag } from '../type'

const emit = defineEmits(['emitTag'])

const currentTagInfo = defineModel<{
  label: string
  value: string | number
}>('currentTagInfo')

const projectList: Tag[] = [
  {
    label: '检验检查',
    value: 'Inspection',
    children: [
      { label: '肠道微生态检测', value: 0 },
      { label: '术前四项', value: 1 },
      { label: '甲胎蛋白', value: 2 },
      { label: '血常规', value: 3 },
      { label: '尿常规', value: 4 },
      { label: '粪便常规', value: 5 },
      { label: '肾功能', value: 6 },
      { label: '肝功能', value: 7 },
      { label: '凝血功能+D-二聚体', value: 8 },
      { label: '电解质', value: 9 },
      { label: 'CRP', value: 10 },
    ],
  },
  { label: '肠菌移植治疗', value: 'BacteriaTreat' },
  { label: '专科评分', value: 'SpecialtyScore' },
]

function onChangeItem(item: Tag) {
  if (item.value === currentTagInfo.value?.value)
    return false

  if (item.value !== 'Inspection') {
    currentTagInfo.value!.label = item.label
    currentTagInfo.value!.value = item.value
  }
}
</script>

<template>
  <div class="entericBacteriaItem">
    <div v-for="item in projectList" :key="item.value" :class="{ activeTag: currentTagInfo?.value === item.value }" @click="onChangeItem(item)">
      <div v-if="item.children">
        <div flex items-center class="ml-18px w-full">
          <SvgIcon class="bottom-6px mr-8px" local-icon="slmc-icon-jiantou" size="8" />
          <span>{{ item.label }}</span>
        </div>
        <div v-for="child in item.children" :key="child.value" :class="{ activeTag: currentTagInfo?.value === child.value }" class="tag flex items-center pl-48px" @click.stop="onChangeItem(child)">
          <SvgIcon class="mr-10px" text="#00a59b" local-icon="slmc-icon-wenjian1" size="14" />
          <span>{{ child.label }}</span>
        </div>
      </div>
      <div v-else class="tag pl-30px">
        {{ item.label }}
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.entericBacteriaItem {
    box-sizing: border-box;
    margin: 0px;
    list-style: none;
    width: 230px;

    height: 100%;
    border-right: 1px solid #DCDCDC ;
    font-size: 12px;
    padding-left: 0;
    height: 100%;

    overflow-x: hidden;
    overflow-y: auto;
    .tag {
        width: 100%;
        box-sizing: border-box;
        height: 32px;
        line-height: 32px;

        list-style: none;
        cursor: pointer;
        &:hover {
            background-color: #fffbe0;
        }
    }
    .activeTag {
        background: #fffbe0;
        color:#06AEA6 ;
        cursor: not-allowed;
    }
}
</style>
