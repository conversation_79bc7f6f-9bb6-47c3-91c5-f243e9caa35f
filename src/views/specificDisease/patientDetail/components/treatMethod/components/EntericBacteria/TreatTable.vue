<script setup lang="ts">
import { cloneDeep } from 'lodash-es'
import { addBacteriaAPI, deleteBacteriaAPI, getBacteriaAPI, updateBacteriaAPI } from '@/api/specificDisease'

const route = useRoute()
const isModalShow = ref(false)
const isLoading = inject<Ref<boolean>>('isLoading')!
const pathWayoption = ['空肠营养管', '下消化道', '口服胶囊', '其他'].map((item) => {
  return {
    label: item,
    value: item,
  }
})

const otherPathWay = ref<string>('')

const formData = ref({
  id: null,
  date: null,
  pathway: null as string | null,
  totalNum: null,
  donorDate: null,
  operation: null,
  donor: null,
  patientId: route.query.patientId,
})

const canSubmit = computed(() => {
  console.log(formData.value.date)
  const { date, pathway, totalNum, donor, donorDate, operation } = formData.value
  if (date || pathway || totalNum || donor || donorDate || operation)
    return true
  else
    return false
})

const page = ref({
  page: 1,
  size: 10,
  total: 0,
})

async function addOrUpdateRecord() {
  try {
    const _formData = cloneDeep(formData.value)
    if (_formData.pathway === '其他')
      _formData.pathway = `其他-${otherPathWay.value}`

    if (_formData.id) {
      const { data } = await updateBacteriaAPI(_formData)
      if (data) {
        isModalShow.value = false
        window.$message.success('更新成功')
        getTableData()
      }
    }
    else {
      const { data } = await addBacteriaAPI(_formData)
      if (data) {
        isModalShow.value = false
        window.$message.success('新增成功')

        getTableData()
      }
    }
  }
  catch (error) {

  }
}

const tableData = ref<any[]>([])

async function getTableData() {
  try {
    isLoading.value = true
    const { data } = await getBacteriaAPI({
      patientId: route.query.patientId,
      size: page.value.size,
      start: page.value.page,
    })

    tableData.value = data.records
    page.value.total = data.total
  }
  catch (error) {
    console.log(error)
  }
  finally {
    isLoading.value = false
  }
}

function handleAddClick() {
  isModalShow.value = true
  formData.value.id = null
  formData.value.date = null
  formData.value.pathway = null
  formData.value.totalNum = null
  formData.value.donorDate = null
  formData.value.operation = null
  formData.value.donor = null
  otherPathWay.value = ''
}

function handleEditClick(row: any) {
  isModalShow.value = true
  formData.value.id = row.id
  formData.value.date = row.date
  formData.value.totalNum = row.totalNum
  formData.value.donorDate = row.donorDate
  formData.value.operation = row.operation
  formData.value.donor = row.donor

  if (row.pathway && row.pathway.startsWith('其他-')) {
    otherPathWay.value = row.pathway.slice(3)
    formData.value.pathway = '其他'
  }
  else if (!['空肠营养管', '下消化道', '口服胶囊'].includes(row.pathway)) {
    otherPathWay.value = ''
    formData.value.pathway = null
  }
  else {
    formData.value.pathway = row.pathway
  }
}

function handleDeleteClick(row: any) {
  window.$dialog.warning({
    title: '确定删除该数据吗？',
    negativeText: '取消',
    positiveText: '确定',
    positiveButtonProps: {
      type: 'primary',
      ghost: true,
    },
    negativeButtonProps: {
      type: 'primary',
      ghost: true,
    },
    onPositiveClick: async () => {
      try {
        const { data } = await deleteBacteriaAPI(row.id)
        if (data) {
          window.$message.success('删除成功')
          getTableData()
        }
      }
      catch (error) {
        console.log(error)
      }
    },

  })
}

onMounted(() => {
  getTableData()
})
</script>

<template>
  <div w-full>
    <n-button type="primary" mb-20px @click="handleAddClick">
      新增
    </n-button>

    <el-table :data="tableData" stripe w-full>
      <el-table-column type="index" label="序号" width="80" />
      <el-table-column label="治疗日期" min-width="120" :formatter="({ date }) => date || '-'" />
      <el-table-column label="移植途径" min-width="80" :formatter="({ pathway }) => (pathway && pathway.startsWith('其他-')) ? (pathway.slice(3) || '-') : (pathway || '-')" />
      <el-table-column label="菌液总量 (ml)" min-width="120" :formatter="({ totalNum }) => totalNum || '-'" />
      <el-table-column label="供体信息" min-width="80" :formatter="({ donor }) => donor || '-'" />
      <el-table-column label="供体捐献日期" min-width="120" :formatter="({ donorDate }) => donorDate || '-'" />
      <el-table-column label="操作人员" min-width="80" :formatter="({ operation }) => operation || '-'" />
      <el-table-column label="操作" width="100" fixed="right">
        <template #default="{ row }">
          <div flex items-center gap-5px>
            <span uno-link @click="handleEditClick(row)">编辑</span>
            <div h-14px w-1px bg="#3B8FD9" />
            <span uno-link @click="handleDeleteClick(row)">删除</span>
          </div>
        </template>
      </el-table-column>
      <template #empty>
        <div h-300px flex items-center justify-center>
          <DataEmpty />
        </div>
      </template>
    </el-table>

    <div v-if="page.total > 0" mt-20px w-full flex justify-end>
      <n-pagination
        v-model:page="page.page"
        v-model:page-size="page.size"
        :item-count="page.total"
        :page-sizes="[5, 10, 20, 30]"
        show-size-picker
        show-quick-jumper
        @update:page="getTableData"
        @update:page-size="() => {
          page.page = 1
          getTableData()
        }"
      />
    </div>

    <n-modal
      v-model:show="isModalShow"
      preset="card"
      :title="`${formData.id ? '编辑' : '新增'}治疗信息`"
      w-560px
      head-style="divide"
    >
      <div flex="~ col" gap-14px class="pb-12px pl-10px pr-20px pt-20px">
        <div flex items-center gap-10px>
          <div text="#666" w-88px text-right>
            日期
          </div>
          <n-date-picker
            v-model:formatted-value="formData.date"
            type="date"
            value-format="yyyy-MM-dd"
            class="!w-350px"
          />
        </div>
        <div flex items-center gap-10px>
          <div text="#666" w-88px text-right>
            移植途径
          </div>
          <n-select v-model:value="formData.pathway" :options="pathWayoption" class="!w-350px" @update-value=" otherPathWay = ''" />
        </div>
        <div v-if="formData.pathway === '其他'" flex items-center gap-10px>
          <div text="#666" w-88px text-right />
          <n-input v-model:value="otherPathWay" placeholder="请输入移植途径" class="!w-350px" />
        </div>
        <div flex items-center gap-10px>
          <div text="#666" w-88px text-right>
            菌液总量 (ml)
          </div>
          <n-input-number v-model:value="formData.totalNum" :show-button="false" class="!w-350px" />
        </div>
        <div flex items-center gap-10px>
          <div text="#666" w-88px text-right>
            供体信息
          </div>
          <n-select
            v-model:value="formData.donor" :options="[
              {
                label: '亲属',
                value: '亲属',
              },
              {
                label: '非亲属',
                value: '非亲属',
              },
            ]" class="!w-350px"
          />
        </div>
        <div flex items-center gap-10px>
          <div text="#666" w-88px text-right>
            供体捐献日期
          </div>
          <n-date-picker
            v-model:formatted-value="formData.donorDate"
            type="date"
            value-format="yyyy-MM-dd"
            class="!w-350px"
          />
        </div>
        <div flex items-center gap-10px>
          <div text="#666" w-88px text-right>
            操作人员
          </div>
          <n-input v-model:value="formData.operation" class="!w-350px" />
        </div>

        <div ml-100px class="mt-10px">
          <n-button type="primary" :disabled="!canSubmit" mr-16px w-100px @click="addOrUpdateRecord">
            保 存
          </n-button>
          <n-button w-100px @click="isModalShow = false">
            取 消
          </n-button>
        </div>
      </div>
    </n-modal>
  </div>
</template>
