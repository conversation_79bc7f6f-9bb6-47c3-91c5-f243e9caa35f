<script lang='ts' setup>
import TreatTable from './TreatTable.vue'
import Rating from './Rating.vue'
import { EntericBacteriaItem } from './index'
import { getAssayDetailList, getAssayDetailResultList } from '@/api/specificDisease'

const route = useRoute()

const columns = ref<any[]>([])
const datas = ref<any[]>([])
const isLoading = ref(false)
provide('isLoading', isLoading)
const tableType = ref<'dynamic' | 'treat' | 'rating'>('dynamic')

const currentTagInfo = ref<{
  label: string
  value: string | number
}>({
  label: '肠道微生态检测',
  value: 0,
})

async function dynamicTableGen(tagName: string) {
  try {
    isLoading.value = true
    const { data: headerData } = await getAssayDetailList<any[]>({
      assayProjectName: `肠菌移植-${tagName}`,
      patientId: route.query.patientId,
    })

    const fixedColumn = [
      {
        title: '序号',
        key: 'no',
        width: '80px',
      },
      {
        title: '日期',
        key: 'date',
        width: '200px',
      },
    ]
    console.log('headerData==', headerData)

    const formatedHeaderData = headerData!.map((item: any) => {
      return {
        title: item.assayProjectDetailName?.replaceAll('\\n', ''),
        key: (item.hisAssayDetailCode || item.hisAssayDetailName)?.toLowerCase(),
        width: '300px',
      }
    })

    columns.value = fixedColumn.concat(formatedHeaderData)

    const { data: tableData } = await getAssayDetailResultList<Record<string, any>>({
      assayProjectName: `肠菌移植-${tagName}`,
      patientId: route.query.patientId,
    })

    if (tableData) {
      const formatedTableData: any[] = []

      let objectLoopIndex = 0

      for (const date in tableData) {
        formatedTableData[objectLoopIndex] = {
          no: objectLoopIndex + 1,
          date,
        }

        columns.value.forEach((column: any) => {
          if (!['date', 'no'].includes(column.key))
            formatedTableData[objectLoopIndex][column.key] = '-'
        })

        tableData[date].forEach((item: any) => {
          if (item.englishName)
            formatedTableData[objectLoopIndex][item.englishName.toLowerCase()] = item.result || '-'

          if (item.chineseName)
            formatedTableData[objectLoopIndex][item.chineseName.toLowerCase()] = item.result || '-'
        })

        objectLoopIndex++
      }
      console.log('formatedTableData===', formatedTableData)

      datas.value = formatedTableData
    }
  }
  catch (error) {
    console.log(error)
  }
  finally {
    isLoading.value = false
  }
}

watch(currentTagInfo, (val) => {
  if (typeof (val.value) === 'number') {
    dynamicTableGen(val.label)
    tableType.value = 'dynamic'
  }
  else if (val.value === 'BacteriaTreat') {
    tableType.value = 'treat'
  }
  else if (val.value === 'SpecialtyScore') {
    tableType.value = 'rating'
  }
}
,
{
  immediate: true,
  deep: true,
},
)
</script>

<template>
  <div class="entericBacteria">
    <EntericBacteriaItem :current-tag-info="currentTagInfo" />

    <div class="w-[calc(100vw-530px)]" flex-1 px-20px pb-14px>
      <div mb-14px flex items-center gap-10px>
        <div sub-title />
        <div>{{ currentTagInfo.label }}</div>
      </div>

      <n-spin type="uni" :show="isLoading" size="medium">
        <n-data-table
          v-if="tableType === 'dynamic'"
          :columns="columns"
          :data="datas"
          max-height="calc(100vh - 380px)"
          :min-height="datas?.length ? 0 : 300"
          :scroll-x="300 * columns?.length"
          striped
          :bordered="false"
        >
          <template #empty>
            <n-empty description="无数据" />
          </template>
        </n-data-table>

        <TreatTable v-else-if="tableType === 'treat'" />
        <Rating v-else-if="tableType === 'rating'" />
      </n-spin>
    </div>
  </div>
</template>

<style scoped lang="scss">
.entericBacteria {
    display: flex;
    justify-content: flex-start;
    width: 100%;
    height: 100%;
    padding-top: 14px;
}
</style>
