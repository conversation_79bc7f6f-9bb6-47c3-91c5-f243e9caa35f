<script lang='ts' setup>
import { onMounted } from 'vue'
import { ArtificialLiver, EntericBacteria } from './components'

import type { PatientInfoModal } from '@/api/patient'

import { getPatientInfoApi } from '@/api/patient'

const route = useRoute()

const PATIENT_ID = route.query?.patientId as string

enum TABS {
  ArtificialLiver = 'ARTIFICIAL_LIVER',
  EntericBacteria = 'ENTERIC_BACTERIA',

}
const defaultTagValue = ref(TABS.ArtificialLiver)
/** 患者信息 */
const patientInfo = ref<PatientInfoModal | null>(null)

/**
 * 获取患者信息
 * @param patientId 患者id
 */
async function getPatientInfo(patientId: string) {
  const res = await getPatientInfoApi<PatientInfoModal>(patientId)

  if (res?.data)
    patientInfo.value = { ...res.data }
}
provide('patientInfo', patientInfo)

onMounted(() => {
  getPatientInfo(PATIENT_ID)
})
</script>

<template>
  <div class="treatMethod">
    <n-tabs animated :width="110" :default-value="defaultTagValue">
      <n-tab-pane :name="TABS.ArtificialLiver" tab="人工肝治疗">
        <ArtificialLiver />
      </n-tab-pane>
      <n-tab-pane :name="TABS.EntericBacteria" tab="肠菌移植治疗">
        <EntericBacteria />
      </n-tab-pane>
    </n-tabs>
  </div>
</template>

<style scoped lang="scss">
.treatMethod {
    margin-top: 2px;

    height: calc(100vh - 233px);
    :deep(.n-tabs .n-tabs-nav){
        margin: 0 14px;
    }

}
</style>
