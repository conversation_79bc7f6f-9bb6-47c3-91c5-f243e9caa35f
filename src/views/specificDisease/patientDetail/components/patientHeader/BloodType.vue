<script lang='ts' setup>
import { toRaw } from 'vue'
import { SvgIcon } from '@/components/Icon'
import { UpdateInfoApi } from '@/api/specificDisease'
import type { PatientInfoModal } from '@/api/specificDisease'

interface Props {
  blood: string | null
  patientInfo: PatientInfoModal

}
const props = withDefaults(defineProps<Props>(), {
  blood: '',

})
const emit = defineEmits(['refresh'])

const bloodTypeOption = [
  { label: 'A型', value: 'A' },
  { label: 'B型', value: 'B' },
  { label: 'O型', value: 'O' },
  { label: 'AB型', value: 'AB' },
]

const isShowPopover = ref(false)
const bloodType = ref<string>('')
const manageDoctorId = ref<string>('')

function changeBlood() {
  isShowPopover.value = true
  bloodType.value = props.blood
}
function onClickOutside() {
  resetDefault()
}

async function onConfirm() {
  const params = {
    ...props.patientInfo,
    bloodType: toRaw(bloodType.value),

  }
  try {
    const res = await UpdateInfoApi(params)
    if (res?.data) {
      window.$message.success('保存成功')
      emit('refresh')
      resetDefault()
    }
  }
  catch (error) {
    console.error(error)
  }
}
function onCancel() {
  resetDefault()
}
function resetDefault() {
  isShowPopover.value = false
  bloodType.value = ''
}

function init() {

}

onMounted(() => {
  init()
})
</script>

<template>
  <div class="flex items-center justify-start">
    <span class="text-#666">血型：</span>
    <span class="flex items-center justify-start">
      <span>{{ blood || '-' }} <span v-if="blood">型</span> </span>
      <n-popover trigger="click" placement="bottom" style="width: 300px" :show="isShowPopover" @clickoutside="onClickOutside">
        <template #trigger>
          <SvgIcon class="ml-10px cursor-pointer" local-icon="slmc-icon-edit1" size="16" @click="changeBlood" />
        </template>
        <div class="mt-10px flex flex-center">
          <span class="mr-10px text-14px text-#666">血型</span>
          <n-select
            v-model:value="bloodType" style="width:194px" :options="bloodTypeOption"
            placeholder="请选择"
          />
        </div>
        <div style=" margin: 18px 0 8px 0">
          <n-space justify="center">
            <n-button type="primary" @click="onConfirm">
              确&nbsp;&nbsp;定
            </n-button>
            <n-button @click="onCancel">取&nbsp;&nbsp;消</n-button>
          </n-space>
        </div>
      </n-popover>
    </span>
  </div>
</template>

<style scoped lang="scss">

</style>
