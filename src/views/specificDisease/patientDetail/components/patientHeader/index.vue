<script lang='ts' setup>
import DoctorManage from './DoctorManage.vue'
import Tag from './Tag.vue'
import BloodType from './BloodType.vue'
import type { PatientInfoModal } from '@/api/specificDisease'
import { getPatientInfoApi } from '@/api/specificDisease'
import { SvgIcon } from '@/components/Icon'
import { RenderPhone } from '@/components/business/UiRender'
import { formatNullValueToShortBar } from '@/utils'
import { getTagListAPI } from '@/api/tag'
import { isArray } from '@/utils/common/is'
import { sexIcon } from '@/utils/common'

const route = useRoute()
const PATIENT_ID = route.query?.patientId as string

const patientInfo = ref<PatientInfoModal>({})
const tagList = ref([])
const tagNames = ref<string[]>([])
const tagIds = ref<string[]>([])

async function getPatientInfo(patientId: string) {
  try {
    const res = await getPatientInfoApi(patientId)

    if (res?.data) {
      patientInfo.value = { ...res.data }
      getTagList(res.data?.tagIdList)
    }
  }
  catch (error) {
    console.log(error)
  }
}
async function getTagList(tagIdList: string | null) {
  try {
    tagNames.value = []
    const tagNameSMap: Record<string, number> = {}
    const res = await getTagListAPI('')
    const tag = tagIdList && tagIdList.split(',').filter(item => item !== '')

    tagIds.value = isArray(tag) ? tag : []

    tagIds.value.forEach((item, index) => {
      tagNameSMap[item] = index
    })

    if (res?.data) {
      tagList.value = res.data
      const nameSort: { name: string; sort: number }[] = []

      res.data.forEach((item) => {
        if (isArray(tag) && tag.includes(item.tagId))

          nameSort.push({ name: item.tagName, sort: tagNameSMap[item.tagId] })
      })
      tagNames.value = nameSort.sort((a, b) => a.sort - b.sort).map(tag => tag.name)
    }
  }
  catch (error) {
    console.log(error)
  }
}
const iconName = computed(() => {
  if (patientInfo.value?.sexName) {
    const localIcon = sexIcon(patientInfo.value.sexName)
    return localIcon
  }
  else {
    return null
  }
})

function onRefresh() {
  init()
}
function init() {
  getPatientInfo(PATIENT_ID)
  getTagList('')
}

onMounted(() => {
  init()
})
</script>

<template>
  <header class="patientHeader">
    <div class="patientHeader-left">
      <div class="mb-12px flex flex-center items-center gap-10px">
        <span class="max-w-100px truncate text-16px font-semibold">{{ patientInfo?.patientName }}</span>
        <SvgIcon v-if="iconName" :local-icon="iconName" size="16" />
        <span class="ageWrap">{{ patientInfo?.age || '-' }}{{ patientInfo?.ageUnit || '岁' }}</span>
      </div>
      <div class="flex flex-center">
        <RenderPhone :phone="patientInfo?.phone" />
      </div>
    </div>
    <div class="patientHeader-right">
      <div class="mb-10px flex justify-start gap-30px">
        <div>
          <BloodType :blood="patientInfo?.bloodType" :patient-info="patientInfo" @refresh="onRefresh" />
          <!-- <span class="text-#666">血型：</span><span>{{ formatNullValueToShortBar(patientInfo?.bloodType) }}型</span> -->
        </div>

        <div>
          <DoctorManage
            :doctor-name="patientInfo?.manageDoctorName"
            :doctor-id="patientInfo?.manageDoctorId"
            :patient-info="patientInfo"
            @refresh="onRefresh"
          />
        </div>
        <div>
          <span class="text-#666">患者健康值：</span>
          <span>{{ formatNullValueToShortBar(patientInfo?.saplingScore) }}</span>
          <span v-if="patientInfo?.saplingScore">分</span>
        </div>
      </div>
      <Tag
        :tag-names="tagNames"
        :tag-ids="tagIds"
        :tag-list="tagList"
        @refresh="onRefresh"
      />
    </div>
  </header>
</template>

<style scoped lang="scss">
.patientHeader {
    display:flex;
    justify-content: flex-start;
    margin-bottom: 14px;
    width: 100%;
    height: 66px;
    background: #ffffff;
    box-shadow: 0px 1px 4px 0px rgba(202,202,202,0.50);
    &-left {
      display: flex;
      flex-direction: column;
      justify-content: center;
      min-width: 200px;
      max-width: 300px;
      border-right: 1px solid #E8E8E8;
    }
    &-right {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: center;
        margin-left: 20px;
        flex:1;
    }
    .ageWrap{
        min-width: 43px;
        max-width:60px;
        padding: 0px 8px;
        font-size: 12px;
        text-align:center;
        height: 20px;
        line-height:20px;
        background: #ffffff;
        border: 1px solid #dcdcdc;
        border-radius: 2px;
        text-overflow: ellipsis;
        white-space: nowrap;
        overflow: hidden;
    }
}
</style>
