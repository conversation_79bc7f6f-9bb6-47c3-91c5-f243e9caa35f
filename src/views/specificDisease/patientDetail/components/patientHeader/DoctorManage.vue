<script lang='ts' setup>
import { useDebounceFn } from '@vueuse/core'
import { toRaw } from 'vue'
import { SvgIcon } from '@/components/Icon'
import { UpdateInfoApi, getManageDoctorListApi } from '@/api/specificDisease'
import type { PatientInfoModal } from '@/api/specificDisease'

interface Props {
  doctorName: string
  doctorId: string
  patientInfo: PatientInfoModal

}
const props = withDefaults(defineProps<Props>(), {
  doctorName: '',
  doctorId: '',
})
const emit = defineEmits(['refresh'])

const isShowPopover = ref(false)
const manageDoctorName = ref<string>('')
const manageDoctorId = ref<string>('')

const doctorOption = ref([])
const loading = ref<boolean>(false)

function changeDoctor() {
  isShowPopover.value = true
  manageDoctorId.value = props.doctorId
}
function onClickOutside() {
  resetDefault()
}

async function onConfirm() {
  const params = {
    ...props.patientInfo,
    manageDoctorId: toRaw(manageDoctorId.value),
    manageDoctorName: toRaw(manageDoctorName.value),
  }

  if (!params?.manageDoctorName && !params?.manageDoctorId) {
    isShowPopover.value = false
    return
  }

  try {
    const res = await UpdateInfoApi(params)
    if (res?.data) {
      window.$message.success('保存成功')
      emit('refresh')
      resetDefault()
    }
  }
  catch (error) {
    console.error(error)
  }
}
function onCancel() {
  resetDefault()
}
function resetDefault() {
  isShowPopover.value = false
  manageDoctorId.value = ''
  manageDoctorName.value = ''
}
/**
 * 获取医生姓名
 * @param query 搜索关键字
 */
const getDoctorName = useDebounceFn(async (manageDoctorName = '') => {
  loading.value = true
  const params = {
    searchName: manageDoctorName,
    size: 10000,
  }
  const res = await getManageDoctorListApi<any>(params)

  if (res?.data) {
    doctorOption.value = res.data.records
    loading.value = false
  }
}, 300)
/** 搜索医生姓名 */
function handleSearchDoctorName(query: string) {
  getDoctorName(query)
}
/**
 * 下拉列表打开/关闭回调
 * @param show 打开/关闭
 */
function handleUpdateShowDoctorName(show: boolean) {
  if (show)
    getDoctorName('')
}

function handleUpdateDoctorName(value: string, options: any) {
  if (value)
    manageDoctorName.value = options.userName

  else
    manageDoctorName.value = null
}
function init() {
  handleSearchDoctorName('')
}

onMounted(() => {
  init()
})
</script>

<template>
  <div class="flex items-center justify-start">
    <span class="text-#666">管理医生：</span>
    <span class="flex items-center justify-start">
      <span>{{ doctorName || '-' }}</span>
      <n-popover trigger="click" placement="bottom" style="width: 300px" :show="isShowPopover" @clickoutside="onClickOutside">
        <template #trigger>
          <SvgIcon id="doctor-manage" class="ml-10px cursor-pointer" local-icon="slmc-icon-edit1" size="16" @click="changeDoctor" />
        </template>
        <div class="mt-10px flex flex-center">
          <span class="mr-10px text-14px text-#666">管理医生</span>
          <n-select
            v-model:value="manageDoctorId" style="width:194px" :options="doctorOption"
            filterable placeholder="请选择"
            :loading="loading"
            label-field="userName"
            value-field="id"
            clearable
            remote
            @search="handleSearchDoctorName"
            @update:show="handleUpdateShowDoctorName"
            @update:value="handleUpdateDoctorName"
          />
        </div>
        <div style=" margin: 18px 0 8px 0">
          <n-space justify="center">
            <n-button type="primary" @click="onConfirm">
              确&nbsp;&nbsp;定
            </n-button>
            <n-button @click="onCancel">取&nbsp;&nbsp;消</n-button>
          </n-space>
        </div>
      </n-popover>
    </span>
  </div>
</template>

<style scoped lang="scss">

</style>
