<script lang='ts' setup>
import BScroll from '@better-scroll/core'
import { nextTick, watch } from 'vue'
import { updatePatientTagAPI } from '@/api/tag'
import { useAuthStore } from '@/store'

const props = withDefaults(defineProps<Props>(), {
  tagIds: () => [],
  tagNames: () => [],
  tagList: () => [],

})

const emit = defineEmits(['refresh'])

const { userInfo } = useAuthStore()

const USER_ID = userInfo?.id
const USER_NAME = userInfo?.userName
interface Props {
  tagIds: string[]
  tagNames: string[]
  tagList: string[]

}

const route = useRoute()
const PATIENT_ID = route.query?.patientId as string
/** 标签选中的id */
const tagsValue = ref(props.tagIds)

const tagsRef = ref(null)

watch(() => props.tagList, () => {
  nextTick(() => {
    const bs = new BScroll(tagsRef.value, {
    // ...... 详见配置项
      scrollX: true,
    })
  })
})

const isShowPopover = ref(false)

function changeTag() {
  isShowPopover.value = true
  tagsValue.value = props.tagIds
}
function onClickOutside() {
  resetDefault()
}
async function onConfirm() {
  const params = {
    patientId: PATIENT_ID,
    tagIds: tagsValue.value,
    userId: USER_ID,
    userName: USER_NAME,
  }
  try {
    const res = await updatePatientTagAPI(params)
    if (res?.data) {
      window.$message.success('保存成功')
      emit('refresh')
      resetDefault()
    }
  }
  catch (error) {
    console.error(error)
  }
}
function onCancel() {
  resetDefault()
}

function resetDefault() {
  isShowPopover.value = false
  tagsValue.value = []
}
</script>

<template>
  <div class="tagBlock">
    <span class="w-42px basis-42px text-#666">标签：</span>
    <div ref="tagsRef" class="tagsWrap">
      <span v-if="tagNames.length > 0" class="content tags">
        <span v-for="(item, index) in tagNames" :key="index" class="tagName">
          <n-ellipsis style="max-width: 90px">
            {{ item }}
          </n-ellipsis>

        </span>
      </span>
      <span v-else>-</span>
    </div>
    <n-popover
      trigger="click" placement="bottom" style="width: 300px"
      :show="isShowPopover"
      @clickoutside="onClickOutside"
    >
      <template #trigger>
        <SvgIcon class="ml-10px cursor-pointer" local-icon="slmc-icon-edit1" size="16" @click="changeTag" />
      </template>
      <div class="mt-10px flex flex-center">
        <span class="mr-10px text-14px text-#666">标签</span>
        <n-select
          v-model:value="tagsValue"
          style="width:222px"
          :options="tagList"
          label-field="tagName"
          value-field="tagId"
          filterable
          clearable
          remote
          multiple
          :max-tag-count="2"
        />
      </div>
      <div style=" margin: 18px 0 8px 0">
        <n-space justify="center">
          <n-button type="primary" @click="onConfirm">
            确&nbsp;&nbsp;定
          </n-button>
          <n-button @click="onCancel">
            取&nbsp;&nbsp;消
          </n-button>
        </n-space>
      </div>
    </n-popover>
  </div>
</template>

<style scoped lang="scss">
.tagBlock {
    display: flex;
    justify-content: flex-start;
    align-items: center;

    .tagsWrap {
        white-space: nowrap;
        max-width:calc(80vw - 400px);

        cursor: pointer;
        overflow: hidden;
    }
    .tags {

        display: inline-block;

    }
    .tagName {
        display: inline-block;
        margin-right: 10px;
        box-sizing: border-box;

        text-align: center;
        height: 20px;
        box-sizing: border-box;
        padding: 4px 10px ;
        color:#fff;
        font-size: 12px;

        background: #ff9b54;
        border-radius: 10px;
        flex-shrink: 0;

    }
}
</style>
