<script lang='ts' setup>
import BaseInfo from './BaseInfo.vue'
import EditInfo from './EditInfo.vue'

const props = defineProps({
  defaultType: {
    type: String,
    default: 'BASE',
  },
  editIndex: {
    type: Number,
    default: 0,
  },
})

const ediIndex = ref(props.editIndex)

enum COMPONENT_NAME {
  BASE = 'BASE',
  EDIT = 'EDIT',
}

type TYPES = keyof typeof COMPONENT_NAME

type BaseComp = typeof BaseInfo
type EditComp = typeof EditInfo

/** 组件列表 */
const componentList: Record<TYPES, BaseComp | EditComp> = {
  BASE: BaseInfo,
  EDIT: EditInfo,
}

/** 目前激活的组件 */
const activeComponent = ref<TYPES>(props.defaultType)

defineExpose({
  activeComponent,
})
/**
 * 点击切换组件
 * @param compName 组件名称
 */
function onToggleComp(compName: any) {
  activeComponent.value = compName.name
  ediIndex.value = compName.index
}
</script>

<template>
  <div class="patientInfo">
    <Transition name="fade" mode="out-in">
      <component :is="componentList[activeComponent]" :edit-index="ediIndex" @toggle-comp="onToggleComp" />
    </Transition>
  </div>
</template>

<style scoped lang="scss">
.patientInfo {
    height:100%;
    width:100%;
    overflow-y: auto;
}
</style>
