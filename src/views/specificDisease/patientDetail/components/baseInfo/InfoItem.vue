<script lang='ts' setup>
import { useSlots } from 'vue'
import { formatNullValueToShortBar } from '@/utils'

const props = withDefaults(defineProps<Props>(), {
  label: '',
  value: '',
  labelWidth: 'auto',
  alignItems: 'center',
  margin: false,
  isRequire: false,
})
// 判断<slot/>是否有传值
const slotDefault = !!useSlots().default
interface Props {
  label: string
  value?: string | number | null
  labelWidth?: string
  alignItems?: 'center' | 'start'
  margin?: boolean
  isRequire?: boolean
}

const labelWidth = computed(() => {
  return `${props.labelWidth}px`
})
const alignItems = computed(() => {
  return `${props.alignItems}`
})
</script>

<template>
  <div class="itemBlock h-full flex">
    <div class="labelName inline-block text-right text-#666" flex-shrink-0>
      <span v-if="isRequire" color="red" mr-4px text-16px>*</span>
      <span :class="[margin ? 'mr-10px' : '']">{{ label }}</span>
    </div>
    <slot v-if="slotDefault" />
    <div v-else class="flex-1">
      {{ formatNullValueToShortBar(value) }}
    </div>
  </div>
</template>

<style scoped lang="scss">
.itemBlock{
    align-items: v-bind(alignItems);
}
.labelName {
    flex-basis:v-bind(labelWidth);
}
</style>
