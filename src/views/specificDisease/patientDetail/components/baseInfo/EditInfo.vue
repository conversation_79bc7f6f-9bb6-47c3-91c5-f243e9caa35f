<!-- eslint-disable no-var -->
<script lang="ts" setup>
import { useRoute } from 'vue-router'
import { useMessage } from 'wowjoy-vui'
import dayjs from 'dayjs'
import _ from 'lodash'
import { isPhone } from '../../../../../utils/common/is'
import InfoItem from './InfoItem.vue'
import DiseaseInfo from './DiseaseInfo.vue'
import { CardType } from './constant'

// import UseMedicalModal from './UseMedicalModal.vue'
import type { PatientInfoModal } from '@/api/patient'
import {
  UpdateInfoApi,
  getPatientDiseaseHistory,
  getPatientInfoApi,
  updateThePatientDiseaseHistory,
} from '@/api/patient'
import { useAuthStore } from '~/src/store/modules/auth'

const props = defineProps({
  editIndex: {
    type: Number,
    default: 0,
  },
})

const emit = defineEmits(['toggleComp'])
const followRightData = ref()
const saveLoading = ref(false)
const { userInfo } = useAuthStore()

const editType = ref(props.editIndex) /// / sb分开编辑

const message = useMessage()

const route = useRoute()

const PATIENT_ID = route.query?.patientId as string
const TYPE = route.query?.type as string

const maritalStatusOptions = ref([
  { label: '未婚', value: '未婚' },
  { label: '已婚', value: '已婚' },
])

const patientIdRef = ref(PATIENT_ID)

const xlOptions = ref([{
  label: '未上学',
  value: '未上学',
}, {
  label: '小学',
  value: '小学',
},
{
  label: '初中',
  value: '初中',
}, {
  label: '高中',
  value: '高中',
},
{
  label: '专科',
  value: '专科',
},
{
  label: '本科',
  value: '本科',
}, {
  label: '研究生',
  value: '研究生',
},
{
  label: '博士',
  value: '博士',
},
{
  label: '其它',
  value: '其它',
}])

const zylbOptions = ref([
  {
    label: '幼托儿童',
    value: '幼托儿童',
  },
  {
    label: '散居儿童',
    value: '散居儿童',
  },
  {
    label: '学生(大中小学)',
    value: '学生(大中小学)',
  },
  {
    label: '教师',
    value: '教师',
  },
  {
    label: '保育员及保姆',
    value: '保育员及保姆',
  },
  {
    label: '餐饮食品业',
    value: '餐饮食品业',
  },
  {
    label: '商业服务',
    value: '商业服务',
  },
  {
    label: '医务人员',
    value: '医务人员',
  },
  {
    label: '企业职员',
    value: '企业职员',
  },
  {
    label: '事业编制',
    value: '事业编制',
  },
  {
    label: '民工',
    value: '民工',
  },
  {
    label: '农民',
    value: '农民',
  },
  {
    label: '牧民',
    value: '牧民',
  },
  {
    label: '渔(船)民',
    value: '渔(船)民',
  },
  {
    label: '干部职员',
    value: '干部职员',
  },
  {
    label: '离退人员',
    value: '离退人员',
  },
  {
    label: '家务及待业',
    value: '家务及待业',
  },
  {
    label: '个体户',
    value: '个体户',
  },
  {
    label: '不详',
    value: '不详',
  },
  {
    label: '其他',
    value: '其他',
  },
])

const yjOptions = [
  {
    label: '白酒',
    value: '白酒',
  },
  {
    label: '啤酒',
    value: '啤酒',
  },
  {
    label: '红酒',
    value: '红酒',
  },
  {
    label: '黄酒',
    value: '黄酒',
  },
  {
    label: '洋烈酒',
    value: '洋烈酒',
  },
]

const patientInfo = ref<PatientInfoModal>({
  age: '',
  birthday: '',
  bloodPressure: '',
  bloodType: '',
  bmi: '',
  companyName: '',
  companyTel: '',
  companyType: '',
  contactsAddr: '',
  contactsName: '',
  contactsRelation: '',
  contactsTel: '',
  countryName: '',
  createTime: '',
  culturalName: '',
  familyMembers: '',
  heartRate: '',
  height: '',
  homeAddr: '',
  id: '',
  income: '',
  lastVisit: '',
  liveAddr: '',
  liverComplaintType: 0,
  marriageName: '',
  medicalInsuranceNo: '',
  medicalInsuranceType: '',
  nationCode: '',
  nationName: '',
  patientIdCardNum: '',
  patientIdCardType: '',
  patientName: '',
  patientRecordNo: '',
  patientSource: '',
  phone: '',
  phySource: '',
  phyUpdateTime: '',
  primaryIndex: '',
  professionName: '',
  pulse: '',
  respiratoryRate: '',
  sexCode: '',
  sexName: '',
  slmcNo: '',
  status: 0,
  tag: '',
  v1Visit: '',
  weight: '',
  healthCardNum: '',
})

let diseaseSelectInfo
const diseaseTextMap = reactive({
  肝病病史: '',
  肝病家族史: '',
  常见慢性病及肿瘤史: '',
  个人史: '',
  体格检查: '',
  婚育史: '',
  过敏史: '',
  重大传染病及精神疾病史: '',
  selectedItems: [],
  status: null,
})

const diseaseHis = ref()

// 标签宽度
const LABEL_WIDTH = {
  demography: '115',
  physique: '115',
}

/**
 * 获取患者信息
 * @param patientId 患者id
 */
async function getPatientInfo(patientId: string) {
  const res = await getPatientInfoApi<PatientInfoModal>(patientId)

  if (res?.data)
    patientInfo.value = { ...res.data }
}

const verifyStatus = ref({
  phone: undefined,
})

function onBlurFrom(key: string) {
  if (key === 'contactsTel') {
    if (!isPhone(patientInfo.value.contactsTel))
      verifyStatus.value.phone = 'error'
    else verifyStatus.value.phone = undefined
  }
  if (key === 'phone') {
    if (!isPhone(patientInfo.value.phone))
      verifyStatus.value.phone = 'error'
    else verifyStatus.value.phone = undefined
  }
}

const confirmThrottle = _.debounce(() => {
  saveDiseaseHistoryInfo()
}, 300)

function handleTheSBText() {
  try {
    console.log(diseaseSelectInfo)
    /// 基本信息
    // let res2
    // if (TYPE === '慢乙肝') {
    handleTheSelectedDiseaseText(unref(diseaseSelectInfo))
    // console.log(diseaseSelectInfo)
    // console.log(diseaseTextMap)
    let status = 1
    for (
      let index = 0;
      index < diseaseTextMap.selectedItems.length;
      index++
    ) {
      const element = diseaseTextMap.selectedItems[index]
      if (!element.check) {
        status = 1
        break
      }
      status = 2
    }
    diseaseTextMap.status = status
  }
  catch (error) {
    console.error(error)
  }
}

async function getPatientDiseaseInfo(patientId) {
  const res = await getPatientDiseaseHistory(patientId)
  if (res?.data) {
    /// sb解析
    handleTheSelectedDiseaseText([
      JSON.parse(res.data?.liverComplaintHistory),
      JSON.parse(res.data?.familyHistory),
      JSON.parse(res.data?.personalHistory),
      JSON.parse(res.data?.commonChronicDiseasesHistory),
      JSON.parse(res.data?.infectiousDiseaseHistory),
      JSON.parse(res.data?.physicalExam),
      JSON.parse(res.data?.maritalHistory),
      JSON.parse(res.data?.allergyHistory),
    ])
    // console.log(diseaseTextMap)
    diseaseHis.value = {
      肝病病史: diseaseTextMap.肝病病史,
      肝病家族史: diseaseTextMap.肝病家族史,
      常见慢性病及肿瘤史: diseaseTextMap.常见慢性病及肿瘤史,
      个人史: diseaseTextMap.个人史,
      重大传染病及精神疾病史: diseaseTextMap.重大传染病及精神疾病史,
      体格检查: diseaseTextMap.体格检查,
      婚育史: diseaseTextMap.婚育史,
      过敏史: diseaseTextMap.过敏史,
    }
  }
}

/** 返回 */
function onCancel() {
  emit('toggleComp', {
    name: 'BASE',
  })
}

/** 初始化函数 */
function init() {
  PATIENT_ID && getPatientInfo(PATIENT_ID)
  if (editType.value === 0 && PATIENT_ID)
    getPatientDiseaseInfo(PATIENT_ID)
}

function selectDataChange(data) {
  console.log(data)
  diseaseSelectInfo = data
}

function followRightCommit(follow) {
  followRightData.value = follow
}

function handleTheSelectedDiseaseText(array) {
  //
  const sarray = unref(array)
  if (!sarray)
    return
  for (let index = 0; index < sarray.length; index++) {
    const element = sarray[index]
    /// 体格检查 特殊处理一下(未选中 也要)
    if (element.selected || element.topTitle === '体格检查') {
      // 到最后一层
      if (element.children)
        handleTheSelectedDiseaseText(element.children)
      else cacleTheTimeOfsbhistory(element)
    }
  }
}

function cacleTheTimeOfsbhistory(item) {
  /// 进来就是 已经选中的了
  let jb = item.title
  switch (item.topTitle) {
    case '肝病病史':
    case '常见慢性病及肿瘤史': {
      if (item.startTime) {
        if (item.title === '其他' && item.type === 'multiple') {
          jb = item.selectTags?.join('、') ?? ''
        }
        else {
          jb
            = (item.title === '其他肝病' || item.title === '其他')
              ? item.otherInput
              : item.title
        }
        jb += item.title === '乙肝' ? '患病' : '确诊'
        // 计算时长
        item.check = true
        const sc = dayjsDiffTimeSB(item.startTime)
        if (sc.length > 0 && !item.useDate) {
          jb += `${sc}`
        }
        else {
          if (item.title === '其他' && item.type === 'multiple') {
            const tag = item.selectTags?.join('、')
            jb
              = tag?.length > 0
                ? `${dayjs(item.startTime).format(
                  'YYYY年MM',
                )}确诊${tag}`
                : ''
          }
          else {
            jb = `${dayjs(item.startTime).format('YYYY年MM')}确诊${item.title === '其他肝病'
                ? item.otherInput
                : item.title
              }`
          }
        }
        /// 有治疗方案的++
        const zl = treatFangAnString(item)
        jb += `${zl.length > 0
            ? `,${zl}\n`
            : item.title === '乙肝'
              ? ''
              : ';\n'
          }`
      }
      else {
        if (item.title === '其他' && item.type === 'multiple') {
          jb = item.selectTags?.join('、') ?? ''
        }
        else {
          jb
            = item.title === '其他肝病'
              ? item.otherInput
              : item.title
        }
        if (item.title !== '乙肝') {
          /// 有治疗方案的++
          const zl = treatFangAnString(item)
          jb += `${zl.length > 0
              ? `,${zl}\n`
              : item.title === '乙肝'
                ? ''
                : ';\n'
            }`
        }
        item.check = false
      }
      if (jb === ';\n')
        jb = ''
      /// 看看乙肝有没有 药品
      if (item.title === '乙肝') {
        const bb = handleTheUseMedicalString(item.medical, [], item)
        console.log(bb)
        const stt = bb.join(',')
        jb += `${stt?.length > 0 ? `,${stt};\n` : ';\n'}`
      }
      break
    }
    case '肝病家族史':
      if (item.chooses?.length > 0) {
        const qs = item.chooses.join('、')
        jb = `${qs}患有${item.title};\n`
        item.check = true
      }
      else {
        jb += ';\n'
      }
      break
    case '个人史':
      if (item.type === 'yj') {
        jb = `饮${item?.yjzl?.length > 0 ? item?.yjzl : '酒'
          }${dayjsDiffTimeSB(item.startTime, item.endTime)}${item?.pjmrycsrl > 0
            ? `,平均每日乙醇摄入${item.pjmrycsrl}g`
            : ''
          }${item.jj
            ? item.endTime
              ? `,于${dayjs(item.endTime).format(
                'YYYY年MM',
              )}月戒酒`
              : ',已戒'
            : item.jj == null
              ? ''
              : ',未戒'
          };\n`
        item.check = item?.yjzl?.length > 0 && item?.pjmrycsrl > 0
      }
      else if (item.type === 'xy') {
        jb = `吸烟${dayjsDiffTimeSB(item.startTime, item.endTime)}${item?.pjmrxyl?.length > 0
            ? `,平均每日${item.pjmrxyl}根`
            : ''
          }${item.jy
            ? item.endTime
              ? `,于${dayjs(item.endTime).format(
                'YYYY年MM',
              )}月戒烟`
              : ',已戒'
            : item.jy == null
              ? ''
              : ',未戒'
          };\n`
        item.check = item?.pjmrxyl?.length > 0
      }
      else if (item.type === 'ysj') {
        /// 傻逼
        jb = `服用味乐舒${dayjsDiffTimeSB(item.startTime, item.endTime)}${item.yfyl ? `,每天${item.yfyl}条` : ''}${item.stop
          ? `,于${dayjs(item.endTime).format(
            'YYYY年MM',
          )}月停用`
          : ''}`
      }
      break
    case '重大传染病及精神疾病史':
      if (item.startTime) {
        jb = `曾在${dayjs(item.startTime).format('YYYY年MM')}月确诊${item.title
          };\n`
        item.check = true
      }
      else {
        jb += ';\n'
      }
      break
    case '肝硬化':
      /// 处理肝硬化
      if (item?.title?.includes('肝硬化')) {
        jb = item.startTime
          ? `于${dayjs(item.startTime).format('YYYY年MM')}月确诊${item.title
          };\n`
          : `${item.title};\n`
        item.check = item.startTime != null
      }
      else {
        /// 并发症
        jb = item.startTime
          ? `于${dayjs(item.startTime).format('YYYY年MM')}月首次出现${item.title === '其他并发症状'
            ? item.otherInput
            : item.title
          }`
          : `出现${item.title === '其他并发症状'
            ? item.otherInput
            : item.title
          }`
        item.check = item.startTime != null
        /// 查看治疗方式
        const zl = treatFangAnString(item)
        jb += `${zl.length > 0 ? `,${zl}\n` : ';\n'}`
        if (
          item.title === '其他并发症状'
          && item?.otherInput?.trim().length === 0
        )
          jb = ''
      }
      break
    case '体格检查':
      jb = `${item.title.slice(0, item.title.length - 2)}${item.selected ? '有' : '未见'}${item.title.slice(item.title.length - 2)};\n`
      break
    case '婚育史':
      jb = item.chooses || ''
      break
    case '过敏史':
      jb = item?.content?.length > 0 ? `${item.content}过敏` : ''
      break
    default:
      break
  }
  diseaseTextMap.selectedItems.push(item)
  /// 处理下肝硬化
  const key = item.topTitle === '肝硬化' ? '肝病病史' : item.topTitle
  /// 如果是肝硬化
  if (diseaseTextMap[key])
    diseaseTextMap[key] += jb
  else diseaseTextMap[key] = jb
}

/// 治疗方式字符串拼接
function treatFangAnString(item) {
  let temp = ''
  if (item.treats && item.treats?.length > 0) {
    item.treats.forEach((element) => {
      let sb = '有进行'
      if (element.type === 'radio') {
        /// 单选
        if (element?.zlfn?.length > 0 && element?.zlfn !== '无') {
          sb += `${element.zlfn === '有' ? '' : `${element.zlfn}的`}${element.title
            };`
        }
        else {
          sb = ''
        }
      }
      else {
        /// 多选
        if (element?.zlfn?.length > 0)
          sb += `${element.zlfn.join('、')}的${element.title};`
        else sb = ''
      }
      temp += sb
    })
  }
  return temp.length > 0 ? temp : ''
}

function dayjsDiffTimeSB(start, end?: any) {
  if (end == null)
    end = dayjs()
  else end = dayjs(end)
  const diffMon = end.diff(dayjs(start), 'month')
  const year = Math.trunc(diffMon / 12)
  const month = diffMon % 12

  return `${year === 0 ? '' : `${year}年`}${month === 0 ? '' : `${month}个月`
    }`
}

function handleTheUseMedicalString(medical, array, parentNode) {
  if (medical == null)
    return array
  for (let index = 0; index < medical.length; index++) {
    const element = medical[index]
    if (element.children?.length > 0 && element.select) {
      handleTheUseMedicalString(element.children, array, element)
    }
    else {
      if (element.select) {
        if (element.type === 'other') {
          array.push(
            `${element.drugInput == null
              || element?.drugInput?.trim()?.length === 0
              ? ''
              : `${element?.drugInput}`
            }${element.times == null
              || element?.times?.trim()?.length === 0
              ? ''
              : `${element?.times}年`
            }${element.stop ? '已停药' : ''}`,
          )
        }
        else {
          let sttr = `${element.startTime
              ? `${dayjs(element.startTime).format(
                'YYYY年MM',
              )}月开始服用${element.drugName}${element.endTime && element.stop
                ? dayjsDiffTimeSB(
                  element.startTime,
                  element.endTime,
                )
                : `至今${dayjsDiffTimeSB(
                  element.startTime,
                  element.endTime,
                )}`
              }`
              : `服用${element.drugName}`
            }${element.endTime && element.stop
              ? `,于${dayjs(element.endTime).format(
                'YYYY年MM',
              )}月停药`
              : ''
            }`
          if (element.drugName === 'Peg-IFN')
            sttr = sttr.replace('服用', '使用')
          if (element.drugName === '胸腺法新')
            sttr = sttr.replace('服用', '注射')
          array.push(sttr)
        }
        /// 看看填写是否完整
        if ((element.stop && element?.endTime) || element?.startTime)
          parentNode.check = true
        else parentNode.check = false
      }
    }
  }

  return array
}

/// / 用药表的数据 选中变化, 还要清理一下数据

async function saveBaseInfo() {
  const baseParams = {
    ...unref(patientInfo),
  }

  if (verifyStatus.value.phone === 'error') {
    window.$message.error('请填写正确手机号')
    return
  }

  if (
    baseParams?.culturalName == null
    || baseParams?.culturalName?.trim()?.length === 0
  ) {
    window.$message.error('请选择学历')
    return
  }

  /// 校验  职业类别 / 国籍 / 现住址
  if (
    baseParams?.professionName == null
    || baseParams?.professionName?.trim()?.length === 0
  ) {
    window.$message.error('请选择职业类别')
    return
  }

  if (
    baseParams?.countryName == null
    || baseParams?.countryName?.trim()?.length === 0
  ) {
    window.$message.error('请填写国籍')
    return
  }

  if (
    baseParams?.liveAddr == null
    || baseParams?.liveAddr?.trim()?.length === 0
  ) {
    window.$message.error('请填写现住址')
    return
  }

  const res = await UpdateInfoApi(baseParams)
  if (res?.data) {
    message.success('保存成功')
    onCancel()
  }
}
/// 保存病史信息
async function saveDiseaseHistoryInfo() {
  /// 校验管理医生是否填写?
  const res = await getPatientInfoApi(PATIENT_ID)
  if (!res?.data?.manageDoctorName) {
    message.warning('请填写管理医生后,再保存')
    /// 弹出来
    const event = new Event('click', {
      bubbles: true,
      cancelable: true,
    })
    document.getElementById('doctor-manage')?.dispatchEvent(event)

    return
  }

  saveLoading.value = true
  const baseParams = {
    ...unref(patientInfo),
  }
  const bbs = unref(diseaseSelectInfo)
  handleTheSBText()
  const res2 = await updateThePatientDiseaseHistory({
    id: baseParams.id,
    patientId: PATIENT_ID,
    patientName: baseParams.patientName,
    patientRecordNo: baseParams.patientRecordNo,
    createName: userInfo.userName,
    slmcNo: baseParams.slmcNo,
    physicalExam: JSON.stringify(bbs[5]),
    maritalHistory: JSON.stringify(bbs[6]),
    allergyHistory: JSON.stringify(bbs[7]),
    liverComplaintHistory: JSON.stringify(bbs[0]),
    commonChronicDiseasesHistory: JSON.stringify(bbs[3]),
    familyHistory: JSON.stringify(bbs[1]),
    personalHistory: JSON.stringify(bbs[2]),
    infectiousDiseaseHistory: JSON.stringify(bbs[4]),
    chbHistory: JSON.stringify(diseaseTextMap),
    refuseStatus: followRightData.value.refuseStatus,
    refuseReason: followRightData.value.refuseReason,
  })
  /// 还要更改下状态
  // baseParams.diagnoseRecordStatus = diseaseTextMap.status
  // console.log(diseaseTextMap)
  // const res33 = await UpdateInfoApi(baseParams)
  saveLoading.value = false
  if (res2?.data) {
    message.success('保存成功')
    onCancel()
  }
}

onMounted(() => {
  init()
  console.log(editType.value)
})
</script>

<template>
  <n-spin :show="saveLoading">
    <div class="editInfo">
      <!-- 人口学信息 -->
      <section class="editInfo-body">
        <SectionTitle mb-15px>
          <span text="#333">基础信息</span>
        </SectionTitle>
        <n-grid x-gap="14" y-gap="14" :cols="2" class="text-#333">
          <n-gi>
            <InfoItem label="加入SLMC日期：" :label-width="LABEL_WIDTH.demography">
              <span>{{
                dayjs(patientInfo?.createTime).format(
                  "YYYY-MM-DD HH:mm:ss",
                )
              }}</span>
            </InfoItem>
          </n-gi>
          <n-gi>
            <InfoItem label="SLMC编号：" :value="patientInfo?.slmcNo" :label-width="LABEL_WIDTH.demography" />
          </n-gi>
          <n-gi>
            <InfoItem label="建档时间：" :label-width="LABEL_WIDTH.demography">
              <span>{{
                patientInfo?.createRecordTime
                  ? dayjs(patientInfo?.createRecordTime).format(
                    "YYYY-MM-DD",
                  )
                  : "-"
              }}</span>
            </InfoItem>
          </n-gi>
          <n-gi>
            <InfoItem label="是否纳入随访：" :value="patientInfo?.slmcNo" :label-width="LABEL_WIDTH.demography">
              <div>
                <SvgIcon v-if="patientInfo?.status === 0" local-icon="slmc-icon-fou-hui" size="16" />
                <SvgIcon v-else local-icon="slmc-icon-shi" size="16" />
              </div>
            </InfoItem>
          </n-gi>
          <n-gi>
            <InfoItem label="姓名：" :value="patientInfo.patientName" :label-width="LABEL_WIDTH.demography" />
          </n-gi>
          <n-gi>
            <InfoItem label="性别：" :value="patientInfo.sexName" :label-width="LABEL_WIDTH.demography" />
          </n-gi>
          <n-gi>
            <InfoItem label="出生年月：" :value="patientInfo.birthday" :label-width="LABEL_WIDTH.demography" />
          </n-gi>
          <n-gi>
            <InfoItem label="年龄：" :label-width="LABEL_WIDTH.demography">
              <span>{{
                `${patientInfo?.age
                  ? `${patientInfo?.age}岁`
                  : "-"
                }`
              }}</span>
            </InfoItem>
          </n-gi>
          <n-gi>
            <InfoItem :is-require="editType === 0" label="学历：" :label-width="LABEL_WIDTH.demography">
              <n-select v-if="editType === 0" v-model:value="patientInfo.culturalName" :options="xlOptions" />
              <span v-else>{{
                patientInfo?.culturalName ?? "-"
              }}</span>
            </InfoItem>
          </n-gi>
          <n-gi>
            <InfoItem :is-require="editType === 0" label="国籍：" :label-width="LABEL_WIDTH.demography">
              <n-input v-if="editType === 0" v-model:value="patientInfo.countryName" />
              <span v-else>{{
                patientInfo?.countryName ?? "-"
              }}</span>
            </InfoItem>
          </n-gi>
          <n-gi>
            <InfoItem label="民族" :label-width="LABEL_WIDTH.demography" margin>
              <n-input
                v-if="editType === 0" v-model:value="patientInfo.nationName" class="flex-1 basis-380px"
                :maxlength="10"
              />
              <span v-else>{{
                patientInfo?.nationName ?? "-"
              }}</span>
            </InfoItem>
          </n-gi>
          <n-gi>
            <InfoItem label="证件类型：" :label-width="LABEL_WIDTH.demography" margin>
              <span>{{
                patientInfo?.patientIdCardType
                  ? CardType[patientInfo?.patientIdCardType]
                  : "-"
              }}</span>
            </InfoItem>
          </n-gi>
          <n-gi>
            <InfoItem label="证件号：" :value="patientInfo.patientIdCardNum" :label-width="LABEL_WIDTH.demography" />
          </n-gi>
          <n-gi>
            <InfoItem
              label="手机号：" :is-require="editType === 0" :value="patientInfo.phone"
              :label-width="LABEL_WIDTH.demography"
            >
              <n-input
                v-if="editType === 0" v-model:value="patientInfo.phone" class="flex-1 basis-380px"
                :maxlength="11" :status="verifyStatus.phone" @blur="(e) => onBlurFrom('phone', e)"
              />
              <span v-else>{{ patientInfo?.phone ?? "-" }}</span>
            </InfoItem>
          </n-gi>

          <n-gi>
            <InfoItem label="紧急联系人" :label-width="LABEL_WIDTH.demography" margin>
              <n-input
                v-if="editType === 0" v-model:value="patientInfo.contactsName" class="flex-1 basis-380px"
                :maxlength="10"
              />
              <span v-else>{{
                patientInfo?.contactsName ?? "-"
              }}</span>
            </InfoItem>
          </n-gi>
          <n-gi>
            <InfoItem label="紧急联系人电话：" :label-width="LABEL_WIDTH.demography">
              <n-input
                v-if="editType === 0" v-model:value="patientInfo.contactsTel" class="flex-1 basis-380px"
                :maxlength="11" @blur="(e) => onBlurFrom('contactsTel', e)"
              />
              <span v-else>{{
                patientInfo?.contactsTel ?? "-"
              }}</span>
            </InfoItem>
          </n-gi>
          <n-gi>
            <InfoItem :is-require="editType === 0" label="职业类别：" :label-width="LABEL_WIDTH.demography">
              <n-select v-if="editType === 0" v-model:value="patientInfo.professionName" :options="zylbOptions" />
              <span v-else>{{
                patientInfo?.professionName ?? "-"
              }}</span>
            </InfoItem>
          </n-gi>
          <n-gi>
            <InfoItem label="医保性质：" :value="patientInfo.medicalInsuranceType" :label-width="LABEL_WIDTH.demography" />
          </n-gi>
          <n-gi>
            <InfoItem label="医保卡号：" :value="patientInfo.medicalInsuranceNo" :label-width="LABEL_WIDTH.demography" />
          </n-gi>
          <n-gi>
            <InfoItem label="居民健康卡号：" :value="patientInfo.healthCardNum" :label-width="LABEL_WIDTH.demography" />
          </n-gi>
          <n-gi>
            <InfoItem label="婚姻状况" :label-width="LABEL_WIDTH.demography" margin>
              <n-select
                v-if="editType === 0" v-model:value="patientInfo.marriageName" :options="maritalStatusOptions"
                class="flex-1 basis-380px"
              />
              <span v-else>{{
                patientInfo?.marriageName ?? "-"
              }}</span>
            </InfoItem>
          </n-gi>
          <n-gi>
            <InfoItem label="家庭成员" :label-width="LABEL_WIDTH.demography" margin>
              <n-input
                v-if="editType === 0" v-model:value="patientInfo.familyMembers" class="flex-1 basis-380px"
                :maxlength="50"
              />
              <span v-else>{{
                patientInfo?.familyMembers ?? "-"
              }}</span>
            </InfoItem>
          </n-gi>
          <n-gi>
            <InfoItem label="家庭年收入" :label-width="LABEL_WIDTH.demography" margin>
              <n-input
                v-if="editType === 0" v-model:value="patientInfo.income" class="flex-1 basis-380px"
                :maxlength="10"
              />
              <span v-else>{{ patientInfo?.income ?? "-" }}</span>
            </InfoItem>
          </n-gi>
          <n-gi>
            <!-- TODO: 交互形式？ -->
            <InfoItem label="工作性质：" :label-width="LABEL_WIDTH.demography">
              <n-input
                v-if="editType === 0" v-model:value="patientInfo.companyType" class="flex-1 basis-380px"
                :maxlength="30"
              />
              <span v-else>{{
                patientInfo?.companyType ?? "-"
              }}</span>
            </InfoItem>
          </n-gi>
          <n-gi>
            <InfoItem label="户口所在地" :label-width="LABEL_WIDTH.demography" margin>
              <n-input
                v-if="editType === 0" v-model:value="patientInfo.homeAddr" class="flex-1 basis-380px"
                :maxlength="50"
              />
              <span v-else>{{
                patientInfo?.homeAddr ?? "-"
              }}</span>
            </InfoItem>
          </n-gi>
          <n-gi>
            <InfoItem :is-require="editType === 0" label="现住址" :label-width="LABEL_WIDTH.demography" margin>
              <n-input
                v-if="editType === 0" v-model:value="patientInfo.liveAddr" class="flex-1 basis-380px"
                :maxlength="50"
              />
              <span v-else>{{
                patientInfo?.liveAddr ?? "-"
              }}</span>
            </InfoItem>
          </n-gi>
        </n-grid>
        <div v-if="editType === 0" mt-24px flex items-center justify-center>
          <n-button type="primary" @click="saveBaseInfo">
            保存
          </n-button>
          <n-button ml-20px @click="onCancel">
            取消
          </n-button>
        </div>
        <div>
          <SectionTitle mt-28px>
            <span text="#333">病史信息</span>
          </SectionTitle>
          <DiseaseInfo
            v-if="editType === 1" :patient-id="patientIdRef" @dataChange="selectDataChange"
            @follow-right="followRightCommit"
          />
          <div v-else>
            <div v-if="diseaseHis" ws-pre-line>
              <div v-for="(value, key) in diseaseHis" :key="key" mb-12px flex items-start text-14px>
                <div mr-10px w-160px text-right text="#666">
                  {{ `${key}:` }}
                </div>
                <div text="#333">
                  {{
                    value == null
                      || value?.trim()?.length === 0
                      ? "无"
                      : value
                  }}
                </div>
              </div>
            </div>
            <n-empty v-else description="未建档" />
          </div>
        </div>
      </section>
      <footer class="footer">
        <n-space v-if="editType === 1" justify="center">
          <n-button type="primary" @click="confirmThrottle">
            建&nbsp;&nbsp;档
          </n-button>
          <n-button @click="onCancel">
            取&nbsp;&nbsp;消
          </n-button>
        </n-space>
      </footer>
    </div>
  </n-spin>
</template>

<style scoped lang="scss">
.editInfo {
  position: relative;
  padding: 0px 0px 60px 0px;
  height: 100%;
  flex: 1;

  &-body {
    padding: 14px;
    height: 100%;
    overflow-y: auto;
  }
}

.footer {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  background-color: #f4f5f6;
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #e8e8e8;
}

.n-table td {
  border-bottom-color: #d1d1d1;
  border-right-color: #d1d1d1;
  padding: 0;
  min-height: 40px;
}

.table-left {
  width: 120px;
  background: #f5f5f5;
  text-align: right;
  color: #666;
  padding-right: 10px !important;
}

.n-table.n-table--bordered {
  border-color: #d1d1d1;
}
</style>
