<script setup lang='ts'>
const props = defineProps({
  modalDrugOptions: {
    type: Object,
    default: () => {},
  },
})

const emits = defineEmits(['ok', 'visibleChangle'])

const selectItem = reactive({})

// const modalDrugOptions = defineModel('modalDrugOptions')

function useMedicalSaveConfirm() {
  console.log(selectItem)
  /// 如果 选中了 没填
  ///
  if (Object.keys(selectItem).length === 0)
    window.$message.error('请选择药品')
  else
    emits('ok')
}

function visibleChange(val) {
  emits('visibleChangle', val)
}

function selectChange(item) {
  console.log(item)
  if (!item.select) {
    clearChildrenData(item.children)
    /// 可能就是最后一层了
    if (item.children == null) {
      item.select = false
      item.times = null
      item.stop = null
      item.drugInput = null
      delete selectItem[item.drugName]
    }
  }
  else {
    /// 选中最后一层的话, 暂存一下
    if (item.children == null)
      selectItem[item.drugName] = item

    if (item.children?.length === 1)
      item.children[0].select = true
  }
}

function clearChildrenData(data) {
  if (data == null)
    return
  for (let index = 0; index < data.length; index++) {
    const element = data[index]
    element.select = false
    element.times = null
    element.stop = null
    element.drugInput = null
    delete selectItem[element.drugName]
    if (element?.children)
      clearChildrenData[element.children]
  }
}

function handleTheUseMedical(medical) {
  if (medical == null)
    return
  for (let index = 0; index < medical.length; index++) {
    const element = medical[index]
    if (element.children?.length > 0 && element.select) {
      handleTheUseMedical(element.children)
    }
    else {
      if (element.select) {
        if (element.type === 'other')
          selectItem['其他'] = element
        else
          selectItem[element.drugName] = element
      }
    }
  }
}

function onlyAllowNumber(value: string) {
  return /^\d+(\.\d+)?$/.test(Number(value))
}

watch(props.modalDrugOptions, () => {
  handleTheUseMedical(props.modalDrugOptions.data)
})

// function disablePreviousDate(ts: number) {
//   return ts > Date.now()
// }
/// / 时间限制 结束时间大于开始时间
function isDateDisable(item, isStart, current) {
  console.log(current)
  if (isStart) {
    /// 是开始
    console.log(item.endTime, 'xxEnd')
    return current > Date.now() || (item.endTime && current > item.endTime)
  }
  else {
    /// 是结束
    console.log(current)
    return current > Date.now() || (item.startTime && current < item.startTime)
  }
}
</script>

<template>
  <BasicModal
    :visible="modalDrugOptions.showImport"
    :title="modalDrugOptions.title"
    :loading="modalDrugOptions.loadingImport"
    :width="modalDrugOptions.width"
    @ok="useMedicalSaveConfirm"
    @visible-change="visibleChange"
  >
    <div max-h-500px flex overflow-auto px-20px pb-4px pt-22px>
      <div mr-13px>
        <span color="red" mr-5px text-16px>*</span>
        <span text-14px color="#666" leading-21px>选择药品</span>
      </div>
      <div flex-1>
        <div
          v-for="(item, index) in modalDrugOptions.data"
          :key="index"
          mb-10px
        >
          <n-checkbox v-model:checked="item.select" mb-10px @update:checked="selectChange(item)">
            <span ml-10px>{{ item.drugName }}</span>
          </n-checkbox>
          <div v-if="item.select" bg="#f5f5f5" ml-26px py-7px>
            <div
              v-if="item.type === 'other'" bg="#F5F5F5"

              ml-26px flex items-center px-14px
            >
              <div>药品名称</div>
              <n-input
                v-model:value="item.drugInput"
                placeholder="请输入"
                style="
                                                        width: 200px;
                                                        margin-left: 10px;
                                                    "
              />
              <div ml-20px mr-10px>
                首次用药时间
              </div>
              <!-- <n-date-picker v-model:value="item.startTime" style="width: 110px;" type="month" clearable :is-date-disabled="disablePreviousDate" /> -->
              <el-date-picker
                v-model="item.startTime"
                type="month"
                placeholder="yyyy-mm"
                clearable
                :teleported="false"
                :disabled-date="(current) => { return isDateDisable(item, true, current) }"
                style="width: 110px;margin-left: 10px;"
              />
              <!-- <timesSelect v-model:value="item.startTime" ml-10px :width="110" placeholder="YY年MM月" :date="item.startTime" /> -->
            </div>
            <div
              v-for="(kitem, kindex) in item.children"
              :key="kindex"
            >
              <div v-if="kitem.children" ml-26px>
                <n-checkbox
                  v-model:checked="kitem.select"
                  mb-10px
                  @update:checked="selectChange(kitem)"
                >
                  <span ml-10px>{{
                    kitem.drugName
                  }}</span>
                </n-checkbox>
                <div v-if="kitem.select" mb-10px>
                  <div
                    v-for="(
                      kitem2, kindex2
                    ) in kitem.children"
                    :key="kindex2"
                    bg="#F5F5F5"
                    ml-26px flex items-center px-14px
                  >
                    <n-checkbox
                      v-model:checked="kitem2.select"
                      @update:checked="selectChange(kitem2)"
                    >
                      <span ml-10px>{{
                        kitem2.drugName
                      }}</span>
                    </n-checkbox>
                    <div
                      v-if="kitem2.select"
                      ml-30px
                      flex
                      items-center
                    >
                      <span>首次用药时间</span>
                      <!-- <n-date-picker v-model:value="kitem2.startTime" style="width: 110px;" type="month" clearable :is-date-disabled="disablePreviousDate" /> -->
                      <el-date-picker
                        v-model="kitem2.startTime"
                        type="month"
                        placeholder="yyyy-mm"
                        clearable
                        :teleported="false"
                        :disabled-date="(current) => { return isDateDisable(kitem2, true, current) }"
                        style="width: 110px;margin-left: 10px;"
                      />
                      <!-- <timesSelect v-model:value="kitem2.startTime" ml-10px :width="110" placeholder="YY年MM月" :date="kitem2.startTime" /> -->
                      <n-checkbox
                        v-if="
                          modalDrugOptions.key
                            === '乙肝'
                        "
                        v-model:checked="
                          kitem2.stop
                        "
                        ml-20px
                        @update:checked="selectChange(kitem2)"
                      >
                        <span mx-10px>已停药</span>
                      </n-checkbox>
                      <!-- <n-date-picker v-if="kitem2.stop" v-model:value="kitem2.endTime" style="width: 110px;" type="month" clearable :is-date-disabled="disablePreviousDate" /> -->
                      <el-date-picker
                        v-if="kitem2.stop"
                        v-model="kitem2.endTime"
                        type="month"
                        placeholder="yyyy-mm"
                        clearable
                        :teleported="false"
                        :disabled-date="(current) => { return isDateDisable(kitem2, false, current) }"
                        style="width: 110px;margin-left: 10px;"
                      />
                      <!-- <timesSelect
                        v-if="kitem2.stop"
                        v-model:value="kitem2.endTime" ml-10px :width="110" placeholder="YY年MM月" :date="kitem2.endTime"
                      /> -->
                    </div>
                  </div>
                </div>
              </div>
              <div
                v-else
                bg="#F5F5F5"
                class="drug-item"
                flex items-center px-14px py-7px
              >
                <n-checkbox v-model:checked="kitem.select" w-190px @update:checked="selectChange(kitem)">
                  <span ml-10px>{{
                    kitem.drugName
                  }}</span>
                </n-checkbox>
                <div
                  v-if="kitem.select"
                  ml-20px
                  flex
                  items-center
                >
                  <span mr-10px>首次用药时间</span>
                  <!-- <n-date-picker v-model:value="kitem.startTime" style="width: 110px;" type="month" clearable :is-date-disabled="disablePreviousDate" /> -->
                  <el-date-picker
                    v-model="kitem.startTime"
                    type="month"
                    placeholder="yyyy-mm"
                    clearable
                    :teleported="false"
                    :disabled-date="(current) => { return isDateDisable(kitem, true, current) }"
                    style="width: 110px;margin-left: 10px;"
                  />
                  <!-- <timesSelect v-model:value="kitem.startTime" ml-10px :width="110" placeholder="YY年MM月" :date="kitem.startTime" /> -->
                  <n-checkbox
                    v-if="modalDrugOptions.key === '乙肝'"
                    v-model:checked="kitem.stop"
                    ml-20px
                    @update:checked="selectChange(kitem)"
                  >
                    <span mx-10px>已停药</span>
                  </n-checkbox>
                  <!-- <n-date-picker v-if="kitem.stop" v-model:value="kitem.endTime" style="width: 110px;" type="month" clearable :is-date-disabled="disablePreviousDate" /> -->
                  <el-date-picker
                    v-if="kitem.stop"
                    v-model="kitem.endTime"
                    type="month"
                    placeholder="yyyy-mm"
                    clearable
                    :teleported="false"
                    :disabled-date="(current) => { return isDateDisable(kitem, false, current) }"
                    style="width: 110px;margin-left: 10px;"
                  />
                  <!-- <timesSelect
                    v-if="kitem.stop"
                    v-model:value="kitem.endTime" ml-10px :width="110" placeholder="YY年MM月" :date="kitem.endTime"
                  /> -->
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </BasicModal>
</template>

<style lang="scss" scoped>
</style>
