<script setup lang="ts">
import { addToPlan, getPlanByPatientid, queryOneFollow } from '@/api/intelligentFollow/plan'
import { getPatientHealthSummary, getPatientHealthSummary2 } from '@/api/patient/patient'
import { useAuthStore } from '@/store'
import { clear, handleTheSelectedDiseaseText } from '@/utils/historyCalc/index'

const PREFIX = '/heper-api'
const patientHealthReport = ref()

const auth = useAuthStore()
const userInfo = auth.userInfo
const route = useRoute()
const haveRecord = ref()
const PATIENT_ID = route.query?.patientId as string
/// 随访计划
const planTemplate = reactive({
  name: undefined,
  value: undefined,
  type: undefined,
})
const followForm = reactive({
  planId: undefined,
  followType: undefined,
})
const followOpts = ref()
const tagId = ref()
const healthSummary = ref()
const unusualList = ref()
// '桂福平的疾病评估属于高风险。\n乙肝患病3个月,2024年02月开始服用恩替卡韦(ETV)至今3个月,2024年05月开始使用Peg-IFN至今;\n于2018-05-04乙肝DNA测定结果(≈59)提示,乙肝病毒少量复制。\n桂福平饮白酒4年4个月,平均每日乙醇摄入40.00g,未戒;\n合并高血压确诊3个月;糖尿病确诊3个月;慢性肾病确诊3个月;\n于2023-11-01,肝脏硬度测定结果(CAP:200.00,LSM:7.40)提示,肝脏属于轻度纤维化。根据病情评估,为桂福平制定的管理方案【急性肝衰竭路随访计划】如下：时间: 2024-06-18至2024-06-24\n内容: \n        血常规,\n        肝功能,\n        肾功能,\n        血脂检测,\n        血糖检测,\n        电解质检测,\n        肿瘤标志物,\n        凝血功能\n疾病、药物和饮食等相关健康宣教内容已推送【肝愈】微信小程序。'
const followTypeOpts = [{ label: '小程序', value: 'XCX' }, { label: '电话', value: 'DH' }, { label: '短信', value: 'DX' }]

// 获取患者的随访计划
function getPatientFollowPlan() {
  getPlanByPatientid({
    patientId: PATIENT_ID,
  }).then((res: any) => {
    planTemplate.value = res.data?.planId || ''
    planTemplate.name = res.data?.planTemplateName || ''
    planTemplate.type = res.data?.followType || ''
  })
}

function getPatientHealthSummaryData() { // '1726121677803233281'   PATIENT_ID
  getPatientHealthSummary(PATIENT_ID).then((res: any) => {
    if (res.data && res.data.tagId) {
      healthSummary.value = res.data.content || ''
      tagId.value = res.data.tagId || ''
    }
  })
  ///  1737629146250526722   1821057297769623554
  patientHealthReport.value = `${PREFIX}/hma/healthReportPdf/1/${PATIENT_ID}`
  try {
    getPatientHealthSummary2(PATIENT_ID).then((res: any) => {
      if (res.data) {
        haveRecord.value = true
        healthSummary.value = res.data || {}
        // const chbHistory = JSON.parse(res.data.chbHistory || '{}')

        unusualList.value = res.data.abnormalAssayDTOList || []

        healthSummary.value.heightSign = getValueTrendArrow(res.data?.heightSign || '')
        healthSummary.value.weightSign = getValueTrendArrow(res.data?.weightSign || '')
        healthSummary.value.bmiSign = getValueTrendArrow(res.data?.bmiSign || '')
        healthSummary.value.hipSign = getValueTrendArrow(res.data?.hipSign || '')
        healthSummary.value.waistSign = getValueTrendArrow(res.data?.waistSign || '')
        healthSummary.value.whrSign = getValueTrendArrow(res.data?.whrSign || '')

        // const str = chbHistory.肝病病史 // '乙肝患病4个月,2024年02月开始使用Peg-IFN至今7个月,2021年01月开始注射胸腺法新至今3年8个月;\n于2022年06月确诊代偿期肝硬化;\n于2024年05月确诊失代偿期肝硬化;\n于2020年05月首次出现22333 发病;\n丙肝,有进行PEG-IFN的药物治疗;\n'
        // healthSummary.value.liverHistory = (str.length > 0 && str.includes('\n')) ? str.substring(0, str.length - 1) : str // chbHistory.肝病病史

        // healthSummary.value.familyHistory = chbHistory.肝病家族史
        // healthSummary.value.chronicDisease = chbHistory.常见慢性病及肿瘤史
        // healthSummary.value.majorDisease = chbHistory.重大传染病及精神疾病史
        // // healthSummary.value.tgjc = chbHistory.体格检查
        // // healthSummary.value.hys = chbHistory.婚育史
        // // healthSummary.value.gms = chbHistory.过敏史
        // console.log('chbHistory====', chbHistory)
        clear()
        const his: any = handleTheSelectedDiseaseText([
          JSON.parse(res.data?.liverComplaintHistory || '{}'),
          JSON.parse(res.data?.familyHistory || '{}'),
          JSON.parse(res.data?.personalHistory || '{}'),
          JSON.parse(res.data?.commonChronicDiseasesHistory || '{}'),
          JSON.parse(res.data?.infectiousDiseaseHistory || '{}'),
        ])

        healthSummary.value.liverHistory = his.肝病病史
        healthSummary.value.familyHistory = his.肝病家族史
        healthSummary.value.chronicDisease = his.常见慢性病及肿瘤史
        healthSummary.value.majorDisease = his.重大传染病及精神疾病史

        const temp = his['个人史'] || ';'
        console.log('item111====', temp)
        temp.split(';').forEach((item: string) => {
          console.log('item====', item)
          if (item.includes('酒'))
            healthSummary.value.drink = item.replaceAll(',', '，')
          if (item.includes('烟'))
            healthSummary.value.smoke = item.replaceAll(',', '，')
        })
      }
      else {
        haveRecord.value = false
      }
    })
  }
  catch (error) {
    haveRecord.value = false
  }
}

function getValueTrendArrow(str: string) {
  if (str === '1')
    return '↑'
  else if (str === '2')
    return '↓'
  else
    return str
}

function queryFollowList() {
  console.log('111=======', userInfo)
  queryOneFollow({
    operateId: userInfo?.id,
    tagIds: tagId.value,
    patientId: PATIENT_ID,
  }).then((res: any) => {
    console.log('随访计划======', res)
    if (res.data) {
      followOpts.value = Object.keys(res.data).map((item) => {
        return {
          value: item,
          label: res.data[item],
        }
      })
      followForm.planId = followOpts.value[0].value
    }
  })
}

function onceFollow() {
  if (!followForm.planId && !planTemplate.value) {
    window.$message.error('请选择随访方案')
    return
  }
  const params = {
    addSource: 'SLMC',
    operatorId: userInfo?.id,
    operatorName: userInfo?.userName,
    patientId: PATIENT_ID,
    planTemplateName: followOpts.value.find((item: any) => item.value === followForm.planId)?.label,
    planTmpId: followForm.planId || planTemplate.value,
    followType: followForm.followType,
  }
  addToPlan(params).then((res) => {
    if (res.data) {
      window.$message.success('纳入随访成功')
      getPatientFollowPlan()
    }
  })
}

function downLoadPdf() {}

function printPdf() {
  // print(patientHealthReport.value)
}

onMounted(() => {
  queryFollowList()
  getPatientFollowPlan()
  getPatientHealthSummaryData()
})
</script>

<template>
  <div mb--14px px-14px pt-14px>
    <div v-if="!haveRecord" style="white-space: pre-line;">
      <n-empty description="未能生成健康小结，请完善专病档案和检查报告。" mt-150px />
    </div>

    <div v-else overflow-auto class="h-[calc(100vh-300px)]" pb-20px>
      <PageTitle class="mb-2px">
        健康小结
      </PageTitle>
      <div mx-25px my-15px>
        <SectionTitle>
          肝病患病情况
        </SectionTitle>
        <div ml-14px flex>
          <div color="#666" w-400px>
            肝病风险等级：<span color="#333">{{ healthSummary?.level || '-' }}</span>
          </div>
          <!-- <div color="#666">
            肝硬度测定结果：<span color="#333">轻/中/重度纤维化</span>
          </div> -->
        </div>

        <SectionTitle mt-20px>
          基本情况
        </SectionTitle>
        <div ml-14px>
          <div flex>
            <div class="basic-message">
              身高：
              <span color="#333">{{ `${healthSummary?.height || '-'}${healthSummary?.height ? 'cm' : ''}` }}</span>
              <span color="#F36969">{{ healthSummary?.heightSign || '' }}</span>
            </div>
            <div class="basic-message">
              体重：
              <span color="#333">{{ `${healthSummary?.weight || '-'}${healthSummary?.weight ? 'kg' : ''}` }}</span>
              <span color="#F36969">{{ healthSummary?.weightSign || '' }}</span>
            </div>
            <div class="basic-message">
              BMI：<span color="#333">{{ `${healthSummary?.bmi || '-'}` }}</span>
              <span color="#F36969">{{ healthSummary?.bmiSign || '' }}</span>
            </div>
          </div>
          <div flex>
            <div class="basic-message">
              腰围：
              <span color="#333">{{ `${healthSummary?.waist || '-'}${healthSummary?.waist ? 'cm' : ''}` }}</span>
              <span color="#F36969">{{ healthSummary?.waistSign || '' }}</span>
            </div>
            <div class="basic-message">
              臀围：
              <span color="#333">{{ `${healthSummary?.hip || '-'}${healthSummary?.hip ? 'cm' : ''}` }}</span>
              <span color="#F36969">{{ healthSummary?.hipSign || '' }}</span>
            </div>
            <div class="basic-message">
              腰臀比：
              <span color="#333">{{ `${healthSummary?.whr || '-'}` }}</span>
              <span color="#F36969">{{ healthSummary?.whrSign || '' }}</span>
            </div>
          </div>
        </div>

        <SectionTitle mt-20px>
          个人生活方式情况
        </SectionTitle>
        <div ml-14px flex>
          <div color="#666">
            饮酒史：<span color="#333">{{ healthSummary?.drink || '无' }}</span>
          </div>
          <div color="#666" ml-30px>
            吸烟史：<span color="#333">{{ healthSummary?.smoke || '无' }}</span>
          </div>
        </div>
        <SectionTitle mt-20px>
          主要健康问题
        </SectionTitle>
        <div ml-14px mt-12px>
          <div color="#666" mt-10px>
            肝病病史：
          </div>
          <div mt-10px p-10px style="background: #f5f5f5;white-space: pre-wrap;">
            {{ healthSummary?.liverHistory.replaceAll(',', '，') || '无' }}
            <!-- <div flex>
              <div color="#666" w-400px>
                合并性肝病：<span color="#333">肝硬化，10年</span>
              </div>
              <div color="#666">
                用药情况：<span color="#333">恩替卡韦片，10年，否认停药</span>
              </div>
            </div>
            <div mt-5px flex>
              <div color="#666" w-400px>
                肝衰竭：<span color="#333">肝硬化，10年</span>
              </div>
              <div color="#666">
                肝衰竭：<span color="#333">恩替卡韦片，10年，否认停药</span>
              </div>
            </div> -->
          </div>
          <div color="#666" mt-12px w-400px>
            肝病家族史：<span color="#333">{{ healthSummary?.familyHistory || '无' }}</span>
          </div>
          <div color="#666" mt-12px>
            常见慢性病及肿瘤史：<span color="#333">{{ healthSummary?.chronicDisease || '无' }}</span>
          </div>
          <!-- <div color="#666" mt-12px w-400px>
            体格检查：<span color="#333">{{ healthSummary?.tgjc || '无' }}</span>
          </div>
          <div color="#666" mt-12px w-400px>
            婚育史：<span color="#333">{{ healthSummary?.hys || '无' }}</span>
          </div>
          <div color="#666" mt-12px w-400px>
            过敏史：<span color="#333">{{ healthSummary?.gms || '无' }}</span>
          </div> -->
          <div color="#666" mt-12px>
            重大传染病及精神疾病史：<span color="#333">{{ healthSummary?.majorDisease || '无' }}</span>
          </div>
        </div>

        <SectionTitle mb-14px mt-20px>
          异常指标情况
        </SectionTitle>
        <el-table
          v-if="unusualList?.length > 0"
          :data="unusualList"
          stripe
          ml-14px
          flex-1
          max-height="400"
        >
          <el-table-column label="指标名称" :formatter="({ chineseName }) => chineseName || '指标名称'" />
          <el-table-column label="结果">
            <template #default="{ row }">
              <div color="#F36969">
                {{ row.result || '-' }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="需要注意">
            <template #default="{ row }">
              <div color="#F36969">
                {{ row.hint ? (row.hint === '1' ? '↑' : row.hint === '2' ? '↓' : row.hint) : '-' }}
              </div>
            </template>
          </el-table-column>
          <el-table-column label="正常参考" :formatter="({ refValue }) => refValue || '-'" />
          <el-table-column label="单位" :formatter="({ unit }) => unit || '-'" />
        </el-table>
        <div v-else ml-14px p-10px style="background: #f5f5f5;white-space: pre-wrap;">
          暂无异常指标
        </div>
      </div>

      <!-- <div mt-10px p-10px style="background: #f5f5f5;white-space: pre-wrap;">
        {{ healthSummary }}
      </div> -->

      <PageTitle class="mb-2px" mt-28px>
        随访计划
      </PageTitle>
      <div mx-28px mt-10px>
        <div v-if="planTemplate.value" style="background-color: #E9EDF4;border-radius: 3px" mt-5px h-60px w-260px flex items-center>
          <img src="@/assets/images/icon_suifang.png" alt="" ml-20px mr-14px h-26px w-25px>
          <div color="#333" font-600>
            {{ planTemplate.name }}
          </div>
        </div>
        <div v-else mt-5px flex>
          <span class="text-#666" mt-5px>随访计划：</span>

          <NSelect
            v-model:value="followForm.planId" :style="{ width: '278px' }" :options="followOpts"
            filterable mr-10px mr-20px text-left placeholder="请选择随访计划" @search="queryFollowList"
          />

          <span class="text-#666" mt-5px>随访方式：</span>
          <div v-if="planTemplate.value" mt-5px>
            {{ planTemplate.type ? followTypeOpts.find((item: any) => item.value === planTemplate.type)?.label : '-' }}
          </div>
          <NSelect
            v-else
            v-model:value="followForm.followType" :style="{ width: '278px' }" :options="followTypeOpts" filterable
            mr-10px text-left placeholder="请选择随访方式"
          />

          <NButton v-if="!planTemplate.value" ml-20px type="primary" :disabled="followOpts && followOpts.length === 0" @click="onceFollow()">
            一键随访
          </NButton>
        </div>
      </div>
    </div>

    <div v-if="haveRecord" h-60px flex items-center justify-center style="background-color: #f4f5f6;margin: 0 -28px -28px -28px;">
      <NButton type="primary" h-32px w-100px @click="downLoadPdf">
        <a :href="patientHealthReport" cursor-pointer style="color: #fff;">报告下载</a>
      </NButton>

      <!-- <NButton type="primary" ml-20px h-32px w-100px @click="printPdf">
        报告打印
      </NButton> -->
    </div>
  </div>
</template>

<style scoped lang="css">
.basic-message {
  color: #666;
  font-size: 14px;
  flex: 1;
  width: 60px;
  text-align: start;
}
</style>
