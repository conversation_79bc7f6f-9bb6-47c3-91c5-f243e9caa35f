<script setup lang="ts">
import { cloneDeep } from 'lodash-es'
import _ from 'lodash'
import timesSelect from '../timesSelect/index.vue'
import UseMedicalModal from './UseMedicalModal.vue'
import {
  getOtherDiseaseForHistory,
  getPatientDiseaseHistory,
  getPatientDiseaseUseDrug,
} from '@/api/patient'

const props = defineProps({
  patientId: {
    type: String,
    default: '',
  },
})

const emits = defineEmits(['dataChange', 'followRight'])

const followRight: any = reactive({
  refuseReason: '',
  refuseStatus: 0,
})

const modalDrugOptions = reactive({
  width: 830,
  loadingImport: false,
  title: '',
  showImport: false,
  data: [],
  key: '',
})

const hyOptions = [
  {
    label: '未婚',
    value: '未婚',
  },
  {
    label: '已婚',
    value: '已婚',
  },
  {
    label: '离异',
    value: '离异',
  },
  {
    label: '丧偶',
    value: '丧偶',
  },
]

const syOptions = [
  {
    label: '未育',
    value: '未育',
  },
  {
    label: '已育',
    value: '已育',
  },
  {
    label: '备孕',
    value: '备孕',
  },
]

const selectsOptions = [
  {
    label: '祖父',
    value: '祖父',
  },
  {
    label: '祖母',
    value: '祖母',
  },
  {
    label: '外祖父',
    value: '外祖父',
  },
  {
    label: '外祖母',
    value: '外祖母',
  },
  {
    label: '父亲',
    value: '父亲',
  },
  {
    label: '母亲',
    value: '母亲',
  },
  {
    label: '配偶',
    value: '配偶',
  },
  {
    label: '儿子',
    value: '儿子',
  },
  {
    label: '女儿',
    value: '女儿',
  },
  {
    label: '孙子',
    value: '孙子',
  },
  {
    label: '孙女',
    value: '孙女',
  },
  {
    label: '外孙',
    value: '外孙',
  },
  {
    label: '外孙女',
    value: '外孙女',
  },
  {
    label: '兄弟姐妹',
    value: '兄弟姐妹',
  },
  {
    label: '舅舅',
    value: '舅舅',
  },
  {
    label: '姨娘',
    value: '姨娘',
  },
  {
    label: '叔父',
    value: '叔父',
  },
  {
    label: '姑姑',
    value: '姑姑',
  },
]

const yjOptions = [
  {
    label: '白酒',
    value: '白酒',
  },
  {
    label: '啤酒',
    value: '啤酒',
  },
  {
    label: '红酒',
    value: '红酒',
  },
  {
    label: '黄酒',
    value: '黄酒',
  },
  {
    label: '洋烈酒',
    value: '洋烈酒',
  },
]

const options = ref([
  {
    title: '肝病病史',
    selected: false,
    cssStyle: { marginLeft: '181px' },
    childrenStyle: { marginLeft: '110px' },
    children: [
      {
        title: '乙肝',
        selected: false,
        haveTimes: true,
        topTitle: '肝病病史',
        times: '',
        medical: [],
        cssStyle: { paddingTop: '14px', paddingBottom: '7px' },
      },
      {
        title: '肝硬化',
        topTitle: '肝病病史',
        selected: false,
        cssStyle: { paddingTop: '7px', paddingBottom: '7px' },
        childrenStyle: {
          marginLeft: '180px',
          background: '#f5f5f5',
          marginTop: '8px',
          padding: '14px',
        },
        children: [
          {
            title: '代偿期肝硬化',
            selected: false,
            haveTimes: true,
            cssStyle: { paddingBottom: '7px' },
            startTime: null,
            topTitle: '肝硬化',
          },
          {
            title: '失代偿期肝硬化',
            selected: false,
            haveTimes: true,
            startTime: null,
            topTitle: '肝硬化',
            cssStyle: { paddingTop: '7px', paddingBottom: '7px' },
          },
          {
            title: '上消化道出血',
            topTitle: '肝硬化',
            timeTitle: '首次出现时间',
            selected: false,
            cssStyle: { paddingTop: '7px', paddingBottom: '7px' },
            haveTimes: true,
            startTime: null,
            childrenStyle: { marginLeft: '130px' },
            treats: [
              {
                title: '胃镜下治疗',
                type: 'group',
                zlfn: null,
                cssStyle: {
                  paddingTop: '14px',
                  paddingBottom: '7px',
                },
                options: [
                  { label: '套扎', value: '套扎' },
                  { label: '硬化剂', value: '硬化剂' },
                ],
              },
              {
                title: '药物治疗',
                type: 'group',
                cssStyle: {
                  paddingTop: '7px',
                  paddingBottom: '7px',
                },
                options: [
                  { label: 'NSBB', value: 'NSBB' },
                  { label: 'PPI', value: 'PPI' },
                  { label: '生长抑素', value: '生长抑素' },
                ],
              },
              {
                title: '介入治疗',
                type: 'radio',
                zlfn: null,
                cssStyle: {
                  paddingTop: '7px',
                  paddingBottom: '7px',
                },
                options: [
                  { label: '无', value: '无' },
                  { label: '有', value: '有' },
                ],
              },
              {
                title: 'TIPs治疗',
                cssStyle: {
                  paddingTop: '7px',
                  paddingBottom: '7px',
                },
                type: 'radio',
                options: [
                  { label: '无', value: '无' },
                  { label: '有', value: '有' },
                ],
              },
              {
                title: '手术治疗',
                cssStyle: { paddingTop: '7px' },
                type: 'radio',
                options: [
                  {
                    label: '脾切除+断流',
                    value: '脾切除+断流',
                  },
                  {
                    label: '脾切除+分流',
                    value: '脾切除+分流',
                  },
                ],
              },
            ],
          },
          {
            title: '腹水',
            selected: false,
            timeTitle: '首次出现时间',
            topTitle: '肝硬化',
            haveTimes: true,
            childrenStyle: { marginLeft: '130px' },
            cssStyle: { paddingTop: '7px', paddingBottom: '7px' },
            startTime: null,
            treats: [
              {
                title: '药物治疗',
                type: 'group',
                cssStyle: {
                  paddingTop: '14px',
                  paddingBottom: '7px',
                },
                options: [
                  { label: '利尿剂', value: '利尿剂' },
                  { label: '白蛋白', value: '白蛋白' },
                  {
                    label: '特利加压素',
                    value: '特利加压素',
                  },
                ],
              },
              {
                title: '放腹水',
                type: 'radio',
                cssStyle: {
                  paddingTop: '7px',
                  paddingBottom: '7px',
                },
                options: [
                  { label: '无', value: '无' },
                  { label: '有', value: '有' },
                ],
              },
              {
                title: 'TIPs治疗',
                type: 'radio',
                cssStyle: { paddingTop: '7px' },
                options: [
                  { label: '无', value: '无' },
                  { label: '有', value: '有' },
                ],
              },
            ],
          },
          {
            title: '肝性脑病',
            timeTitle: '首次出现时间',
            topTitle: '肝硬化',
            haveTimes: true,
            cssStyle: { paddingTop: '7px', paddingBottom: '7px' },
            childrenStyle: { marginLeft: '130px' },
            selected: false,
            startTime: null,
            treats: [
              {
                title: '催便灌肠',
                type: 'radio',
                cssStyle: {
                  paddingTop: '14px',
                  paddingBottom: '7px',
                },
                options: [
                  { label: '无', value: '无' },
                  { label: '有', value: '有' },
                ],
              },
              {
                title: '药物治疗',
                type: 'group',
                cssStyle: {
                  paddingTop: '7px',
                  paddingBottom: '7px',
                },
                options: [
                  { label: '乳果糖', value: '乳果糖' },
                  { label: '利福昔明', value: '利福昔明' },
                  {
                    label: '门冬氨酸鸟氨酸',
                    value: '门冬氨酸鸟氨酸',
                  },
                ],
              },
              {
                title: '粪菌移植',
                type: 'radio',
                cssStyle: { paddingTop: '7px' },
                options: [
                  { label: '无', value: '无' },
                  { label: '有', value: '有' },
                ],
              },
            ],
          },
          {
            title: '肝肾综合征',
            timeTitle: '首次出现时间',
            topTitle: '肝硬化',
            haveTimes: true,
            childrenStyle: { marginLeft: '130px' },
            cssStyle: { paddingTop: '7px', paddingBottom: '7px' },
            selected: false,
            startTime: null,
            treats: [
              {
                title: '药物治疗',
                type: 'group',
                cssStyle: { paddingTop: '14px' },
                options: [
                  { label: '白蛋白', value: '白蛋白' },
                  {
                    label: '特利加压素',
                    value: '特利加压素',
                  },
                ],
              },
            ],
          },
          {
            title: '原发性腹膜炎',
            topTitle: '肝硬化',
            timeTitle: '首次出现时间',
            childrenStyle: { marginLeft: '130px' },
            haveTimes: true,
            cssStyle: { paddingTop: '7px', paddingBottom: '7px' },
            selected: false,
            startTime: null,
            treats: [
              {
                title: '抗生素治疗',
                type: 'radio',
                cssStyle: {
                  paddingTop: '14px',
                  paddingBottom: '7px',
                },
                options: [
                  { label: '无', value: '无' },
                  { label: '有', value: '有' },
                ],
              },
              {
                title: '放腹水',
                type: 'radio',
                cssStyle: {
                  paddingTop: '7px',
                  paddingBottom: '7px',
                },
                options: [
                  { label: '无', value: '无' },
                  { label: '有', value: '有' },
                ],
              },
              {
                title: '药物治疗',
                type: 'group',
                cssStyle: { paddingTop: '7px' },
                options: [
                  { label: '利尿剂', value: '利尿剂' },
                  { label: '白蛋白', value: '白蛋白' },
                  {
                    label: '特利加压素',
                    value: '特利加压素',
                  },
                ],
              },
            ],
          },
          {
            timeTitle: '首次出现时间',
            title: '其他并发症状',
            topTitle: '肝硬化',
            haveTimes: true,
            cssStyle: { paddingTop: '7px', paddingBottom: '7px' },
            selected: false,
            startTime: null,
            otherInput: '',
          },
        ],
      },
      {
        title: '丙肝',
        haveTimes: true,
        topTitle: '肝病病史',
        useDate: true, /// 使用时间节点
        selected: false,
        startTime: null,
        childrenStyle: {
          marginLeft: '180px',
          background: '#f5f5f5',
          marginTop: '7px',
          padding: '14px',
        },
        cssStyle: { paddingTop: '7px', paddingBottom: '7px' },
        treats: [
          {
            title: '药物治疗',
            type: 'radio',
            options: [
              { label: 'DDA', value: 'DDA' },
              { label: 'PEG-IFN', value: 'PEG-IFN' },
            ],
          },
        ],
      },
      {
        title: '酒精肝',
        topTitle: '肝病病史',
        haveTimes: true,
        selected: false,
        cssStyle: { paddingTop: '7px', paddingBottom: '7px' },
        startTime: null,
      },
      {
        title: '脂肪肝',
        topTitle: '肝病病史',
        haveTimes: true,
        selected: false,
        cssStyle: { paddingTop: '7px', paddingBottom: '7px' },
        startTime: null,
      },
      {
        title: '药物性肝损伤',
        topTitle: '肝病病史',
        haveTimes: true,
        useDate: true, /// 使用时间节点
        cssStyle: { paddingTop: '7px', paddingBottom: '7px' },
        selected: false,
        startTime: null,
      },
      {
        title: '自身免疫性肝病',
        topTitle: '肝病病史',
        haveTimes: true,
        childrenStyle: {
          marginLeft: '180px',
          background: '#f5f5f5',
          marginTop: '7px',
          padding: '14px',
        },
        cssStyle: { paddingTop: '7px', paddingBottom: '7px' },
        selected: false,
        startTime: null,
        treats: [
          {
            title: '药物治疗',
            type: 'radio',
            options: [
              { label: '糖皮质激素', value: '糖皮质激素' },
              { label: 'UDCA', value: 'UDCA' },
            ],
          },
        ],
      },
      {
        title: '肝衰竭',
        topTitle: '肝病病史',
        haveTimes: true,
        cssStyle: { paddingTop: '7px', paddingBottom: '7px' },
        selected: false,
        startTime: null,
      },
      {
        title: '肝脏肿瘤',
        topTitle: '肝病病史',
        haveTimes: true,
        selected: false,
        cssStyle: { paddingTop: '7px', paddingBottom: '7px' },
        startTime: null,
      },
      {
        title: '其他肝病',
        topTitle: '肝病病史',
        haveTimes: true,
        selected: false,
        useDate: true, /// 使用时间节点
        cssStyle: { paddingTop: '7px' },
        startTime: null,
        otherInput: '',
      },
    ],
  },
  {
    title: '肝病家族史',
    selected: false,
    childrenStyle: { marginLeft: '110px' },
    cssStyle: { marginLeft: '126px' },
    // tip: '请询问患者直系三代以内',
    children: [
      {
        title: '肝硬化',
        selected: false,
        chooses: [],
        options: selectsOptions,
        cssStyle: { paddingTop: '14px', paddingBottom: '7px' },
        topTitle: '肝病家族史',
      },
      {
        title: '肝癌',
        selected: false,
        chooses: [],
        options: selectsOptions,
        cssStyle: { paddingTop: '7px', paddingBottom: '7px' },
        topTitle: '肝病家族史',
      },
      {
        title: '乙肝',
        selected: false,
        chooses: [],
        options: selectsOptions,
        cssStyle: { paddingTop: '7px' },
        topTitle: '肝病家族史',
      },
    ],
  },
  {
    title: '个人史',
    selected: false,
    childrenStyle: { marginLeft: '110px' },
    cssStyle: { marginLeft: '140px' },
    children: [
      {
        type: 'yj',
        title: '是否饮酒',
        selected: false,
        yjzl: null,
        times: '',
        pjmryjl: '',
        jj: null,
        topTitle: '个人史',
        yjOptions,
        cssStyle: { paddingTop: '14px', paddingBottom: '7px' },
      },
      {
        type: 'xy',
        title: '是否吸烟',
        selected: false,
        times: '',
        topTitle: '个人史',
        pjmrxyl: '',
        cssStyle: { paddingTop: '7px' },
        jy: null,
      },
      {
        type: 'ysj',
        title: '是否服用味乐舒',
        selected: false,
        times: '',
        startTime: '',
        endTime: '',
        yfyl: '',
        stop: false,
        topTitle: '个人史',
        cssStyle: { paddingTop: '14px' },
      },
    ],
  },
  {
    title: '常见慢性病及肿瘤史',
    childrenStyle: { marginLeft: '110px' },
    selected: false,
    cssStyle: { marginLeft: '168px' },
    children: [
      {
        title: '高血压',
        selected: false,
        times: '',
        haveTimes: true,
        topTitle: '常见慢性病及肿瘤史',
        cssStyle: { paddingTop: '14px', paddingBottom: '7px' },
      },
      {
        title: '糖尿病',
        selected: false,
        times: '',
        haveTimes: true,
        topTitle: '常见慢性病及肿瘤史',
        cssStyle: { paddingTop: '7px', paddingBottom: '7px' },
      },
      {
        title: '血脂异常',
        selected: false,
        times: '',
        haveTimes: true,
        topTitle: '常见慢性病及肿瘤史',
        cssStyle: { paddingTop: '7px', paddingBottom: '7px' },
      },
      {
        title: '心血管疾病',
        selected: false,
        times: '',
        haveTimes: true,
        topTitle: '常见慢性病及肿瘤史',
        cssStyle: { paddingTop: '7px', paddingBottom: '7px' },
      },
      {
        title: 'COPD',
        selected: false,
        times: '',
        haveTimes: true,
        topTitle: '常见慢性病及肿瘤史',
        cssStyle: { paddingTop: '7px', paddingBottom: '7px' },
      },
      {
        title: '慢性肾病',
        selected: false,
        times: '',
        haveTimes: true,
        topTitle: '常见慢性病及肿瘤史',
        cssStyle: { paddingTop: '7px', paddingBottom: '7px' },
      },
      {
        title: '肿瘤',
        selected: false,
        times: '',
        haveTimes: true,
        topTitle: '常见慢性病及肿瘤史',
        cssStyle: { paddingTop: '7px', paddingBottom: '7px' },
      },
      {
        title: '其他',
        selected: false,
        haveTimes: true,
        cssStyle: { paddingTop: '7px', paddingBottom: '7px' },
        otherInput: '',
        selectTags: null,
        // type: 'input', /// 多选
        topTitle: '常见慢性病及肿瘤史',
      },
    ],
  },
  {
    title: '重大传染病及精神疾病史',
    childrenStyle: { marginLeft: '110px' },
    selected: false,
    cssStyle: { marginLeft: '140px' },
    children: [
      {
        title: '艾滋病',
        selected: false,
        times: '',
        haveTimes: true,
        cssStyle: { paddingTop: '14px', paddingBottom: '7px' },
        topTitle: '重大传染病及精神疾病史',
      },
      {
        title: '梅毒',
        selected: false,
        times: '',
        haveTimes: true,
        cssStyle: { paddingTop: '7px', paddingBottom: '7px' },
        topTitle: '重大传染病及精神疾病史',
      },
      {
        title: '结核',
        selected: false,
        timeTitle: '最近确诊时间',
        times: '',
        haveTimes: true,
        cssStyle: { paddingTop: '7px', paddingBottom: '7px' },
        topTitle: '重大传染病及精神疾病史',
      },
      {
        title: '寄生虫病',
        selected: false,
        timeTitle: '最近确诊时间',
        times: '',
        haveTimes: true,
        cssStyle: { paddingTop: '7px', paddingBottom: '7px' },
        topTitle: '重大传染病及精神疾病史',
      },
      {
        title: '精神疾病',
        selected: false,
        times: '',
        haveTimes: true,
        cssStyle: { paddingTop: '7px', paddingBottom: '7px' },
        topTitle: '重大传染病及精神疾病史',
      },
    ],
  },
  {
    title: '体格检查',
    childrenStyle: { marginLeft: '110px' },
    selected: false,
    cssStyle: { marginLeft: '140px' },
    children: [
      {
        title: '皮肤巩膜黄染',
        selected: false,
        times: '',
        haveTimes: false,
        cssStyle: { paddingTop: '14px', paddingBottom: '7px' },
        topTitle: '体格检查',
      },
      {
        title: '下肢水肿',
        selected: false,
        times: '',
        haveTimes: false,
        cssStyle: { paddingTop: '7px', paddingBottom: '7px' },
        topTitle: '体格检查',
      },
    ],
  },
  {
    title: '婚育史',
    childrenStyle: { marginLeft: '110px' },
    selected: false,
    cssStyle: { marginLeft: '140px' },
    children: [
      {
        title: '婚姻状况',
        type: 'select',
        selected: true,
        chooses: null,
        options: hyOptions,
        cssStyle: { paddingTop: '14px', paddingBottom: '7px' },
        topTitle: '婚育史',
      },
      {
        title: '生育状况',
        chooses: null,
        selected: true,
        type: 'select',
        options: syOptions,
        cssStyle: { paddingTop: '7px', paddingBottom: '7px' },
        topTitle: '婚育史',
      },
    ],
  },
  {
    title: '过敏史',
    childrenStyle: { marginLeft: '110px' },
    selected: false,
    cssStyle: { marginLeft: '140px' },
    children: [
      {
        title: ' 过敏原',
        type: 'input',
        selected: true,
        content: '',
        cssStyle: { paddingTop: '14px' },
        topTitle: '过敏史',
      },
    ],
  },
])

function useMedicalSaveConfirm(ss) {
  console.log(ss)
  modalDrugOptions.showImport = false
  switch (modalDrugOptions.key) {
    case '乙肝':
    case '慢乙肝':
      options.value[0].children[0].medical = cloneDeep(
        modalDrugOptions.data,
      )
      break
    case '高血压':
      options.value[2].children[0].medical = cloneDeep(
        modalDrugOptions.data,
      )
      break
    case '糖尿病':
      options.value[2].children[1].medical = cloneDeep(
        modalDrugOptions.data,
      )
      break

    case '血脂异常':
      options.value[2].children[2].medical = cloneDeep(
        modalDrugOptions.data,
      )
      break

    case '冠心病':
      options.value[2].children[3].medical = cloneDeep(
        modalDrugOptions.data,
      )
      break
  }
}

function visibleChange(bb) {
  if (!bb)
    modalDrugOptions.showImport = false
}

/** 用药调查表 */
function popDrugUse(param: string, array: []) {
  modalDrugOptions.showImport = true
  modalDrugOptions.title = `${param}用药调查表`
  modalDrugOptions.loadingImport = true
  modalDrugOptions.key = param

  getPatientDiseaseUseDrug(param === '乙肝' ? '慢乙肝' : param).then(
    (res) => {
      modalDrugOptions.loadingImport = false
      /// 处理一下 添加其他
      if (param !== '乙肝') {
        res?.data.push({
          drugName: '其他',
          type: 'other',
        })
      }
      // modalDrugOptions.data = res?.data ?? []
      /// 合并一下
      modalDrugOptions.data = _.merge(array, res?.data ?? [])
    },
  )

  // modalDrugOptions.loadingImport = false
  // modalDrugOptions.data = array
}

///
watch(
  options,
  () => {
    /// 回调出去
    emits('dataChange', options)
  },
  {
    deep: true,
    immediate: true,
  },
)

watch(
  followRight,
  () => {
    /// 回调出去
    emits('followRight', followRight)
  },
  {
    deep: true,
    immediate: true,
  },
)

function otherInputBlur(item) {
  /// warning 其他的输入
  // item.selected = item.otherInput.trim().length > 0
}

async function getDiseaseHis() {
  const res = await getPatientDiseaseHistory(props.patientId)
  const chb = JSON.parse(res?.data?.chbHistory || '{}') || {}
  /// 保存的已经选中的项目
  followRight.refuseStatus = res?.data?.refuseStatus
  followRight.refuseReason = res?.data?.refuseReason
  const selectItems = chb?.selectedItems
  ///
  if (chb.status == null) {
    /// 更改状态
    options.value[0].selected = true
    options.value[0].children[0].selected = true
    options.value[1].selected = true
    options.value[2].selected = true
    options.value[3].selected = true
  }

  /// 查一下保存的数据, 看看选中了没 fuck
  const gbbs = JSON.parse(res?.data?.liverComplaintHistory || '{}') || {}
  const gbjzs = JSON.parse(res?.data?.familyHistory || '{}') || {}
  const grs = JSON.parse(res?.data?.personalHistory || '{}') || {}
  const cjmxbs
    = JSON.parse(res?.data?.commonChronicDiseasesHistory || '{}') || {}
  const zdcrbs
    = JSON.parse(res?.data?.infectiousDiseaseHistory || '{}') || {}

  if (gbbs.selected) {
    options.value[0].selected = true
    if (gbbs.children[1].selected)
      options.value[0].children[1].selected = true
    if (gbbs.children[2].selected)
      options.value[0].children[2].selected = true
  }

  if (gbjzs.selected)
    options.value[1].selected = true

  if (grs.selected)
    options.value[2].selected = true

  if (cjmxbs.selected)
    options.value[3].selected = true

  if (zdcrbs.selected)
    options.value[4].selected = true

  /// 如果 status
  startFz(options.value, selectItems, null)

  /// 如果是未建档状态 要默认展开, 其他不默认展开
}

/// 给已保存的数据赋值
function startFz(array, selects, parent) {
  if (!array)
    return
  for (let index = 0; index < array.length; index++) {
    const element = array[index]
    // 到最后一层
    if (element.children) {
      startFz(element.children, selects, element)
    }
    else {
      /// 进入最后一层
      /// 去查找赋值
      for (let kindex = 0; kindex < selects?.length; kindex++) {
        const selItem = selects[kindex]
        if (
          (selItem.title === element.title
            || (element.title === '慢乙肝'
              && selItem.title === '乙肝'))
          && element.topTitle === selItem.topTitle
        ) {
          parent.selected = true
          Object.assign(element, selItem)
          ///  options 可能会变, 但是必须以最新数据为准 所以 options 需要重新赋值下
          if (element.yjOptions)
            element.yjOptions = yjOptions
          break
        }
        else {
          element.selected = false
        }
      }
    }
  }
}

/// 输入限制 正数
function onlyAllowNumber(value: string) {
  if (value.length === 0)
    return true
  return /^[1-9]\d*$/.test(value)
}

function disablePreviousDate(ts: number) {
  return ts > Date.now()
}

function yjsbchange(item) {
  const temp = {
    白酒: 0.4,
    洋烈酒: 0.344,
    啤酒: 0.04,
    红酒: 0.12,
    黄酒: 0.096,
  }
  item.pjmrycsrl = item.pjmryjl ? ((temp[item.yjzl] ?? 0) * item.pjmryjl).toFixed(2) : null
  console.log(temp[item.yjzl])
}

onMounted(() => {
  if (props.patientId?.length > 0)
    getDiseaseHis()
})

/// / 时间限制 结束时间大于开始时间
function isDateDisable(item, isStart, current) {
  console.log(current)
  if (isStart) {
    /// 是开始
    console.log(item.endTime, 'xxEnd')
    return current > Date.now() || (item.endTime && current > item.endTime)
  }
  else {
    /// 是结束
    console.log(current)
    return (
      current > Date.now() || (item.startTime && current < item.startTime)
    )
  }
}

/// 单选还要能取消单选
function radioZlfnSelected(val, item) {
  // console.log(val)
  if (val === item.zlfn) {
    /// 相同的又鸡儿点, 取消选中
    item.zlfn = null
  }
}

/// / 获取其他疾病数据
const otherOptions = reactive({
  data: [],
  loading: false,
})
function remoteGetOtherDisease(query: string) {
  otherOptions.loading = true
  getOtherDiseaseForHistory(1)
    .then((res) => {
      otherOptions.loading = false
      console.log(res)
      otherOptions.data = res.data ?? []
    })
    .catch((err) => {
      otherOptions.loading = false
      otherOptions.data = []
    })
}

function followSelectChange() {
  followRight.refuseReason = ''
}

function outerSelectChange(item) {
  switch (item.title) {
    case '婚育史':
      item.children[0].selected = item.selected
      item.children[1].selected = item.selected
      break
    case '过敏史':
      item.children[0].selected = item.selected
      break
    default:
      break
  }
}
</script>

<template>
  <div class="content">
    <div my-20px ml-127px flex items-center>
      <div mr-10px>
        随访意愿
      </div>
      <n-radio-group v-model:value="followRight.refuseStatus" name="radiogroup" @update:value="followSelectChange">
        <n-space item-style="display: flex;" :size="[40, 0]">
          <n-radio
            v-for="song in [
              { title: '接受', value: 0 },
              { title: '拒绝', value: 1 },
            ]" :key="song.title" :value="song.value"
          >
            {{ song.title }}
          </n-radio>
        </n-space>
      </n-radio-group>
      <div w-20px />
      <el-input
        v-if="followRight.refuseStatus" v-model="followRight.refuseReason" style="width: 300px;"
        placeholder="请输入拒绝原因"
      />
    </div>
    <div v-for="(item, index) in options" :key="index" class="bs-item">
      <div>
        <div flex items-center>
          <div mr-10px w-184px text-right>
            {{ item.title }}
          </div>
          <n-radio-group
            v-model:value="item.selected" name="radiogroup" @update:value="(v) => {
              outerSelectChange(item)
            }"
          >
            <n-space item-style="display: flex;" :size="[40, 0]">
              <n-radio
                v-for="song in [
                  { title: '无', value: false },
                  { title: '有', value: true },
                ]" :key="song.title" :value="song.value"
              >
                {{ song.title }}
              </n-radio>
            </n-space>
          </n-radio-group>
        </div>
        <div
          v-if="
            item.children
              && item.children.length > 0
              && item.selected
          " :style="item.childrenStyle"
        >
          <div v-for="(item2, index2) in item.children" :key="index2" :style="item2.cssStyle">
            <div>
              <div flex-inline items-center>
                <div mr-10px w-184px text-right>
                  {{ item2.title }}
                </div>
                <n-select
                  v-if="item2.type === 'select'" v-model:value="item2.chooses" :options="item2.options"
                  style="width: 120px;margin-left: 10px;"
                />
                <n-input
                  v-else-if="item2.type === 'input'" v-model:value="item2.content" mr-20px placeholder="请输入"
                  style="width: 134px; margin-left: 10px"
                />
                <n-radio-group v-else v-model:value="item2.selected" name="radiogroup">
                  <n-space item-style="display: flex;" :size="[40, 0]">
                    <n-radio
                      v-for="song in [
                        { title: '无', value: false },
                        { title: '有', value: true },
                      ]" :key="song.title" :value="song.value"
                    >
                      {{ song.title }}
                    </n-radio>
                  </n-space>
                </n-radio-group>
                <div v-if="item2.haveTimes && item2.selected" ml-20px flex items-center>
                  <n-input
                    v-if="
                      item2.title?.includes('其他')
                        && !item2.type
                    " v-model:value="item2.otherInput" mr-20px placeholder="请输入"
                    style="width: 134px; margin-left: 10px" @blur="otherInputBlur(item2)"
                  />
                  <el-select
                    v-if="
                      item2.title?.includes('其他')
                        && item2?.type === 'multiple'
                    " v-model="item2.selectTags" placeholder="请选择" default-first-option multiple remote reserve-keyword
                    collapse-tags collapse-tags-tooltip mr-20px :loading="otherOptions.loading" style="width: 135px"
                    @focus="remoteGetOtherDisease"
                  >
                    <el-option
                      v-for="diss in otherOptions.data" :key="diss.id" :label="diss.diseaseName"
                      :value="diss.diseaseName"
                    />
                  </el-select>
                  <!-- <n-select
                    v-if="item2.title?.includes('其他') && item2?.type === 'multiple'"
                    v-model:value="item2.selectTags"
                    placeholder="请选择(可多选)"
                    :options="otherOptions.data"
                    :loading="otherOptions.loading"
                    label-field="diseaseName"
                    value-field="diseaseName"
                    max-tag-count="responsive"
                    clearable remote multiple mr-20px
                    style="width: 135px;"
                    @update:show="remoteGetOtherDisease"
                  /> -->
                  <div>
                    {{ item2.timeTitle ?? "患病时长" }}
                  </div>
                  <!-- <el-date-picker
                    v-model="item2.startTime"
                    type="month"
                    placeholder="yyyy-mm"
                    clearable
                    :disabled-date="disablePreviousDate"
                    style="width: 164px; margin-left: 10px"
                  /> -->
                  <timesSelect
                    v-model:value="item2.startTime" ml-10px :width="164" placeholder="YY年MM月"
                    :date="item2.startTime"
                  />
                  <div
                    v-if="item2.medical" ml-20px flex items-center @click="
                      popDrugUse(
                        item2.title,
                        item2.medical,
                      )
                    "
                  >
                    <img h-16px w-16px src="@/assets/images/use_medical_report.png">
                    <span ml-8px color="#3B8FD9">{{
                      `${item2.title}用药调查表`
                    }}</span>
                  </div>
                </div>
              </div>
              <div
                v-if="
                  item2.children
                    && item2.children.length > 0
                    && item2.selected
                " :style="item2.childrenStyle"
              >
                <div v-for="(item3, index3) in item2.children" :key="index3" :style="item3.cssStyle">
                  <div>
                    <div flex items-center>
                      <div mr-10px w-100px text-right>
                        {{ item3.title }}
                      </div>
                      <n-radio-group v-model:value="item3.selected" name="radiogroup">
                        <n-space item-style="display: flex;" :size="[40, 0]">
                          <n-radio
                            v-for="song in [
                              {
                                title: '无',
                                value: false,
                              },
                              {
                                title: '有',
                                value: true,
                              },
                            ]" :key="song.title" :value="song.value"
                          >
                            {{ song.title }}
                          </n-radio>
                        </n-space>
                      </n-radio-group>
                      <div
                        v-if="
                          item3.haveTimes
                            && item3.selected
                        " ml-20px flex items-center
                      >
                        <n-input
                          v-if="
                            item3.title?.includes(
                              '其他',
                            )
                          " v-model:value="item3.otherInput
                          " mr-20px placeholder="请输入其他并发症" style="
                                                        width: 145px;
                                                        margin-left: 10px;
                                                    " @blur="
                                                      otherInputBlur(item2)
                                                      "
                        />
                        <div>
                          {{
                            item3.timeTitle
                              ?? "患病时长"
                          }}
                        </div>
                        <!-- <n-date-picker v-model:value="item3.startTime" placeholder="yyyy-mm" style="width: 164px;margin-left: 10px;" type="month" clearable :is-date-disabled="disablePreviousDate" /> -->
                        <!-- <el-date-picker
                          v-model="item3.startTime"
                          type="month"
                          placeholder="yyyy-mm"
                          clearable
                          :disabled-date="
                            disablePreviousDate
                          "
                          style="
                                                        width: 164px;
                                                        margin-left: 10px;
                                                    "
                        /> -->
                        <timesSelect
                          v-model:value="item3.startTime" ml-10px :width="164" placeholder="YY年MM月"
                          :date="item3.startTime"
                        />
                      </div>
                    </div>
                    <div
                      v-if="
                        item3.treats
                          && item3.treats.length > 0
                          && item3.selected
                      " :style="item3.childrenStyle"
                    >
                      <div
                        v-for="(
                          item4, index4
                        ) in item3.treats" :key="index4" :style="item4.cssStyle"
                      >
                        <div flex items-center>
                          <div mr-10px w-90px text-right>
                            {{ item4.title }}
                          </div>
                          <div
                            v-if="
                              item4.type
                                === 'radio'
                            "
                          >
                            <n-radio-group
                              v-model:value="item4.zlfn
                              " name="radiogroup"
                            >
                              <n-space item-style="display: flex;" :size="[40, 0]">
                                <n-radio
                                  v-for="song in item4.options" :key="song.value
                                  " :value="song.value
                                  " @click="() => {
                                    radioZlfnSelected(
                                      song.label,
                                      item4,
                                    );
                                  }
                                  "
                                >
                                  {{
                                    song.label
                                  }}
                                </n-radio>
                              </n-space>
                            </n-radio-group>
                          </div>
                          <div v-else>
                            <n-checkbox-group
                              v-model:value="item4.zlfn
                              "
                            >
                              <n-space item-style="display: flex;" :size="[40, 0]">
                                <n-checkbox
                                  v-for="song in item4.options" :key="song.value
                                  " :value="song.value
                                  "
                                >
                                  <span ml-10px>{{
                                    song.label
                                  }}</span>
                                </n-checkbox>
                              </n-space>
                            </n-checkbox-group>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div
                v-if="
                  item2.treats
                    && item2.treats.length > 0
                    && item2.selected
                " :style="item2.childrenStyle"
              >
                <div v-for="(zl, zindex) in item2.treats" :key="zindex" :style="zl.cssStyle" flex items-center>
                  <div mr-10px>
                    {{ zl.title }}
                  </div>
                  <div v-if="zl.type === 'radio'">
                    <n-radio-group v-model:value="zl.zlfn" name="radiogroup">
                      <n-space item-style="display: flex;" :size="[40, 0]">
                        <n-radio
                          v-for="song in zl.options" :key="song.value" :value="song.value" @click="() => {
                            radioZlfnSelected(
                              song.label,
                              zl,
                            );
                          }
                          "
                        >
                          {{ song.label }}
                        </n-radio>
                      </n-space>
                    </n-radio-group>
                  </div>
                  <div v-else>
                    <n-checkbox-group v-model:value="zl.zlfn">
                      <n-space item-style="display: flex;" :size="[40, 0]">
                        <n-checkbox v-for="song in zl.options" :key="song.value" :value="song.value">
                          <span ml-10px>{{
                            song.label
                          }}</span>
                        </n-checkbox>
                      </n-space>
                    </n-checkbox-group>
                  </div>
                </div>
              </div>
              <div
                v-if="
                  item2.topTitle === '肝病家族史'
                    && item2.selected
                " :style="{
                  display: 'inline-block',
                }"
              >
                <div v-if="item2.chooses" ml-25px flex items-center>
                  <div mr-10px>
                    与本人关系
                  </div>
                  <n-select
                    v-model:value="item2.chooses" style="width: 430px" multiple :options="item2.options"
                    clearable
                  />
                </div>
              </div>
              <div
                v-if="
                  item2.topTitle === '个人史'
                    && item2.selected
                "
              >
                <div v-if="item2.type === 'yj' && item2.selected" flex flex-wrap items-center class="grsBack">
                  <div flex items-center pt-14px>
                    <span color="#666" ml-20px>饮酒种类</span>
                    <n-select
                      v-model:value="item2.yjzl" :options="item2.yjOptions" style="
                                                width: 120px;
                                                margin-left: 10px;
                                            " @update:value="() => {
                                                yjsbchange(item2);
                                              }
                    "
                    />
                    <span color="#666" ml-20px>开始饮酒时间</span>
                    <!-- <n-date-picker v-model:value="item2.startTime" :is-date-disabled="(current) => { return isDateDisable(item2, true, current) }" style="width: 120px;margin-left: 10px;" type="month" clearable /> -->
                    <el-date-picker
                      v-model="item2.startTime" type="month" placeholder="yyyy-mm" clearable
                      :disabled-date="(current) => {
                        return isDateDisable(
                          item2,
                          true,
                          current,
                        );
                      }
                      " style="
                                                width: 120px;
                                                margin-left: 10px;
                                            "
                    />
                    <!-- <timesSelect v-model:value="item2.startTime" ml-10px :width="120" placeholder="YY年MM月" :date="item2.startTime" /> -->
                    <span color="#666" ml-20px>平均每日饮酒量</span>
                    <n-input
                      v-model:value="item2.pjmryjl" placeholder="请输入" :allow-input="onlyAllowNumber" style="
                                                width: 100px;
                                                margin-left: 10px;
                                            " @input="() => {
                                                yjsbchange(item2);
                                              }
                    "
                    >
                      <template #suffix>
                        <span color="#999">ml</span>
                      </template>
                    </n-input>
                  </div>
                  <div flex items-center pt-14px>
                    <span color="#666" ml-20px>平均每日乙醇摄入量</span>
                    <n-input
                      v-model:value="item2.pjmrycsrl" placeholder="自动计算" :allow-input="onlyAllowNumber" style="
                                                width: 100px;
                                                margin-left: 10px;
                                            "
                    >
                      <template #suffix>
                        <span color="#999">g</span>
                      </template>
                    </n-input>
                    <span ml-20px mr-10px>是否戒酒</span>
                    <n-radio-group v-model:value="item2.jj" name="radiogroup">
                      <n-radio :value="true">
                        是
                      </n-radio>
                      <div w-40px />
                      <n-radio :value="false">
                        否
                      </n-radio>
                    </n-radio-group>
                    <div v-if="item2.jj" ml-20px flex items-center>
                      <span>戒酒时间</span>
                      <!-- <n-date-picker v-model:value="item2.endTime" :is-date-disabled="(current) => { return isDateDisable(item2, false, current) }" style="width: 120px;margin-left: 10px;" type="month" clearable /> -->
                      <el-date-picker
                        v-model="item2.endTime" type="month" placeholder="yyyy-mm" clearable
                        :disabled-date="(current) => {
                          return isDateDisable(
                            item2,
                            false,
                            current,
                          );
                        }
                        " style="
                                                    width: 120px;
                                                    margin-left: 10px;
                                                "
                      />
                      <!-- <timesSelect v-model:value="item2.endTime" ml-10px :width="120" placeholder="YY年MM月" :date="item2.endTime" /> -->
                    </div>
                  </div>
                </div>
                <div v-if="item2.type === 'xy' && item2.selected" flex flex-wrap items-center class="grsBack">
                  <div flex items-center pt-14px>
                    <span color="#666" ml-20px>开始吸烟时间</span>
                    <!-- <n-date-picker v-model:value="item2.startTime" :is-date-disabled="(current) => { return isDateDisable(item2, true, current) }" style="width: 120px;margin-left: 10px;" type="month" clearable /> -->
                    <el-date-picker
                      v-model="item2.startTime" type="month" placeholder="yyyy-mm" clearable
                      :disabled-date="(current) => {
                        return isDateDisable(
                          item2,
                          true,
                          current,
                        );
                      }
                      " style="width: 120px; margin-left: 10px"
                    />
                    <!-- <timesSelect v-model:value="item2.startTime" ml-10px :width="120" placeholder="YY年MM月" :date="item2.startTime" /> -->
                    <span color="#666" ml-20px>平均每日吸烟量</span>
                    <n-input
                      v-model:value="item2.pjmrxyl" placeholder="请输入" :allow-input="onlyAllowNumber"
                      style="width: 100px; margin-left: 10px"
                    >
                      <template #suffix>
                        <span color="#999">支</span>
                      </template>
                    </n-input>
                    <span ml-20px mr-10px>是否戒烟</span>
                    <n-radio-group v-model:value="item2.jy" name="radiogroup">
                      <n-radio :value="true">
                        是
                      </n-radio>
                      <div w-40px />
                      <n-radio :value="false">
                        否
                      </n-radio>
                    </n-radio-group>
                  </div>
                  <div v-if="item2.jy" ml-20px flex items-center pt-14px>
                    <span>戒烟时间</span>
                    <!-- <n-date-picker v-model:value="item2.endTime" :is-date-disabled="(current) => { return isDateDisable(item2, false, current) }" style="width: 120px;margin-left: 10px;" type="month" clearable /> -->
                    <el-date-picker
                      v-model="item2.endTime" type="month" placeholder="yyyy-mm" clearable :disabled-date="(current) => {
                        return isDateDisable(
                          item2,
                          false,
                          current,
                        );
                      }
                      " style="
                                                width: 120px;
                                                margin-left: 10px;
                                            "
                    />
                    <!-- <timesSelect v-model:value="item2.endTime" ml-10px :width="120" placeholder="YY年MM月" :date="item2.endTime" /> -->
                  </div>
                </div>
                <div v-if="item2.type === 'ysj' && item2.selected" flex flex-wrap items-center class="grsBack">
                  <div flex items-center pt-14px>
                    <span color="#666" ml-20px>开始服用时间</span>
                    <!-- <timesSelect v-model:value="item2.startTime" ml-10px :width="120" placeholder="YY年MM月" :date="item2.startTime" /> -->
                    <el-date-picker
                      v-model="item2.startTime" type="month" placeholder="yyyy-mm" clearable
                      :disabled-date="(current) => {
                        return isDateDisable(
                          item2,
                          true,
                          current,
                        );
                      }
                      " style="
                                                width: 120px;
                                                margin-left: 10px;
                                            "
                    />
                    <span color="#666" ml-20px>用法用量</span>
                    <el-input v-model="item2.yfyl" style="width: 90px;margin-left: 10px" placeholder="输入">
                      <template #suffix>
                        <span>条/天</span>
                      </template>
                    </el-input>
                    <n-checkbox v-model:checked="item2.stop" ml-20px>
                      <span mx-10px>已停</span>
                    </n-checkbox>
                    <!-- <n-date-picker v-if="kitem.stop" v-model:value="kitem.endTime" style="width: 110px;" type="month" clearable :is-date-disabled="disablePreviousDate" /> -->
                    <!-- <timesSelect v-if="item2.stop" v-model:value="item2.endTime" ml-10px :width="120" placeholder="YY年MM月" :date="item2.endTime" /> -->
                    <el-date-picker
                      v-if="item2.stop" v-model="item2.endTime" type="month" placeholder="yyyy-mm"
                      clearable :disabled-date="(current) => {
                        return isDateDisable(
                          item2,
                          false,
                          current,
                        );
                      }
                      " style="
                                                width: 120px;
                                                margin-left: 10px;
                                            "
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <UseMedicalModal
      v-model:modal-drug-options="modalDrugOptions" @ok="useMedicalSaveConfirm"
      @visible-change="visibleChange"
    />
  </div>
</template>

<style lang="scss" scoped>
.bs-item {
  padding-top: 13px;
  padding-bottom: 13px;
  border-top: 1px dashed #ccc;
}

.bs-item:first-of-type {
  padding-top: 14px;
}

.bs-item:last-of-type {
  padding-bottom: 14px;
}

.bs-item:first-child {
  border-top: none;
}

.grsBack {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  margin-top: 8px;
  padding-bottom: 14px;
  margin-left: 122px;
  flex: 1;
}
</style>
