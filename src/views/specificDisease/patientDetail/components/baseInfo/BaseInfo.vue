<script lang="ts" setup>
import { useRoute } from 'vue-router'
import dayjs from 'dayjs'
import InfoItem from './InfoItem.vue'
import { CardType } from './constant'
import { SvgIcon } from '@/components/Icon'

import type { PatientInfoModal } from '@/api/patient'
import { getEarlySeriousChecks, getPatientDiseaseHistory, getPatientInfoApi } from '@/api/patient'

const emit = defineEmits(['toggleComp'])
const route = useRoute()
const PATIENT_ID = route.query?.patientId as string
const TYPE = route.query?.type as string

const importAssays = ref()
const assays = ref([])
const followRefuse = reactive({})
/** 患者信息 */
const patientInfo = ref<PatientInfoModal | null>({
  age: '',
  birthday: '',
  bloodPressure: '',
  bloodType: '',
  bmi: '',
  companyName: '',
  companyTel: '',
  companyType: '',
  contactsAddr: '',
  contactsName: '',
  contactsRelation: '',
  contactsTel: '',
  countryName: '',
  createTime: '',
  culturalName: '',
  familyMembers: '',
  heartRate: '',
  height: '',
  homeAddr: '',
  id: '',
  income: '',
  lastVisit: '',
  liveAddr: '',
  liverComplaintType: 0,
  marriageName: '',
  medicalInsuranceNo: '',
  medicalInsuranceType: '',
  nationCode: '',
  nationName: '',
  patientIdCardNum: '',
  patientIdCardType: '',
  patientName: '',
  patientRecordNo: '',
  patientSource: '',
  phone: '',
  phySource: '',
  phyUpdateTime: '',
  primaryIndex: '',
  professionName: '',
  pulse: '',
  respiratoryRate: '',
  sexCode: '',
  sexName: '',
  slmcNo: '',
  status: 0,
  tag: '',
  v1Visit: '',
  weight: '',
})

const diseaseHis = ref()
const diseaseTextMap = reactive({
  肝病病史: '',
  肝病家族史: '',
  常见慢性病及肿瘤史: '',
  个人史: '',
  重大传染病及精神疾病史: '',
  体格检查: '',
  婚育史: '',
  过敏史: '',
})

// 标签宽度
const LABEL_WIDTH = {
  // 人口学信息
  demography: ['120', '120'],
}

/**
 * 获取患者信息
 * @param patientId 患者id
 */
async function getPatientInfo(patientId: string) {
  const res = await getPatientInfoApi<PatientInfoModal>(patientId)

  if (res?.data) {
    patientInfo.value = { ...res.data }
    tipTheJobAndLearnInput()
  }
}

async function getPatientDiseaseInfo(patientId) {
  const res = await getPatientDiseaseHistory(patientId)
  if (res?.data) {
    /// sb解析
    handleTheSelectedDiseaseText([
      JSON.parse(res.data?.liverComplaintHistory),
      JSON.parse(res.data?.familyHistory),
      JSON.parse(res.data?.personalHistory),
      JSON.parse(res.data?.commonChronicDiseasesHistory),
      JSON.parse(res.data?.infectiousDiseaseHistory),
      JSON.parse(res.data?.physicalExam),
      JSON.parse(res.data?.maritalHistory),
      JSON.parse(res.data?.allergyHistory),
    ])
    console.log(diseaseTextMap)
    followRefuse.refuseStatus = res?.data?.refuseStatus
    followRefuse.refuseReason = res?.data?.refuseReason
  }
}

function goEditFile(index) {
  emit('toggleComp', {
    name: 'EDIT',
    index,
  })
}
/** 初始化函数 */
function init() {
  PATIENT_ID && getPatientInfo(PATIENT_ID)
  PATIENT_ID && getPatientDiseaseInfo(PATIENT_ID)
  PATIENT_ID && getImportantChecks()
}

function showText(str) {
  if (str == null || str.trim().length === 0)
    return '-'
  return str
}

function handleTheSelectedDiseaseText(array) {
  //
  const sarray = unref(array)
  if (!sarray)
    return
  for (let index = 0; index < sarray.length; index++) {
    const element = sarray[index]
    if (element.selected || element.topTitle === '体格检查') {
      // 到最后一层
      if (element.children)
        handleTheSelectedDiseaseText(element.children)
      else cacleTheTimeOfsbhistory(element)
    }
  }
}

/// 计算这个傻逼病史选择的时间
function cacleTheTimeOfsbhistory(item) {
  /// 进来就是 已经选中的了
  let jb = item.title
  switch (item.topTitle) {
    case '肝病病史':
    case '常见慢性病及肿瘤史': {
      if (item.startTime) {
        if (item.title === '其他' && item.type === 'multiple') { jb = item.selectTags?.join('、') ?? '' }
        else {
          jb
            = (item.title === '其他肝病' || item.title === '其他')
              ? item.otherInput
              : item.title
        }
        jb += item.title === '乙肝' ? '患病' : '确诊'
        // 计算时长
        item.check = true
        const sc = dayjsDiffTimeSB(item.startTime)
        if (sc.length > 0 && !item.useDate) {
          jb += `${sc}`
        }
        else {
          if (item.title === '其他' && item.type === 'multiple') {
            const tag = item.selectTags?.join('、')
            jb
              = tag?.length > 0
                ? `${dayjs(item.startTime).format(
                  'YYYY年MM',
                )}确诊${tag}`
                : ''
          }
          else {
            jb = `${dayjs(item.startTime).format('YYYY年MM')}确诊${item.title === '其他肝病'
                ? item.otherInput
                : item.title
              }`
          }
        }
        /// 有治疗方案的++
        const zl = treatFangAnString(item)
        jb += `${zl.length > 0
            ? `,${zl}\n`
            : item.title === '乙肝'
              ? ''
              : ';\n'
          }`
      }
      else {
        if (item.title === '其他' && item.type === 'multiple') { jb = item.selectTags?.join('、') ?? '' }
        else {
          jb
            = item.title === '其他肝病'
              ? item.otherInput
              : item.title
        }
        if (item.title !== '乙肝') {
          /// 有治疗方案的++
          const zl = treatFangAnString(item)
          jb += `${zl.length > 0
              ? `,${zl}\n`
              : item.title === '乙肝'
                ? ''
                : ';\n'
            }`
        }
        item.check = false
      }
      if (jb === ';\n')
        jb = ''
      /// 看看乙肝有没有 药品
      if (item.title === '乙肝') {
        const bb = handleTheUseMedicalString(item.medical, [], item)
        console.log(bb)
        const stt = bb.join(',')
        jb += `${stt?.length > 0 ? `,${stt};\n` : ';\n'}`
      }
      break
    }
    case '肝病家族史':
      if (item.chooses?.length > 0) {
        const qs = item.chooses.join('、')
        jb = `${qs}患有${item.title};\n`
        item.check = true
      }
      else {
        jb += ';\n'
      }
      break
    case '个人史':
      if (item.type === 'yj') {
        jb = `饮${item?.yjzl?.length > 0 ? item?.yjzl : '酒'
          }${dayjsDiffTimeSB(item.startTime, item.endTime)}${item?.pjmrycsrl > 0
            ? `,平均每日乙醇摄入${item.pjmrycsrl}g`
            : ''
          }${item.jj
            ? item.endTime
              ? `,于${dayjs(item.endTime).format(
                'YYYY年MM',
              )}月戒酒`
              : ',已戒'
            : item.jj == null
              ? ''
              : ',未戒'
          };\n`
        item.check = item?.yjzl?.length > 0 && item?.pjmrycsrl > 0
      }
      else if (item.type === 'xy') {
        jb = `吸烟${dayjsDiffTimeSB(item.startTime, item.endTime)}${item?.pjmrxyl?.length > 0
            ? `,平均每日${item.pjmrxyl}根`
            : ''
          }${item.jy
            ? item.endTime
              ? `,于${dayjs(item.endTime).format(
                'YYYY年MM',
              )}月戒烟`
              : ',已戒'
            : item.jy == null
              ? ''
              : ',未戒'
          };\n`
        item.check = item?.pjmrxyl?.length > 0
      }
      else if (item.type === 'ysj') {
        /// 傻逼
        jb = `服用味乐舒${dayjsDiffTimeSB(item.startTime, item.endTime)}${item.yfyl ? `,每天${item.yfyl}条` : ''}${item.stop
          ? `,于${dayjs(item.endTime).format(
            'YYYY年MM',
          )}月停用`
          : ''}`
      }
      break
    case '重大传染病及精神疾病史':
      if (item.startTime) {
        jb = `曾在${dayjs(item.startTime).format('YYYY年MM')}月确诊${item.title
          };\n`
        item.check = true
      }
      else {
        jb += ';\n'
      }
      break
    case '肝硬化':
      /// 处理肝硬化
      if (item?.title?.includes('肝硬化')) {
        jb = item.startTime
          ? `于${dayjs(item.startTime).format('YYYY年MM')}月确诊${item.title
          };\n`
          : `${item.title};\n`
        item.check = item.startTime != null
      }
      else {
        /// 并发症
        jb = item.startTime
          ? `于${dayjs(item.startTime).format('YYYY年MM')}月首次出现${item.title === '其他并发症状'
            ? item.otherInput
            : item.title
          }`
          : `出现${item.title === '其他并发症状'
            ? item.otherInput
            : item.title
          }`
        item.check = item.startTime != null
        /// 查看治疗方式
        const zl = treatFangAnString(item)
        jb += `${zl.length > 0 ? `,${zl}\n` : ';\n'}`
        if (
          item.title === '其他并发症状'
          && item?.otherInput?.trim().length === 0
        )
          jb = ''
      }
      break
    case '体格检查':
      jb = `${item.title.slice(0, item.title.length - 2)}${item.selected ? '有' : '未见'}${item.title.slice(item.title.length - 2)};\n`
      break
    case '婚育史':
      jb = item.chooses || ''
      break
    case '过敏史':
      jb = item?.content?.length > 0 ? `${item.content}过敏` : ''
      break
    default:
      break
  }
  /// 处理下肝硬化
  const key = item.topTitle === '肝硬化' ? '肝病病史' : item.topTitle
  /// 如果是肝硬化
  if (diseaseTextMap[key])
    diseaseTextMap[key] += jb
  else diseaseTextMap[key] = jb
}

/// 治疗方式字符串拼接
function treatFangAnString(item) {
  let temp = ''
  if (item.treats && item.treats?.length > 0) {
    item.treats.forEach((element) => {
      let sb = '有进行'
      if (element.type === 'radio') {
        /// 单选
        if (element?.zlfn?.length > 0 && element?.zlfn !== '无') {
          sb += `${element.zlfn === '有' ? '' : `${element.zlfn}的`}${element.title
            };`
        }
        else { sb = '' }
      }
      else {
        /// 多选
        if (element?.zlfn?.length > 0)
          sb += `${element.zlfn.join('、')}的${element.title};`
        else sb = ''
      }
      temp += sb
    })
  }
  return temp.length > 0 ? temp : ''
}

function dayjsDiffTimeSB(start, end?: any) {
  if (end == null)
    end = dayjs()
  else end = dayjs(end)
  const diffMon = end.diff(dayjs(start), 'month')
  const year = Math.trunc(diffMon / 12)
  const month = diffMon % 12

  return `${year === 0 ? '' : `${year}年`}${month === 0 ? '' : `${month}个月`
    }`
}

function handleTheUseMedicalString(medical, array, parentNode) {
  if (medical == null)
    return array
  for (let index = 0; index < medical.length; index++) {
    const element = medical[index]
    if (element.children?.length > 0 && element.select) {
      handleTheUseMedicalString(element.children, array, element)
    }
    else {
      if (element.select) {
        if (element.type === 'other') {
          array.push(
            `${element.drugInput == null
              || element?.drugInput?.trim()?.length === 0
              ? ''
              : `${element?.drugInput}`
            }${element.times == null
              || element?.times?.trim()?.length === 0
              ? ''
              : `${element?.times}年`
            }${element.stop ? '已停药' : ''}`,
          )
        }
        else {
          let sttr = `${element.startTime
              ? `${dayjs(element.startTime).format(
                'YYYY年MM',
              )}月开始服用${element.drugName}${element.endTime && element.stop
                ? dayjsDiffTimeSB(
                  element.startTime,
                  element.endTime,
                )
                : `至今${dayjsDiffTimeSB(
                  element.startTime,
                  element.endTime,
                )}`
              }`
              : `服用${element.drugName}`
            }${element.endTime && element.stop
              ? `,于${dayjs(element.endTime).format(
                'YYYY年MM',
              )}月停药`
              : ''
            }`
          if (element.drugName === 'Peg-IFN')
            sttr = sttr.replace('服用', '使用')
          if (element.drugName === '胸腺法新')
            sttr = sttr.replace('服用', '注射')
          array.push(sttr)
        }
        /// 看看填写是否完整
        if ((element.stop && element.endTime) || element?.startTime)
          parentNode.check = true
        else parentNode.check = false
      }
    }
  }

  return array
}

function getImportantChecks() {
  getEarlySeriousChecks(PATIENT_ID).then((res) => {
    console.log(res)
    importAssays.value = res?.data

    const p1 = {
      title: '最近一次',
    }
    const p2 = {
      title: '距今(天)',
    }
    for (const key in res?.data) {
      const element = res?.data[key]
      p1[key] = element ? dayjs(element).format('YYYY-MM-DD') : '-'
      p2[key] = '-'
      if (element)
        p2[key] = dayjs().diff(dayjs(element), 'day')
    }
    assays.value = [p1, p2]
    console.log(assays.value)
  }).catch((_) => { })
}

onMounted(() => {
  init()
})

/// 检查 必填的两项是否填写, 没填写 提醒填写
function tipTheJobAndLearnInput() {
  const tip = '请将患者基础信息中的'
  let pp = ''
  if (patientInfo.value?.culturalName == null || patientInfo.value.culturalName?.length === 0)
    pp = ' 学历 '
  if (patientInfo.value?.professionName == null || patientInfo.value?.professionName.length === 0)
    pp += ' 职业类型 '
  if (pp.length > 0)
    window.$message.warning(`${tip + pp}填写完整`)
}
</script>

<template>
  <div class="p-14px">
    <!-- 人口学信息 -->
    <section>
      <SectionTitle mb-15px w-full>
        <div w-full flex flex-justify-between>
          <span text="#333">基础信息</span>
          <div flex cursor-pointer items-center @click="goEditFile(0)">
            <SvgIcon local-icon="slmc-icon-edit1" size="14" />
            <span ml-10px text="14px #3B8FD9">编辑</span>
          </div>
        </div>
      </SectionTitle>
      <n-divider style="margin: 0 0 14px 0" />
      <n-grid x-gap="14" y-gap="14" :cols="2" class="text-#333">
        <n-gi>
          <InfoItem label="加入SLMC日期：" :label-width="LABEL_WIDTH.demography[0]">
            <span>{{
              dayjs(patientInfo?.createTime).format(
                "YYYY-MM-DD HH:mm:ss",
              )
            }}</span>
          </InfoItem>
        </n-gi>
        <n-gi>
          <InfoItem label="SLMC编号：" :value="patientInfo?.slmcNo" :label-width="LABEL_WIDTH.demography[1]" />
        </n-gi>
        <n-gi>
          <InfoItem label="建档时间：" :label-width="LABEL_WIDTH.demography[0]">
            <span>{{
              patientInfo?.createRecordTime
                ? dayjs(patientInfo?.createRecordTime).format(
                  "YYYY-MM-DD",
                )
                : "-"
            }}</span>
          </InfoItem>
        </n-gi>
        <n-gi>
          <InfoItem label="是否纳入随访：" :value="patientInfo?.status" :label-width="LABEL_WIDTH.demography[0]">
            <SvgIcon v-if="patientInfo?.status === 1" local-icon="slmc-icon-shi" size="16" />
            <SvgIcon v-else local-icon="slmc-icon-fou-hui" size="16" />
          </InfoItem>
        </n-gi>
        <n-gi>
          <InfoItem label="姓名：" :value="patientInfo?.patientName" :label-width="LABEL_WIDTH.demography[1]" />
        </n-gi>
        <n-gi>
          <InfoItem label="性别：" :value="patientInfo?.sexName" :label-width="LABEL_WIDTH.demography[0]" />
        </n-gi>
        <n-gi>
          <InfoItem label="出生年月：" :value="patientInfo?.birthday" :label-width="LABEL_WIDTH.demography[1]" />
        </n-gi>
        <n-gi>
          <InfoItem label="年龄：" :label-width="LABEL_WIDTH.demography[0]">
            <span>{{
              `${patientInfo?.age
                ? `${patientInfo?.age}${patientInfo?.ageUnit || "岁"
                }`
                : "-"
              }`
            }}</span>
          </InfoItem>
        </n-gi>
        <n-gi>
          <InfoItem label="学历：" :label-width="LABEL_WIDTH.demography[0]">
            <span>{{ patientInfo?.culturalName || '-' }}</span>
          </InfoItem>
        </n-gi>
        <n-gi>
          <InfoItem label="国籍：" :label-width="LABEL_WIDTH.demography[1]">
            <span>{{ patientInfo?.countryName ?? "-" }}</span>
          </InfoItem>
        </n-gi>
        <n-gi>
          <InfoItem label="民族：" :value="patientInfo?.nationName" :label-width="LABEL_WIDTH.demography[0]" />
        </n-gi>
        <n-gi>
          <InfoItem label="证件类型：" :label-width="LABEL_WIDTH.demography[1]">
            <span>{{
              patientInfo?.patientIdCardType
                ? CardType[patientInfo?.patientIdCardType]
                : "-"
            }}</span>
          </InfoItem>
        </n-gi>
        <n-gi>
          <InfoItem label="证件号：" :value="patientInfo?.patientIdCardNum" :label-width="LABEL_WIDTH.demography[0]" />
        </n-gi>
        <n-gi>
          <InfoItem label="手机号：" :value="patientInfo?.phone" :label-width="LABEL_WIDTH.demography[1]" />
        </n-gi>

        <n-gi>
          <InfoItem label="紧急联系人：" :value="patientInfo?.contactsName" :label-width="LABEL_WIDTH.demography[0]" />
        </n-gi>
        <n-gi>
          <InfoItem label="紧急联系人电话：" :value="patientInfo?.contactsTel" :label-width="LABEL_WIDTH.demography[1]" />
        </n-gi>
        <n-gi>
          <InfoItem label="职业类别：" :value="patientInfo?.professionName" :label-width="LABEL_WIDTH.demography[0]" />
        </n-gi>
        <n-gi>
          <InfoItem label="医保性质：" :value="patientInfo?.medicalInsuranceType" :label-width="LABEL_WIDTH.demography[1]" />
        </n-gi>
        <n-gi>
          <InfoItem label="医保卡号：" :value="patientInfo?.medicalInsuranceNo" :label-width="LABEL_WIDTH.demography[0]" />
        </n-gi>
        <n-gi>
          <InfoItem label="居民健康卡号：" :value="patientInfo?.healthCardNum" :label-width="LABEL_WIDTH.demography[1]" />
        </n-gi>
        <n-gi>
          <InfoItem label="婚姻状况：" :value="patientInfo?.marriageName" :label-width="LABEL_WIDTH.demography[1]" />
        </n-gi>
        <n-gi>
          <InfoItem label="家庭成员：" :value="patientInfo?.familyMembers" :label-width="LABEL_WIDTH.demography[0]" />
        </n-gi>
        <n-gi>
          <InfoItem label="家庭收入：" :value="patientInfo?.income" :label-width="LABEL_WIDTH.demography[1]" />
        </n-gi>
        <n-gi>
          <InfoItem label="工作性质：" :value="patientInfo?.companyType" :label-width="LABEL_WIDTH.demography[0]" />
        </n-gi>
        <n-gi>
          <InfoItem label="户口所在地：" :value="patientInfo?.homeAddr" :label-width="LABEL_WIDTH.demography[1]" />
        </n-gi>
        <n-gi>
          <InfoItem label="现住址：" :value="patientInfo?.liveAddr" :label-width="LABEL_WIDTH.demography[0]" />
        </n-gi>
      </n-grid>
    </section>
    <section mt-25px>
      <SectionTitle mb-15px>
        <div w-full flex flex-justify-between>
          <span text="#333">最近一次主要检查</span>
        </div>
      </SectionTitle>
      <el-table :data="assays" border style="width: 100%">
        <el-table-column align="center" prop="title" />
        <el-table-column v-for="(value, key) in importAssays" :key="key" align="center" :label="key">
          <template #default="scope">
            {{ scope?.row?.[key] }}
          </template>
        </el-table-column>
        <el-table-column v-if="!importAssays" align="center" label="暂无检查" />
      </el-table>
    </section>
    <section mt-25px>
      <SectionTitle mb-15px>
        <div w-full flex flex-justify-between>
          <span text="#333">病史信息</span>
          <div
            v-if="
              (patientInfo?.diagnoseRecordStatus !== 0 && patientInfo?.diagnoseRecordStatus !== 4)
                && patientInfo?.diagnoseRecordStatus
            " flex cursor-pointer items-center @click="goEditFile(1)"
          >
            <SvgIcon local-icon="slmc-icon-edit1" size="14" />
            <span ml-10px text="14px #3B8FD9">编辑</span>
          </div>
        </div>
      </SectionTitle>
      <n-divider style="margin: 0 0 14px 0" />
      <div
        v-if="
          (patientInfo?.diagnoseRecordStatus !== 0 && patientInfo?.diagnoseRecordStatus !== 4)
            && patientInfo?.diagnoseRecordStatus
        "
      >
        <div mb-14px flex items-center>
          <div w-160px text-right>
            随访意愿:
          </div>
          <div ml-10px>
            {{ followRefuse?.refuseStatus === 0 ? '接受' : `拒绝 (${followRefuse?.refuseReason})` }}
          </div>
        </div>
        <div v-for="(value, key) in diseaseTextMap" :key="key" mb-12px flex items-start text-14px>
          <div mr-10px w-160px text-right text="#666">
            {{ `${key}:` }}
          </div>
          <div flex-1 text="#333" ws-pre-line>
            {{
              value == null || value?.trim()?.length === 0
                ? "无"
                : value
            }}
          </div>
        </div>
      </div>
      <n-empty v-else description="未建档">
        <div flex-col items-center>
          <span>未建档</span>
          <n-button color="#06AEA6" ghost mt-10px @click="goEditFile(1)">
            去建档
          </n-button>
        </div>
      </n-empty>
    </section>
  </div>
</template>

<style scoped lang="scss">
.planStatus {
  display: flex;
  align-items: center;

  &::before {
    display: block;
    content: "";
    width: 6px;
    height: 6px;
    border-radius: 50%;
    margin-right: 6px;
  }
}

.table-left {
  width: 120px;
  background: #f5f5f5;
  text-align: right;
  color: #666;
}

.n-table td {
  border-bottom-color: #d1d1d1;
  border-right-color: #d1d1d1;
}

.n-table.n-table--bordered {
  border-color: #d1d1d1;
}
</style>
