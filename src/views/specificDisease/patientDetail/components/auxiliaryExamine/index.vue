<script lang='ts' setup>
import { Gastroscopy, LiverB, LiverBiopsy, LiverCT, LiverCarotid, LiverHardness, LiverMRI } from './components'

enum TABS {
  BUltrasound = 'B_ULTRASOUND',
  Hardness = 'HARDNESS',
  CT = 'CT',
  MRI = 'MRI',
  CarotidUltrasound = 'CAROTID_ULTRASOUND',
  LiverBiopsy = 'LIVER_BIOPSY',
  Gastroscopy = 'GASTROSCOPY',

}

const route = useRoute()

const exmTab = route.query?.exmTab as string

const defaultTagValue = ref(exmTab || TABS.Hardness)
</script>

<template>
  <div class="auxiliaryExamine">
    <n-tabs animated :width="120" :default-value="defaultTagValue">
      <n-tab-pane :name="TABS.Hardness" tab="肝脏硬度测定">
        <LiverHardness />
      </n-tab-pane>
      <n-tab-pane :name="TABS.BUltrasound" tab="肝胆脾胰B超">
        <LiverB />
      </n-tab-pane>
      <n-tab-pane :name="TABS.CT" tab="肝脏CT">
        <LiverCT />
      </n-tab-pane>
      <n-tab-pane :name="TABS.MRI" tab="肝脏MRI">
        <LiverMRI />
      </n-tab-pane>
      <n-tab-pane :name="TABS.LiverBiopsy" tab="肝脏活组织检查">
        <LiverBiopsy />
      </n-tab-pane>
      <n-tab-pane :name="TABS.CarotidUltrasound" tab="颈动脉超声">
        <LiverCarotid />
      </n-tab-pane>
      <n-tab-pane :name="TABS.Gastroscopy" tab="胃镜检查">
        <Gastroscopy />
      </n-tab-pane>
    </n-tabs>
  </div>
</template>

<style scoped lang="scss">
.auxiliaryExamine {
    margin-top: 2px;
    // padding:0 14px 14px 14px;
    height: calc(100vh - 221px);

}
</style>
