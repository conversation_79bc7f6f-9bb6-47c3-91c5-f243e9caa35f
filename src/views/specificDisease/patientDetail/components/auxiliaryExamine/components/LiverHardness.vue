<script lang='ts' setup>
import dayjs from 'dayjs'
import { onMounted } from 'vue'
import { BasicTable } from '@/components/Table'
import { type ECOption, useEcharts } from '@/hooks'
import { getLsmAllListApi, getLsmListApi } from '@/api/specificDisease'
import type { LsmListModal } from '@/api/specificDisease'

const route = useRoute()

const PATIENT_ID = route.query?.patientId as string
const EXAM_PROJECT_NAME = '肝脏硬度测定'
const showChart = ref<boolean>(false)

const lineOptions = ref<ECOption>({
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      label: {
        backgroundColor: '#6a7985',
      },

    },
    backgroundColor: 'rgba(0,0,0,0.70)',
    textStyle: {
      color: '#fff',
      fontSize: 12,
    },
  },
  legend: {
    data: ['肝脏脂肪检测 (CAP)', '肝脏硬度值测定 (LSM)'],
    right: 14,
    itemWidth: 16,
    itemHeight: 8,
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true,
  },
  xAxis: {
    type: 'category',

    color: 'rgba(153,153,153,1)',
    axisLabel: {
      formatter(val) {
        // 当字符串长度超过2时
        if (val.length > 2) {
          // 把字符串分割成字符串数组
          const array = val.split(' ')
          // 在下标2处删除0个，加上回车
          array.splice(1, 0, '\n')
          return array.join('')
        }
        else {
          return val
        }
      },
    },

    data: [],

  },
  yAxis: [
    {
      type: 'value',
      name: 'dB/m',
      position: 'left',
      alignTicks: true,
      axisLine: {
        show: true,
        lineStyle: {
          color: '#5B96FD',
        },
      },

    },
    {
      type: 'value',
      name: 'KPa',
      position: 'right',
      alignTicks: true,
      axisLine: {
        show: true,
        lineStyle: {
          color: '#FFC569',
        },
      },
    },
  ],
  series: [
    {
      color: '#5B96FD',
      name: '肝脏脂肪检测 (CAP)',
      type: 'line',
      emphasis: {
        focus: 'series',
      },
      data: [],
      yAxisIndex: 0,
    },
    {
      color: '#FFC569',
      name: '肝脏硬度值测定 (LSM)',
      type: 'line',
      emphasis: {
        focus: 'series',
      },
      data: [],
      yAxisIndex: 1,
    },
  ],
}) as unknown as Ref<ECOption>
const { domRef: lineRef } = useEcharts(lineOptions)

// 表格列初始化
function createColumns() {
  return [
    {
      title: '序号',
      key: 'index',
      width: 70,
      render(_: LsmListModal, index: number) {
        return index + 1
      },
    },
    {
      title: '日期',
      key: 'reportTime',
      width: 170,
      render(row: LsmListModal) {
        const { reportTime } = row
        return reportTime ? dayjs(reportTime).format('YYYY-MM-DD') : '-'
      },
    },
    {
      title: '肝脏脂肪检测 (CAP)',
      key: 'capValue',
      width: 110,
    },
    {
      title: '肝脏硬度值测定 (LSM)',
      key: 'evalue',
      width: 120,
    },
  ]
}

const columns = createColumns()
// 分页器
const paginationReactive = reactive({
  page: 1,
  pageSize: 10,
  pageSizes: [10, 20, 50, 100],
})
const tableRef = ref<InstanceType<typeof BasicTable> | null>(null)

// 加载表格数据
async function loadDataTable(res: { size: number; start: number }) {
  const params = {
    patientId: PATIENT_ID,
    examProjectName: EXAM_PROJECT_NAME,
  }
  return await getLsmListApi({ ...res, ...params })
}

/** 获取全量指标数据 */
async function getLsmAllList() {
  const params = {
    patientId: PATIENT_ID,
    examProjectName: EXAM_PROJECT_NAME,
  }
  try {
    const res = await getLsmAllListApi<LsmListModal[]>(params)
    if (res?.data) {
      showChart.value = res.data?.length > 0
      const formatData = res.data.sort((a: LsmListModal, b: LsmListModal) => {
        const timeA: any = new Date(a.reportTime)
        const timeB: any = new Date(b.reportTime)
        return timeA - timeB
      }).map((item) => {
        return {
          ...item,
          reportTime: dayjs(item.reportTime).format('YYYY-MM-DD HH:mm'),
        }
      }).filter((value: any) => value !== undefined)

      lineOptions.value.series![0].data = formatData.map(
        (item: LsmListModal) => [item.reportTime, item.capValue]).reverse()

      lineOptions.value.series![1].data = formatData.map(
        (item: LsmListModal) => [item.reportTime, item.evalue]).reverse()

      const sortXTime = formatData.map(item => item.reportTime)

      lineOptions.value.xAxis!.data = sortXTime
    }
  }
  catch (error) {
    console.error(error)
  }
}

onMounted(() => {
  getLsmAllList()
})
</script>

<template>
  <div class="h-full overflow-y-auto p-14px">
    <BasicTable
      ref="tableRef"
      :columns="columns"
      :request="loadDataTable"
      :pagination="paginationReactive"
      striped
    />
    <SectionTitle class="mt-20px -mb-20px">
      肝脏硬度指标趋势图
    </SectionTitle>
    <div v-if="showChart" ref="lineRef" class="h-270px" />
    <div v-else class="pt-2vh">
      <n-empty description="无数据" />
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>
