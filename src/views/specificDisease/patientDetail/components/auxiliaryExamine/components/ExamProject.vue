<script lang='ts' setup>
import { SvgIcon } from '@/components/Icon'
import { getExamListApi } from '@/api/specificDisease'
import type { ExamListModel, ExamListParams } from '@/api/specificDisease'
import { formatNullValueToShortBar } from '@/utils'

interface Props {
  examProjectName: string
}

const props = defineProps<Props>()

const route = useRoute()

const PATIENT_ID = route.query?.patientId as string

const examList = ref<ExamListModel[]>([])
async function getExamList(params: ExamListParams) {
  try {
    const res = await getExamListApi<ExamListModel[]>(params)

    if (res?.data)
      examList.value = [...res.data]
  }
  catch (error) {

  }
}

function init() {
  const params = {
    patientId: PATIENT_ID,
    examProjectName: props.examProjectName,
  }
  getExamList(params)
}

onMounted(() => {
  init()
})
</script>

<template>
  <div class="liverB">
    <n-collapse v-if="examList?.length" arrow-placement="right">
      <n-collapse-item v-for="exam in examList" :key="exam.id" :name="exam.id">
        <template #header>
          <div class="collapseHeader pl-20px">
            报告日期：{{ exam.reportTime ?? '-' }}
          </div>
        </template>
        <template #arrow>
          <div>
            <SvgIcon local-icon="slmc-icon-crumbs1 " class="rotate-90" size="10" />
          </div>
        </template>
        <div v-if="examProjectName !== '肝脏活组织检查'" class="collapseBody">
          <div class="collapseBody-row mb-14px">
            <span class="w-70px basis-70px text-#666">检查项目：</span>
            <span class="flex-1 text-#333">
              {{ formatNullValueToShortBar(exam?.examItem) }}
            </span>
          </div>
          <div class="collapseBody-row mb-14px">
            <span class="w-70px basis-70px text-#666">检查描述：</span>
            <span class="flex-1 text-#333">
              {{ formatNullValueToShortBar(exam?.imageSight) }}
            </span>
          </div>
          <div class="collapseBody-row">
            <span class="w-70px basis-70px text-#666">检查结论：</span>
            <span class="flex-1 text-#333">
              {{ formatNullValueToShortBar(exam?.imageDiagnosis) }}
            </span>
          </div>
        </div>
        <div v-else class="collapseBody">
          <div class="collapseBody-row mb-14px">
            <span class="w-70px basis-70px text-#666">样本类型：</span>
            <span class="flex-1 text-#333">
              {{ formatNullValueToShortBar(exam?.examBodypart) }}
            </span>
          </div>
          <div class="collapseBody-row">
            <span class="w-70px basis-70px text-#666">病理诊断：</span>
            <span class="flex-1 text-#333">
              {{ formatNullValueToShortBar(exam?.imageDiagnosis) }}
            </span>
          </div>
        </div>
      </n-collapse-item>
    </n-collapse>
    <div v-else class="h-full flex items-center justify-center">
      <n-empty description="无数据" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.liverB {
    box-sizing: border-box;
    padding: 14px;
    padding-bottom: 28px;
    overflow:auto;
    height: 100%;
    :deep(.n-collapse .n-collapse-item .n-collapse-item__header .n-collapse-item__header-main){
        justify-content:space-between;
        height: 40px;
        background: #ecfbfb;
        border: 1px solid #dcdcdc;
    }
    :deep(.n-collapse .n-collapse-item:not(:first-child)){
        border:none;
    }
    :deep(.n-collapse .n-collapse-item .n-collapse-item__content-wrapper .n-collapse-item__content-inner){
      padding-top: 0;
    }
    :deep(.n-collapse .n-collapse-item.n-collapse-item--active .n-collapse-item__header.n-collapse-item__header--active .n-collapse-item-arrow){
        transform: rotate(180deg);
        transform-origin: center;
    }
    :deep(.n-collapse .n-collapse-item.n-collapse-item--right-arrow-placement .n-collapse-item__header .n-collapse-item-arrow){
        margin-right: 10px;
    }
    :deep(.n-collapse .n-collapse-item .n-collapse-item__header){
        padding-top: 10px;
    }
    :deep(.n-collapse .n-collapse-item:first-child > .n-collapse-item__header){
        padding-top: 0px;
    }
    :deep(.n-collapse .n-collapse-item){
        margin-top: 0px;
    }
   .collapseHeader {
    font-weight: 500;
   }
   .collapseBody {
        border: 1px solid #DCDCDC;
        padding: 14px 20px;
        border-top-color: transparent;
        &-row{
            display: flex;
            align-items: flex-start;
            justify-content: flex-start;

        }
    }
}
</style>
