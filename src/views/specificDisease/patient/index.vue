<script setup lang="ts">
import { useDebounceFn } from '@vueuse/core'
import dayjs from 'dayjs'
import { NEllipsis } from 'wowjoy-vui'
import { RenderPatientTag, RenderPhone } from '@/components/business/UiRender'
import { Breadcrumb } from '@/layouts/common'
import { BasicTable, RowAction } from '@/components/Table'
import { isPhone, sexIcon } from '@/utils/common'
import { SvgIcon } from '@/components/Icon'
import { getManageDoctorListApi, getPatientListApi, getPatientNameApi } from '@/api/specificDisease'
import type { PatientRecord } from '@/api/specificDisease'
import { useAuthStore } from '@/store'

import { useRouterPush } from '@/hooks'

import iconReport from '@/assets/images/use_medical_report.png'
import { getTagListAPI } from '@/api/tag'
import { getDiagnoseListApi } from '@/api/dic'

import '../patientDetail/index.vue' /// 为了加载更快

defineOptions({ name: 'SpecificDiseaseList' })

const { routerPush } = useRouterPush()

const auth = useAuthStore()
const userInfo = auth.userInfo
const currentTab = ref('慢乙肝')

/** 机构ID */
const ORGAN_ID = userInfo.organId
/** 性别枚举 */
const genderOptions = [
  { label: '全部', value: '' },
  { label: '男', value: '男' },
  { label: '女', value: '女' },
]
/** 随访渠道枚举 */
const planOptions = [
  { label: '全部', value: '' },
  { label: 'HMA', value: 'HMA' },
  { label: 'SLMC平台', value: 'SLMC' },
]

const options = ref({
  patientName: [],
  sex: genderOptions,
  doctorName: [],
  plan: planOptions,
  tagName: [],
  dignose: [],
})
const loading = ref({
  patientName: false,
  doctorName: false,
  tagName: false,
  dignose: false,
})

// 获取患者姓名
const getPatientName = useDebounceFn(async (patientName = '') => {
  loading.value.patientName = true
  const params = { patientName, organId: ORGAN_ID }
  if (currentTab.value === '慢乙肝')
    params.diagnoseType = '1'

  const res = await getPatientNameApi<string[]>(params)

  if (res.data) {
    const array = res.data.map((item) => {
      return {
        label: item,
        value: item,
      }
    }).slice(0, 100)
    options.value.patientName = array
    loading.value.patientName = false
  }
}, 300)

// 获取患者姓名
const getDignosise = useDebounceFn(async (digno = '') => {
  loading.value.dignose = true
  const res = await getDiagnoseListApi({
    name: digno,
  })
  options.value.dignose = res?.data || []
  loading.value.dignose = false
}, 300)

/**
 * 查找患者姓名
 * @param query 搜索key
 */
function handleSearchPatientName(query: string) {
  getPatientName(query)
}
/**
 * 患者名字打开/关闭回调
 * @param show 下拉打开/关闭
 */
function handleUpdateShowPatientName(show: boolean) {
  if (show)
    getPatientName('')
}
/**
 * 获取医生姓名
 * @param query 搜索关键字
 */
const getDoctorName = useDebounceFn(async (doctorName = '') => {
  loading.value.doctorName = true
  const params = {
    searchName: doctorName,
    size: 1000,
  }
  const res = await getManageDoctorListApi<any>(params)

  if (res?.data) {
    options.value.doctorName = res.data.records
    loading.value.doctorName = false
  }
}, 300)
/** 搜索医生姓名 */
function handleSearchDoctorName(query: string) {
  getDoctorName(query)
}
/**
 * 下拉列表打开/关闭回调
 * @param show 打开/关闭
 */
function handleUpdateShowDoctorName(show: boolean) {
  if (show)
    getDoctorName('')
}

const getTagNameList = useDebounceFn(async (tagName = '') => {
  loading.value.tagName = true
  const res = await getTagListAPI(tagName)

  if (res?.data) {
    options.value.tagName = res.data
    loading.value.tagName = false
  }
}, 300)

/** 搜索患者标签 */
function handleSearchTagName(query: string) {
  getTagNameList(query)
}
/**
 * 下拉列表打开/关闭回调
 * @param show 打开/关闭
 */
function handleUpdateShowTagName(show: boolean) {
  if (show)
    getTagNameList('')
}

/// 搜索患者 诊断

function handleSearhDignosise(query: string) {
  getDignosise(query)
}
/**
 * 患者名字打开/关闭回调
 * @param show 下拉打开/关闭
 */
function handleUpdateShowDignosise(show: boolean) {
  if (show)
    getDignosise('')
}

/**
 * 渲染患者姓名列
 * @param row 行数据
 */
function renderPatientNameInfo(row: PatientRecord) {
  const { patientName, sexName, age, ageUnit } = row

  const localIcon = sexIcon(sexName)
  return h('div', {
    class: 'flex ',
  }, [
    h(NEllipsis, {
      class: 'truncate',
    }, patientName),
    sexName && h(SvgIcon, {
      size: 16,
      class: 'mx-6px ',
      localIcon,

    }),
    h(NEllipsis, { class: 'truncate' }, { default: () => age ? `${age}${ageUnit || '岁'}` : '' }),
  ])
}
/**
 * 渲染手机号信息
 * @param row 行信息
 */
function renderPhoneInfo(row: PatientRecord) {
  const { phone } = row

  // 判断是否手机号 ，再脱敏
  if (isPhone(phone))
    return h(RenderPhone, { phone })

  else return phone
}
/**
 * 渲染患者标签
 * @param row 行信息
 */
function renderPatientTagInfo(row: PatientRecord) {
  const { tagName } = row
  const tag = tagName ? tagName.split(',') : []
  return tagName
    ? h(
      RenderPatientTag,
      { tag },
    )
    : '-'
}

/**
 * 渲染随访方案信息
 * @param row 行信息
 */
function renderPlanInfo(row: PatientRecord) {
  const { planName } = row
  return planName || '-'
}
/**
 *渲染随访状态
 * @param row 行信息
 */
function renderPlanStatusInfo(row: PatientRecord) {
  const { status } = row
  const statusType: Record<number, string> = {
    0: '未纳入',
    1: '已纳入',
  }
  const isStatusExist = status !== null && status !== undefined

  return isStatusExist
    ? h('div', {
      class: `followStatus  cursor-pointer ${status === 1 ? 'bg-primary' : 'bg-#ccc '}`,

    }, `${statusType[status]}`)
    : '-'
}
// 表格列初始化
function createColumns() {
  return [
    {
      title: '序号',
      key: 'index',
      width: 70,
      render(_: PatientRecord, index: number) {
        return index + 1
      },
    },
    {
      title: 'SLMC编号',
      key: 'slmcNo',
      width: 170,
    },
    {
      title: '患者信息',
      key: 'patientName',
      width: 180,
      render(row: PatientRecord) {
        return renderPatientNameInfo(row)
      },
    },
    {
      title: '患者标签',
      key: 'tagName',
      width: 210,
      render(row: PatientRecord) {
        return renderPatientTagInfo(row)
      },
    },
    {
      title: '手机号',
      key: 'phone',
      width: 150,
      render(row: PatientRecord) {
        return renderPhoneInfo(row)
      },
    },
    {
      title: '患者健康值',
      key: 'saplingScore',
      width: 110,
    },
    {
      title: '随访状态',
      key: 'status',
      width: 110,
      render(row: PatientRecord) {
        return renderPlanStatusInfo(row)
      },

    },
    {
      title: '随访方案',
      key: 'planName',
      minWidth: 200,
      ellipsis: false,
      render(row: PatientRecord) {
        return renderPlanInfo(row)
      },
    },

    {
      title: '管理医生',
      key: 'manageDoctorName',
      width: 120,
    },
  ]
}
function createActionColumns() {
  return {
    title: '操作',
    key: 'address',
    width: 80,
    fixed: 'right',
    render(row: PatientRecord) {
      return h(RowAction, {
        actions: [
          {
            label: '查看',
            onClick: () => handleLook(row),
            type: 'primary',
            text: true,
          },

        ],
      })
    },
  }
}

const columns = createColumns()
const actionColumns = createActionColumns()
// 默认从参数
const defaultTableParams = {
  size: 10,
  start: 1,
  sex: '',
  patientNames: [],
  plan: '',
  doctorNames: [],
  slmcNo: '',
  diagnose: '',
  endSaplingScore: '',
  startSaplingScore: '',
  tagName: [],
  diagnoseRecordStatus: null,
}

const tableParams = reactive({
  size: 10,
  start: 1,
  sex: '',
  patientNames: [],
  plan: '',
  doctorNames: [],
  diagnose: [],
  diagnoseType: '1',
  endSaplingScore: '',
  startSaplingScore: '',
  diagnoseRecordStatus: [],
  tagName: [],
  archiveTime: null,
})
// 分页器
const paginationReactive = reactive({
  page: 1,
  pageSize: 10,
  pageSizes: [10, 20, 50, 100],
})
const tableRef = ref<InstanceType<typeof BasicTable> | null>(null)
const mygRef = ref<InstanceType<typeof BasicTable> | null>(null)

function handleLook(row: PatientRecord, goEdit?: boolean) {
  const { id, patientName } = row
  const param = {
    patientId: id,
    patientName,
    isEdit: goEdit,
  }
  if (row.diagnoseType === 1)
    param.type = '慢乙肝'

  routerPush({
    name: 'specificDisease_detail',
    query: param,
  })
}
function handleSearchClick() {
  if (!jkInputBlur(1))
    return
  const payload = { start: 1 }
  tableRef?.value?.fetch(payload)
}
/** 重置查询参数 */
function handleResetClick() {
  Object.assign(tableParams, { ...defaultTableParams, diagnose: [], diagnoseRecordStatus: [], archiveTime: null })

  tableRef?.value?.fetch({ start: 1, size: 10 })
}

// 加载表格数据
async function loadDataTable(res: { size: number; start: number }) {
  const patientName = tableParams.patientNames.join(',')
  const manageDoctorId = tableParams.doctorNames.join(',')
  const diagnose = tableParams?.diagnose?.join(',')
  const diagnoseRecordStatus = tableParams.diagnoseRecordStatus.join(',')
  const tagName = tableParams?.tagName?.join(',')
  const params = {
    patientName,
    manageDoctorId,
    plan: tableParams.plan,
    sex: tableParams.sex,
    diagnose,
    tagName,
    diagnoseRecordStatus,
    startSaplingScore: tableParams.startSaplingScore,
    endSaplingScore: tableParams.endSaplingScore,
  }

  if (tableParams?.archiveTime?.length > 0) {
    params.archiveStartTime = dayjs(tableParams?.archiveTime[0]).format('YYYY-MM-DD')
    params.archiveEndTime = dayjs(tableParams?.archiveTime[1]).format('YYYY-MM-DD')
  }

  return await getPatientListApi<PatientRecord>({ ...params, ...res })
}

/** 初始化 */
function init() {
  handleSearchPatientName('')
  handleSearchDoctorName('')
}

/**
 * 慢乙肝数据相关
 */
const mygStatusOptions = {
  0: {
    title: '待建档',
    color: '#45A8E6',
    showEdit: true,
  },
  1: {
    title: '完善中',
    color: '#FF9B54',
    showEdit: true,
  },
  2: {
    title: '已建档',
    color: '#4ACFB1',
    showEdit: false,
  },
  3: {
    title: '拒绝建档',
    color: '#FF0000',
    showEdit: true,
  },
  4: {
    title: '申请建档',
    color: '#4ACFB1',
    showEdit: true,
  },
}

const diagnoseOptions = [{
  label: '乙型病毒性肝炎',
  value: '乙型病毒性肝炎',
}, {
  label: '慢性乙型病毒性肝炎',
  value: '慢性乙型病毒性肝炎',
}]

const mygDaOptions = [{
  value: 0,
  label: '待建档',
}, {
  value: 2,
  label: '已建档',
},
{
  value: 4,
  label: '申请建档',
},
{
  value: 3,
  label: '拒绝建档',
}]

function mygColumns() {
  return [
    {
      title: '序号',
      key: 'index',
      width: 70,
      render(_: PatientRecord, index: number) {
        return index + 1
      },
    },
    {
      title: 'SLMC编号',
      key: 'slmcNo',
      width: 170,
    },
    {
      title: '患者信息',
      key: 'patientName',
      width: 160,
      render(row: PatientRecord) {
        return renderPatientNameInfo(row)
      },
    },
    {
      title: '档案状态',
      key: 'diagnoseRecordStatus',
      width: 130,
      render(row: PatientRecord) {
        const opt = mygStatusOptions[row?.diagnoseRecordStatus] ?? {}
        return h('div', { class: 'flex items-center' }, [
          h('div', {
            style: {
              background: opt?.color,
              width: '6px',
              height: '6px',
              marginRight: '5px',
              borderRadius: '3px',
            },
          }, ''),
          h('div', { class: 'text-14px text-#333 mr-9px' }, opt?.title),
          h('img', {
            class: 'w-15px',
            style: {
              visibility: opt.showEdit ? 'visible' : 'hidden',
            },
            src: iconReport,
            onClick: () => {
              // console.log('')
              handleLook(row, true)
            },
          }),
        ])
      },

    },
    {
      title: '当前诊断',
      key: 'diagnose',
      width: 180,
    },
    {
      title: '就诊医生',
      key: 'lastDoctorName',
      width: 120,
    },
    {
      title: '患者标签',
      key: 'tagName',
      width: 180,
      render(row: PatientRecord) {
        return renderPatientTagInfo(row)
      },
    },
    {
      title: '病历号',
      key: 'patientRecordNo',
      width: 140,
    },
    {
      title: '手机号',
      key: 'phone',
      width: 140,
      render(row: PatientRecord) {
        return renderPhoneInfo(row)
      },
    },
    {
      title: '申请时间',
      key: 'applyRecordTime',
      width: 120,
      render(row: PatientRecord) {
        return h('div', {}, row?.applyRecordTime ? dayjs(row?.applyRecordTime).format('YYYY-MM-DD') : '-')
      },
    },
    {
      title: '建档时间',
      key: 'createRecordTime',
      width: 120,
      render(row: PatientRecord) {
        return h('div', {}, row?.createRecordTime ? dayjs(row?.createRecordTime).format('YYYY-MM-DD') : '-')
      },
    },
    {
      title: '健康值',
      key: 'saplingScore',
      width: 110,
    },
    {
      title: '随访状态',
      key: 'status',
      width: 110,
      render(row: PatientRecord) {
        return renderPlanStatusInfo(row)
      },

    },
    {
      title: '随访方案',
      key: 'planName',
      minWidth: 200,
      ellipsis: false,
      render(row: PatientRecord) {
        return renderPlanInfo(row)
      },
    },
    {
      title: '管理医生',
      key: 'manageDoctorName',
      width: 120,
    },
  ]
}

const mygTableSearchParams = reactive({
  size: 10,
  start: 1,
  sex: '',
  patientNames: [],
  plan: '',
  doctorNames: [],
  diagnose: [],
  diagnoseType: '1',
  endSaplingScore: '',
  startSaplingScore: '',
  diagnoseRecordStatus: [],
  tagName: [],
  archiveTime: null,
})

async function loadMygData(res: { size: number; start: number }) {
  const patientName = mygTableSearchParams.patientNames.join(',')
  const manageDoctorId = mygTableSearchParams.doctorNames.join(',')
  const diagnose = mygTableSearchParams?.diagnose?.join(',')
  const diagnoseRecordStatus = mygTableSearchParams.diagnoseRecordStatus.join(',')
  const tagName = mygTableSearchParams?.tagName?.join(',')
  const params = {
    patientName,
    manageDoctorId,
    plan: mygTableSearchParams.plan,
    sex: mygTableSearchParams.sex,
    diagnose,
    tagName,
    diagnoseRecordStatus,
    diagnoseType: '1',
    startSaplingScore: mygTableSearchParams.startSaplingScore,
    endSaplingScore: mygTableSearchParams.endSaplingScore,
  }
  if (mygTableSearchParams?.archiveTime?.length > 0) {
    params.archiveStartTime = dayjs(mygTableSearchParams?.archiveTime[0]).format('YYYY-MM-DD')
    params.archiveEndTime = dayjs(mygTableSearchParams?.archiveTime[1]).format('YYYY-MM-DD')
  }

  return await getPatientListApi<PatientRecord>({ ...params, ...res })
}

function handleMygClick() {
  if (!jkInputBlur(0))
    return
  const payload = { start: 1 }
  mygRef?.value?.fetch(payload)
}
/** 重置查询参数 */
function handleMygResetClick() {
  Object.assign(mygTableSearchParams, { ...defaultTableParams, diagnoseType: '1', diagnose: [], diagnoseRecordStatus: [], archiveTime: null })

  mygRef?.value?.fetch({ start: 1, size: 10 })
}

function rowProps(row: any) {
  return {
    style: 'cursor: pointer;',
    onDblclick: () => {
      handleLook(row)
    },
  }
}

function onlyNumberInput(value) {
  if (value.length === 0)
    return true
  return /^(0|[1-9]\d*)$/.test(value)
}

// /// / 处理sb 健康值乱输
// watch(() => tableParams.endSaplingScore, () => {
//   /// 查看 范围是否对

// })
watch(() => currentTab.value, () => {
  /// 查看 范围是否对
  if (currentTab.value === '全部') {
    nextTick(() => {
      tableRef?.value?.computeTableHeight()
      // console.log('xxx')
    })
  }
})

function jkInputBlur(tab) {
  if (tab === 0) {
    if (Number(mygTableSearchParams.endSaplingScore) < Number(mygTableSearchParams.startSaplingScore) && mygTableSearchParams.endSaplingScore.length > 0) {
      window.$message.warning('健康值范围错误, 请重输')
      mygTableSearchParams.endSaplingScore = ''
      return false
    }
    return true
  }
  else {
    if (Number(tableParams.endSaplingScore) < Number(tableParams.startSaplingScore) && tableParams.endSaplingScore.length > 0) {
      window.$message.warning('健康值范围错误, 请重输')
      tableParams.endSaplingScore = ''
      return false
    }
    return true
  }
}

onMounted(() => {
  init()
})

///  缓存了, 但是更改详情后 需要刷新
onActivated(() => {
  /// 刷新, 场景.  从 detail ---> list  还是需要刷新的, 但是其他内容还是需要缓存的
  init()
  if (currentTab.value === '全部')
    tableRef.value?.fetch()
  else
    mygRef.value?.fetch()
})
</script>

<template>
  <div class="specificDiseaseList">
    <Breadcrumb route-name="specificDisease_list" />
    <PageCard breadcrumb flex-col>
      <div h-full flex-col>
        <PageTitle class="mb-2px">
          患者列表
        </PageTitle>
        <n-tabs v-model:value="currentTab" :bar-width="82">
          <n-tab-pane display-directive="show" name="慢乙肝" tab="慢乙肝">
            <div>
              <n-form label-placement="left" :model="tableParams" :show-feedback="false">
                <div class="mb-14px flex flex-wrap justify-start gap-14px">
                  <n-form-item label="患者姓名" path="patientNames" class="basis-326px">
                    <n-select
                      v-model:value="mygTableSearchParams.patientNames" placeholder="请选择(可多选)"
                      :options="options.patientName" :loading="loading.patientName" style="width: 260px;" multiple
                      filterable clearable remote max-tag-count="responsive" @search="handleSearchPatientName"
                      @update:show="handleUpdateShowPatientName"
                    />
                  </n-form-item>
                  <n-form-item label="性别" path="sex" class="basis-298px">
                    <n-select
                      v-model:value="mygTableSearchParams.sex" placeholder="请输入" :options="options.sex"
                      style="width: 260px;"
                    />
                  </n-form-item>
                  <n-form-item label="随访渠道" path="plan" class="basis-326px">
                    <n-select
                      v-model:value="mygTableSearchParams.plan" placeholder="请输入" :options="options.plan"
                      style="width: 260px;"
                    />
                  </n-form-item>
                  <n-form-item label="档案状态" class="basis-326px">
                    <n-select
                      v-model:value="mygTableSearchParams.diagnoseRecordStatus" placeholder="请选择(可多选)"
                      :options="mygDaOptions" style="width: 260px;" multiple clearable remote
                    />
                  </n-form-item>
                  <n-form-item label="管理医生" path="doctorNames" class="basis-326px">
                    <n-select
                      v-model:value="mygTableSearchParams.doctorNames" filterable placeholder="请选择(可多选)"
                      :options="options.doctorName" :loading="loading.doctorName" label-field="userName"
                      value-field="id" clearable remote multiple max-tag-count="responsive" style="width: 260px;"
                      @search="handleSearchDoctorName" @update:show="handleUpdateShowDoctorName"
                    />
                  </n-form-item>
                  <n-form-item label="诊断" class="basis-298px">
                    <n-select
                      v-model:value="mygTableSearchParams.diagnose" filterable placeholder="请选择(可多选)"
                      :options="options.dignose" :loading="loading.dignose" label-field="diagnoseName"
                      value-field="diagnoseName" clearable max-tag-count="responsive" remote multiple
                      style="width: 260px;" @search="handleSearhDignosise" @update:show="handleUpdateShowDignosise"
                    />
                  </n-form-item>
                  <n-form-item label="患者标签" class="basis-326px">
                    <n-select
                      v-model:value="mygTableSearchParams.tagName" filterable placeholder="请选择(可多选)"
                      :options="options.tagName" :loading="loading.tagName" label-field="tagName" value-field="tagId"
                      clearable max-tag-count="responsive" remote multiple style="width: 260px;"
                      @search="handleSearchTagName" @update:show="handleUpdateShowTagName"
                    />
                  </n-form-item>
                  <n-form-item label="建档时间" class="basis-325px">
                    <n-date-picker
                      v-model:value="mygTableSearchParams.archiveTime" style="width: 260px" separator="~"
                      type="daterange" :is-date-disabled="(ts: number) => dayjs().isBefore(dayjs(ts))" clearable
                    />
                  </n-form-item>
                  <n-form-item label="健康值" class="basis-320px">
                    <n-input
                      v-model:value="mygTableSearchParams.startSaplingScore" :allow-input="onlyNumberInput"
                      placeholder="请输入"
                    />
                    <div mx-10px>
                      一
                    </div>
                    <n-input
                      v-model:value="mygTableSearchParams.endSaplingScore" :allow-input="onlyNumberInput"
                      placeholder="请输入"
                    />
                  </n-form-item>
                  <n-form-item>
                    <n-button type="primary" class="mr-10px" @click="handleMygClick">
                      查询
                    </n-button>
                    <n-button @click="handleMygResetClick">
                      重置
                    </n-button>
                  </n-form-item>
                </div>
              </n-form>
              <BasicTable
                ref="mygRef" :columns="mygColumns()" :action-column="actionColumns" :request="loadMygData"
                :row-props="rowProps" :row-key="(row: any) => row.id" :pagination="paginationReactive" :scroll-x="2120"
                striped
              />
            </div>
          </n-tab-pane>
          <n-tab-pane display-directive="show" name="全部" tab="全部">
            <div h-full flex-col>
              <n-form label-placement="left" :model="tableParams" :show-feedback="false">
                <div class="mb-14px flex flex-wrap justify-start gap-14px">
                  <n-form-item label="患者姓名" path="patientNames" class="basis-326px">
                    <n-select
                      v-model:value="tableParams.patientNames" placeholder="请选择(可多选)"
                      :options="options.patientName" :loading="loading.patientName" style="width: 260px;" multiple
                      filterable clearable remote max-tag-count="responsive" @search="handleSearchPatientName"
                      @update:show="handleUpdateShowPatientName"
                    />
                  </n-form-item>
                  <n-form-item label="性别" path="sex" class="basis-298px">
                    <n-select
                      v-model:value="tableParams.sex" placeholder="请输入" :options="options.sex"
                      style="width: 260px;"
                    />
                  </n-form-item>
                  <n-form-item label="随访渠道" path="plan" class="basis-326px">
                    <n-select
                      v-model:value="tableParams.plan" placeholder="请输入" :options="options.plan"
                      style="width: 260px;"
                    />
                  </n-form-item>
                  <n-form-item label="档案状态" class="basis-326px">
                    <n-select
                      v-model:value="tableParams.diagnoseRecordStatus" placeholder="请选择(可多选)"
                      :options="mygDaOptions" style="width: 260px;" multiple clearable remote
                    />
                  </n-form-item>
                  <n-form-item label="管理医生" path="doctorNames" class="basis-326px">
                    <n-select
                      v-model:value="tableParams.doctorNames" filterable placeholder="请选择(可多选)"
                      :options="options.doctorName" :loading="loading.doctorName" label-field="userName"
                      value-field="id" clearable remote multiple max-tag-count="responsive" style="width: 260px;"
                      @search="handleSearchDoctorName" @update:show="handleUpdateShowDoctorName"
                    />
                  </n-form-item>
                  <n-form-item label="诊断" class="basis-298px">
                    <n-select
                      v-model:value="tableParams.diagnose" filterable placeholder="请选择(可多选)"
                      :options="options.dignose" :loading="loading.dignose" label-field="diagnoseName"
                      value-field="diagnoseName" clearable remote multiple max-tag-count="responsive"
                      style="width: 260px;" @search="handleSearhDignosise" @update:show="handleUpdateShowDignosise"
                    />
                  </n-form-item>
                  <n-form-item label="患者标签" class="basis-326px">
                    <n-select
                      v-model:value="tableParams.tagName" filterable placeholder="请选择(可多选)"
                      :options="options.tagName" :loading="loading.tagName" label-field="tagName" value-field="tagId"
                      clearable max-tag-count="responsive" remote multiple style="width: 260px;"
                      @search="handleSearchTagName" @update:show="handleUpdateShowTagName"
                    />
                  </n-form-item>
                  <n-form-item label="建档时间" class="basis-325px">
                    <n-date-picker
                      v-model:value="tableParams.archiveTime" style="width: 260px" separator="~"
                      type="daterange" :is-date-disabled="(ts: number) => dayjs().isBefore(dayjs(ts))" clearable
                    />
                  </n-form-item>
                  <n-form-item label="健康值" class="basis-320px">
                    <n-input
                      v-model:value="tableParams.startSaplingScore" :allow-input="onlyNumberInput"
                      placeholder="请输入"
                    />
                    <div mx-10px>
                      一
                    </div>
                    <n-input
                      v-model:value="tableParams.endSaplingScore" :allow-input="onlyNumberInput"
                      placeholder="请输入"
                    />
                  </n-form-item>
                  <n-form-item>
                    <n-button type="primary" class="mr-10px" @click="handleSearchClick">
                      查询
                    </n-button>
                    <n-button @click="handleResetClick">
                      重置
                    </n-button>
                  </n-form-item>
                </div>
              </n-form>
              <BasicTable
                ref="tableRef" :columns="mygColumns()" :action-column="actionColumns" :request="loadDataTable"
                :row-key="(row: any) => row.id" :pagination="paginationReactive" :scroll-x="2120" striped flex-1
              />
            </div>
          </n-tab-pane>
        </n-tabs>
      </div>
    </PageCard>
  </div>
</template>

<style scoped lang="scss">
.specificDiseaseList {
  height: 100%;

  :deep(.followStatus) {
    width: 56px;
    height: 18px;
    color: #fff;
    border-radius: 9px;
    text-align: center;
    line-height: 18px;

  }

}

:deep(.n-tabs-bar) {
  width: 82px !important;
  max-width: 82px !important;
}

:deep(.n-tabs .n-tabs-tab) {
  padding-left: 20px;
  padding-right: 20px;
  width: 82px;
  display: flex;
  justify-content: center;
}

:deep(.n-tabs-tab-pad) {
  width: 0;
}

:deep(.n-base-selection-tag-wrapper .n-tag) {
  min-width: auto;
}

// :deep(.n-scrollbar){
//   height:fit-content;
// }
// :deep(.n-spin-content){
//   height: 100%;
// }
// :deep(.n-data-table){
//   height: 100%;
// }

:deep(.n-date-picker--range) {
  .n-input {
    width: 260px
  }
}
</style>
