<script lang='ts' setup>
import { useMessage } from 'wowjoy-vui'
import type { FormInst } from 'wowjoy-vui'
import { BasicModal, useModalInner } from '@/components/Modal'
import { resetPasswordApi } from '@/api/system/login'

interface ResetInfo {
  code: string
  phone: string
}

const props = defineProps({
  resetInfo: {
    type: Object as PropType<ResetInfo>,
    default: () => {},
  },
})

defineEmits((['register', 'register']))

const message = useMessage()

const formRef = ref<FormInst | null>(null)
const formValue = reactive({
  password: '',
  repeatPassword: '',
})
const rules = {
  password: {
    required: true,
    trigger: ['blur'],
    validator(rule: any, value: string) {
      if (!value)
        return new Error('内容不能为空')

      else if (value.length < 6 || value.length > 11)
        return new Error('新密码格式错误')

      return true
    },
  },
  repeatPassword: {
    required: true,
    trigger: ['blur'],
    validator(rule: any, value: string) {
      if (!value)
        return new Error('内容不能为空')

      else if (value.length < 6 || value.length > 11)
        return new Error('新密码格式错误')

      return true
    },
  },
}
function resetFormValue() {
  formValue.password = ''
  formValue.repeatPassword = ''
}
const [register, { closeModal }] = useModalInner()

// 两次输入密码是否一致
function isPasswordSame(password: string, repeatPassword: string) {
  if (password !== repeatPassword)
    return false
  return true
}

// 验证
async function handleReset<T>(): Promise<Service.RequestResult<T>> {
  const params = {
    smsCode: props.resetInfo.code,
    password: formValue.password,
    phone: props.resetInfo.phone,
  }
  try {
    const res = await resetPasswordApi<T>(params)
    return new Promise((resolve) => {
      resolve(res)
    })
  }
  catch (error) {
    return Promise.reject(error)
  }
}
function handleConfirm() {
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
      const samePassword = isPasswordSame(formValue.password, formValue.repeatPassword)
      if (!samePassword) {
        message.error('两次输入密码不一致！')
        return
      }

      const verifyCodeRes = await handleReset<boolean> ()

      if (verifyCodeRes?.data) {
        closeModal()
        message.success('重置密码成功！')
      }
    }
    else {
      console.log(errors)
    }
  })
}
function visibleChange(v: boolean) {
  // 关闭时重置状态
  if (!v)
    resetFormValue()
}
</script>

<template>
  <div>
    <BasicModal
      v-bind="$attrs"
      title="重置密码"
      :min-height="105"
      width="400"
      ok-text="重置"
      :footer-offset="-16"
      @register="register"
      @ok="handleConfirm"
      @visible-change="visibleChange"
    >
      <n-form
        ref="formRef"
        label-placement="left"
        class="my-24px ml-17px mr-30px"
        require-mark-placement="left"
        :label-width="100"
        :model="formValue"
        :rules="rules"
      >
        <n-form-item path="password" label="新密码">
          <n-input
            v-model:value="formValue.password"
            :maxlength="11"
            placeholder="请输入"
          />
        </n-form-item>
        <n-form-item path="repeatPassword" label="再次输入">
          <n-input
            v-model:value="formValue.repeatPassword"
            :maxlength="11"
            :minlength="6"
            placeholder="请输入"
          />
        </n-form-item>
      </n-form>
    </BasicModal>
  </div>
</template>

<style scoped lang="scss">

</style>
