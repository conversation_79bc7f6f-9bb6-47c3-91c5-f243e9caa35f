<script lang='ts' setup>
import { onUnmounted } from 'vue'
import type { FormInst } from 'wowjoy-vui'
import { useMessage } from 'wowjoy-vui'
import { BasicModal, useModalInner } from '@/components/Modal'
import { isPhone } from '@/utils/common'
import { checkPhoneApi, verifyCode<PERSON>pi } from '@/api/user/user'
import { getVerifyCodeApi, resetPasswordApi } from '@/api/system/login'

const emit = defineEmits(['verifySuccess', 'register'])

const message = useMessage()

const formRef = ref<FormInst | null>(null)
const formValue = reactive({
  phone: '',
  verificationCode: '',
  password: '',
})
const rules = {
  phone: {
    required: true,
    trigger: ['blur'],
    validator(rule: any, value: string) {
      if (!value)
        return new Error('内容不能为空')

      else if (!isPhone(value))
        return new Error('手机号格式错误')

      return true
    },
  },
  password: {
    required: true,
    trigger: ['blur'],
    validator(rule: any, value: string) {
      if (!value)
        return new Error('内容不能为空')

      else if (value.length < 6 || value.length > 11)
        return new Error('新密码格式错误')

      return true
    },
  },
  verificationCode: {
    required: true,
    message: '内容不能为空',
    trigger: ['blur'],
  },
}
// 手机号输入
function handleInput(e: string) {
  formValue.phone = e.replace(/[^0-9]/g, '') // 不可输入数字和小数点以外的
}

const COUNT_TIME = 60
const buttonText = ref('获取验证码')
const second = ref<number>(COUNT_TIME)
const disabled = ref(false)
let timer: any = null
async function getVerificationCode() {
  if (!formValue.phone)
    return
  const params = {
    phone: formValue.phone,
  }
  const { data: isPlatformUser } = await checkPhoneApi<boolean>({ value: params.phone })
  if (!isPlatformUser) {
    message.error('用户不存在')
    return
  }

  const res = await getVerifyCodeApi<boolean>(params)
  if (res.data) {
    disabled.value = true
    buttonText.value = '重新获取'

    timer = setInterval(() => {
      second.value--

      if (second.value <= 0) {
        clearInterval(timer)
        disabled.value = false
        second.value = COUNT_TIME
      }
    }, 1000)
  }
}
onUnmounted(() => {
  clearInterval(timer)
  timer = null
})

function resetFormValue() {
  formValue.phone = ''
  formValue.verificationCode = ''
}
const [register, { closeModal }] = useModalInner()

// 验证
async function handleVerifyCode<T>(): Promise<Service.RequestResult<T>> {
  const params = {
    code: formValue.verificationCode,
    phone: formValue.phone,
  }
  try {
    const res = await verifyCodeApi<T>(params)

    return new Promise((resolve) => {
      resolve(res)
    })
  }
  catch (error) {
    return Promise.reject(error)
  }
}
// 验证
async function handleReset<T>(): Promise<Service.RequestResult<T>> {
  const params = {
    smsCode: formValue.verificationCode,
    password: formValue.password,
    phone: formValue.phone,
  }
  try {
    const res = await resetPasswordApi<T>(params)
    return new Promise((resolve) => {
      resolve(res)
    })
  }
  catch (error) {
    return Promise.reject(error)
  }
}
function handleConfirm() {
  formRef.value?.validate(async (errors: any) => {
    if (!errors) {
    //   const verifyCodeRes = await handleVerifyCode<string>()
      const verifyRestRes = await handleReset<string>()

      if (verifyRestRes?.data) {
        closeModal()
        // emit('verifySuccess')
        message.success('重置密码成功！')
      }
    }
    else {
      console.log(errors)
    }
  })
}

function visibleChange(v: boolean) {
  if (v) {
    nextTick(() => {
      buttonText.value = '获取验证码'

      disabled.value = false
    })
  }
  // 关闭时重置状态
  if (!v) {
    resetFormValue()
    clearInterval(timer)
    timer = null
    second.value = COUNT_TIME
  }
}

function noSideSpace(value: string) {
  return !value.startsWith(' ') && !value.endsWith(' ')
}
</script>

<template>
  <BasicModal
    v-bind="$attrs"
    title="重置密码"
    :min-height="105"
    ok-text="验证"
    width="400"
    :footer-offset="14"
    @register="register"
    @ok="handleConfirm"
    @visible-change="visibleChange"
  >
    <n-form
      ref="formRef"
      label-placement="left"
      class="my-24px ml-17px mr-30px"
      require-mark-placement="left"
      :model="formValue"
      :rules="rules"
    >
      <n-form-item path="phone" label="手机号">
        <n-input
          v-model:value="formValue.phone"
          :maxlength="11"
          placeholder="请输入"
          @input="handleInput"
        />
        <n-button v-if="disabled" class="ml-10px" disabled>
          {{ second }}秒
        </n-button>
        <n-button v-else class="ml-10px" type="primary" @click="getVerificationCode">
          {{ buttonText }}
        </n-button>
      </n-form-item>
      <n-form-item path="password" label="新密码">
        <n-input
          v-model:value="formValue.password"
          :maxlength="11"
          placeholder="请输入"
          type="password"
          :allow-input="noSideSpace"
        />
      </n-form-item>
      <n-form-item path="verificationCode" label="验证码">
        <n-input
          v-model:value="formValue.verificationCode"
          :maxlength="11"
          :minlength="6"
          placeholder="请输入"
        />
      </n-form-item>
    </n-form>
  </BasicModal>
</template>

<style scoped lang="scss">

</style>
