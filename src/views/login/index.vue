<script lang="ts" setup>
import { useMessage } from 'wowjoy-vui'
import type { FormInst } from 'wowjoy-vui'
import VerifyModal from './VerifyModal.vue'
import ResetPassword from './resetPassword.vue'

import dragVerify from './dragVerify.vue'
import loginHeader from '@/assets/images/logo-header.png'
import loginCopyright from '@/assets/images/login-copyright.png'
import loginHuazhuo from '@/assets/images/wowjoy_login.png'
import loginForm from '@/assets/images/login-form.png'

import { isPhone } from '@/utils/common'
import { useAuthStore } from '@/store'

import { SvgIcon } from '@/components/Icon'
import { useModal } from '@/components/Modal'

import { getEDCLoginCode } from '~/src/api/system/login'
import { checkPhoneApi } from '@/api/user/user'

const message = useMessage()
const useAuth = useAuthStore()
const verify = ref()

type ErrorType =
    | 'phoneFormat'
    | 'passwordRequired'
    | 'phoneAndPasswordNotHave'
    | 'phoneNotMatchPassword'
    | 'phoneNotHave'
    | 'codeError'

const ERROR_TYPE: Record<string, ErrorType> = {
  PHONE_FORMAT: 'phoneFormat',
  PASSWORD_REQUIRED: 'passwordRequired',
  PHONE_NOT_MATCH_PASSWORD: 'phoneNotMatchPassword',
  PHONE_NOT_HAVE: 'phoneNotHave',
  PHONE_AND_PASSWORD_NOT_HAVE: 'phoneAndPasswordNotHave',
  CODE_ERROR: 'codeError',
}

const formRef = ref<FormInst | null>(null)
const loginType = ref('password')

const formValue = reactive({
  phone: '', /// mima
  phone1: '', /// yanzma
  password: '',
  code: '',
  isVerifyPass: false,
})
const timeDown = ref(59)
const timer = ref()

const ERROR_TEXT = {
  phoneFormat: '手机号格式错误',
  passwordRequired: '密码不能为空',
  phoneNotMatchPassword: '手机号或密码不正确，请重新输入',
  phoneNotHave: '该用户不存在',
  phoneAndPasswordNotHave: '请输入正确的手机号或密码',
  codeError: '验证码有误',
}

const errorPhoneTypeRef = ref<ErrorType | null>(null)
const errorPasswordTypeRef = ref<ErrorType | null>(null)
const currentErrorType = ref<ErrorType | null>(null)
const currentErrorType2 = ref<ErrorType | null>(null)

// 手机号输入框UI状态
const phoneInputStatus = computed(() => {
  const isPhoneError
        = errorPhoneTypeRef.value
        && ['phoneFormat'].includes(errorPhoneTypeRef.value)
  return isPhoneError ? 'warning' : 'success'
})

// 密码输入框UI状态
const passwordInputStatus = computed(() => {
  const isPasswordError
        = errorPasswordTypeRef.value
        && ['passwordRequired'].includes(errorPasswordTypeRef.value)
  return isPasswordError ? 'warning' : 'success'
})

// 验证码输入状态
const verifyCodeInputStatus = computed(() => {
  const isError
        = errorPasswordTypeRef.value
        && ['codeError'].includes(errorPasswordTypeRef.value)
  return isError ? 'warning' : 'success'
})

// 手机号校验函数
function verifyPhone(value: string) {
  if (!isPhone(value)) {
    errorPhoneTypeRef.value = ERROR_TYPE.PHONE_FORMAT
    return false
  }
  else {
    errorPhoneTypeRef.value = null
    return true
  }
}
// 手机号输入框失焦
function handlePhoneBlur() {
  const phone = formValue.phone
  if (phone.length === 0)
    return
  verifyPhone(phone)

  currentErrorType.value
        = errorPhoneTypeRef.value || errorPasswordTypeRef.value
}

function handlePhoneBlur1() {
  const phone1 = formValue.phone1
  if (phone1.length === 0)
    return
  currentErrorType2.value = !isPhone(phone1) ? ERROR_TYPE.PHONE_FORMAT : null
}

// 密码校验函数
function verifyPassword(value: string) {
  if (!value) {
    errorPasswordTypeRef.value = ERROR_TYPE.PASSWORD_REQUIRED
    return false
  }
  else {
    errorPasswordTypeRef.value = null
    return true
  }
}
// 手机号输入
function handleInput(e: string) {
  formValue.phone = e.replace(/[^0-9]/g, '') // 不可输入数字和小数点以外的
}

function handleInput1(e: string) {
  formValue.phone1 = e.replace(/[^0-9]/g, '') // 不可输入数字和小数点以外的
}

// 密码输入框失焦
function handlePasswordBlur() {
  const password = formValue.password

  verifyPassword(password)

  currentErrorType.value
        = errorPasswordTypeRef.value || errorPhoneTypeRef.value
}

/// 验证码输入
function handleVerifyCode() {
  if (currentErrorType2.value)
    return

  if (formValue.code?.length < 6)
    currentErrorType2.value = ERROR_TYPE.CODE_ERROR
  else
    currentErrorType2.value = null
}

// 登录
async function handleSubmit() {
  if (loginType.value === 'password') {
    const phone = formValue.phone
    const password = formValue.password
    if (!phone && !password) {
      currentErrorType.value = ERROR_TYPE.PHONE_AND_PASSWORD_NOT_HAVE
      return
    }

    const phoneVerify = verifyPhone(phone)
    const passwordVerify = verifyPassword(password)

    currentErrorType.value
            = errorPhoneTypeRef.value || errorPasswordTypeRef.value

    if (!passwordVerify || !phoneVerify)
      return

    try {
      const params = {
        loginType: 'PASSWORD',
        phone: formValue.phone,
        password: formValue.password,
      }
      await useAuth.login(params)
    }
    catch (error: any) {
      const errorMsg = error?.message

      errorMsg ?? message.error(errorMsg)
    }
  }
  else {
    /// 验证码登录
    handleVerifyCode()
    if (currentErrorType2.value)
      return

    try {
      const params = {
        loginType: 'SMS',
        phone: formValue.phone1,
        smsCode: formValue.code,
      }

      await useAuth.login(params)
    }
    catch (error: any) {
      const errorMsg = error?.message

      errorMsg ?? message.error(errorMsg)
    }
  }
}

// const resetInfo = ref({
//   code: '',
//   phone: '',
// })

// let timer: any = null
// function onVerifySuccess({ code, phone }: { code: string; phone: string }) {
//   resetInfo.value.code = code
//   resetInfo.value.phone = phone
//   timer = setTimeout(() => {
//     showResetModal()
//     clearTimeout(timer)
//     timer = null
//   }, 500)
// }

// // 重置密码弹窗
// const [registerResetModal, { openModal: openResetModal }] = useModal()
// function showResetModal() {
//   openResetModal()
// }

/// 重置密码(主要是科研管理平台)
const [registerResetPassword, { openModal: openResetPassword }] = useModal()
function showResetpasswordModal() {
  openResetPassword()
}

// 获取验证码弹窗
const [registerVerifyModal, { openModal: openVerifyModal }] = useModal()
function showVerifyModal() {
  // openVerifyModal()
  openResetPassword()
}

/// 获取验证吗
async function getVerifyCode() {
  handlePhoneBlur1()
  if (currentErrorType2.value)
    return
  const { data: isPlatformUser } = await checkPhoneApi<boolean>({
    value: loginType.value === 'password' ? formValue.phone : formValue.phone1,
  })
  if (!isPlatformUser) {
    if (loginType.value === 'password')
      currentErrorType.value = ERROR_TYPE.PHONE_NOT_HAVE
    else
      currentErrorType2.value = ERROR_TYPE.PHONE_NOT_HAVE
    return
  }
  if (!timer.value) {
    timer.value = setInterval(() => {
      if (timeDown.value === 1) {
        clearInterval(timer.value)
        timer.value = null
        timeDown.value = 59
      }
      timeDown.value -= 1
    }, 1000)
    /// 发送验证码
    getEDCLoginCode(formValue.phone1)
  }
}

/// 监听滑块

function dragPassChange(val) {
  if (val) {
    /// 动画缓慢消失
    const dom = document.getElementById('verify')
    dom.style.opacity = 0
    dom.style.zIndex = '0'
    const dom1 = document.getElementById('code-input')
    dom1.style.opacity = 1
    dom1.style.zIndex = '1'

    /// 查看是否输入手机号
    const isverify = verifyPhone(formValue.phone1)
    if (!isverify) {
      currentErrorType2.value = ERROR_TYPE.PHONE_FORMAT
      verify.value?.resetNormal()
      dom.style.opacity = 1
      dom.style.zIndex = '1'
      dom1.style.opacity = 0
      dom1.style.zIndex = '0'
    }
    else {
      getVerifyCode()
    }
  }
}

/// 切换登录
function tabChange(name) {
  console.log(name)
  // currentErrorType2.value = null
}
</script>

<template>
  <div class="login">
    <header class="px-39px py-6vh">
      <img :src="loginHeader" alt="" class="w-457px">
    </header>
    <section class="login-container">
      <div class="form-wrap">
        <div class="mb-30px flex items-center">
          <img :src="loginForm" alt="" class="block h-30px w-42px">
          <span class="ml-12px text-24px fw-500">SLMC医护管理平台</span>
        </div>
        <n-tabs v-model:value="loginType" type="bar" animated @update:value="tabChange">
          <n-tab-pane name="password" tab="密码登录">
            <div v-if="currentErrorType" class="error">
              <SvgIcon local-icon="slmc-icon-wrong1" />
              <span class="ml-10px">{{
                ERROR_TEXT[currentErrorType]
              }}</span>
            </div>
            <n-form
              ref="formRef"
              :show-label="false"
              :model="formValue"
            >
              <n-form-item path="phone">
                <n-input
                  v-model:value="formValue.phone"
                  :maxlength="11"
                  placeholder="请输入手机号"
                  style="height: 45px"
                  :status="phoneInputStatus"
                  @blur="handlePhoneBlur"
                  @input="handleInput"
                >
                  <template #prefix>
                    <SvgIcon
                      local-icon="slmc-icon-shouji"
                      size="20"
                      class="mr-10px"
                      color="#ccc"
                    />
                  </template>
                </n-input>
              </n-form-item>
              <n-form-item path="password">
                <n-input
                  v-model:value="formValue.password"
                  :maxlength="15"
                  :minlength="6"
                  placeholder="请输入密码"
                  style="height: 45px"
                  :status="passwordInputStatus"
                  type="password"
                  @blur="handlePasswordBlur"
                >
                  <template #prefix>
                    <SvgIcon
                      local-icon="slmc-icon-mima"
                      size="20"
                      class="mr-10px"
                      color="#ccc"
                    />
                  </template>
                </n-input>
              </n-form-item>
            </n-form>
          </n-tab-pane>
          <n-tab-pane name="sms" tab="免密登录">
            <div v-if="currentErrorType2" class="error">
              <SvgIcon local-icon="slmc-icon-wrong1" />
              <span class="ml-10px">{{
                ERROR_TEXT[currentErrorType2]
              }}</span>
            </div>
            <n-form
              ref="formRef"
              :show-label="false"
              :model="formValue"
            >
              <n-form-item path="phone1">
                <n-input
                  v-model:value="formValue.phone1"
                  :maxlength="11"
                  placeholder="请输入手机号"
                  style="height: 45px"
                  :status="phoneInputStatus"
                  @blur="handlePhoneBlur1"
                  @input="handleInput1"
                >
                  <template #prefix>
                    <SvgIcon
                      local-icon="slmc-icon-shouji"
                      size="20"
                      class="mr-10px"
                      color="#ccc"
                    />
                  </template>
                </n-input>
              </n-form-item>
              <n-form-item path="code">
                <div
                  style="
                                        position: relative;
                                        width: 100%;
                                        height: 47px;
                                    "
                >
                  <n-input
                    id="code-input"
                    v-model:value="formValue.code"
                    :maxlength="6"
                    :minlength="6"
                    placeholder="请输入验证码"
                    :status="verifyCodeInputStatus"
                    style="height: 45px"
                  >
                    <template #suffix>
                      <div
                        class="code-suffix"
                        :style="{
                          color: timer
                            ? '#999'
                            : '#06AEA6',
                          background: timer
                            ? '#f5f5f5'
                            : '#fff',
                        }"
                        @click="getVerifyCode"
                      >
                        <span v-if="timer">{{
                          `${timeDown}s后重新获取`
                        }}</span>
                        <span v-else>获取验证码</span>
                      </div>
                    </template>
                  </n-input>
                  <dragVerify
                    id="verify"
                    ref="verify"
                    v-model:value="formValue.isVerifyPass"
                    @change="dragPassChange"
                  />
                </div>
              </n-form-item>
            </n-form>
          </n-tab-pane>
        </n-tabs>
        <n-button
          type="primary"
          class="h-45px w-268px"
          @click="handleSubmit"
        >
          登录
        </n-button>
        <div class="form-foot mt-10px flex justify-between">
          <!--   <n-checkbox v-model:checked="rememberPassword">
            <span class="pl-10px">记住密码</span>
          </n-checkbox> -->
          <span />
          <n-button
            text
            class="text-primary"
            @click="showVerifyModal"
          >
            忘记密码？
          </n-button>
        </div>
      </div>
    </section>
    <footer class="absolute bottom-20px w-full">
      <div class="flex justify-center">
        <img :src="loginHuazhuo" alt="" class="mb-4px block w-324px">
      </div>
      <div class="flex justify-center">
        <img :src="loginCopyright" alt="" class="block w-283px">
      </div>
    </footer>

    <!-- 获取验证码 -->
    <VerifyModal @register="registerVerifyModal" />
    <!-- 重置密码 -->
    <!-- <ResetModal :reset-info="resetInfo" @register="registerResetModal" /> -->
    <ResetPassword @register="registerResetPassword" />
  </div>
</template>

<style scoped lang="scss">
.login {
    height: 100%;

    &-container {
        position: relative;
        height: 60vh;
        width: 100%;
        background: url("@/assets/images/login-bg.jpg") no-repeat center;
    }
    .form-wrap {
        box-sizing: border-box;
        position: absolute;
        padding: 36px 36px 20px 36px;
        width: 340px;
        min-height: 327px;
        top: 15%;
        right: 8%;
        z-index: 2;
        background: #ffffff;

        :deep(.n-input__input-el) {
            height: 45px;
        }
        :deep(.n-input--warning-status) {
            background-color: #fffcfa;
        }
    }
    .error {
        box-sizing: border-box;
        width: 268px;
        height: 36px;
        padding: 10px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        margin-bottom: 10px;
        background: #fff4f2;
        border: 1px solid #f2d2ce;
        font-size: 12px;
        color: #f36969;
    }
}
:deep(.n-tabs-tab) {
    padding: 14px 0;
}

#code-input {
    opacity: 0;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    transition: opacity 0.5s ease-in-out;
    :deep(.n-input-wrapper) {
        padding-right: 0;
    }
}

.code-suffix {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 0 14px;
    border-left: 1px solid #d1d1d1;
}

#verify {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 1;
    transition: opacity 0.5s ease-in-out;
}
</style>
