<script lang="ts" setup>
import { ref } from 'vue'
import loginDrag from '@/assets/images/login_drag.png'
import dragPass from '@/assets/images/drag_pass.png'

defineProps({
  value: {
    type: Boolean,
    defalut: false,
  },

  // 成功文字
  successText: {
    type: String,
    default: '验证通过',
  },

  // 开始的文字
  startText: {
    type: String,
    default: '请按住滑块，拖动到最右边',
  },
})
const emit = defineEmits(['update:value', 'change'])
const verifyResult = ref(false) // 验证结果
const isTouch = 'ontouchstart' in document.documentElement
const moveEvent = isTouch ? 'touchmove' : 'mousemove'
const upEvent = isTouch ? 'touchend' : 'mouseup'

// 滑块触摸开始
function onStart(ev: MouseEvent | TouchEvent) {
  let disX = 0 // 滑块移动距离
  const iconWidth = 50 // 滑动图标宽度
  const ele = document.querySelector('.drag-verify .block') as HTMLElement
  const startX = (ev as MouseEvent).clientX || (ev as TouchEvent).touches[0].pageX
  const parentWidth = ele.offsetWidth
  const MaxX = parentWidth - iconWidth
  if (verifyResult.value)
    return false

  // 滑块触摸移动
  const onMove = (e: MouseEvent | TouchEvent) => {
    const endX = (e as MouseEvent).clientX || (e as TouchEvent).touches[0].pageX
    disX = endX - startX
    if (disX <= 0)
      disX = 0

    if (disX >= MaxX - iconWidth)
      disX = MaxX

    ele.style.transition = '.1s all'
    ele.style.transform = `translateX(${disX}px)`
  }

  // 滑块触摸结束
  const onEnd = () => {
    if (disX !== MaxX) {
      ele.style.transition = '.5s all'
      ele.style.transform = 'translateX(0)'
    }
    else {
      // 执行成功
      verifyResult.value = true
      emit('update:value', verifyResult.value)
      emit('change', verifyResult.value)
    }

    document.removeEventListener(moveEvent, onMove)
    document.removeEventListener(upEvent, onEnd)
  }

  document.addEventListener(moveEvent, onMove)
  document.addEventListener(upEvent, onEnd)
}

function resetNormal() {
  verifyResult.value = false
  const ele = document.querySelector('.drag-verify .block') as HTMLElement
  ele.style.transition = '.5s all'
  ele.style.transform = 'translateX(0)'
}

defineExpose({
  resetNormal,
})
</script>

<template>
  <div class="drag-verify">
    <div class="range" :class="verifyResult ? 'success' : ''">
      <div class="block" @mousedown="onStart" @touchstart="onStart">
        <div class="img-content">
          <img :src="verifyResult ? dragPass : loginDrag" h-15px>
        </div>
      </div>
      <span class="text">{{ verifyResult ? successText : startText }}</span>
    </div>
  </div>
</template>

  <style lang="scss" scoped>
  $color-primary: #3AC9A8;

  @mixin flex {
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .drag-verify {
    width: 100%;
    border: 1px solid #dcdcdc;
    .range {
      background-color: #f6f6f6;
      position: relative;
      transition: 1s all;
      user-select: none;
      color: #666;
      overflow: hidden;
      @include flex;
      height: 45px;
      .text{
        margin-left: 50px;
      }

      &.success {
        background-color: $color-primary;
        color: #fff;
        .text {
          position: relative;
          z-index: 1;
          font-size: 14px;
          margin-right: 50px;
          margin-left: 0;
          color:#fff;
        }
        .block i {
          color: $color-primary;
        }

        .block{
          .img-content{
          border-right: none;
          border-left: 1px solid #dcdcdc;
        }
        }
      }
      .block {
        display: block;
        position: absolute;
        left: calc(-100% + 50px);
        width: 100%;
        height: 100%;
        background: $color-primary;
        overflow: hidden;
        .img-content {
          position: absolute;
          right: 0;
          width: 50px;
          height: 100%;
          display: flex;
          font-size: 20px;
          background-color: #fff;
          border-right: 1px solid #dcdcdc;
          border-left: none;
          cursor: pointer;
          @include flex;

          img{
            -webkit-user-drag: none;
            -khtml-user-drag: none;
            -moz-user-drag: none;
            -o-user-drag: none;
            user-drag: none;
          }
        }
      }
    }
  }
  </style>
