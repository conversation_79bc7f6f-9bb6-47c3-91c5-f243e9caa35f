<script setup lang="ts">
import QuestionLib from './components/QuestionLib.vue'
import { Breadcrumb } from '@/layouts/common'
import SurveyEditor from '@/components/Survey/Editor.vue'
import { getSurveyDetail, isSurveyAvailableAPI, updateSurveyAPI } from '@/api/toolsFollow/survey'
import { useAuthStore } from '@/store'
import PageFooter from '@/components/PageFooter/index.vue'
import AuthRange from '@/views/toolsFollow/components/AuthRange.vue'

const { userInfo } = useAuthStore()
const router = useRouter()
const route = useRoute()
const scaleId = route.query.id as string
const surveyEditorRef = ref<InstanceType<typeof SurveyEditor>>()
const isLoading = ref(false)

const surveyContent = ref('{}')

const isAddModalShow = ref(false)
const relations = ref<string[]>([])
const visibleRange = ref('ALL_USER')
const followPlanId = route.query.followPlanId as string
const view = route.query.view as string | undefined

const canEdit = ref(false)
async function init() {
  try {
    isLoading.value = true
    const { data } = await getSurveyDetail(scaleId)
    surveyContent.value = data.content
    await nextTick()
    canEdit.value = (data.createId === userInfo.id) && !view
    surveyEditorRef.value?.renderSurvey((data.createId === userInfo.id) && !view)
  }
  catch (error) {
    console.log(error)
  }
  finally {
    isLoading.value = false
  }
}

async function handleSave() {
  try {
    await new Promise(resolve => setTimeout(resolve, 600))

    const data = JSON.parse(surveyContent.value)
    if (!data?.title) {
      window.$message.warning('请输入问卷标题')
      isLoading.value = false
      return false
    }

    if (followPlanId) {
      handleConfirm()
    }
    else {
      const { data: available } = await isSurveyAvailableAPI({
        scaleId,
        name: data.title,
      })

      if (!available) {
        window.$message.warning('问卷标题不能重复')
        isLoading.value = false
        return false
      }

      isAddModalShow.value = true
    }
  }
  catch (error) {
    window.$message.warning('请检查问卷模板')
  }
}

async function handleConfirm() {
  try {
    isLoading.value = true
    isAddModalShow.value = false
    await new Promise(resolve => setTimeout(resolve, 600))

    const data = JSON.parse(surveyContent.value)
    const { data: results } = await updateSurveyAPI({
      scaleName: data.title,
      content: JSON.stringify(data),
      introduce: data.introduce,
      scaleId,
      visibleRange: visibleRange.value,
      relations: relations.value,
      isTemplate: true,
      updateId: userInfo.id,
      updateName: userInfo.userName,
    })

    if (results) {
      if (followPlanId) {
        router.replace({
          path: '/intelligentFollow/manage/create',
          query: {
            id: followPlanId,
            restore: '1',
            seriesId: results,
            relationName: encodeURIComponent(data.title),
          },
        })
      }
      else {
        router.replace('/toolsFollow/survey')
      }
    }
    else {
      window.$message.error('保存失败')
    }
  }
  catch (error) {

  }
  finally {
    isLoading.value = false
  }
}

async function addQuestion(content: string) {
  surveyEditorRef.value?.handleAdd(content)
}

const isLibShow = ref(false)
provide('isLibShow', isLibShow)

onMounted(() => {
  init()
})
</script>

<template>
  <div>
    <Breadcrumb
      :bread-list="[
        {
          title: '随访工具',
          link: null,
          key: 'toolsFollow',
        },
        {
          title: '问卷模板',
          link: '/',
          key: 'toolsFollow_survey',
        },
        {
          title: canEdit ? '编辑问卷模板' : '查看问卷模板',
          link: null,
          key: 'toolsFollow_survey_edit',
        },
      ]"
    />

    <n-spin type="uni" size="medium" :show="isLoading">
      <PageCard breadcrumb>
        <SurveyEditor ref="surveyEditorRef" v-model:content="surveyContent" />
        <PageFooter>
          <template #default>
            <n-button v-if="canEdit" w-100px type="primary" @click="handleSave">
              保 存
            </n-button>
            <n-button secondary w-100px @click="router.go(-1)">
              返 回
            </n-button>
          </template>
        </PageFooter>
      </PageCard>
    </n-spin>

    <n-modal
      v-model:show="isAddModalShow"
      :mask-closable="false"
      preset="card" :style="{ width: '720px' }"
      title="编辑问卷模板" head-style="divide"
    >
      <div flex items-center gap-10px py-20px>
        <div w-70px text="#666" text-right>
          可见范围
        </div>

        <AuthRange v-model:relations="relations" v-model:visibleRange="visibleRange" />
      </div>

      <n-button ml-200px mr-16px w-100px type="primary" @click="handleConfirm">
        确 定
      </n-button>
      <n-button mb-10px w-100px @click="isAddModalShow = false">
        取 消
      </n-button>
    </n-modal>

    <QuestionLib v-if="canEdit" v-model:content="surveyContent" @addQuestion="addQuestion" />
  </div>
</template>
