<script setup lang="ts">
import { useDebounceFn, useMouseInElement, useStorage } from '@vueuse/core'
import { getLabelByTypeAPI } from '@/api/intelligentFollow/manage'
import { getQuestionLibAPI } from '@/api/toolsFollow'

// withDefaults(defineProps<{
//   existQuestionIds: string[]
// }>(), {
//   existQuestionIds: () => [],
// })

const emits = defineEmits<{
  addQuestion: [content: Array<any>]
}>()

const isLibShow = inject<Ref<boolean>>('isLibShow')!
const libBtnRef = ref(null)
const libRef = ref(null)
const { isOutside: isLibBtnOutside } = useMouseInElement(libBtnRef)
const { isOutside: isLibOutside } = useMouseInElement(libRef)

const isPin = useStorage('isPin', false)

watchEffect(() => {
  if (isLibBtnOutside.value && isLibOutside.value)
    isLibShow.value = false

  if (!isLibBtnOutside.value || !isLibOutside.value || isPin.value)
    isLibShow.value = true
})

const surveyContent = defineModel<string>('content')!

const existQuestionIds = computed(() => {
  const content = JSON.parse(surveyContent.value!)
  return content?.elements?.map((item: any) => item.name)
})

interface Label {
  labelId: string
  labelName: string
  child: Label[]
}

interface Question {
  content: string
  topic: string
  labelName: string
  type: string
  questionId: string
}

const labelTree = ref<Label[]>([])
const labelId = ref(null)
const questionTopic = ref('')

const questionTypeMap: Record<string, {
  name: string
  color: string
}> = {
  radiogroup: {
    name: '单选',
    color: 'bg-#4ACFB1',
  },
  rating: {
    name: '评分',
    color: 'bg-#9EC97F',
  },
  checkbox: {
    name: '多选',
    color: 'bg-#FF9B54',
  },
  text: {
    name: '文本',
    color: 'bg-#9D96F5',
  },
  comment: {
    name: '文本',
    color: 'bg-#9D96F5',
  },
  multipletext: {
    name: '文本',
    color: 'bg-#9D96F5',
  },
  panel: {
    name: '面板',
    color: 'bg-#9D96F5',
  },
  monthyear: {
    name: '文本',
    color: 'bg-#9D96F5',
  },
  dropdown: {
    name: '下拉',
    color: 'bg-#5B96FD',
  },
}

async function getQuestionLabel() {
  try {
    const { data } = await getLabelByTypeAPI('QUESTION')
    if (data)
      labelTree.value = data
  }
  catch (error) {

  }
}

const questions = ref<Question[]>([])

async function getQuestionLib() {
  try {
    const { data } = await getQuestionLibAPI({
      labelId: labelId.value || '',
      topic: questionTopic.value,
    })
    if (data) {
      questions.value = data?.slice(0, 100)?.map((item) => {
        let realType = ''
        if (item.type === 'panel')
          realType = JSON.parse(item.content)?.elements[0]?.type || 'text'

        else
          realType = item.type

        return {
          ...item,
          type: realType,
        }
      })
    }
  }
  catch (error) {

  }
}

const handleInputTopic = useDebounceFn(getQuestionLib, 500)

function handleUpdateLabel() {
  getQuestionLib()
}

onMounted(() => {
  getQuestionLabel()
  getQuestionLib()
})
</script>

<template>
  <div ref="libBtnRef" fixed flex="~ col items-center" cursor-pointer class="right-0 top-50% h-72px w-24px bg-#FF9B54" style="border-radius: 3px 0 0 3px;">
    <SvgIcon mt-10px local-icon="slmc-icon-a-wenjian1" text="#fff" />
    <div text="#fff" mt-6px tracking-3px font="600" style="writing-mode: tb;">
      题库
    </div>
  </div>
  <div ref="libRef" fixed w-280px transition-all class="i-transform right-0 top-95px z-88 h-[calc(100vh-110px)]" :class="{ 'i-transform-show': isLibShow }" bg="#fff">
    <div p-14px>
      <div flex justify-between>
        <div flex items-center gap-14px>
          <div sub-title />
          <div text="#333">
            题库
          </div>
        </div>
        <div text="#3B8FD9" flex cursor-pointer items-center gap-10px @click="isPin = !isPin">
          <SvgIcon local-icon="slmc-icon-guding" size="16" :style="{ transform: isPin ? 'rotate(270deg)' : '' }" />
          <div>
            {{ isPin ? '取消固定' : '固定' }}
          </div>
        </div>
      </div>
      <div bg="#ccc" my-14px h-1px w-full />
      <div flex items-center gap-10px>
        <div text="#666">
          题目标签
        </div>
        <n-tree-select
          v-model:value="labelId"
          placeholder="全部"
          clearable
          :options="labelTree"
          :consistent-menu-width="false"
          :show-path="true"
          class="w-186px"
          label-field="labelName"
          key-field="labelId"
          children-field="child"
          check-strategy="parent"
          @update:value="handleUpdateLabel"
        />
      </div>
      <div mt-14px flex items-center gap-10px>
        <div text="#666">
          题目标题
        </div>
        <n-input v-model:value="questionTopic" class="!w-186px" @update:value="handleInputTopic" />
      </div>
      <div my-14px w-full style="border-top:1px dashed #ccc" />
      <div flex items-center gap-10px>
        <SvgIcon local-icon="slmc-icon-information_line" />
        <div text="#666">
          点击下方卡片，可将题目添加至问卷
        </div>
      </div>
    </div>

    <div flex="~ col" gap-14px overflow-auto px-8px pb-14px style="height: calc(100vh - 363px);">
      <div
        v-for="(question) in questions"
        :key="question.questionId"
        border="1px solid #dbdbdb"
        hover:border="1px solid #06AEA6" h-79px w-252px cursor-pointer b-rd-3px
        :class="existQuestionIds?.includes(question.questionId) ? 'q-exist' : ''"
        @click="() => {
          if (!existQuestionIds?.includes(question.questionId))
            emits('addQuestion', [JSON.parse(question.content)])
        }"
      >
        <div relative>
          <div h-32px w-full bg="#A5B8D1" op-20 />
          <div relative bottom-26px z-100 flex items-center justify-between px-10px>
            <div font-600>
              <n-ellipsis class="!max-w-180px">
                {{ question.labelName }}
              </n-ellipsis>
            </div>
            <div :class="existQuestionIds?.includes(question.questionId) ? 'bg-#ccc' : (questionTypeMap[question.type]?.color || 'bg-#3AC9A8')" h-20px w-44px b-rd-13px text="#fff center 12px" leading-20px>
              {{ questionTypeMap[question.type]?.name || question.type }}
            </div>
          </div>
        </div>
        <div text="#333" relative bottom-10px left-8px>
          <n-ellipsis max-w-200px>
            {{ question.topic }}
          </n-ellipsis>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.i-transform {
  transform: translate(280px);
}

.i-transform-show {
  transform: translate(0);
}

.q-exist {
  border: 1px solid #dbdbdb;
  opacity: 30%;
  cursor: not-allowed;

  &:hover {
    border: 1px solid #dbdbdb!important;
  }
}
</style>
