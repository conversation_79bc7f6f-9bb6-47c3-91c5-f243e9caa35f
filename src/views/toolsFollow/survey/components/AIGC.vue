<script setup lang="ts">
import { getLabelByTypeAPI } from '@/api/intelligentFollow/manage'
import { getAIScale } from '@/api/toolsFollow'

const emits = defineEmits<{
  addQuestion: [content: Array<any>]
  'update:isLoading': [isLoading: boolean]
}>()

interface Label {
  labelId: string
  labelName: string
  isActive: boolean
}

const isModalShow = ref(true)

const questionCount = ref(20)
const labelTree = ref<Label[]>([])

const isLabelAcitve = computed(() => {
  return labelTree.value.some(item => item.isActive)
})

async function getQuestionLabel() {
  try {
    const { data } = await getLabelByTypeAPI('QUESTION')
    if (data) {
      labelTree.value = data.map((item) => {
        return {
          labelId: item.labelId,
          labelName: item.labelName,
          isActive: false,
        }
      })

      labelTree.value.unshift({
        labelId: 'ALL',
        labelName: '全部',
        isActive: false,
      })
    }
  }
  catch (error) {

  }
}

function handleLabelClick(label: Label) {
  if (label.labelId === 'ALL') {
    if (label.isActive) {
      labelTree.value.forEach((item) => {
        item.isActive = false
      })
    }
    else {
      labelTree.value.forEach((item) => {
        item.isActive = true
      })
    }
  }
  else {
    labelTree.value.forEach((item) => {
      if (item.labelId === label.labelId)
        item.isActive = !item.isActive
    })
  }

  if (labelTree.value.some(item => item.isActive === false))
    labelTree.value[0].isActive = false

  if (labelTree.value.every((item, index) => index === 0 || item.isActive === true))
    labelTree.value[0].isActive = true
}

// async function processArray(data: any []) {
//   emits('update:isLoading', true)
//   for (const item of data) {
//     await new Promise(resolve => setTimeout(resolve, 400))
//     emits('addQuestion', item.content)
//   }
//   emits('update:isLoading', false)
// }

async function handleGenerate() {
  try {
    const { data } = await getAIScale({
      labels: labelTree.value.filter(item => item.isActive).map(item => item.labelId),
      nums: questionCount.value,
    })

    if (data) {
      isModalShow.value = false
      const allElements = data.map((item) => {
        return JSON.parse(item.content)
      })
      emits('update:isLoading', true)
      await new Promise(resolve => setTimeout(resolve, 4000))
      emits('addQuestion', allElements)
      emits('update:isLoading', false)
    }
  }
  catch (error) {

  }
  finally {
    emits('update:isLoading', false)
  }
}

onMounted(() => {
  getQuestionLabel()
})
</script>

<template>
  <div>
    <n-modal
      v-model:show="isModalShow"
      preset="card" :style="{ width: '560px' }"
      title="AI问卷" head-style="divide"
      :mask-closable="false"
    >
      <div p-20px>
        <div flex items-center>
          <SvgIcon local-icon="slmc-icon-app_required1" size="14" class="text-#F36969" />
          <div text="#666">
            问卷总题数
          </div>
          <n-input-number v-model:value="questionCount" ml-10px :show-button="false" :default-value="2" :min="1" :max="100" class="!w-154px">
            <template #suffix>
              <span text="#999">题</span>
            </template>
          </n-input-number>
        </div>
        <div bg="#f5f5f5" ml-94px mt-10px h-36px w-318px flex items-center gap-10px px-10px>
          <span flex-shrink-0> 1 </span>
          <n-slider v-model:value="questionCount" :step="1" />
          <span flex-shrink-0> 100 </span>
        </div>
        <div mt-14px flex>
          <SvgIcon ml-14px local-icon="slmc-icon-app_required1" size="14" class="text-#F36969" />
          <div text="#666">
            题目范围
          </div>
          <div flex="~ wrap" ml-10px w-350px gap-10px>
            <div v-for="(label) in labelTree" :key="label.labelId" class="question-label" :class="{ 'question-label-active': label.isActive }" @click="handleLabelClick(label)">
              {{ label.labelName }}
            </div>
          </div>
        </div>
        <div ml-93px mt-20px>
          <n-button :disabled="!isLabelAcitve" class="!w-100px" type="primary" @click="handleGenerate">
            保 存
          </n-button>
        </div>
      </div>
    </n-modal>
  </div>
</template>

<style lang="scss" scoped>
.question-label {
  border: 1px solid #dbdbdb;
  border-radius: 3px;
  --uno: h-26px min-w-72px cursor-pointer px-10px text-center text-12px leading-26px;

  &:hover {
    border:1px solid #06AEA6;
    --uno: bg-#E7F9F7 text-#06AEA6
  }
}

.question-label-active {
  border:1px solid #06AEA6;
  --uno: bg-#06AEA6 text-#fff;

  &:hover {
    color: #fff!important;
    background-color: #06AEA6!important;
  }
}
</style>
