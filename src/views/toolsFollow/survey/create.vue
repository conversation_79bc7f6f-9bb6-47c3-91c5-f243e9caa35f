<script setup lang="ts">
import QuestionLib from './components/QuestionLib.vue'
import AI from './components/AIGC.vue'
import { Breadcrumb } from '@/layouts/common'
import SurveyEditor from '@/components/Survey/Editor.vue'
import { isSurveyAvailableAPI, saveSurveyAPI } from '@/api/toolsFollow/survey'
import { useAuthStore } from '@/store'
import PageFooter from '@/components/PageFooter/index.vue'
import AuthRange from '@/views/toolsFollow/components/AuthRange.vue'

const { userInfo } = useAuthStore()
const router = useRouter()
const route = useRoute()
const surveyEditorRef = ref<InstanceType<typeof SurveyEditor>>()
const surveyContent = ref('{}')

const followPlanId = route.query.followPlanId as string
const isAI = route.query.ai as string

const isAddModalShow = ref(false)
const relations = ref<string[]>([])
const visibleRange = ref('ALL_USER')

const isLoading = ref(false)
async function handleSave() {
  try {
    isLoading.value = true
    await new Promise(resolve => setTimeout(resolve, 600))
    const data = JSON.parse(surveyContent.value)

    if (!data?.title) {
      window.$message.warning('请输入问卷标题')
      isLoading.value = false
      return false
    }

    if (followPlanId) {
      // isAddModalShow.value = true
      handleConfirm()
    }
    else {
      const { data: available } = await isSurveyAvailableAPI({
        name: data.title,
      })

      if (!available) {
        window.$message.warning('问卷标题不能重复')
        isLoading.value = false
        return false
      }

      isAddModalShow.value = true
    }
  }
  catch (error) {
    window.$message.warning('请检查问卷模板')
  }
  finally {
    isLoading.value = false
  }
}

async function handleConfirm() {
  try {
    isLoading.value = true
    isAddModalShow.value = false
    await new Promise(resolve => setTimeout(resolve, 600))

    const data = JSON.parse(surveyContent.value)
    const { data: results } = await saveSurveyAPI({
      scaleName: data.title,
      content: JSON.stringify(data),
      visibleRange: visibleRange.value,
      relations: relations.value,
      // 有followPlanId 不保存为模板
      isTemplate: !followPlanId,
      introduce: data.introduce,
      createId: userInfo.id,
      createName: userInfo.userName,
    })

    if (results) {
      window.$message.success('保存成功')
      if (followPlanId) {
        router.replace({
          path: '/intelligentFollow/manage/create',
          query: {
            id: followPlanId,
            restore: '1',
            seriesId: results.seriesId,
            relationName: encodeURIComponent(data.title),
          },
        })
      }
      else {
        router.replace('/toolsFollow/survey')
      }
    }
    else {
      window.$message.error('保存失败')
    }
  }
  catch (error) {

  }
  finally {
    isLoading.value = false
  }
}

function handleGoBack() {
  router.replace({
    path: '/intelligentFollow/manage/create',
    query: {
      id: followPlanId,
      restore: '1',
    },
  })
}

async function addQuestion(elements: Array<any>) {
  surveyEditorRef.value?.handleAdd(elements)
}

onMounted(() => {
  surveyEditorRef.value?.renderSurvey(true)
})

const isLibShow = ref(false)
provide('isLibShow', isLibShow)
</script>

<template>
  <div>
    <Breadcrumb
      :bread-list="[
        {
          title: '随访工具',
          link: null,
          key: 'toolsFollow',
        },
        {
          title: '问卷模板',
          link: '/toolsFollow/survey',
          key: 'toolsFollow_survey',
        },
        {
          title: '新增问卷',
          link: null,
          key: 'toolsFollow_survey_create',
        },
      ]"
    />

    <n-spin type="uni" size="medium" :show="isLoading">
      <PageCard breadcrumb>
        <SurveyEditor ref="surveyEditorRef" v-model:content="surveyContent" mb-60px />
        <PageFooter :right-zero="isLibShow">
          <template #default>
            <n-button w-100px type="primary" @click="handleSave">
              保 存
            </n-button>

            <n-button v-if="followPlanId" secondary w-100px @click="handleGoBack">
              返 回
            </n-button>
            <n-button v-else secondary w-100px @click="router.go(-1)">
              取 消
            </n-button>
          </template>
        </PageFooter>
      </PageCard>
    </n-spin>

    <n-modal
      v-model:show="isAddModalShow"
      :mask-closable="false"
      preset="card" :style="{ width: '720px' }"
      title="新增问卷模板" head-style="divide"
    >
      <div flex items-center gap-10px py-20px>
        <div w-70px text="#666" text-right>
          可见范围
        </div>

        <AuthRange v-model:relations="relations" v-model:visibleRange="visibleRange" />
      </div>

      <n-button ml-200px mr-16px w-100px type="primary" @click="handleConfirm">
        确 定
      </n-button>
      <n-button mb-10px w-100px @click="isAddModalShow = false">
        取 消
      </n-button>
    </n-modal>
    <QuestionLib v-model:content="surveyContent" @addQuestion="addQuestion" />
    <AI v-if="isAI" v-model:isLoading="isLoading" @addQuestion="addQuestion" />
  </div>
</template>
