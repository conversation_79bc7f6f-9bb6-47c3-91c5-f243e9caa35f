<script setup lang="ts">
import dayjs from 'dayjs'
import { useDialog } from 'wowjoy-vui'
import TemplateAddCard from '../components/TemplateAddCard.vue'
import TemplateCard from '../components/TemplateCard.vue'
import { Breadcrumb } from '@/layouts/common'
import { deleteSurveyAPI, getSurveyListAPI } from '@/api/toolsFollow'
import { useAuthStore } from '@/store'

const { userInfo } = useAuthStore()

const router = useRouter()
const route = useRoute()
const followPlanId = route.query.followPlanId

const dialog = useDialog()
const search = ref('')

function handleAddClick(ai = '') {
  router.push(`/toolsFollow/survey/create?ai=${ai}`)
}

const dataList = ref<any[]>([])
const isLoading = ref(false)
async function getList() {
  try {
    isLoading.value = true
    const { data } = await getSurveyListAPI(search.value || '')
    dataList.value = data?.map((item: any) => {
      return {
        canEdit: item.createId === userInfo.id,
        ...item,
      }
    })
  }
  catch (error) {
    window.$message.error('获取问卷模板失败')
  }
  finally {
    isLoading.value = false
  }
}

function handleDelete(item: any) {
  if (!item.canEdit)
    return false

  dialog.warning({
    title: '您确认要删除该问卷吗？',
    content: '问卷删除后将不可恢复！',
    positiveText: '确定',
    negativeText: '取消',
    btnGhost: true,
    onPositiveClick: async () => {
      try {
        const { data } = await deleteSurveyAPI(item.scaleId)
        if (data) {
          window.$message.success('删除成功')
          getList()
        }
        else {
          window.$message.error('删除失败')
        }
      }
      catch (error) {
        window.$message.error('删除失败')
      }
    },
  })
}

function toEditPage(item: any) {
  if (item.canEdit)
    router.push(`/toolsFollow/survey/edit?id=${item.scaleId}`)
}

function toViewPage(item: any) {
  router.push(`/toolsFollow/survey/edit?id=${item.scaleId}&view=1`)
}

function handleReferenceClick(item: any) {
  router.replace({
    path: '/intelligentFollow/manage/create',
    query: {
      id: followPlanId,
      restore: '1',
      seriesId: item.seriesId,
      relationName: encodeURIComponent(item.scaleName),
      type: item.type,
      createAt: dayjs(item.createTime).format('YYYY-MM-DD'),
    },
  })
}

function goBack() {
  router.replace({
    path: '/intelligentFollow/manage/create',
    query: {
      id: followPlanId,
      restore: '1',
    },
  })
}

onMounted(() => {
  getList()
})
</script>

<template>
  <div>
    <Breadcrumb
      :bread-list="[
        {
          title: '随访工具',
          link: null,
          key: 'toolsFollow',
        },
        {
          title: '问卷模板',
          link: null,
          key: 'toolsFollow_survey',
        },
      ]"
    />

    <n-spin type="uni" :show="isLoading" size="medium">
      <PageCard breadcrumb>
        <PageTitle>
          问卷模板
        </PageTitle>

        <n-input-group class="my-15px !w-350px">
          <n-input
            v-model:value="search"
            placeholder="请输入问卷模板名称搜索"
            clearable
            @clear="() => {
              search = ''
              getList()
            }"
          />
          <n-button type="primary" class="!min-w-16px" @click="getList">
            <template #icon>
              <SvgIcon local-icon="slmc-icon-search" size="16" style="color: #fff; cursor: pointer;" />
            </template>
          </n-button>
        </n-input-group>

        <div flex="~ wrap" mb-60px gap-14px>
          <TemplateAddCard v-if="!followPlanId" h-174px width="325px" title="新增问卷模板" @click="() => handleAddClick('1')">
            <template #content>
              <div relative>
                <img w-323px src="@/assets/images/ai-survey.png" alt="" srcset="">
                <div text="#5B5B5B" absolute bottom-39px left-112px leading-none>
                  新增AI问卷模板
                </div>
              </div>
            </template>
          </TemplateAddCard>
          <TemplateAddCard v-if="!followPlanId" h-174px width="325px" title="新增问卷模板" @click="() => handleAddClick('')" />
          <TemplateCard v-for="item in dataList" :key="item.scaleId" :is-default="item.type === 'SYSTEM'" h-174px width="325px" @click="toViewPage(item)">
            <div flex="~ col">
              <div text="16px #333" font-500>
                {{ item.scaleName }}
              </div>
              <div bg="#e8e8e8" mb-20px mt-14px h-1px w-full />
              <div flex>
                <div text="#666 12px" class="mb-10px flex-shrink-0">
                  适用场景：
                </div>
                <n-ellipsis text="#333 12px" :line-clamp="2" relative max-w-250px break-all>
                  {{ item.introduce || '-' }}
                </n-ellipsis>
              </div>

              <div flex>
                <div w-160px flex>
                  <span text="#666 12px" class="flex-shrink-0">创建日期：</span>
                  <span text="#333 12px">{{ dayjs(item.createTime).format('YYYY-MM-DD') }}</span>
                </div>
                <div v-if="item.type !== 'SYSTEM'" flex>
                  <span text="#666 12px" class="flex-shrink-0">创建人：</span>
                  <span text="#333 12px">{{ item.createId === userInfo.id ? '本人创建' : item.createName }}</span>
                </div>
              </div>
            </div>
            <template #footer>
              <div v-if="followPlanId" h-full flex items-center justify-center @click.stop="handleReferenceClick(item)">
                <div flex items-center gap-10px>
                  <SvgIcon local-icon="slmc-icon-yinyong" size="14" />
                  <span text="14px #3B8FD9">引用模板</span>
                </div>
              </div>
              <div v-else h-full flex items-center justify-center gap-56px :class="item.canEdit ? '' : '!cursor-not-allowed op30'" @click.stop="toEditPage(item)">
                <n-popover v-if="item.createId !== userInfo.id">
                  <template #trigger>
                    <div flex items-center gap-10px>
                      <SvgIcon local-icon="slmc-icon-edit1" size="14" />
                      <span text="14px #3B8FD9">编辑</span>
                    </div>
                  </template>
                  <span>非本人创建，不可编辑</span>
                </n-popover>
                <div v-else flex items-center gap-10px>
                  <SvgIcon local-icon="slmc-icon-edit1" size="14" />
                  <span text="14px #3B8FD9">编辑</span>
                </div>

                <div h-14px w-1px bg="#3B8FD9" />
                <n-popover v-if="item.createId !== userInfo.id">
                  <template #trigger>
                    <div flex items-center gap-10px>
                      <SvgIcon local-icon="slmc-icon-delete2" size="14" />
                      <span text="14px #3B8FD9">删除</span>
                    </div>
                  </template>
                  <span>非本人创建，不可编辑</span>
                </n-popover>
                <div v-else flex items-center gap-10px @click.stop="handleDelete(item)">
                  <SvgIcon local-icon="slmc-icon-delete2" size="14" />
                  <span text="14px #3B8FD9">删除</span>
                </div>
              </div>
            </template>
          </TemplateCard>
        </div>
      </PageCard>
    </n-spin>

    <PageFooter v-if="followPlanId">
      <n-button w-100px @click="goBack">
        返 回
      </n-button>
    </PageFooter>
  </div>
</template>

<style lang="scss" scoped>
.counter {
  --uno: text-#FF9B54 text-20px font-700 relative bottom-3px;
}
</style>
