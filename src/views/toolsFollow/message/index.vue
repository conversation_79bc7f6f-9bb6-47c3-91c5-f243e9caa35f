<script setup lang="ts">
import dayjs from 'dayjs'
import TemplateAddCard from '../components/TemplateAddCard.vue'
import TemplateCard from '../components/TemplateCard.vue'
import AuthRange from '../components/AuthRange.vue'
import { Breadcrumb } from '@/layouts/common'
import { deleteSmsAPI, getSmsListAPI, saveSmsAPI } from '@/api/toolsFollow'
import { useAuthStore } from '@/store'

const { userInfo } = useAuthStore()

const messageForm = ref({
  smsTmpContent: '',
  smsTmpName: '',
  smsTmpMemo: '',
  relations: [] as {
    userId: string
    userName: string
  }[],
  visibleRange: 'ALL_USER',
  createId: userInfo.id,
  createName: userInfo.userName,
  smsTmpId: '',
})

const isAddModalShow = ref(false)

const searchKeyword = ref('')

function handleClick() {
  messageForm.value.smsTmpContent = ''
  messageForm.value.smsTmpName = ''
  messageForm.value.smsTmpMemo = ''
  messageForm.value.relations = []
  messageForm.value.visibleRange = 'ALL_USER'
  messageForm.value.smsTmpId = ''
  isAddModalShow.value = true
}

async function handleSaveSms() {
  try {
    if (!messageForm.value.smsTmpName) {
      window.$message.warning('请输入模板名称')
      return false
    }

    if (!messageForm.value.smsTmpContent) {
      window.$message.warning('请输入短信内容')
      return false
    }

    const { data } = await saveSmsAPI(messageForm.value)

    if (data) {
      window.$message.success('保存成功')
      messageForm.value.smsTmpContent = ''
      messageForm.value.smsTmpName = ''
      messageForm.value.smsTmpMemo = ''
      messageForm.value.relations = []
      messageForm.value.visibleRange = 'ALL_USER'
      isAddModalShow.value = false
      getSmsList()
    }
  }
  catch (error) {

  }
}

const dataList = ref<any[]>([])
const isLoading = ref(false)
async function getSmsList() {
  try {
    isLoading.value = true
    const { data } = await getSmsListAPI({
      operateId: userInfo.id,
      queryName: searchKeyword.value,
    })

    dataList.value = data
  }
  catch (error) {

  }
  finally {
    isLoading.value = false
  }
}

function handleEditClick(item: any) {
  messageForm.value.smsTmpId = item.smsTmpId
  messageForm.value.smsTmpName = item.smsTmpName
  messageForm.value.smsTmpContent = item.smsTmpContent
  messageForm.value.smsTmpMemo = item.smsTmpMemo
  messageForm.value.visibleRange = item.visibleRange
  messageForm.value.relations = item.relations
  isAddModalShow.value = true
}

function handleDelete(item: any) {
  window.$dialog.warning({
    title: '确定删除本短信模板吗?',
    positiveText: '确定',
    negativeText: '取消',
    btnGhost: true,
    onPositiveClick: async () => {
      try {
        const { data } = await deleteSmsAPI({
          operateId: userInfo.id,
          smsTmpId: item.smsTmpId,
        })

        if (data) {
          window.$message.success('删除成功')
          getSmsList()
        }
        else {
          window.$message.error('删除失败')
        }
      }
      catch (error) {
        window.$message.error('删除失败')
      }
    },
  },
  )
}

async function handleClear() {
  await nextTick()
  getSmsList()
}

onMounted(() => {
  getSmsList()
})
</script>

<template>
  <div>
    <Breadcrumb
      :bread-list="[
        {
          title: '随访工具',
          link: null,
          key: 'toolsFollow',
        },
        {
          title: '短信模板',
          link: null,
          key: 'toolsFollow_message',
        },
      ]"
    />
    <n-spin type="uni" :show="isLoading" size="medium">
      <PageCard breadcrumb>
        <PageTitle>
          短信模板
        </PageTitle>

        <n-input-group class="my-15px !w-350px">
          <n-input
            v-model:value="searchKeyword" placeholder="请输入短信模板名称搜索" clearable @clear="handleClear"
          />
          <n-button type="primary" class="!min-w-16px" @click="getSmsList">
            <template #icon>
              <SvgIcon local-icon="slmc-icon-search" size="16" style="color: #fff; cursor: pointer;" />
            </template>
          </n-button>
        </n-input-group>

        <div flex="~ wrap" gap-14px>
          <TemplateAddCard h-204px title="新增短信模板" width="325px" @click="handleClick" />
          <TemplateCard v-for="(item, index) in dataList" :key="index" width="325px" h-204px>
            <div flex="~ col">
              <div flex justify-between>
                <div text="16px #333" font-500>
                  {{ item.smsTmpName }}
                </div>
                <div v-if="item.tmpState === 'IN_AUDIT'" h-20px w-56px flex items-center justify-center b-rd-10px text="12px #fff" bg="#FF9B54">
                  审核中
                </div>
              </div>

              <div bg="#e8e8e8" mb-20px mt-14px h-1px w-full />

              <div mb-10px flex>
                <div text="#666 12px" class="flex-shrink-0">
                  适用场景：
                </div>
                <div text="#333 12px" flex leading-14px>
                  {{ item.smsTmpMemo || '-' }}
                </div>
              </div>

              <div flex gap-40px>
                <div mb-10px flex>
                  <span text="#666 12px" class="flex-shrink-0">创建日期：</span>
                  <span text="#333 12px">{{ dayjs(item.createTime).format('YYYY-MM-DD') }}</span>
                </div>
                <div v-if="item.createId !== 'SYSTEM'" flex>
                  <span text="#666 12px" class="flex-shrink-0">创建人：</span>
                  <span text="#333 12px">{{ item.createId === userInfo.id ? '本人创建' : item.createName }}</span>
                </div>
              </div>

              <div flex gap-40px>
                <div flex>
                  <span text="#666 12px" class="flex-shrink-0">发送次数：</span>
                  <span class="counter">{{ (item.sendNum || 0).toLocaleString() }}</span>
                </div>
              </div>
            </div>
            <template #footer>
              <div :class="item.createId === userInfo.id ? '' : '!cursor-not-allowed op30' " h-full flex items-center justify-center gap-56px>
                <n-popover v-if="item.createId !== userInfo.id">
                  <template #trigger>
                    <div flex items-center gap-10px>
                      <SvgIcon local-icon="slmc-icon-edit1" size="14" />
                      <span text="14px #3B8FD9">编辑</span>
                    </div>
                  </template>
                  <span>非本人创建，不可编辑</span>
                </n-popover>
                <div v-else flex items-center gap-10px @click="handleEditClick(item)">
                  <SvgIcon local-icon="slmc-icon-edit1" size="14" />
                  <span text="14px #3B8FD9">编辑</span>
                </div>

                <div h-14px w-1px bg="#3B8FD9" />
                <n-popover v-if="item.createId !== userInfo.id">
                  <template #trigger>
                    <div flex items-center gap-10px>
                      <SvgIcon local-icon="slmc-icon-delete2" size="14" />
                      <span text="14px #3B8FD9">删除</span>
                    </div>
                  </template>
                  <span>非本人创建，不可编辑</span>
                </n-popover>
                <div v-else flex items-center gap-10px @click="handleDelete(item)">
                  <SvgIcon local-icon="slmc-icon-delete2" size="14" />
                  <span text="14px #3B8FD9">删除</span>
                </div>
              </div>
            </template>
          </TemplateCard>
        </div>
      </PageCard>
    </n-spin>

    <n-modal
      v-model:show="isAddModalShow"
      preset="card" :style="{ width: '720px' }"
      :title="`${messageForm.smsTmpId ? '编辑' : '新增'}短信模板`" head-style="divide"
    >
      <div flex="~ col" gap-14px py-20px>
        <div flex items-center gap-10px>
          <div w-70px flex items-center text-right>
            <SvgIcon local-icon="slmc-icon-app_required1" size="14" class="text-#F36969" />
            <div text="#666">
              模板名称
            </div>
          </div>
          <n-input v-model:value="messageForm.smsTmpName" class="!w-540px" show-count :maxlength="10" />
        </div>

        <div flex items-start gap-10px>
          <div w-70px flex items-center text-right>
            <SvgIcon local-icon="slmc-icon-app_required1" size="14" class="text-#F36969" />
            <div text="#666">
              短信内容
            </div>
          </div>
          <n-input v-model:value="messageForm.smsTmpContent" type="textarea" class="!w-540px" show-count :maxlength="70" />
        </div>

        <div flex items-center gap-10px>
          <div w-70px text="#666" text-right>
            可见范围
          </div>

          <AuthRange v-model:visibleRange="messageForm.visibleRange" v-model:relationsRange="messageForm.relations" />
        </div>

        <div flex items-center gap-10px>
          <div w-70px text="#666" text-right>
            备注
          </div>
          <n-input v-model:value="messageForm.smsTmpMemo" class="!w-540px" show-count :maxlength="10" />
        </div>

        <div ml-80px>
          <n-button mr-16px w-100px type="primary" @click="handleSaveSms">
            保 存
          </n-button>
          <n-button w-100px @click="isAddModalShow = false">
            取 消
          </n-button>
        </div>
      </div>
    </n-modal>
  </div>
</template>

<style lang="scss" scoped>
.counter {
  --uno: text-#FF9B54 text-20px font-700 relative bottom-3px
}
</style>
