<script setup lang="ts">
import dayjs from 'dayjs'
import { Breadcrumb } from '@/layouts/common'
import { useAuthStore } from '@/store'
import { type WarningData, checkWarningName } from '@/api/toolsFollow'
import { deleteWarningAPI, getWarningPageAPI, saveWarningAPI, updateWarningAPI } from '@/api/toolsFollow'

const isLoading = ref(false)
const { userInfo } = useAuthStore()
const isAddModalShow = ref(false)

const formData = ref<WarningData>({
  warningName: '',
  content: '',
  createId: userInfo.id,
  createName: userInfo.userName,
  warningId: '',
  updateId: userInfo.id,
  updateName: userInfo.userName,
})

async function handleSaveWarning() {
  try {
    if (!formData.value.warningName) {
      window.$message.warning('请输入预警事件名称')
      return false
    }

    const { data: nameAvailable } = await checkWarningName({
      warningName: formData.value.warningName,
      warningId: formData.value.warningId,
    })

    if (!nameAvailable) {
      window.$message.warning('预警事件名称不能重复')
      return false
    }

    if (!formData.value.content) {
      window.$message.warning('请输入预警事件内容')
      return false
    }

    if (formData.value.warningId) {
      const _formData = {
        ...formData.value,
      }

      delete _formData.createId
      delete _formData.createName

      const { data } = await updateWarningAPI(_formData)
      if (data) {
        window.$message.success('保存成功')
        isAddModalShow.value = false
        getDataList()
      }
    }
    else {
      const { data } = await saveWarningAPI(formData.value)
      if (data) {
        window.$message.success('保存成功')
        isAddModalShow.value = false
        getDataList()
      }
    }
  }
  catch (error) {

  }
}

const searchName = ref('')
const dataList = ref<WarningData[]>([])
const page = ref({
  page: 1,
  size: 10,
  total: 0,
})
async function getDataList() {
  try {
    const { data } = await getWarningPageAPI({
      organId: userInfo.organId,
      page: page.value.page,
      size: page.value.size,
      userId: userInfo.id,
      warningName: searchName.value,
    })

    dataList.value = data.records
    page.value.total = data.total
  }
  catch (error) {

  }
}

async function deleteHandler(row: any) {
  window.$dialog.warning({
    title: '确定删除此预警模板吗？',
    positiveText: '确定',
    negativeText: '取消',
    btnGhost: true,
    onPositiveClick: async () => {
      try {
        const { data } = await deleteWarningAPI(row.warningId)
        if (data) {
          window.$message.success('删除成功')
          getDataList()
        }
      }
      catch (error) {
        window.$message.error('删除失败')
      }
    },
  })
}

onMounted(() => {
  getDataList()
})

function handleEditBtnClick(row: WarningData) {
  formData.value.warningName = row.warningName
  formData.value.content = row.content
  formData.value.warningId = row.warningId
  isAddModalShow.value = true
}
</script>

<template>
  <div>
    <Breadcrumb
      :bread-list="[
        {
          title: '随访工具',
          link: null,
          key: 'toolsFollow',
        },
        {
          title: '预警模版',
          link: null,
          key: 'toolsFollow_warning',
        },
      ]"
    />

    <n-spin type="uni" :show="isLoading" size="medium">
      <PageCard breadcrumb>
        <PageTitle>
          预警模版
        </PageTitle>
        <div my-14px flex justify-between>
          <div flex items-center gap-10px>
            <div text="#666">
              预警事件名称
            </div>

            <n-input-group class="my-15px !w-350px">
              <n-input v-model:value="searchName" clearable class="!w-450px" />

              <n-button type="primary" class="!min-w-16px" @click="getDataList">
                <template #icon>
                  <SvgIcon local-icon="slmc-icon-search" size="16" style="color: #fff; cursor: pointer;" />
                </template>
              </n-button>
            </n-input-group>
          </div>
          <n-button
            type="primary" @click="() => {
              formData.content = ''
              formData.warningName = ''
              isAddModalShow = true
            }"
          >
            新增预警
          </n-button>
        </div>

        <el-table :data="dataList" stripe>
          <el-table-column label="序号" type="index" width="80" />
          <el-table-column label="预警事件名称" prop="warningName" width="180" />
          <el-table-column label="预警内容" prop="content" min-width="380">
            <template #default="{ row }">
              <n-ellipsis :line-clamp="2">
                {{ row.content }}
                <template #tooltip>
                  <div max-w-500px>
                    {{ row.content }}
                  </div>
                </template>
              </n-ellipsis>
            </template>
          </el-table-column>
          <el-table-column label="创建人">
            <template #default="{ row }">
              <div v-if="row.createId === 'SYSTEM'" flex items-center>
                <div>
                  系统默认
                </div>
                <img ml-5px h-20px w-20px flex-shrink-0 src="@/assets/images/default-tag.png" alt="">
              </div>
              <div v-else>
                {{ row.createName }}
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="createTime" label="创建时间" width="150" :formatter="({ createTime }) => dayjs(createTime).format('YYYY-MM-DD')" />
          <el-table-column label="操作" fixed="right" width="100">
            <template #default="{ row }">
              <div flex items-center gap-6px>
                <div
                  v-if="row.createId !== 'SYSTEM'"
                  uno-link
                  @click="handleEditBtnClick(row)"
                >
                  编辑
                </div>
                <div h-14px w-1px bg="#3B8FD9" />
                <div
                  uno-link
                  @click="deleteHandler(row)"
                >
                  删除
                </div>
              </div>
            </template>
          </el-table-column>
          <template #empty>
            <div flex justify-center>
              <DataEmpty />
            </div>
          </template>
        </el-table>

        <div v-if="page.total > 0" mt-20px w-full flex justify-end>
          <n-pagination
            v-model:page="page.page"
            v-model:page-size="page.size"
            :item-count="page.total"
            :page-sizes="[5, 10, 20, 30]"
            show-size-picker
            show-quick-jumper
            @update:page="getDataList"
            @update:page-size="() => {
              page.page = 1
              getDataList()
            }"
          />
        </div>
      </PageCard>
    </n-spin>

    <n-modal
      v-model:show="isAddModalShow"
      preset="card" :style="{ width: '560px' }"
      :title="`${formData.warningId ? '编辑' : '新增'}预警模板`" head-style="divide"
    >
      <div flex="~ col" gap-14px py-20px>
        <div flex items-center gap-10px>
          <div w-98px flex items-center text-right>
            <SvgIcon local-icon="slmc-icon-app_required1" size="14" class="text-#F36969" />
            <div text="#666">
              预警事件名称
            </div>
          </div>
          <n-input v-model:value="formData.warningName" class="!w-320px" show-count :maxlength="10" />
        </div>

        <div flex items-start gap-10px>
          <div w-98px flex items-center text-right>
            <SvgIcon local-icon="slmc-icon-app_required1" size="14" class="text-#F36969" />
            <div text="#666">
              预警事件内容
            </div>
          </div>
          <n-input v-model:value="formData.content" type="textarea" class="!w-320px" show-count :maxlength="500" />
        </div>

        <div ml-108px>
          <n-button mr-16px w-100px type="primary" @click="handleSaveWarning">
            保 存
          </n-button>
          <n-button w-100px @click="isAddModalShow = false">
            取 消
          </n-button>
        </div>
      </div>
    </n-modal>
  </div>
</template>
