<script setup lang="ts">
import { cloneDeep } from 'lodash-es'
import { useWindowSize } from '@vueuse/core'
import AuthRange from '../components/AuthRange.vue'
import { Breadcrumb } from '@/layouts/common'
import RichEditor from '@/components/RichEditor/index.vue'
import { deleteEduAPI, getEduDetailAPI, getEduPageAPI, getEduTreeAPI, isEduNameAvailableAPI, saveEduAPI, updateEduAPI } from '@/api/toolsFollow'
import { useAuthStore } from '@/store'

interface Label {
  labelName: string
  labelId: string
  isActive: boolean
}

const { userInfo } = useAuthStore()

const isAddModalShow = ref(false)
const isCheckModalShow = ref(false)

const richEditorRef = ref<InstanceType<typeof RichEditor>>()

const modalForm = ref<Record<string, any>>({
  content: '',
  createId: userInfo.id,
  createName: userInfo.userName,
  isTemplate: true,
  labelId: null,
  introduce: '',
  title: '',
  relations: [] as string[],
  visibleRange: 'ALL_USER',
})

const labelKeyword = ref('')
const eduLabelTree = ref<Label[]>([])

const currentActiveId = computed(() => {
  return eduLabelTree.value.find(item => item.isActive)?.labelId
})

async function getEduTree() {
  try {
    const { data } = await getEduTreeAPI()
    eduLabelTree.value = [
      {
        labelName: '全部',
        labelId: 'ALL',
      },
      ...data,
    ].map((item) => {
      return {
        isActive: item.labelId === 'ALL',
        ...item,
      }
    })
  }
  catch (error) {
    console.log(error)
  }
}

function handleLabelClick(item: Label) {
  if (item.isActive)
    return false

  eduLabelTree.value.forEach((item2) => {
    item2.isActive = (item.labelId === item2.labelId)
  })

  getEduPage()
}

let selectedEduId = ''

async function handleSaveClick() {
  try {
    if (!modalForm.value.title) {
      window.$message.warning('请输入模板名称')
      return false
    }

    if (richEditorRef.value?.isEmpty) {
      window.$message.warning('请输入模板内容')
      return false
    }

    if (!modalForm.value.labelId) {
      window.$message.warning('请选择模板分类')
      return false
    }

    const { data: nameAvailableData } = await isEduNameAvailableAPI({
      educationId: selectedEduId,
      title: modalForm.value.title,
    })

    if (!nameAvailableData) {
      window.$message.warning('问卷标题不能重复')
      return false
    }

    if (selectedEduId) {
      const form = cloneDeep(modalForm.value)
      delete form.createId
      delete form.createName

      const { data } = await updateEduAPI({
        educationId: selectedEduId,
        updateId: userInfo.id,
        updateName: userInfo.userName,
        ...form,
      })

      if (data) {
        window.$message.success('修改成功')
        isAddModalShow.value = false
        getEduPage()
        selectedEduId = ''
      }
    }
    else {
      const { data } = await saveEduAPI({
        ...modalForm.value,
      })

      if (data) {
        window.$message.success('保存成功')
        isAddModalShow.value = false
        getEduPage()
        modalForm.value.content = ''
        modalForm.value.labelId = null
        modalForm.value.introduce = ''
        modalForm.value.title = ''
        modalForm.value.relations = []
        modalForm.value.visibleRange = 'ALL_USER'
      }
    }
  }
  catch (error) {
    console.log(error)
  }
}

async function handleEditClick(row: any, type: 'edit' | 'check') {
  try {
    selectedEduId = row.educationId
    const { data } = await getEduDetailAPI(row.educationId)
    Object.keys(modalForm.value).forEach((key: string) => {
      modalForm.value[key] = data[key]
    })
    if (type === 'edit')
      isAddModalShow.value = true
    else
      isCheckModalShow.value = true
  }
  catch (error) {

  }
}

function handleAddClick() {
  isAddModalShow.value = true

  modalForm.value.content = ''
  modalForm.value.labelId = null
  modalForm.value.introduce = ''
  modalForm.value.title = ''
  modalForm.value.relations = []
  modalForm.value.visibleRange = 'ALL_USER'
  selectedEduId = ''
}

function handleDelete(row: any) {
  window.$dialog?.warning({
    title: '您确认要删除该吗？',
    content: '问卷删除后将不可恢复！',
    positiveText: '确定',
    negativeText: '取消',
    btnGhost: true,
    onPositiveClick: async () => {
      try {
        const { data } = await deleteEduAPI(row.educationId)
        if (data) {
          window.$message.success('删除成功')
          getEduPage()
        }
      }
      catch {

      }
    },
  })
}

const pageList = ref<any[]>([])
const page = ref({
  page: 1,
  size: 10,
  total: 0,
})
const isLoading = ref(false)
async function getEduPage() {
  try {
    isLoading.value = true
    const labelId = currentActiveId.value === 'ALL' ? '' : currentActiveId.value
    const { data } = await getEduPageAPI({
      labelId,
      organId: userInfo.organId,
      page: page.value.page,
      size: page.value.size,
      title: '',
      userId: userInfo.id,
    })

    pageList.value = data.records
    page.value.total = data.total
  }
  catch (error) {

  }
  finally {
    isLoading.value = false
  }
}

const { height } = useWindowSize()

const tableMaxHeight = computed(() => {
  if (height.value <= 700)
    return 'calc(100vh - 260px)'
  else
    return 'auto'
})

onMounted(() => {
  getEduTree()
  getEduPage()
})
</script>

<template>
  <div>
    <n-spin type="uni" :show="isLoading" size="medium">
      <Breadcrumb
        :bread-list="[
          {
            title: '随访工具',
            link: null,
            key: 'toolsFollow',
          },
          {
            title: '患教模板',
            link: null,
            key: 'toolsFollow_education',
          },
        ]"
      />

      <PageCard breadcrumb padding="0px 0px 0px 0px">
        <PageTitle mb-14px ml-14px mt-14px>
          患教模板
        </PageTitle>

        <div flex>
          <div w-230px flex-shrink-0 overflow-auto>
            <n-input v-model:value="labelKeyword" clearable mb-10px ml-14px placeholder="请输入分类名称搜索" class="!w-200px" />

            <div
              v-for="(item) in eduLabelTree"
              :key="item.labelId"
            >
              <div
                v-if="item.labelName.includes(labelKeyword)"
                text="12px"
                h-32px flex items-center pl-20px
                hover="bg-#FFFBE0" :class="item.isActive ? 'bg-#FFFBE0 text-#06AEA6 cursor-not-allowed' : 'cursor-pointer'"
                @click="handleLabelClick(item)"
              >
                {{ item.labelName }}
              </div>
            </div>
          </div>
          <div mr-14px class="h-[calc(100vh-154px)]" w-1px flex-shrink-0 bg="#ccc" />
          <div class="w-[calc(100vw-494px)] pr-14px">
            <n-button type="primary" mb-14px @click="handleAddClick">
              新增患教模板
            </n-button>

            <el-table
              :data="pageList"
              stripe
              show-overflow-tooltip
              :max-height="tableMaxHeight"
              w-full
            >
              <el-table-column label="序号" width="100" type="index" />
              <el-table-column label="宣教标题" prop="title" min-width="250" :formatter="({ title }) => title || '-'" />
              <el-table-column label="备注" prop="introduce" min-width="250" :formatter="({ introduce }) => introduce || '-'" />
              <el-table-column prop="createName" label="创建人" min-width="150" :formatter="({ createName, createId }) => createId === 'SYSTEM' ? '系统默认' : createName" />
              <el-table-column label="创建时间" min-width="250" prop="createTime" :formatter="({ createTime }) => createTime || '-'" />
              <el-table-column label="操作" width="100" fixed="right">
                <template #default="{ row }">
                  <div v-if="userInfo.id === row.createId" flex items-center gap-5px>
                    <span uno-link @click="handleEditClick(row, 'edit')">
                      编辑
                    </span>
                    <div h-14px w-1px bg="#3B8FD9" />
                    <span uno-link @click="handleDelete(row)">删除</span>
                  </div>
                  <div v-else>
                    <span uno-link @click="handleEditClick(row, 'check')">查看</span>
                  </div>
                </template>
              </el-table-column>
              <template #empty>
                <div flex justify-center>
                  <DataEmpty />
                </div>
              </template>
            </el-table>

            <div v-if="page.total > 0" mt-20px w-full flex justify-end>
              <n-pagination
                v-model:page="page.page"
                v-model:page-size="page.size"
                :item-count="page.total"
                :page-sizes="[5, 10, 20, 30]"
                show-size-picker
                show-quick-jumper
                @update:page="getEduPage"
              />
            </div>
          </div>
        </div>
      </PageCard>
    </n-spin>

    <n-modal
      v-model:show="isAddModalShow"
      preset="card" :style="{ width: '1000px' }"
      title="新增患教模板" head-style="divide"
    >
      <div h-530px flex items-start gap-10px py-20px>
        <div flex="~ col" gap-14px>
          <div flex items-center gap-10px>
            <div w-70px flex items-center text-right>
              <SvgIcon local-icon="slmc-icon-app_required1" size="14" class="text-#F36969" />
              <div text="#666">
                模板名称
              </div>
            </div>
            <n-input v-model:value="modalForm.title" class="!w-540px" show-count :maxlength="10" />
          </div>

          <div flex items-start gap-10px>
            <div w-70px flex items-center text-right>
              <SvgIcon local-icon="slmc-icon-app_required1" size="14" class="text-#F36969" />
              <div text="#666">
                宣教内容
              </div>
            </div>
            <RichEditor ref="richEditorRef" v-model:html="modalForm.content" width="540px" />
          </div>

          <div flex items-center gap-10px>
            <div w-70px text="#666" text-right>
              可见范围
            </div>

            <AuthRange v-model:relations="modalForm.relations" v-model:visibleRange="modalForm.visibleRange" />
          </div>

          <div flex items-center gap-10px>
            <div w-70px flex items-center text-right>
              <SvgIcon local-icon="slmc-icon-app_required1" size="14" class="text-#F36969" />
              <div text="#666">
                模板分类
              </div>
            </div>

            <n-select
              v-model:value="modalForm.labelId"
              :options="eduLabelTree.slice(1, eduLabelTree.length)" label-field="labelName" value-field="labelId"
              class="!w-540px"
            />
          </div>

          <div flex items-center gap-10px>
            <div w-70px text="#666" text-right>
              备注
            </div>
            <n-input v-model:value="modalForm.introduce" class="!w-540px" show-count :maxlength="10" />
          </div>
        </div>
        <div mx-10px h-full w-1px bg="#ccc" />
        <div h-full flex-1>
          <div flex items-center gap-10px>
            <div sub-title />
            <div text="#333">
              患教内容预览
            </div>
          </div>
          <div v-if="richEditorRef?.isEmpty" h-full flex items-center justify-center>
            <n-empty description="无数据" />
          </div>
          <div v-else h-500px overflow-auto class="editor-content-view" v-html="modalForm.content" />
        </div>
      </div>
      <template #footer>
        <div absolute bottom-0 left-0 h-60px w-full flex items-center justify-center bg="#f9f9f9" style="border-radius: 0 0 3px 3px;" border-t="1px solid #e8e8e8">
          <n-button mr-16px w-100px type="primary" @click="handleSaveClick">
            保 存
          </n-button>
          <n-button w-100px @click="isAddModalShow = false">
            取 消
          </n-button>
        </div>
      </template>
    </n-modal>

    <n-modal
      v-model:show="isCheckModalShow" preset="card" :style="{ width: '640px' }"
      :title="modalForm.title" head-style="divide"
    >
      <div style="white-space:pre-wrap;" min-h-240px p-10px v-html="modalForm.content" />
    </n-modal>
  </div>
</template>

<style lang="scss" scoped>
.counter {
  --uno: text-#FF9B54 text-20px font-700 relative bottom-3px
}
</style>
