<script setup lang="ts">
withDefaults(defineProps<{
  title: string
  width?: string
}>(), {
  isDefault: false,
  width: '338px',
})

const slots = useSlots()
</script>

<template>
  <div :style="{ width }" class="hoverable-card" flex="~ col" items-center justify-center>
    <template v-if="slots.content">
      <slot name="content" />
    </template>
    <template v-else>
      <img class="add-img" src="@/assets/images/add.png">
      <img class="add-img-hover" src="@/assets/images/add-hover.png">
      <div text="#5B5B5B" mt-14px>
        {{ title }}
      </div>
    </template>
  </div>
</template>
