<script setup lang="ts">
import { cloneDeep } from 'lodash-es'
import { useAuthStore } from '@/store'
import { getUserApi } from '@/api/user/user'
import type { UserPageListRes } from '@/api/user/type'

interface UserRange {
  userId: string
  userName: string
}

const { userInfo } = useAuthStore()

const visibleRange = defineModel<string>('visibleRange')
const relations = defineModel<string[]>('relations', {
  default: [],
})

const relationsRange = defineModel<UserRange[]>('relationsRange', {
  default: [],
})

const assisnUsers = ref([])
const assisnUserRange = ref([])

const userList = ref<{
  value: string
  label: string
}[]>([])

async function getUserList() {
  try {
    const { data } = await getUserApi<UserPageListRes>({
      start: 1,
      size: 1000,
      userType: '',
    })

    if (data) {
      const userData = cloneDeep(data?.records?.map((item) => {
        return {
          value: item.id,
          label: item.userName,
        }
      }) || [])

      userList.value = userData

      relations.value = userData.map(item => item.value)
      relationsRange.value = data?.records?.map((item) => {
        return {
          userId: item.id,
          userName: item.userName,
        }
      })
    }
  }
  catch (error) {

  }
}

onMounted(() => {
  getUserList()
})
function handleUsersUpdate(_val: string, option: any) {
  assisnUserRange.value = option.map((item: any) => {
    return {
      userId: item.value,
      userName: item.label,
    }
  })

  relationsRange.value = assisnUserRange.value
}

function handleVisibleRangeUpdate(val: string) {
  if (val === 'ALL_USER') {
    relations.value = userList.value.map(item => item.value)
    relationsRange.value = userList.value.map((item) => {
      return {
        userId: item.value,
        userName: item.label,
      }
    })
  }
  else if (val === 'ASSIGN_USER') {
    relations.value = assisnUsers.value
    relationsRange.value = assisnUserRange.value
  }
  else if (val === 'OWN_USER') {
    relations.value = [userInfo.id]
    relationsRange.value = [{
      userId: userInfo.id,
      userName: userInfo.userName,
    }]
  }
}

// watch(visibleRange, (val) => {
//   if (val === 'ALL_USER') {
//     relations.value = userList.value.map(item => item.value)
//     relationsRange.value = userList.value.map((item) => {
//       return {
//         userId: item.value,
//         userName: item.label,
//       }
//     })
//   }
//   else if (val === 'ASSIGN_USER') {
//     relations.value = assisnUsers.value
//     relationsRange.value = assisnUserRange.value
//   }
//   else if (val === 'OWN_USER') {
//     relations.value = [userInfo.id]
//     relationsRange.value = [{
//       userId: userInfo.id,
//       userName: userInfo.userName,
//     }]
//   }
// }, {
//   immediate: true,
// })
</script>

<template>
  <n-radio-group v-model:value="visibleRange" @update:value="handleVisibleRangeUpdate">
    <div flex items-center gap-40px>
      <n-radio value="ALL_USER">
        全部用户
      </n-radio>
      <div flex items-center gap-10px>
        <n-radio value="ASSIGN_USER">
          指定用户
        </n-radio>
        <n-select
          v-model:value="assisnUsers"
          multiple max-tag-count="responsive"
          :options="userList"
          :disabled="visibleRange !== 'ASSIGN_USER'"
          class="!w-218px"
          @update:value="handleUsersUpdate"
        />
      </div>
      <n-radio value="OWN_USER">
        仅自己
      </n-radio>
    </div>
  </n-radio-group>
</template>
