<script setup lang="ts">
withDefaults(defineProps<{
  isDefault?: boolean
  width?: string
  hideFooter?: boolean
}>(), {
  isDefault: false,
  width: '338px',
  hideFooter: false,
})
</script>

<template>
  <div relative :style="{ width }" class="hoverable-card">
    <div px-20px py-14px>
      <slot />
    </div>
    <div v-if="!hideFooter" absolute bottom-0 h-36px w-full bg="#A5B8D1/20">
      <slot name="footer" />
    </div>
    <img v-if="isDefault" absolute right-0 top-0 w-45px src="@/assets/images/default.png" alt="">
  </div>
</template>
