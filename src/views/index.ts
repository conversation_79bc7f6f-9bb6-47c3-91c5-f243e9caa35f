import type { RouteComponent } from 'vue-router'

export const views: Record<
  PageRoute.LastDegreeRouteKey,
  RouteComponent | (() => Promise<{ default: RouteComponent }>)
    > = {
      '403': () => import('./exception/403/index.vue'),
      '404': () => import('./exception/404/index.vue'),
      'not-found': () => import('./exception/notFound/index.vue'),
      'home': () => import('./home/<USER>'),
      'login': () => import('./login/index.vue'),
      'system_log': () => import('./system/systemLog/index.vue'),
      'system_roleManage': () => import('./system/role/index.vue'),
      'system_memberManage': () => import('./system/member/index.vue'),
      'system_permissionEdit': () => import('./system/permission/edit/Edit.vue'),
      'system_permissionLook': () => import('./system/permission/looking/index.vue'),
      'system_permissionManage': () => import('./system/permission/manage/index.vue'),
      'system_parameterSetting': () => import('./system/setting/index.vue'),
      'system_userManage': () => import('./system/userManage/index.vue'),
      'system_tag': () => import('./system/tag/index.vue'),
      'system_baseSetting': () => import('./system/baseSetting/index.vue'),
      'patient_list': () => import('./patient/patientList/index.vue'),
      'patient_file': () => import('./patient/file/index.vue'),
      'patient_editFile': () => import('./patient/file/components/BaseInfo/EditInfo.vue'),
      'patient_check': () => import('./patient/checkReport/index.vue'),
      'patient_inspection': () => import('./patient/inspectionReport/index.vue'),
      'patient_medicalHistory': () => import('./patient/medicalHistory/index.vue'),
      'dataScreen_wane': () => import('./dataScreen/wane/index.vue'),
      'fullScreen_data': () => import('./dataScreen/full/index.vue'),
      'analysis_specialDisease': () => import('./analysis/specialDisease/index.vue'),
      'analysis_statement': () => import('./analysis/statement/index.vue'),
      'follow_workspace': () => import('./follow/workspace/index.vue'),
      'follow_manage': () => import('./follow/manage/index.vue'),
      'follow_questionnaire': () => import('./follow/questionnaire/index.vue'),
      'intelligentFollow_registration': () => import('./intelligentFollow/registration/index.vue'),
      'intelligentFollow_manage': () => import('./intelligentFollow/manage/index.vue'),
      'intelligentFollow_manage_create': () => import('./intelligentFollow/manage/create.vue'),
      'intelligentFollow_manage_detail': () => import('./intelligentFollow/manage/detail.vue'),
      'intelligentFollow_plan': () => import('./intelligentFollow/plan/index.vue'),
      'intelligentFollow_templateCenter': () => import('./intelligentFollow/templateCenter/index.vue'),
      'intelligentFollow_todo': () => import('./intelligentFollow/todo/index.vue'),
      'intelligentFollow_workbench': () => import('./intelligentFollow/workbench/index.vue'),
      'intelligentFollow_confirm': () => import('./intelligentFollow/confirm/index.vue'),
      'toolsFollow_message': () => import('./toolsFollow/message/index.vue'),
      'toolsFollow_survey': () => import('./toolsFollow/survey/index.vue'),
      'toolsFollow_survey_create': () => import('./toolsFollow/survey/create.vue'),
      'toolsFollow_survey_edit': () => import('./toolsFollow/survey/edit.vue'),
      'toolsFollow_education': () => import('./toolsFollow/education/index.vue'),
      'toolsFollow_warning': () => import('./toolsFollow/warning/index.vue'),
      'specificDisease_list': () => import('./specificDisease/patient/index.vue'),
      'specificDisease_detail': () => import('./specificDisease/patientDetail/index.vue'),
      'tag': () => import('./tag/index.vue'),
      'dataCenter': () => import('./dataCenter/index.vue'),
      'dataCenter_records': () => import('./dataCenter/records.vue'),
      'researcher_projectManage': () => import('./researchPlatform/projectManage/index.vue'),
      'researcher_projectManage_create': () => import('./researchPlatform/projectManage/create.vue'),
      'researcher_projectManage_detail': () => import('./researchPlatform/projectManage/detail.vue'),
      'researcher_scaleManage': () => import('./researchPlatform/scaleManage/index.vue'),
      'researcher_scaleDetail': () => import('./researchPlatform/scaleManage/detail.vue'),
      'researcher_workerManage': () => import('./researchPlatform/workerManage/index.vue'),
      'researcher_projectManage_subjectDetail': () => import('./researchPlatform/projectManage/subjectDetail.vue'),
      'researcher_subjectManage_subjectDetail': () => import('./researchPlatform/projectManage/subjectDetail.vue'),
      'researcher_projectManage_subject_create': () => import('./researchPlatform/projectManage/subject/addSubject.vue'),
      'dataCenter_zheYiDataCenter': () => import('./dataCenter/zheYiDataCenter.vue'),
      'fullScreen_zheYiDataFullScreen': () => import('./dataCenter/zheYiFullScreen.vue'),
      'analysis_antibacterial': () => import('./analysis/antibacterial/index.vue'),
      'intelligentFollow_todo2': () => import('./intelligentFollow/todo2/index.vue'),
      'intelligentFollow_tongJi': () => import('./intelligentFollow/tongJi/index.vue'),
      'knowledge_knowledgeInfo_1638862590740656063': () => import('./dataCenter/knowledge.vue'),
      'knowledge_knowledgeInfo_1567677491634533065': () => import('./dataCenter/knowledge.vue'),
      'knowledge_knowledgeInfo_1567677479246637451': () => import('./dataCenter/knowledge.vue'),
      'knowledge_knowledgeInfo_1567677480327367618': () => import('./dataCenter/knowledge.vue'),
      'knowledge_knowledgeInfo_1567677480184566528': () => import('./dataCenter/knowledge.vue'),
      'knowledge_knowledgeInfo_1567677479246421568': () => import('./dataCenter/knowledge.vue'),
      'exportdata_inpatient': () => import('./exportdata/inpatient/index.vue'),
      'exportdata_inpatient_record': () => import('./exportdata/inpatient/record.vue'),
      'researcher_groupManage': () => import('./researchPlatform/groupManage/index.vue'),
      'researcher_subjectManage': () => import('./researchPlatform/subjectManage/index.vue'),
      'researcher_historyManage': () => import('./researchPlatform/historyManage/index.vue'),
      'researcher_historyManage_historyDetail': () => import('./researchPlatform/historyManage/detail.vue'),
    }
