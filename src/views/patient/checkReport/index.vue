<script lang='ts' setup>
import dayjs from 'dayjs'
import InfoItem from './components/InfoItem.vue'
import { BasicTable } from '@/components/Table'
import type { ExamListResult, ExamRecord, VisitRecordParams } from '@/api/patient'
import { getVisitExamListApi } from '@/api/patient'
import { SectionTitle } from '@/components/Title'
import type { BreadLit } from '@/layouts/common'
import { Breadcrumb } from '@/layouts/common'

const route = useRoute()
const patientId = route.query?.patientId as string
const recordId = route.query?.recordId as string

function createColumns() {
  return [
    {
      title: '序号',
      width: 80,
      render(_: ExamRecord, index: number) {
        return index + 1
      },

    },
    {
      title: '项目名称',
      key: 'examItem',
    },
    {
      title: '申请时间',
      key: 'requestTime',
      width: 150,
      render(row: ExamRecord) {
        const { requestTime } = row
        return requestTime ? dayjs(requestTime).format('YYYY-MM-DD') : '-'
      },
    },
    {
      title: '检查类型',
      key: 'examType',
      width: 100,
    },
    {
      title: '报告时间',
      key: 'reportTime',
      width: 150,
      render(row: ExamRecord) {
        const { reportTime } = row
        return reportTime ? dayjs(reportTime).format('YYYY-MM-DD') : '-'
      },
    },
    {
      title: '报告医生',
      key: 'reporter',
    },
  ]
}

const tableRef = ref<InstanceType<typeof BasicTable> | null>(null)
const columns = createColumns()
/** 当前点击行的数据 */
const currentRecord = ref<ExamRecord>({
  auditTime: '',
  auditor: '',
  emergency: 0,
  examBodypart: '',
  examItem: '',
  examType: '',
  id: '',
  imageDiagnosis: '',
  imageSight: '',
  patientId: '',
  patientName: '',
  performTime: '',
  performer: '',
  recordId: '',
  register: '',
  registerTime: '',
  reportTime: '',
  reporter: '',
  requestTime: '',
  requester: '',
  reviseTime: '',
  reviser: '',
  slmcNo: '',
})
/** 当前点击行id */
const currentRowId = ref('')

// 分页器
const paginationReactive = reactive({
  page: 1,
  pageSize: 10,
  pageSizes: [10, 20, 50, 100],
})
const tableParams = reactive<VisitRecordParams>({
  size: 10,
  start: 1,
  patientId,
  recordId,
})
// 加载表格数据
async function loadDataTable(res: { size: number; start: number }) {
  const response = await getVisitExamListApi<ExamListResult>({ ...tableParams, ...res })
  if (response.data?.records) {
    currentRecord.value = response.data?.records[0]
    currentRowId.value = response.data?.records[0].id
  }

  return response
}

function rowClassName(row: ExamRecord) {
  if (currentRowId.value === row.id)
    return 'clickActive'

  return ''
}

function rowProps(row: ExamRecord) {
  return {
    style: 'cursor: pointer;',
    onClick: () => {
      currentRowId.value = row.id
      currentRecord.value = { ...row }
    },
  }
}

const renderBread = computed(() => {
  const patientId = route.query?.patientId as string
  const slmcNo = route.query?.slmcNo as string
  const patientName = route.query?.patientName as string
  const primaryIndex = route.query?.primaryIndex as string

  const list: BreadLit[] = [
    { title: '患者数据库', link: '/patient', key: 'patient' },
    { title: '患者列表', link: '/patient/list', key: 'patient_list' },
    { title: `${patientName}档案信息`, link: `/patient/file?patientId=${patientId}&patientName=${patientName}&primaryIndex=${primaryIndex}&slmcNo=${slmcNo}&tabName=MedicalRecord`, key: 'patient_file' },
    { title: '检查报告', link: null, key: 'patient_check' },
  ]
  return list
})
</script>

<template>
  <div>
    <Breadcrumb :bread-list="renderBread" />
    <PageCard breadcrumb>
      <PageTitle class="mb-14px">
        检查报告
      </PageTitle>
      <BasicTable
        ref="tableRef"
        class="table"

        :columns="columns"
        :request="loadDataTable"
        :row-key="(row:ExamRecord) => row.id"
        :pagination="paginationReactive"
        :row-props="rowProps"
        :row-class-name="rowClassName"
        striped
        :scroll-x="1000"
      />
      <SectionTitle>详情信息</SectionTitle>
      <n-divider margin="10px 0 14px 0" />
      <n-space :size="[14, 14]" vertical>
        <InfoItem label="检查项目" :value="currentRecord.examItem" label-width="85" />
        <InfoItem label="检查描述" align-items="start" label-width="85">
          <span class="flex-1 bg-#F4F5F6 p-14px">  {{ currentRecord.imageSight }}</span>
        </InfoItem>
        <InfoItem label="检查结论" label-width="85" :value="currentRecord.imageDiagnosis" />
        <InfoItem label="报告医生" :value="currentRecord.reporter" label-width="85" />
        <InfoItem label="审核医生" :value="currentRecord.auditor" label-width="85" />
        <InfoItem label="报告时间" :value="currentRecord.reportTime" label-width="85" />
      </n-space>
      <div />
    </PageCard>
  </div>
</template>

<style scoped lang="scss">
.table {
    :deep(.clickActive .n-data-table-td){
        background-color: #FFFBE0 !important;
    }
}
</style>
