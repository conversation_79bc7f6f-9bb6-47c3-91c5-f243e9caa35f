<script lang='ts' setup>
import { useRoute } from 'vue-router'
import { BaseInfo, IndicatorTrend, MedicalRecord, SlmcInterview, TreatmentPlan } from './components'
import type { BreadLit } from '@/layouts/common'
import { Breadcrumb } from '@/layouts/common'
import { usePermission } from '@/hooks'

const route = useRoute()

const patientName = route.query?.patientName as string
const tabName = route.query?.tabName as string
const primaryIndex = route.query?.primaryIndex as string
const source = route.query?.source as string

const { hasPermission } = usePermission()

const currentTab = ref('BaseInfo')
tabName && (currentTab.value = tabName)

const renderBread = computed(() => {
  const followList: BreadLit[] = [
    {
      title: '随访中心',
      link: '/follow',
      key: 'follow',
    },
    {
      title: '随访管理',
      link: '/follow/manage',
      key: 'follow_manage',
    },
  ]

  const patientList: BreadLit[] = [
    { title: '患者数据库', link: '/patient', key: 'patient' },
    { title: '患者列表', link: '/patient/list', key: 'patient_list' },
  ]

  const common: BreadLit[] = [
    {
      title: `${patientName || '未知病人'}档案信息`,
      link: null,
      key: 'patient_file',
    },
  ]

  if (source === 'follow')
    return [...followList, ...common]
  else
    return [...patientList, ...common]
})
</script>

<template>
  <div>
    <Breadcrumb :bread-list="renderBread" />
    <PageCard breadcrumb>
      <PageTitle class="mb-2px">
        {{ patientName }}档案信息
      </PageTitle>
      <n-tabs v-model:value="currentTab" animated>
        <n-tab-pane v-if="hasPermission('lddp:database:patientDetail:info')" name="BaseInfo" tab="基础信息">
          <BaseInfo />
        </n-tab-pane>
        <n-tab-pane v-if="hasPermission('lddp:database:patientDetail:treat')" name="TreatmentPlan" tab="治疗方案">
          <TreatmentPlan />
        </n-tab-pane>
        <n-tab-pane v-if="hasPermission('lddp:database:patientDetail:record')" name="MedicalRecord" tab="就诊记录">
          <MedicalRecord />
        </n-tab-pane>
        <n-tab-pane v-if="hasPermission('lddp:database:patientDetail:main_target')" name="IndicatorTrend" tab="关键指标趋势图">
          <IndicatorTrend />
        </n-tab-pane>
        <n-tab-pane v-if="hasPermission('lddp:database:patientDetail:slmc')" name="SlmcInterview" tab="SLMC访视">
          <SlmcInterview :primary-index="primaryIndex" />
        </n-tab-pane>
      </n-tabs>
    </PageCard>
  </div>
</template>

<style scoped lang="scss">
:deep(.pageCard-content){
  display: flex;
  flex-direction: column;
}
:deep(.n-tabs-pane-wrapper){
  flex: 1;
}
:deep(.n-tabs){
  flex: 1;
}
:deep(.n-tab-pane){
  height: 100%;
}
</style>
