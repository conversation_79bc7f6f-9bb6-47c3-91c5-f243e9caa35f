<script lang='ts' setup>
const props = defineProps({
  text: String,
  color: String,
})
const bgColor = computed(() => {
  return props.color
})
</script>

<template>
  <div class="useStatus">
    <span> {{ text }}</span>
  </div>
</template>

<style scoped lang="scss">
.useStatus {
    background-color: v-bind(bgColor);
    color: #fff;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
}
</style>
