<script lang='ts' setup>
import { useMessage } from 'wowjoy-vui'
import type { FormInst } from 'wowjoy-vui'
import { BasicModal, useModalInner } from '@/components/Modal'
import type { CreateTreatPlanParams, FormattedValue, Frequency, StandardDrugList, TreatMentRecord, TreatPlanDetail, UpdateTreatPlanParams, supply } from '@/api/patient'
import { createTreatPlanApi, getFrequencyApi, getStandardDrugListApi, getSupplyApi, getTreatMentDetailApi, updateTreatPlanApi } from '@/api/patient'

interface FormValue {
  id: string
  drugName: string
  drugId: null | string
  /** 购药渠道 */
  drugChannel: null | string
  drugType: string
  drugGroup: string
  /** 药品规格 */
  specName: string | null
  /** 频次 */
  frequencyName: string
  frequencyId: null | string
  /** 用法 */
  supplyName: string
  supplyId: null | string
  /** 单次剂量 */
  dose: string | null
  /** 剂量单位 */
  doseUnit: string
  /** 正在使用 */
  status: 0 | 1 | null
  /** 处方日期 */
  prescriptionDate: FormattedValue | null | undefined
  /** 数据来源 */
  source: string
}

interface Props {
  drugInfo: TreatMentRecord | null
  patientInfo: {
    patientId: string
    patientName: string
    slmcNo: string
  }

}

const props = withDefaults(defineProps<Props>(), {
  drugInfo: null,
})

const emit = defineEmits(['onSuccess', 'register'])
const message = useMessage()

/** 购药渠道枚举 */
const optionsDrugChannel = [
  { label: '医院药房', value: '医院药房' },
  { label: '网上药店', value: '网上药店' },
  { label: '线下药店', value: '线下药店' },
  { label: '社区医院', value: '社区医院' },
]

const formRef = ref<FormInst | null>(null)

const formValue = reactive<FormValue>({
  id: '',
  drugName: '',
  drugId: null,
  drugChannel: null,
  drugType: '',
  specName: null,
  frequencyName: '',
  frequencyId: null,
  supplyName: '',
  supplyId: null,
  dose: '',
  doseUnit: '',
  status: null,
  prescriptionDate: null,
  source: '',
  drugGroup: '',

})
const rules = {
  drugId: {
    required: true,
    trigger: ['blur'],
    message: '内容不能为空',
  },
  frequencyId: {
    required: true,
    message: '内容不能为空',
    trigger: ['blur'],
  },
  supplyId: {
    required: true,
    message: '内容不能为空',
    trigger: ['blur'],
  },
  dose: {
    required: true,
    message: '内容不能为空',
    trigger: ['blur'],
  },
  doseUnit: {
    required: true,
    message: '内容不能为空',
    trigger: ['blur'],
  },
  status: {
    type: 'number',
    required: true,
    message: '内容不能为空',
    trigger: 'change',
  },
}
function resetFormValue() {
  formValue.id = ''
  formValue.drugId = null
  formValue.drugName = ''
  formValue.drugChannel = null
  formValue.drugType = ''
  formValue.specName = null
  formValue.frequencyName = ''
  formValue.frequencyId = null
  formValue.supplyName = ''
  formValue.supplyId = null
  formValue.dose = ''
  formValue.doseUnit = ''
  formValue.status = null
  formValue.prescriptionDate = null
  formValue.source = ''
  formValue.drugGroup = ''
}

/** 是否是编辑弹窗 */
const isEdit = computed(() => {
  return formValue?.id?.length > 0
})
/** 标准药品列表 */
const optionsDrug = ref<StandardDrugList[]>([])
/** 药品规格 */
const optionsSpecName = ref<{ label: string; value: string }[]>([])

/** 获取标准药品列表 */
async function getStandardDrugList() {
  const res = await getStandardDrugListApi<StandardDrugList[]>()
  if (res.data)
    optionsDrug.value = res.data
}
/** 特殊的药品,需要在规格字段特殊处理 */
const SPECIAL_DRUG = '聚乙二醇干扰素α-2b注射液(派格宾)'

/**
 * 药品名称更新时的callback
 * @param value 药品id
 * @param option 药品option
 */
function onUpdateDrugValue(value: string, option: StandardDrugList) {
  // 选择好药品名称后 单次剂量、剂量单位、频次、用法、正在使用 自动带出，可修改
  // 药品回填
  formValue.drugId = value
  formValue.drugName = option.drugName
  // 频次回填
  formValue.frequencyName = option.frequencyName
  formValue.frequencyId = option.frequencyName || null
  // 用法
  formValue.supplyName = option.supplyName
  formValue.supplyId = option.supplyName || null
  // 单次剂量
  formValue.dose = option.dose ? `${option.dose}` : null
  // 剂量单位
  formValue.doseUnit = option.doseUnit
  // 药品类型
  formValue.drugType = option.drugType

  formValue.drugGroup = option.drugGroup

  // 特殊规格药物处理
  if (SPECIAL_DRUG === option.drugName && option.specName.includes('|')) {
    const specialNames = option.specName.split('|') ?? []
    optionsSpecName.value = specialNames.map((name) => {
      return { label: name, value: name }
    })
    formValue.specName = optionsSpecName.value[0].value
  }
  else {
    formValue.specName = option.specName
  }
}
/**
 * 获取治疗方案详情
 * @param TreatPlanId 治疗方案id
 */
async function getTreatMentDetail(TreatPlanId: string) {
  const res = await getTreatMentDetailApi<TreatPlanDetail>(TreatPlanId)

  if (res.data) {
    formValue.drugId = res.data.drugName
    formValue.drugName = res.data.drugName
    formValue.drugType = res.data.drugType

    // 频率
    formValue.frequencyName = res.data.frequencyName
    formValue.frequencyId = res.data.frequencyName
    // 给药方式
    formValue.supplyName = res.data.supplyName
    formValue.supplyId = res.data.supplyName
    // 单位
    formValue.dose = res.data.dose ? `${res.data.dose}` : null
    formValue.doseUnit = res.data.doseUnit
    // 购药渠道
    formValue.drugChannel = res.data.drugChannel

    // 状态
    formValue.status = res.data.status
    // 规格
    formValue.specName = res.data.specName
    // 处方日期
    formValue.prescriptionDate = res.data.prescriptionDate
    // 数据来源
    formValue.source = res.data.source

    // 药品组
    formValue.drugGroup = res.data.drugGroup
  }
}

const [register, { closeModal }] = useModalInner()

/** 弹窗名字 */
const modalTitle = ref('')

/**
 * 弹窗打开/关闭的callback
 * @param v true:打开，false:关闭
 */
async function visibleChange(v: boolean) {
  v && nextTick(() => {
    const { id = '' } = props.drugInfo ?? {}
    modalTitle.value = id ? '编辑药品' : '新增药品'

    formValue.id = id

    // 新增时候
    !id && (formValue.source = '手动添加')
    // 编辑状态在去请求方案详情
    id && getTreatMentDetail(id)
  })
  if (!v)
    resetFormValue()
}

/** 频次枚举 */
const optionsFrequency = ref<Frequency[]>([])
/** 获取频次 */
async function getFrequency() {
  const res = await getFrequencyApi<Frequency[]>()
  if (res.data)
    optionsFrequency.value = res.data
}
/**
 * 频率下拉更新时的回调
 * @param value 频率id
 * @param option 当前频率option
 */
function onUpdateFrequencyValue(value: string, option: Frequency) {
  formValue.frequencyId = value
  formValue.frequencyName = option.frequencyName
}
/** 用法枚举 */
const optionsSupply = ref<supply[]>([])
/**
 * 获取用法，给药方式
 */
async function getSupply() {
  const res = await getSupplyApi<supply[]>()
  if (res.data)
    optionsSupply.value = res.data
}
/**
 * 用法下拉更新时的回调
 * @param value 用法id
 * @param option 当前用法option
 */
function onUpdateSupplyValue(value: string, option: supply) {
  formValue.supplyId = value
  formValue.supplyName = option.supplyName
}

/** 新增治疗方案 */
async function createTreatPlan(params: CreateTreatPlanParams, stillCreate?: boolean) {
  const res = await createTreatPlanApi(params)
  if (res.data) {
    message.success('保存成功')
    // 如果继续新增就重置数据
    emit('onSuccess')
    resetFormValue()

    !stillCreate && closeModal()
  }
}
/** 更新治疗方案 */
async function updateTreatPlan(params: UpdateTreatPlanParams) {
  const res = await updateTreatPlanApi(params)
  if (res.data) {
    message.success('保存成功')
    emit('onSuccess')
    resetFormValue()
    closeModal()
  }
}
// 保存
function handleConfirm(stillCreate = false) {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      const payload = {
        ...formValue,
        ...props.patientInfo,

      }
      if (isEdit.value)

        updateTreatPlan(payload)

      else
        createTreatPlan(payload, stillCreate)
    }
    else { console.log(errors) }
  })
}

/** 初始化函数 */
function init() {
  getStandardDrugList()
  getFrequency()
  getSupply()
}
onMounted(() => {
  init()
})
</script>

<template>
  <div>
    <BasicModal
      v-bind="$attrs"
      :title="modalTitle"
      :min-height="105"
      width="720"
      @register="register"
      @ok="handleConfirm(false)"
      @visible-change="visibleChange"
    >
      <n-form
        ref="formRef"
        label-placement="left"
        class="my-24px ml-17px mr-30px"
        require-mark-placement="left"
        :label-width="100"
        :model="formValue"
        :rules="rules"
      >
        <n-grid :cols="2" x-gap="20">
          <n-form-item-gi path="drugId" label="药品名称">
            <n-select
              v-model:value="formValue.drugId"
              filterable
              :options="optionsDrug"
              clearable
              label-field="drugName"
              value-field="id"
              :disabled="isEdit"
              @update:value="onUpdateDrugValue"
            />
          </n-form-item-gi>
          <n-form-item-gi path="drugChannel" label="购药渠道">
            <n-select
              v-model:value="formValue.drugChannel"
              :options="optionsDrugChannel"
            />
          </n-form-item-gi>
          <n-form-item-gi path="drugType" label="类型">
            <n-input
              v-model:value="formValue.drugType"
              disabled
              :maxlength="30"
            />
          </n-form-item-gi>
          <n-form-item-gi path="specName" label="规格">
            <!-- 特殊药品规格，格式为数组，需要用户选择 -->
            <n-select
              v-if="SPECIAL_DRUG === formValue.drugName "
              v-model:value="formValue.specName"
              :options="optionsSpecName"
              :disabled="isEdit"
            />
            <n-input
              v-else
              v-model:value="formValue.specName"
              :disabled="isEdit"
              :maxlength="30"
            />
          </n-form-item-gi>
          <n-form-item-gi path="frequencyId" label="频次">
            <n-select
              v-model:value="formValue.frequencyId"
              :options="optionsFrequency"
              filterable
              clearable
              label-field="frequencyName"
              value-field="id"
              @update:value="onUpdateFrequencyValue"
            />
          </n-form-item-gi>
          <n-form-item-gi path="supplyId" label="用法">
            <n-select
              v-model:value="formValue.supplyId"
              :options="optionsSupply"
              filterable
              clearable
              label-field="supplyName"
              value-field="id"
              @update:value="onUpdateSupplyValue"
            />
          </n-form-item-gi>
          <n-form-item-gi path="dose" label="单次剂量">
            <n-input
              v-model:value="formValue.dose"
              :maxlength="10"
            />
          </n-form-item-gi>
          <n-form-item-gi path="doseUnit" label="剂量单位">
            <n-input
              v-model:value="formValue.doseUnit"
              :maxlength="10"
            />
          </n-form-item-gi>
          <n-form-item-gi path="status" label="正在使用">
            <n-radio-group v-model:value="formValue.status" name="radiogroup" :disabled="isEdit">
              <n-space :size="[40, 10]">
                <n-radio :value="1">
                  是
                </n-radio>
                <n-radio :value="0">
                  否
                </n-radio>
              </n-space>
            </n-radio-group>
          </n-form-item-gi>
          <n-form-item-gi path="prescriptionDate" label="处方日期">
            <n-date-picker
              v-model:formatted-value="formValue.prescriptionDate"
              value-format="yyyy-MM-dd HH:mm:ss" style="width:100%"
            />
          </n-form-item-gi>
        </n-grid>
      </n-form>
      <template v-if="!isEdit" #centerFooter>
        <n-button
          type="primary"
          ghost
          class="ml-16px"
          @click="handleConfirm(true)"
        >
          保存并继续
        </n-button>
      </template>
    </BasicModal>
  </div>
</template>

<style scoped lang="scss">

</style>
