<script lang='ts' setup>
import { type RadioGroupProps } from 'wowjoy-vui'
import { useMessage } from 'wowjoy-vui'
import dayjs from 'dayjs'
import { useRoute } from 'vue-router'
import RenderStatus from './components/RenderStatus.vue'
import DrugModal from './components/DrugModal.vue'
import { useThemeStore } from '@/store'
import { BasicTable, RowAction } from '@/components/Table'
import { useModal } from '@/components/Modal'
import type { FetchReload } from '@/components/Table/src/types/table'
import { deleteTreatPlanApi, getTreatMentApi } from '@/api/patient'
import type { DrugGroup, TreatMentPlanParams, TreatMentRecord } from '@/api/patient'
import { isNullValue } from '@/utils'
import { usePermission } from '@/hooks'

const theme = useThemeStore()
type RadioGroupThemeOverrides = NonNullable<RadioGroupProps>['themeOverrides']
const setColors: RadioGroupThemeOverrides = {
  // TIPS:后期需要统一更换主题色再用
  // buttonColorActive: addColorAlpha(theme.themeColor, 0.1),
  buttonColorActive: '#F1F9FF',
  buttonBorderColorActive: '#BED5E8',
}

const message = useMessage()
const { hasPermission } = usePermission()

const route = useRoute()
const patientId = route.query?.patientId as string
const slmcNo = route.query?.slmcNo as string
const patientName = route.query?.patientName as string

const drugButtonList = [
  { label: '抗炎保肝类药物', value: '抗炎保肝类药物' },
  { label: '抗纤维化类药物', value: '抗纤维化类药物' },
  { label: '抗病毒类药物', value: '抗病毒类药物' },
  { label: '降脂类药物', value: '降脂类药物' },
  { label: '降糖类药物', value: '降糖类药物' },
  { label: '降压类药物', value: '降压类药物' },
  { label: '肝损伤类药物', value: '肝损伤类药物' },
]
/** 药物选择 */
const drugButtonType = ref<DrugGroup>('抗炎保肝类药物')

/** 创建列 */
function createColumns() {
  return [
    {
      title: '序号',
      width: 80,
      render(_: TreatMentRecord, index: number) {
        return index + 1
      },

    },
    {
      title: '类型',
      key: 'drugType',
      width: 120,
    },
    {
      title: '药品名称',
      key: 'drugName',
      width: 160,
    },
    {
      title: '规格',
      key: 'specName',
      width: 160,
    },
    {
      title: '用法',
      key: 'supplyName',
      width: 100,
    },
    {
      title: '频次',
      key: 'frequencyName',
      width: 150,
    },
    {
      title: '单次剂量',
      key: 'dose',
      width: 100,
    },
    {
      title: '剂量单位',
      key: 'doseUnit',
      width: 100,
    },
    // {
    //   title: '数量',
    //   key: 'qty',
    // },
    {
      title: '处方日期',
      key: 'prescriptionDate',
      width: 150,
      render(row: TreatMentRecord) {
        const { prescriptionDate } = row
        return prescriptionDate ? dayjs(prescriptionDate).format('YYYY-MM-DD') : '-'
      },
    },
    {
      title: '正在使用',
      key: 'status',
      width: 100,
      render(row: TreatMentRecord) {
        const { status } = row
        const state: Record<number, { text: string; color: string }> = {
          0: {
            text: '否',
            color: '#C1C1C1',
          },
          1: {
            text: '是',
            color: '#25CCC4',
          },
        }

        return isNullValue(status) ? '-' : h(RenderStatus, { text: state[status].text, color: state[status].color })
      },
    },
    {
      title: '购药渠道',
      key: 'drugChannel',
      width: 160,
    },
    {
      title: '数据来源',
      key: 'source',
      width: 160,
      render(row: TreatMentRecord) {
        const { source } = row

        return source || '手动添加'
      },
    },

  ]
}
/** 创建操作列 */
function createActionColumns() {
  return {
    title: '操作',
    key: 'address',
    width: 160,
    fixed: 'right',
    render(row: TreatMentRecord) {
      const { source, id } = row
      return h(RowAction, {
        actions: [
          {
            label: '编辑',
            onClick: () => handleEdit(row),
            type: 'primary',
            text: true,
            ifShow: showEdit(source),
          },
          {
            label: '删除',
            onClick: () => handleDelete(id),
            type: 'primary',
            text: true,
            ifShow: showDelete(source),
          },
        ],
      })
    },
  }
}
/** 当前编辑的治疗信息 */
const editDrugInfo = ref<TreatMentRecord | null>(null)
/** 患者信息 */
const patientInfo = ref({
  patientId,
  slmcNo,
  patientName,
})
/**
 * 编辑治疗方案
 * @param row 单条治疗方案数据
 */
function handleEdit(row: TreatMentRecord) {
  editDrugInfo.value = { ...row }

  showDrugModal(false)
}
async function deleteTreatPlan(treatMentId: string) {
  const res = await deleteTreatPlanApi(treatMentId)
  if (res.data) {
    message.success('删除成功')
    onRefresh({ isRemove: true })
  }
}

/**
 * 删除操作
 * @param treatMentId 治疗方案id
 */
function handleDelete(treatMentId: string) {
  window.$dialog?.warning({
    title: '确定删除此药品吗？',
    content: '删除后数据不可恢复。',
    positiveText: '确定',
    negativeText: '取消',
    positiveButtonProps: {
      type: 'primary',
    },
    onPositiveClick: () => {
      deleteTreatPlan(treatMentId)
    },
  })
}
/**
 * 来源于HIS的数据不可编辑
 * @param source 数据来源
 */
function showEdit(source: string) {
  return source !== 'HIS'
}
/**
 * 来源于HIS的数据不可删除
 * @param source 数据来源
 */
function showDelete(source: string) {
  return source !== 'HIS'
}

const tableRef = ref<InstanceType<typeof BasicTable> | null>(null)
const columns = createColumns()
const actionColumns = createActionColumns()
// 分页器
const paginationReactive = reactive({
  page: 1,
  pageSize: 10,
  pageSizes: [10, 20, 50, 100],
})
/** 表格查询参数 */
const tableParams = reactive<TreatMentPlanParams>({
  drugGroup: drugButtonType.value,
  size: 10,
  start: 1,
  patientId,
})
/** 是否有查询权限 */
function hasSearchPermission() {
  return hasPermission('lddp:database:patientDetail:treat:search')
}

// 加载表格数据
async function loadDataTable(res: { size: number; start: number }) {
  if (!hasSearchPermission())
    return

  return await getTreatMentApi<TreatMentPlanParams>({ ...tableParams, ...res })
}
/**
 * 切换药物按钮的回调
 * @param value 药物类型
 */
function onDrugButtonUpdate(value: DrugGroup) {
  tableParams.start = 1
  tableParams.drugGroup = value
  tableRef.value?.fetch({ ...tableParams })
}
// 刷新表格
function onRefresh(params?: FetchReload) {
  tableRef.value?.reload(params)
}

const [registerDrugModal, { openModal: openResetModal }] = useModal()
function showDrugModal(isCreate: boolean) {
  if (isCreate)
    editDrugInfo.value = null
  openResetModal()
}
// 新增成功回调
function onSuccess() {
  onRefresh()
}
</script>

<template>
  <div>
    <n-space
      justify="space-between"
      class="mb-14px"
    >
      <n-radio-group
        v-model:value="drugButtonType" :theme-overrides="setColors"
        name="drugButtonType" mode="ghost" @update:value="onDrugButtonUpdate"
      >
        <n-radio-button v-for="(btn, index) in drugButtonList" :key="index" :value="btn.value" :label="btn.label" />
      </n-radio-group>
      <n-button v-permission="'lddp:database:patientDetail:add'" type="primary" @click="showDrugModal(true)">
        新增药品
      </n-button>
    </n-space>
    <BasicTable
      ref="tableRef"
      striped
      :columns="columns"
      :request="loadDataTable"
      :row-key="(row:TreatMentRecord) => row.id"
      :action-column="actionColumns"
      :pagination="paginationReactive"
      :scroll-x="1600"
    />
    <DrugModal
      :drug-info="editDrugInfo" :patient-info="patientInfo"
      @register="registerDrugModal"
      @on-success="onSuccess"
    />
  </div>
</template>

<style scoped lang="scss">

</style>
