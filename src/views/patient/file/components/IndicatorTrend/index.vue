<script lang="ts" setup>
import dayjs from 'dayjs'
import * as echarts from 'echarts'
import _ from 'lodash'
import { getKeyItemStatisticsDatas } from '~/src/api/statistics'
import { addColorAlpha } from '~/src/utils/common/color'

const timeRange = ref([
  dayjs().subtract(5, 'month').startOf('month').valueOf(),
  dayjs().valueOf(),
])
let myChart: echarts.ECharts | null
const route = useRoute()
const selectLineColors: Map<string, any> = {}
const lodingSpin = ref(false)
const shortTimes = {
  本月: [dayjs().startOf('month').valueOf(), dayjs().valueOf()],
  上月: [dayjs().subtract(1, 'month').startOf('month').valueOf(), dayjs().subtract(1, 'month').endOf('month').valueOf()],
  最近三个月: [dayjs().subtract(2, 'month').startOf('month').valueOf(), dayjs().valueOf()],
  最近六个月: [dayjs().subtract(5, 'month').startOf('month').valueOf(), dayjs().valueOf()],
  最近12个月: [dayjs().subtract(11, 'month').startOf('month').valueOf(), dayjs().valueOf()],
}

const tags = ref([
  {
    name: '乙肝表面抗原(HBsAg)定量检测',
    select: true, /// 默认选中第一个
    color: '#FF9B54',
    code: 'HBsAg',
    max: 1,
    unit: 'IU/mL',
  },
  {
    name: '乙型肝炎病毒DNA扩增定量检测',
    select: false,
    color: '#FFC569',
    code: 'HBVDNAQD',
    max: 1000,
    unit: 'IU/mL',
  },
  {
    name: '天门冬氨基转移酶(AST)',
    select: false,
    color: '#24BEE8',
    code: 'AST',
    unit: 'U/L',
    max: route.query.patientSex === '男' ? 40 : 35,
    markArea: {
      itemStyle: {
        color: addColorAlpha('#c6eae8', 0.3),
      },
      data: [
        [{
          name: '',
          yAxis: route.query.patientSex === '男' ? '15' : '13',
        },
        {
          yAxis: route.query.patientSex === '男' ? '40' : '35',
        }],
      ],
    },
  },
  {
    name: '丙氨酸氨基转移酶(ALT)',
    select: false,
    color: '#E3C97A',
    code: 'ALT',
    unit: 'U/L',
    max: route.query.patientSex === '男' ? 30 : 19,
    markLine: {
      symbol: ['none', 'none'],
      data: [{ name: '正常值', yAxis: route.query.patientSex === '男' ? '30' : '19', lineStyle: { type: 'solid', width: 1.5, color: '#A5B8D1' } }],
    },
  },
  {
    name: '总胆红素(T-Bil)',
    select: false,
    color: '#3AC9A8',
    code: 'T-BIL',
    max: 17.1,
    unit: 'umol/L',
    markArea: {
      itemStyle: {
        color: addColorAlpha('#c6eae8', 0.3),
      },
      data: [
        [{
          name: '',
          yAxis: '3.4',
        },
        {
          yAxis: '17.1',
        }],
      ],
    },
    visualMap: {
      dimension: 1,
      pieces: [
        {
          min: 3.4,
          max: 17.1,
          color: 'red',
        },
      ],
    },
  },
  {
    name: '血清白蛋白(Alb)',
    select: false,
    color: '#9D96F5',
    code: 'ALB',
    unit: 'g/L',
    max: 51,
    min: 35,
    markArea: {
      itemStyle: {
        color: addColorAlpha('#c6eae8', 0.3),
      },
      data: [
        [{
          name: '',
          yAxis: '35',
        },
        {
          yAxis: '51',
        }],
      ],
    },
  },
  {
    name: 'γ-谷氨酰基转移酶(GGT)',
    select: false,
    color: '#8ABD76',
    code: 'GGT',
    unit: 'U/L',
    max: 50,
    markArea: {
      itemStyle: {
        color: addColorAlpha('#c6eae8', 0.3),
      },
      data: [
        [{
          name: '',
          yAxis: '0',
        },
        {
          yAxis: '50',
        }],
      ],
    },
  },
  {
    name: '肝纤维化4因子指数(FIB-4)',
    select: false,
    color: '#E690D1',
    code: 'FIB-4',
    unit: '',
    max: 3.25,
  },
  {
    name: '天门冬氨基转移酶和血小板比率指数(APRI)',
    select: false,
    color: '#5B96FD',
    code: 'APRI',
    unit: 'U/L',
    max: 1,
  },
  // {
  //   name: '肝脏硬度值测定(LSM)',
  //   select: false,
  //   color: '#FA8383',
  //   code: 'LSM',
  //   max: 30.0,
  // },
])

function sliderLabelShow() {
  const sliderZoom = (myChart as any)._componentsViews.find((view: any) => view.type == 'dataZoom.slider')

  let leftP = sliderZoom._displayables.handleLabels[0].style.text.length * 8
  let rightP = -sliderZoom._displayables.handleLabels[1].style.text.length * 8

  sliderZoom._displayables.handleLabels[0].x = leftP
  sliderZoom._displayables.handleLabels[1].x = rightP

  myChart?.on('datazoom', (e: any) => {
    if (e.start < 10)
      leftP = sliderZoom._displayables.handleLabels[0].style.text.length * 8

    else
      leftP = 0

    if (e.end > 90)
      rightP = -sliderZoom._displayables.handleLabels[1].style.text.length * 8

    else
      rightP = 0

    sliderZoom._displayables.handleLabels[0].x = leftP
    sliderZoom._displayables.handleLabels[1].x = rightP
  })
}

function timeFormaterSet(value: any) {
  if (value) {
    let startUnix = dayjs(value[0]).valueOf()
    let endUnix = dayjs(value[1]).endOf('month').valueOf()

    if (endUnix > Date.now())
      endUnix = Date.now()

    if (startUnix > Date.now())
      startUnix = dayjs().startOf('month').valueOf()

    const mondif = dayjs(endUnix).diff(startUnix, 'month')
    /// 时间最长跨度不能超过12个月
    if (mondif > 11) {
      /// 则 限定在12 以内
      endUnix = dayjs(startUnix).add(11, 'month').endOf('month').valueOf()
      window.$message.warning('时间跨度最多可选择12个月')
    }
    timeRange.value = [startUnix, endUnix]
  }
}

/// 标签选择
function tagSelect(tag: any) {
  tag.select = !tag.select
  _.throttle(() => {
    getDatasFromNetWork()
  }, 400)()
}

/// 初始化chart
function initChartDom() {
  // setTimeout(() => {
  myChart ??= echarts.init(document.getElementById('chart'), null, { renderer: 'svg', locale: 'ZH' })
  // }, 200)
}

/// 配置数据
function setChartOptionAndData(series: Array<any>) {
  const xMin = `${dayjs(timeRange.value[0]).format('YYYY-MM-DD')} 00:00:00`
  const xMax = `${dayjs(timeRange.value[1]).format('YYYY-MM-DD')} 23:59:59`

  const option: echarts.EChartsOption = {
    legend: {
      right: 14,
      show: false,
    },
    tooltip: {
      backgroundColor: 'rgba(0,0,0,0.7)',
      textStyle: {
        fontSize: '12px',
        color: 'white',
      },
      trigger: 'axis',
      padding: 0,
      axisPointer: {
        type: 'line',
        show: false,
      },
      formatter(params) {
        let itemstr = ''
        params.forEach((element) => {
          itemstr += `<div flex items-center my-8px>
              <div w-5px h-1px style=background-color:${element.color}></div>
              <div w-8px h-8px rounded-4px style='border:1px solid ${element.color}'></div>
              <div w-5px h-1px mr-5px  style=background-color:${element.color}></div>
              <div>
                <span mr-3px>${element.seriesName}:</span>
                <span>${element.data.label.formatter}</span>
                </div>
            </div>`
        })
        const top = `<div>${dayjs(params[0].axisValue).format('YYYY-MM-DD')}</div>`
        return `<div px-8px pt-8px>
          ${top}
          ${itemstr}
          </div>`
      },
      borderColor: 'rgba(0,0,0,0,0)',
    },
    color: ['#FF9B54'],
    xAxis: {
      type: 'time',
      name: '时间',
      splitNumber: 4,
      min: xMin,
      max: xMax,
      axisTick: {
        show: false,
      },
      nameTextStyle: {
        color: '#999',
      },
      axisLabel: {
        color: '#999',
        formatter(value, index) {
          // return value.toString()
          return dayjs(value).format('YYYY-MM-DD')
        },
      },
      axisLine: {
        lineStyle: {
          color: '#e8e8e8',
        },
      },
    },
    yAxis: {
      show: true,
      name: Object.values(selectLineColors).length === 1 ? Object.values(selectLineColors)[0].unit : '',
      // max: (value) => {
      //   return setTheMaxYaxisValue(value.max)
      // },
      nameTextStyle: {
        color: '#999',
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#e8e8e8',
        },
      },
      axisLabel: {
        show: !(Object.keys(selectLineColors).length > 1),
        interval: 'auto',
        color: '#999',
      },
    },
    dataZoom: [
      {
        type: 'slider',
      },
    ],
    grid: {
      bottom: 50,
      left: 34,
      right: 52,
      top: 30,
      containLabel: true,
    },
    series,
  }
  myChart?.setOption(option, true)
  sliderLabelShow()
}

function dynamicHover(e: any, inside: any, item: any) {
  const dot = e.target.children[0]
  if (inside || item.select)
    dot.style.backgroundColor = item.color
  else
    dot.style.backgroundColor = '#ccc'
}

/// / 寻早最合适的 Y轴最大值
function setTheMaxYaxisValue(value) {
  /// 当前选中的Item 里的最大值
  let maxV = value
  const bbb = value / 5
  try {
    maxV = _.maxBy(Object.values(selectLineColors), 'max').max
  }
  catch (error) {

  }
  if (value > maxV)
    return (value > 100 ? (value + bbb) : (value <= 1 ? (value + 0.5) : value + bbb)).toFixed(2)
  else
    return (maxV > 100 ? (maxV + bbb) : (maxV <= 1 ? value + 0.5 : maxV + bbb)).toFixed(2)
}

onMounted(() => {
  initChartDom()
  getDatasFromNetWork()
  // const xDatas = generateFullDateArray(dayjs(timeRange.value[0]).format('YYYY-MM-DD'), dayjs(timeRange.value[1]).format('YYYY-MM-DD'))
  // console.log(xDatas)
})
window.addEventListener('resize', () => {
  myChart?.resize()
})

function getDatasFromNetWork() {
  if (timeRange.value[0] == null) {
    window.$message.warning('请选择时间段')
    return
  }

  const patientId = route?.query?.patientId
  if (patientId == null || patientId.length === 0) {
    window.$message.warning('未找到患者id')
    return
  }

  const codes: Array<string> = []
  tags.value.forEach((element) => {
    if (element.select) {
      codes.push(element.code)
      selectLineColors[element.code] = {
        color: element.color,
        markLine: element?.markLine,
        markArea: element?.markArea,
        max: element.max,
        visualMap: element.visualMap,
        unit: element.unit,
      }
    }
    else {
      delete selectLineColors[element.code]
    }
  })

  const start = dayjs(timeRange.value[0]).format('YYYY-MM')
  const end = dayjs(timeRange.value[1]).format('YYYY-MM')

  if (Object.keys(selectLineColors).length === 0) {
    myChart?.clear()
    return
  }
  lodingSpin.value = true
  getKeyItemStatisticsDatas({
    startTime: start,
    endTime: end,
    patientId,
    code: codes,
  }).then((res) => {
    lodingSpin.value = false

    setSeriesChart(res.data ?? {})
  }).catch((e) => {
    lodingSpin.value = false
  })
}

function setSeriesChart(datas: any) {
  const series = []
  const currLength = Object.values(datas).length
  for (const key in datas) {
    const line = {
      type: 'line',
      name: key,
      colorBy: 'data',
      symbolSize: 6,
      data: [],
      connectNulls: false,
      itemStyle: {
        color: selectLineColors[key]?.color,
      },
      markLine: currLength === 1 ? selectLineColors[key]?.markLine : null,
      markArea: currLength === 1 ? selectLineColors[key]?.markArea : null,
      visualMap: currLength === 1 ? selectLineColors[key]?.visualMap : null,
    }

    /// 排个序c
    const sortDatas = _.sortBy(datas[key] || [], (e) => {
      return dayjs(e?.reportTime).valueOf()
    })
    sortDatas?.forEach((element: any) => {
      line.name = element?.chineseName
      line.data.push({
        value: [element.reportTime, handleTheLgResult(element.result, element.englishName)],
        label: {
          formatter: `${element.result}${element.unit}`,
        },
      })
      selectLineColors[key].unit = element.unit
    })
    series.push(line)
  }

  setChartOptionAndData(series)
}
function timeChange() {
  /// 刷新数据
  setTimeout(() => {
    getDatasFromNetWork()
  }, 100)
}

/// 处理非数值的值函数
function handleTheLgResult(result, englishName) {
  ///
  switch (englishName) {
    case 'HBsAg':
      /// 去除里面包含的汉字
      const reg = /[\u4E00-\u9FA5]/g

      return result.replace(reg, '')
    default:
      return result
  }
}

function isEmpty() {
  let empty = true
  for (let index = 0; index < tags.value.length; index++) {
    const element = tags.value[index]
    if (element.select) {
      empty = false
      break
    }
  }
  return empty
}
</script>

<template>
  <div class="content">
    <div flex items-center>
      <span mr-10px>时间</span>
      <n-date-picker
        v-model:value="timeRange"
        style="width: 280px"
        separator="~"
        format="yyyy-MM"
        type="monthrange"
        :shortcuts="shortTimes"
        @confirm="timeChange"
        @update-formatted-value="timeFormaterSet"
      />
    </div>
    <div mt-4px flex>
      <span mr-10px mt-16px>指标</span>
      <div flex flex-1 flex-wrap>
        <div v-for="(item, index) in tags" :key="index" mr-10px mt-10px flex items-center :class="`btn-tag ${item.select ? 'btn-tag-active' : ''} `" @mouseenter="(e) => dynamicHover(e, 1, item)" @mouseleave="(e) => dynamicHover(e, 0, item)" @click="tagSelect(item)">
          <div mr-6px h-8px w-8px rounded-4px class="tag-dot" :style="{ backgroundColor: item.select ? item.color : '#ccc' }" />
          <div>{{ item.name }}</div>
        </div>
      </div>
    </div>
    <SectionTitle mb-4px mt-30px>
      趋势统计
    </SectionTitle>
    <n-spin type="uni" size="medium" h-full :show="lodingSpin">
      <n-empty v-if="isEmpty()" description="无数据" style="height: 100%;display: flex;align-items: center;justify-content: center;" />
      <div v-show="!isEmpty()" id="chart" style="flex:1;width: 100%; margin-top: 10px;height: 100%;" />
    </n-spin>
  </div>
</template>

<style lang="scss" scoped>
.content {
    display: flex;
    flex-direction: column;
    height: 100%;
}

.btn-tag {
    border: 1px solid #cccccc;
    height: 26px;
    justify-content: center;
    padding: 0 18px;
    border-radius: 4px;
    font-size: 12px;
    color: #333;
    .tag-dot {
        background-color: #ccc;
    }

    &:hover {
        border: 1px solid #06AEA6;
    }
}

.btn-tag-active {
    background: #ecf6ff;
    border: 1px solid #06AEA6;
}

:deep(.n-input-wrapper:nth-child(1)) {
    padding-left: 0;
}

:deep(.n-tabs-pane-wrapper){
  height: 100%;
}
:deep(.n-spin-content){
  height: 100%;
}
</style>
