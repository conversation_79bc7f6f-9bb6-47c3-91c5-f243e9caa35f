<script lang='ts' setup>
import { useRoute } from 'vue-router'
import dayjs from 'dayjs'
import { getVisitRecordListApi } from '@/api/patient'
import type { VisitRecord, VisitRecordParams } from '@/api/patient'
import { BasicTable, RowAction } from '@/components/Table'
import { usePermission, useRouterPush } from '@/hooks'

const { routerPush } = useRouterPush()
const { hasPermission } = usePermission()
const route = useRoute()
// TEST_id:1680788234854936578
const patientId = route.query?.patientId as string
const primaryIndex = route.query?.primaryIndex as string
const slmcNo = route.query?.slmcNo as string
// const patientId = '1680788234854936578'

function createColumns() {
  return [
    {
      title: '序号',
      width: '80',
      render(_, index: number) {
        return index + 1
      },
    },
    {
      title: '就诊时间',
      key: 'treatDate',
      width: '150',
      render(row: VisitRecord) {
        const { treatDate } = row
        return treatDate ? dayjs(treatDate).format('YYYY-MM-DD') : '-'
      },
    },
    {
      title: '就诊医院',
      key: 'hospitalName',
      width: '200',
    },
    {
      title: '类型',
      key: 'type',
      width: '80',
      render(row: VisitRecord) {
        const { type } = row
        const typeObj: Record<number, string> = {
          1: '门诊',
          2: '住院',
        }
        return typeObj[type]
      },
    },
    {
      title: '就诊科室',
      key: 'departmentName',

    },

  ]
}
/**
 * 跳转检查报告
 * @param patientId 患者id
 * @param recordId 就诊id
 *
 */
function handleGoExamList(patientId: string, recordId: string, patientName: string) {
  routerPush({
    name: 'patient_check',
    query: {
      patientId, recordId, patientName, primaryIndex, slmcNo,
    },
  })
}
/**
 * 跳转检验报告
 * @param patientId 患者id
 * @param recordId 就诊id
 *
 */
function handleGoAssayList(patientId: string, recordId: string, patientName: string) {
  routerPush({
    name: 'patient_inspection',
    query: {
      patientId, recordId, patientName, primaryIndex, slmcNo,
    },
  })
}
/**
 * 跳转病历文书
 * @param patientId 患者id
 * @param recordId 就诊id
 *
 */
function handleGoMedicalHistory(patientId: string, recordId: string, type: number, patientName: string) {
  routerPush({
    name: 'patient_medicalHistory',
    query: {
      patientId, recordId, type, patientName, primaryIndex, slmcNo,
    },
  })
}
function createActionColumns() {
  return {
    title: '操作',
    key: 'address',
    width: 250,
    fixed: 'right',
    render(row: VisitRecord) {
      const { patientId, recordId, type, patientName } = row
      return h(RowAction, {
        actions: [
          {
            label: '病历文书',
            onClick: () => handleGoMedicalHistory(patientId, recordId, type, patientName),
            type: 'primary',
            text: true,

          },
          {
            label: '检查报告',
            onClick: () => handleGoExamList(patientId, recordId, patientName),
            type: 'primary',
            text: true,
          },
          {
            label: '检验报告',
            onClick: () => handleGoAssayList(patientId, recordId, patientName),
            type: 'primary',
            text: true,

          },
        ],
      })
    },
  }
}

const tableRef = ref<InstanceType<typeof BasicTable> | null>(null)
const columns = createColumns()
const actionColumns = hasPermission('lddp:database:patientDetail:record:operate') ? createActionColumns() : null
// 分页器
const paginationReactive = reactive({
  page: 1,
  pageSize: 10,
  pageSizes: [10, 20, 50, 100],
})
const tableParams = reactive<VisitRecordParams>({
  size: 10,
  start: 1,
  patientId,
})
/** 是否有查询权限 */
function hasSearchPermission() {
  return hasPermission('lddp:database:patientDetail:record:search')
}
/**
 * 获取表格数据
 * @param res 查询参数
 */
async function loadDataTable(res: { size: number; start: number }) {
  if (!hasSearchPermission())
    return
  return await getVisitRecordListApi<VisitRecord>({ ...tableParams, ...res })
}
</script>

<template>
  <div>
    <BasicTable
      ref="tableRef"
      :columns="columns"
      :request="loadDataTable"
      :action-column="actionColumns"
      :pagination="paginationReactive"
      striped
      :scroll-x="1000"
    />
  </div>
</template>

<style scoped lang="scss">

</style>
