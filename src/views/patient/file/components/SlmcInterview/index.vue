<script lang='ts' setup>
import dayjs from 'dayjs'
import { useRoute, useRouter } from 'vue-router'
import { planPhaseAPI, resendAPI } from '@/api/followCenter'
import { formatRate, isNull } from '@/utils'
import { FeedbackEnum } from '@/constants'
import { usePermission } from '@/hooks'

defineOptions({
  name: 'SlmcInterview',
})

const { hasPermission } = usePermission()

const router = useRouter()
const route = useRoute()

const primaryIndex = route.query?.primaryIndex as string
const patientId = route.query?.patientId as string
const patientName = route.query?.patientName as string
const source = route.query?.source as string
const slmcNo = route.query?.slmcNo as string
const age = route.query?.age as string

const tableData = ref<any[]>([{}])

const resendIndex = computed(() => {
  return tableData.value.findIndex((i: any) => {
    return i.isEditor === true && i.sendStatus === 'SEND'
  })
})

const sendMap: Record<string, string> = {
  SEND: '已发送',
  NO_SEND: '未发送',
}

async function getData() {
  try {
    const { data } = await planPhaseAPI<any>(primaryIndex)
    if (data[0].planTemplateId === 'v0') {
      tableData.value = [
        {
          planTemplateName: null,
          phaseTemplateCode: 0,
          isEditor: true,
          allRate: null,
          primaryIndex,
        },
      ]
    }
    else {
      tableData.value = data
    }
  }
  catch (error) {
    console.log(error)
  }
}

function toQuestionPage(row: any) {
  const { planId, planPhaseId, planTemplateId, phaseTemplateCode, primaryIndex, isEditor } = row
  router.push({
    name: 'follow_questionnaire',
    query: {
      planId,
      planPhaseId,
      planTemplateId,
      phaseTemplateCode,
      primaryIndex,
      fromPage: 'slmc',
      patientId,
      patientName,
      source,
      slmcNo,
      age,
      isEditor,
    },
  })
}

function getColor(row: any) {
  if (row.phaseTemplateCode === 0)
    return 'text-#333'

  switch (row.isEditor) {
    case true:
      return 'text-#FF9B54'
    case false:
      return 'text-#999'
    default:
      return 'text-#999'
  }
}

async function handleResend(row: any) {
  try {
    const results = await resendAPI<any>(row.planPhaseId as string)
    if (results) {
      window.$message.success('发送成功!')
      getData()
    }
  }
  catch (error) {

  }
}
/** 是否有查询权限 */
function hasSearchPermission() {
  return hasPermission('lddp:database:patientDetail:slmc:search')
}

function init() {
  if (hasSearchPermission())
    getData()
  else
    tableData.value = []
}
onMounted(() => {
  init()
})
</script>

<template>
  <div h-300px>
    <el-table

      :data="tableData" stripe
      show-overflow-tooltip
    >
      <el-table-column label="序号" type="index" width="80" />
      <el-table-column min-width="200" label="随访计划名称" :formatter="(row:any) => row.planTemplateName || '-'" />
      <el-table-column width="140" label="访视阶段" align="right">
        <template #header>
          <div flex items-center justify-end gap-6px>
            <n-popover trigger="hover" placement="bottom">
              <template #trigger>
                <div flex items-center justify-end>
                  <img w-16px src="@/assets/images/information.svg" alt="">
                </div>
              </template>
              <div flex items-center>
                <div class="bg-[#ff9b54]" h-8px w-8px b-rd-2px />
                <div ml-5px>
                  代表正在进行的访视
                </div>
              </div>
              <div flex items-center>
                <div class="bg-[#999999]" h-8px w-8px b-rd-2px />
                <div ml-5px>
                  代表还未开始的访视
                </div>
              </div>
            </n-popover>
            <div>
              访视阶段
            </div>
          </div>
        </template>
        <template #default="{ row }">
          <div :class="getColor(row)">
            V{{ row.phaseTemplateCode }}
            <span v-if="!isNull(row.allRate)">
              ({{ formatRate(row.allRate) }})
            </span>
          </div>
        </template>
      </el-table-column>
      <el-table-column width="110" label="计划随访日期" align="right" :formatter="(row:any) => row.followTime ? dayjs(row.followTime).format('YYYY-MM-DD') : '-'" />
      <el-table-column width="110" label="基础信息采集" align="right" :formatter="(row:any) => formatRate(row.basicInfoRate)" />
      <el-table-column width="90" label="病史采集" align="right" :formatter="(row:any) => formatRate(row.medicalHistoryRate)" />
      <el-table-column width="100" label="实验室检查" align="right" :formatter="(row:any) => formatRate(row.inspectRate)" />
      <el-table-column width="100" label="影像学检查" align="right" :formatter="(row:any) => formatRate(row.imageRate)" />
      <el-table-column min-width="100" label="短信状态">
        <template #default="{ row }">
          <div v-if="row.sendStatus" flex items-center gap-6px>
            <div h-6px w-6px class="b-rd-50%" :class="[row.sendStatus === 'SEND' ? 'bg-#3AC9A8' : 'bg-#FEB034']" />
            <div>
              {{ sendMap[row.sendStatus as string] }}
            </div>
          </div>
          <div v-else>
            -
          </div>
        </template>
      </el-table-column>
      <el-table-column min-width="100" label="患者反馈" :formatter="(row:any) => FeedbackEnum[row.patientFeedback] || '-'" />
      <el-table-column v-permission="'lddp:database:patientDetail:slmc:operate'" label="操作" :width=" resendIndex > -1 ? 160 : 100" fixed="right">
        <template #default="{ row, $index }">
          <div flex items-center gap-5px>
            <span uno-link @click="toQuestionPage(row)">
              {{ row.isEditor ? '录入信息' : '查看信息' }}
            </span>
            <template v-if="$index === resendIndex">
              <div h-14px w-1px bg="#3B8FD9" />
              <span uno-link @click="handleResend(row)">重新发送</span>
            </template>
          </div>
        </template>
      </el-table-column>
      <template #empty>
        <div flex justify-center>
          <DataEmpty />
        </div>
      </template>
    </el-table>
  </div>
</template>

<style scoped lang="scss">

</style>
