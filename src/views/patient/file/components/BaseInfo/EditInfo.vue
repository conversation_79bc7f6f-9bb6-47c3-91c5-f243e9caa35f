<script lang='ts' setup>
import { useRoute } from 'vue-router'
import { useMessage } from 'wowjoy-vui'
import dayjs from 'dayjs'
import { useDebounceFn } from '@vueuse/core'
import InfoItem from './InfoItem.vue'
import type { FollowBaseInfo } from '@/api/followCenter/type'
import type { MedicalHistoryModal, PatientInfoModal } from '@/api/patient'
import { UpdateInfoApi, getMedicalHistoryInfoApi, getPatientInfoApi } from '@/api/patient'
import { useRouterPush } from '@/hooks'
import { compareArray, formatNullValueToShortBar, isNumber } from '@/utils'
import { batchUpdate, editPatient, editPlan, getFollowBaseInfoApi, nameIsAvailable, pageTagList } from '@/api/followCenter'
import { getDoctorListApi } from '@/api/doctor'
import type { BreadLit } from '@/layouts/common'
import { Breadcrumb } from '@/layouts/common'

import { useAuthStore } from '@/store'

const message = useMessage()

const route = useRoute()
const { routerPush } = useRouterPush()

const patientId = route.query?.patientId as string
const patientName = route.query?.patientName as string
const primaryIndex = route.query?.primaryIndex as string

const { userInfo } = useAuthStore()
const organId = userInfo.organId
const userId = userInfo.id

const patientInfo = ref<PatientInfoModal>({
  age: '',
  birthday: '',
  bloodPressure: '',
  bloodType: '',
  bmi: '',
  companyName: '',
  companyTel: '',
  companyType: '',
  contactsAddr: '',
  contactsName: '',
  contactsRelation: '',
  contactsTel: '',
  countryName: '',
  createTime: '',
  culturalName: '',
  familyMembers: '',
  heartRate: '',
  height: '',
  homeAddr: '',
  id: '',
  income: '',
  lastVisit: '',
  liveAddr: '',
  liverComplaintType: 0,
  marriageName: '',
  medicalInsuranceNo: '',
  medicalInsuranceType: '',
  nationCode: '',
  nationName: '',
  patientIdCardNum: '',
  patientIdCardType: '',
  patientName: '',
  patientRecordNo: '',
  patientSource: '',
  phone: '',
  phySource: '',
  phyUpdateTime: '',
  primaryIndex: '',
  professionName: '',
  pulse: '',
  respiratoryRate: '',
  sexCode: '',
  sexName: '',
  slmcNo: '',
  status: 0,
  tag: '',
  v1Visit: '',
  weight: '',
})
const bmi = computed(() => {
  const height = Number.isNaN(Number(patientInfo.value.height)) ? 0 : Number(patientInfo.value.height)
  const weight = Number.isNaN(Number(patientInfo.value.weight)) ? 0 : Number(patientInfo.value.weight)

  if (isNumber(height) && height > 0 && isNumber(weight) && weight > 0)
    return Number((weight / (height / 100) ** 2).toFixed(1))
  else
    return null
})
/** 病史信息 */
const medicalHistory = ref<MedicalHistoryModal | null>({
  allergyHistory: '',
  chiefComplaint: '',
  dataSource: '',
  dataUpdateTime: '',
  drugHistory: '',
  familyHistory: '',
  id: '',
  maritalHistory: '',
  menstrualHistory: '',
  operationHistory: '',
  pastHistory: '',
  patientId: '',
  patientName: '',
  patientRecordNo: '',
  personalHistory: '',
  presentHistory: '',
  slmcNo: '',
})
/** 随访信息 */
const followBaseInfo = ref<FollowBaseInfo>({
  joinTime: null,
  manageDoctorId: null,
  manageDoctorName: null,
  nextFollowTime: null,
  patientId: null,
  planName: null,
  planSort: null,
  planSortName: null,
  planStatus: null,
  primaryIndex: null,
  tagIds: null,
  tagName: null,
  visitTime: '',
  planTemplateId: null,
  manageOrganName: null,
  manageOrganId: null,
})

// 原始标签数据
let originalTags: string[] = []

// 标签宽度
const LABEL_WIDTH = {
  demography: '115',
  physique: '115',
}
// 血型枚举
const bloodTypeOptions = [
  { label: 'A型', value: 'A' },
  { label: 'B型', value: 'B' },
  { label: 'AB型', value: 'AB' },
  { label: 'O型', value: 'O' },
  { label: 'RH型 ', value: 'RH' },
]

// 患者随访状态枚举
const planStatusOptions = [
  { label: '随访中', value: 'FOLLOW' },
  { label: '完成', value: 'FINISH' },
  { label: '拒绝随访', value: 'REFUSE' },
  { label: '死亡', value: 'DEAD' },
  { label: '失访', value: 'LOSS' },
]
// 婚姻状态枚举
const maritalStatusOptions = [
  { label: '已婚', value: '已婚' },
  { label: '未婚', value: '未婚' },
  { label: '未知', value: '未知' },
]
interface DoctorNameOption {
  doctorName: string
  doctorId: string
}
// 医生枚举
const optionsDoctorName = ref<DoctorNameOption[]>([])
// 随访计划枚举
const planAOps = [
  { label: '乙肝', value: 'CHB' },
  { label: '脂肪肝', value: 'FLB' },
  { label: '丙肝', value: 'HCV' },
]
// 随访路径
const planAOpsB = ref<any>({})
// 标签枚举
const optionsTag = ref<any>([])

/**
 * 获取患者信息
 * @param patientId 患者id
 */
async function getPatientInfo(patientId: string) {
  const res = await getPatientInfoApi<PatientInfoModal>(patientId)

  if (res?.data)
    patientInfo.value = { ...res.data }
}
/**
 * 获取病史信息
 * @param patientId 患者id
 */
async function getMedicalHistory(patientId: string) {
  const res = await getMedicalHistoryInfoApi<MedicalHistoryModal>(patientId)

  if (res?.data)
    medicalHistory.value = { ...res.data }
}
/**
 * 获取随访相关信息
 * @param primaryIndex 患者主索引
 */
async function getFollowBaseInfo(primaryIndex: string) {
  const { data } = await getFollowBaseInfoApi<FollowBaseInfo>({ primaryIndex })

  if (data) {
    const keys = Object.keys(data) as Array<keyof typeof followBaseInfo.value>
    keys.forEach((key) => {
      if (key === 'tagIds') {
        followBaseInfo.value[key] = (data[key] as string)?.split(',')
        const keySplit = (data[key] as string)?.split(',')
        if (Array.isArray(keySplit))
          originalTags = [...keySplit]
      }
      else {
        followBaseInfo.value[key] = data[key]
      }
    })
  }
}
// 获取随访路径
async function getNamePlan() {
  const res = await nameIsAvailable({ sortType: '' })
  if (res.data)
    planAOpsB.value = res.data
}
// 获取患者标签
async function getTagListType() {
  const res = await pageTagList({ organId, page: 1, size: 100, tagName: '', tagStatus: true, userId })
  if (res.data)
    optionsTag.value = res.data.records
}

// 搜索做防抖
const getDoctorName = useDebounceFn(async (name = '') => {
  const res = await getDoctorListApi<DoctorNameOption[]>({ name, organId })
  if (res.data)

    optionsDoctorName.value = res.data
}, 300)
/**
 * 医生姓名
 * @param query 搜索key
 */
function handleSearchDoctorName(query: string) {
  getDoctorName(query)
}
/**
 * 医生名字打开/关闭回调
 * @param show 下拉打开/关闭
 */
function handleUpdateShowDoctorName(show: boolean) {
  if (show)
    getDoctorName('')
}
// 医生名字更新
function handleUpdateValueDoctorName(value: string, options: any) {
  if (value)
    followBaseInfo.value.manageDoctorName = options?.doctorName
}
// 随访计划跟新
function handleUpdatePlanSort(value: string, options: any) {
  if (value)
    followBaseInfo.value.planSortName = options?.planTemplateName
}

function handleUpdatePlanName(val: string) {
  followBaseInfo.value.planTemplateId = planAOpsB.value[val][0].planTemplateId
  followBaseInfo.value.planSortName = planAOpsB.value[val][0].planTemplateName
}

/**
 * 保存
 */
async function handleConfirm() {
  const baseParams = {
    ...unref(patientInfo),
    bmi: bmi.value,
  }

  const statusParams = {
    doctorId: followBaseInfo.value.manageDoctorId,
    doctorName: followBaseInfo.value.manageDoctorName,
    primaryIndex: [primaryIndex],
    organizationId: organId,
    patientStatus: followBaseInfo.value.planStatus,
  }
  const planParams = {
    doctorId: followBaseInfo.value.manageDoctorId,
    doctorName: followBaseInfo.value.manageDoctorName,
    planTemplateId: followBaseInfo.value.planTemplateId,
    planTemplateName: followBaseInfo.value.planName,
    planTemplateSort: followBaseInfo.value.planSort,
    primaryIndex: [
      primaryIndex,
    ],
  }

  const { removed, added } = compareArray(originalTags, followBaseInfo.value.tagIds as string[])

  const tagParams = {
    addTagIds: added,
    originId: organId,
    primaryIndexes: [
      primaryIndex,
    ],
    removeTagIds: removed,
    userId: patientId,
    userName: patientName,
  }
  let res = []

  if (patientInfo.value.status === 1) {
    // 患者状态和随访计划不能一起调用
    await editPatient(statusParams)
    await editPlan(planParams)
    res = [
      await UpdateInfoApi(baseParams),
      await batchUpdate(tagParams),
    ]
  }
  else {
    res = [
      await UpdateInfoApi(baseParams),
    ]
  }

  const isSuccess = res.every(({ data }) => !!data)

  if (isSuccess) {
    message.success('保存成功')
    GoPrevPage()
  }
}
/**
 * 取消，返回上一级页面
 */
function handleCancel() {
  GoPrevPage()
}
function GoPrevPage() {
  routerPush({
    name: 'patient_file',
    query: {
      patientId,
      patientName,
      primaryIndex,
      tabName: 'BaseInfo',
    },
  })
}
// 查看随访计划介绍
function renderPlanTemplateMemo(key: string, compare: string) {
  if (!planAOpsB.value[key])
    return
  const renderText = planAOpsB.value[key].find((v: any) => v.planTemplateId === compare)
  if (renderText)
    return renderText.planTemplateMemo
  else
    return '无'
}
// 查看随访路径介绍
function renderSortMemo(key: string, compare: string) {
  if (!planAOpsB.value[key])
    return
  const renderText = planAOpsB.value[key].find((v: any) => v.planTemplateId === compare)
  if (renderText)
    return renderText.sortMemo
  else
    return '无'
}

/** 初始化函数 */
function init() {
  patientId && getPatientInfo(patientId)
  patientId && getMedicalHistory(patientId)
  primaryIndex && getFollowBaseInfo(primaryIndex)
  getNamePlan()
  getTagListType()
  handleSearchDoctorName('')
}
onMounted(() => {
  init()
})
const renderBread = computed(() => {
  const patientId = route.query?.patientId as string
  const slmcNo = route.query?.slmcNo as string
  const patientName = route.query?.patientName as string
  const primaryIndex = route.query?.primaryIndex as string
  const patientList: BreadLit[] = [
    { title: '患者数据库', link: '/patient', key: 'patient' },
    { title: '患者列表', link: '/patient/list', key: 'patient_list' },
    {
      title: `${patientName || '未知病人'}档案信息`,
      link: `/patient/file?patientId=${patientId}&patientName=${patientName}&primaryIndex=${primaryIndex}&slmcNo=${slmcNo}&tabName=BaseInfo`,
      key: 'patient_file',
    },
    { title: '编辑信息', link: null, key: 'patient_editFile' },
  ]

  return [...patientList]
})
</script>

<template>
  <div>
    <Breadcrumb :bread-list="renderBread" />
    <PageCard breadcrumb>
      <PageTitle class="mb-2px">
        {{ patientInfo?.patientName }}档案信息
      </PageTitle>
      <div>
        <!-- 人口学信息 -->
        <section>
          <SectionTitle class="my-14px">
            人口学信息
          </SectionTitle>
          <n-grid x-gap="14" y-gap="14" :cols="2" class="bg-#F8F8F8 p-14px text-#333">
            <n-gi>
              <InfoItem label="加入SLMC日期：" :label-width="LABEL_WIDTH.demography">
                <span>{{ dayjs(patientInfo?.createTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
              </InfoItem>
            </n-gi>
            <n-gi>
              <InfoItem label="SLMC编号：" :value="patientInfo?.slmcNo" :label-width="LABEL_WIDTH.demography" />
            </n-gi>
            <n-gi>
              <InfoItem label="状态" :label-width="LABEL_WIDTH.demography" margin>
                <span> {{ patientInfo.status === 0 ? '未纳入随访' : '纳入随访' }}</span>
              </InfoItem>
            </n-gi>
            <n-gi>
              <InfoItem label="姓名：" :value="patientInfo.patientName" :label-width="LABEL_WIDTH.demography" />
            </n-gi>
            <n-gi>
              <InfoItem label="性别：" :value="patientInfo.sexName" :label-width="LABEL_WIDTH.demography" />
            </n-gi>
            <n-gi>
              <InfoItem label="出生年月：" :value="patientInfo.birthday" :label-width="LABEL_WIDTH.demography" />
            </n-gi>
            <n-gi>
              <InfoItem label="年龄：" :label-width="LABEL_WIDTH.demography">
                <span>{{ patientInfo?.age }}岁</span>
              </InfoItem>
            </n-gi>
            <n-gi>
              <InfoItem label="民族" :label-width="LABEL_WIDTH.demography" margin>
                <n-input v-model:value="patientInfo.nationName" class="flex-1 basis-380px" :maxlength="10" />
              </InfoItem>
            </n-gi>
            <n-gi>
              <InfoItem label="证件类型" :value="patientInfo.patientIdCardType" :label-width="LABEL_WIDTH.demography" margin />
            </n-gi>
            <n-gi>
              <InfoItem label="证件号：" :value="patientInfo.patientIdCardNum" :label-width="LABEL_WIDTH.demography" />
            </n-gi>
            <n-gi>
              <InfoItem label="手机号：" :value="patientInfo.phone" :label-width="LABEL_WIDTH.demography" />
            </n-gi>
            <n-gi>
              <InfoItem label="工作性质：" :label-width="LABEL_WIDTH.demography">
                <n-input v-model:value="patientInfo.companyType" class="flex-1 basis-380px" :maxlength="30" />
              </InfoItem>
            </n-gi>
            <n-gi>
              <InfoItem label="紧急联系人" :label-width="LABEL_WIDTH.demography" margin>
                <n-input v-model:value="patientInfo.contactsName" class="flex-1 basis-380px" :maxlength="10" />
              </InfoItem>
            </n-gi>
            <n-gi>
              <InfoItem label="紧急联系人电话：" :label-width="LABEL_WIDTH.demography">
                <n-input v-model:value="patientInfo.contactsTel" class="flex-1 basis-380px" :maxlength="10" />
              </InfoItem>
            </n-gi>
            <n-gi>
              <InfoItem label="医保性质：" :value="patientInfo.medicalInsuranceType" :label-width="LABEL_WIDTH.demography" />
            </n-gi>
            <n-gi>
              <InfoItem label="医保卡号：" :value="patientInfo.medicalInsuranceNo" :label-width="LABEL_WIDTH.demography" />
            </n-gi>
            <n-gi>
              <InfoItem label="婚姻状况" :label-width="LABEL_WIDTH.demography" margin>
                <!-- <n-select v-model:value="patientInfo.marriageName" :options="maritalStatusOptions" class="basis-380px" /> -->
                <n-input v-model:value="patientInfo.marriageName" class="flex-1 basis-380px" :maxlength="50" />
              </InfoItem>
            </n-gi>
            <n-gi>
              <InfoItem label="家庭成员" :label-width="LABEL_WIDTH.demography" margin>
                <n-input v-model:value="patientInfo.familyMembers" class="flex-1 basis-380px" :maxlength="50" />
              </InfoItem>
            </n-gi>
            <n-gi>
              <InfoItem label="家庭年收入" :label-width="LABEL_WIDTH.demography" margin>
                <n-input v-model:value="patientInfo.income" class="flex-1 basis-380px" :maxlength="10" />
              </InfoItem>
            </n-gi>
            <n-gi>
              <InfoItem label="户口所在地" :label-width="LABEL_WIDTH.demography" margin>
                <n-input v-model:value="patientInfo.homeAddr" class="flex-1 basis-380px" :maxlength="50" />
              </InfoItem>
            </n-gi>
            <n-gi>
              <InfoItem label="常住地址" :label-width="LABEL_WIDTH.demography" margin>
                <n-input v-model:value="patientInfo.liveAddr" class="flex-1 basis-380px" :maxlength="50" />
              </InfoItem>
            </n-gi>
          </n-grid>
        </section>
        <!-- 随访信息 -->
        <section v-show="patientInfo.status === 1">
          <SectionTitle class="my-14px">
            随访信息
          </SectionTitle>
          <n-grid x-gap="14" y-gap="14" :cols="2" class="bg-#F8F8F8 p-14px text-#333">
            <n-gi>
              <InfoItem label="加入随访日期：" :label-width="LABEL_WIDTH.demography">
                <span>{{ followBaseInfo?.joinTime ? dayjs(followBaseInfo?.joinTime).format('YYYY-MM-DD') : '-' }}</span>
              </InfoItem>
            </n-gi>
            <n-gi>
              <InfoItem label="最近就诊日期：" :value="followBaseInfo?.visitTime" :label-width="LABEL_WIDTH.demography">
                <span>{{ followBaseInfo?.visitTime ? dayjs(followBaseInfo?.visitTime).format('YYYY-MM-DD') : '-' }}</span>
              </InfoItem>
            </n-gi>
            <n-gi>
              <InfoItem label="下次随访日期：" :value="followBaseInfo?.nextFollowTime" :label-width="LABEL_WIDTH.demography">
                <span>{{ followBaseInfo?.nextFollowTime ? dayjs(followBaseInfo?.nextFollowTime).format('YYYY-MM-DD') : '-' }}</span>
              </InfoItem>
            </n-gi>
            <n-gi>
              <InfoItem label="患者状态" :label-width="LABEL_WIDTH.demography" margin>
                <n-select v-model:value="followBaseInfo.planStatus" :options="planStatusOptions" class="flex-1 basis-380px" />
              </InfoItem>
            </n-gi>
            <n-gi>
              <InfoItem label="管理医生" :label-width="LABEL_WIDTH.demography" margin>
                <n-select
                  v-model:value="followBaseInfo.manageDoctorId"
                  class="flex-1 basis-380px"
                  filterable placeholder="请选择"
                  label-field="doctorName"
                  value-field="doctorId"
                  :options="optionsDoctorName"
                  clearable
                  remote
                  @search="handleSearchDoctorName"
                  @update:show="handleUpdateShowDoctorName"
                  @update:value="handleUpdateValueDoctorName"
                />
              </InfoItem>
            </n-gi>
            <n-gi>
              <InfoItem label="随访计划" :label-width="LABEL_WIDTH.demography" margin align-items="start">
                <div class="w-full flex flex-col flex-1 basis-380px">
                  <div class="mb-10px flex items-center">
                    <n-select
                      v-model:value="followBaseInfo.planSort"
                      placeholder="请输入"
                      class="basis-170px"
                      :options="planAOps"
                      @update:value="handleUpdatePlanName"
                    />
                    <div class="mx-10px">
                      —
                    </div>
                    <n-select
                      v-model:value="followBaseInfo.planTemplateId"
                      placeholder="请输入"
                      label-field="planTemplateName"
                      value-field="planTemplateId"
                      class="basis-180px"
                      :options="planAOpsB[followBaseInfo.planSort!]"
                      @update:value="handleUpdatePlanSort"
                    />
                  </div>
                  <div class="flex items-center text-primary">
                    <n-popover trigger="hover" placement="bottom" raw arrow-style="background-color:rgba(0,0,0,0.70)">
                      <template #trigger>
                        <span class="cursor-pointer">查看随访路径</span>
                      </template>
                      <div
                        style="background-color:rgba(0,0,0,0.70);
                     color:#fff;
                     padding:15px 20px 10px 20px;
                     width:340px;max-height:150px;overflow-y:auto;
                     box-sizing: border-box;
                     font-size: 12px;"
                      >
                        {{ renderSortMemo(followBaseInfo.planSort!, followBaseInfo.planTemplateId!) }}
                      </div>
                    </n-popover>
                    <span class="mx-14px">|</span>
                    <n-popover trigger="hover" placement="bottom" raw arrow-style="background-color:rgba(0,0,0,0.70)">
                      <template #trigger>
                        <span class="cursor-pointer">查看随访介绍</span>
                      </template>
                      <div
                        style="background-color:rgba(0,0,0,0.70);
                     color:#fff;
                     padding:15px 20px 10px 20px;
                     width:340px;max-height:150px;overflow-y:auto;
                     box-sizing: border-box;
                     font-size: 12px;"
                      >
                        {{ renderPlanTemplateMemo(followBaseInfo.planSort!, followBaseInfo.planTemplateId!) }}
                      </div>
                    </n-popover>
                  </div>
                </div>
              </InfoItem>
            </n-gi>
            <n-gi>
              <InfoItem label="患者标签" :label-width="LABEL_WIDTH.demography" margin>
                <n-select
                  v-model:value="followBaseInfo.tagIds"
                  placeholder="请输入"
                  multiple
                  label-field="tagName"
                  value-field="tagId"
                  :options="optionsTag"
                  :max-tag-count="6"
                  class="flex-1 basis-380px"
                />
              </InfoItem>
            </n-gi>
          </n-grid>
        </section>
        <!-- 体格检查 -->
        <section class="pt-6px">
          <SectionTitle class="my-14px">
            体格检查<span class="ml-10px text-#666">(更新时间：{{ patientInfo?.phyUpdateTime ? dayjs(patientInfo?.phyUpdateTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}，数据来源：{{ formatNullValueToShortBar(patientInfo?.phySource) }})</span>
          </SectionTitle>
          <n-grid x-gap="14" y-gap="14" :cols="2" class="bg-#F8F8F8 p-14px text-#333">
            <n-gi>
              <InfoItem label="血型" :label-width="LABEL_WIDTH.physique" margin>
                <n-select v-model:value="patientInfo.bloodType" :options="bloodTypeOptions" class="flex-1 basis-380px" />
              <!-- <n-input v-model:value="patientInfo.bloodType" class="basis-380px" :maxlength="10" /> -->
              </InfoItem>
            </n-gi>
            <n-gi>
              <InfoItem label="体重" :label-width="LABEL_WIDTH.physique" margin>
                <n-input v-model:value="patientInfo.weight" class="flex-1 basis-380px" :maxlength="10">
                  <template #suffix>
                    kg
                  </template>
                </n-input>
              </InfoItem>
            </n-gi>
            <n-gi>
              <InfoItem label="血压" :label-width="LABEL_WIDTH.physique" margin>
                <n-input v-model:value="patientInfo.bloodPressure" class="flex-1 basis-380px" :maxlength="10">
                  <template #suffix>
                    mmHg
                  </template>
                </n-input>
              </InfoItem>
            </n-gi>
            <n-gi>
              <InfoItem label="脉搏" :label-width="LABEL_WIDTH.physique" margin>
                <n-input v-model:value="patientInfo.pulse" class="flex-1 basis-380px" :maxlength="10">
                  <template #suffix>
                    次/分
                  </template>
                </n-input>
              </InfoItem>
            </n-gi>
            <n-gi>
              <InfoItem label="身高" :label-width="LABEL_WIDTH.physique" margin>
                <n-input v-model:value="patientInfo.height" class="flex-1 basis-380px" :maxlength="10">
                  <template #suffix>
                    cm
                  </template>
                </n-input>
              </InfoItem>
            </n-gi>
            <n-gi>
              <InfoItem label="BMI" :label-width="LABEL_WIDTH.physique" margin :value="bmi" disabled>
                <template #suffix>
                  kg/m²
                </template>
              </InfoItem>
            </n-gi>
            <n-gi>
              <InfoItem label="心率" :label-width="LABEL_WIDTH.physique" margin>
                <n-input v-model:value="patientInfo.heartRate" class="flex-1 basis-380px" :maxlength="10">
                  <template #suffix>
                    次/分
                  </template>
                </n-input>
              </InfoItem>
            </n-gi>
            <n-gi>
              <InfoItem label="呼吸率" :label-width="LABEL_WIDTH.physique" margin>
                <n-input v-model:value="patientInfo.respiratoryRate" class="flex-1 basis-380px" :maxlength="10">
                  <template #suffix>
                    次/分
                  </template>
                </n-input>
              </InfoItem>
            </n-gi>
          </n-grid>
        </section>
        <!-- 就诊信息 -->
        <section class="pt-6px">
          <SectionTitle class="my-14px">
            就诊信息<span class="ml-10px text-#666">(展示近半年最新数据，数据来源：{{ formatNullValueToShortBar(medicalHistory?.dataSource) }})</span>
          </SectionTitle>
          <div
            class="bg-#F8F8F8 p-14px leading-tight text-#333"
          >
            <InfoItem label="主诉：" :value="medicalHistory?.chiefComplaint" label-width="60" class="mb-14px" align-items="start" />
            <InfoItem label="现病史：" :value="medicalHistory?.presentHistory" label-width="60" class="mb-14px" align-items="start" />
            <InfoItem label="既往史：" :value="medicalHistory?.pastHistory" label-width="60" class="mb-14px" align-items="start" />
            <InfoItem label="个人史：" :value="medicalHistory?.personalHistory" label-width="60" class="mb-14px" align-items="start" />
            <InfoItem label="婚育史：" :value="medicalHistory?.maritalHistory" label-width="60" class="mb-14px" align-items="start" />
            <InfoItem label="家族史：" :value="medicalHistory?.familyHistory" label-width="60" class="mb-14px" align-items="start" />
          </div>
        </section>
        <n-space :size="[16, 16]" justify="center" class="mb-10px mt-24px">
          <n-button type="primary" min-width="100px" class="tracking-4px" @click="handleConfirm">
            保存
          </n-button>
          <n-button min-width="100px" class="tracking-4px" @click="handleCancel">
            取消
          </n-button>
        </n-space>
      </div>
    </PageCard>
  </div>
</template>

<style scoped lang="scss">

</style>
