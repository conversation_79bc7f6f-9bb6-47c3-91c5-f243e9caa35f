<script lang='ts' setup>
import { useRoute } from 'vue-router'
import dayjs from 'dayjs'
import InfoItem from './InfoItem.vue'
import { SectionTitle } from '@/components/Title'
import type { MedicalHistoryModal, PatientInfoModal } from '@/api/patient'
import { useRouterPush } from '@/hooks'
import { formatNullValueToShortBar } from '@/utils'
import { getMedicalHistoryInfoApi, getPatientInfoApi } from '@/api/patient'
import { getFollowBaseInfoApi } from '@/api/followCenter'
import type { FollowBaseInfo } from '@/api/followCenter'

const route = useRoute()

const { routerPush } = useRouterPush()

const patientId = route.query?.patientId as string
const patientName = route.query?.patientName as string
const primaryIndex = route.query?.primaryIndex as string
/** 患者信息 */
const patientInfo = ref<PatientInfoModal | null>({
  age: '',
  birthday: '',
  bloodPressure: '',
  bloodType: '',
  bmi: '',
  companyName: '',
  companyTel: '',
  companyType: '',
  contactsAddr: '',
  contactsName: '',
  contactsRelation: '',
  contactsTel: '',
  countryName: '',
  createTime: '',
  culturalName: '',
  familyMembers: '',
  heartRate: '',
  height: '',
  homeAddr: '',
  id: '',
  income: '',
  lastVisit: '',
  liveAddr: '',
  liverComplaintType: 0,
  marriageName: '',
  medicalInsuranceNo: '',
  medicalInsuranceType: '',
  nationCode: '',
  nationName: '',
  patientIdCardNum: '',
  patientIdCardType: '',
  patientName: '',
  patientRecordNo: '',
  patientSource: '',
  phone: '',
  phySource: '',
  phyUpdateTime: '',
  primaryIndex: '',
  professionName: '',
  pulse: '',
  respiratoryRate: '',
  sexCode: '',
  sexName: '',
  slmcNo: '',
  status: 0,
  tag: '',
  v1Visit: '',
  weight: '',
})
/** 病史信息 */
const medicalHistory = ref<MedicalHistoryModal | null>({
  allergyHistory: '',
  chiefComplaint: '',
  dataSource: '',
  dataUpdateTime: '',
  drugHistory: '',
  familyHistory: '',
  id: '',
  maritalHistory: '',
  menstrualHistory: '',
  operationHistory: '',
  pastHistory: '',
  patientId: '',
  patientName: '',
  patientRecordNo: '',
  personalHistory: '',
  presentHistory: '',
  slmcNo: '',
})
/** 随访信息 */
const followBaseInfo = ref<FollowBaseInfo>({
  joinTime: null,
  manageDoctorId: null,
  manageDoctorName: null,
  nextFollowTime: null,
  patientId: null,
  planName: null,
  planSort: null,
  planSortName: null,
  planStatus: null,
  primaryIndex: null,
  tagIds: null,
  tagName: null,
  visitTime: '',
  planTemplateId: null,
  manageOrganName: null,
  manageOrganId: null,
})

const planSortMap: Record<string, string> = {
  CHB: '乙肝',
  FLB: '脂肪肝',
  HCV: '丙肝',
}

const followStatusMap: Record<string, {
  name: string
  color: string
}> = {
  FOLLOW: {
    name: '随访中',
    color: 'before:bg-#ff9b54',
  },
  FINISH: {
    name: '完成',
    color: 'before:bg-#4ACFB1',
  },
  REFUSE: {
    name: '拒绝随访',
    color: 'before:bg-#f36969',
  },
  DEAD: {
    name: '死亡',
    color: 'before:bg-#a5b8d1',
  },
  LOSS: {
    name: '失访',
    color: 'before:bg-#ccc',
  },
}

// 标签宽度
const LABEL_WIDTH = {
  // 人口学信息
  demography: ['110', '120'],
  // 随访信息
  follow: ['98', '120'],
  // 体格检查
  physique: ['42', '120'],
  // 就诊信息
  visiting: ['56'],
}

/**
 * 获取患者信息
 * @param patientId 患者id
 */
async function getPatientInfo(patientId: string) {
  const res = await getPatientInfoApi<PatientInfoModal>(patientId)

  if (res?.data)
    patientInfo.value = { ...res.data }
}
/**
 * 获取病史信息
 * @param patientId 患者id
 */
async function getMedicalHistory(patientId: string) {
  const res = await getMedicalHistoryInfoApi<MedicalHistoryModal>(patientId)

  if (res?.data)
    medicalHistory.value = { ...res.data }
}
/**
 * 获取随访相关信息
 * @param primaryIndex 患者主索引
 */
async function getFollowBaseInfo(primaryIndex: string) {
  const res = await getFollowBaseInfoApi<FollowBaseInfo>({ primaryIndex })

  if (res?.data)
    followBaseInfo.value = { ...res.data }
}

function goEditFile() {
  routerPush({ name: 'patient_editFile', query: { patientId, primaryIndex, patientName } })
}
/** 初始化函数 */
function init() {
  patientId && getPatientInfo(patientId)
  patientId && getMedicalHistory(patientId)
  primaryIndex && getFollowBaseInfo(primaryIndex)
}
onMounted(() => {
  init()
})
</script>

<template>
  <div class="pt-2px">
    <n-button v-permission="'lddp:database:patientDetail:edit'" type="primary" ghost @click="goEditFile">
      编辑
    </n-button>
    <div v-permission="'lddp:database:patientDetail:search'">
      <!-- 人口学信息 -->
      <section>
        <SectionTitle class="my-14px">
          人口学信息
        </SectionTitle>
        <n-grid x-gap="14" y-gap="14" :cols="2" class="bg-#F8F8F8 p-14px text-#333">
          <n-gi>
            <InfoItem label="加入SLMC日期：" :label-width="LABEL_WIDTH.demography[0]">
              <span>{{ dayjs(patientInfo?.createTime).format('YYYY-MM-DD HH:mm:ss') }}</span>
            </InfoItem>
          </n-gi>
          <n-gi>
            <InfoItem label="SLMC编号：" :value="patientInfo?.slmcNo" :label-width="LABEL_WIDTH.demography[1]" />
          </n-gi>
          <n-gi>
            <InfoItem label="状态：" :value="patientInfo?.slmcNo" :label-width="LABEL_WIDTH.demography[0]">
              <span>{{ patientInfo?.status === 0 ? '未纳入随访' : '已纳入随访' }}</span>
            </InfoItem>
          </n-gi>
          <n-gi>
            <InfoItem label="姓名：" :value="patientInfo?.patientName" :label-width="LABEL_WIDTH.demography[1]" />
          </n-gi>
          <n-gi>
            <InfoItem label="性别：" :value="patientInfo?.sexName" :label-width="LABEL_WIDTH.demography[0]" />
          </n-gi>
          <n-gi>
            <InfoItem label="出生年月：" :value="patientInfo?.birthday" :label-width="LABEL_WIDTH.demography[1]" />
          </n-gi>
          <n-gi>
            <InfoItem label="年龄：" :label-width="LABEL_WIDTH.demography[0]">
              <span>{{ `${patientInfo?.age ? `${patientInfo?.age}岁` : '-'}` }}</span>
            </InfoItem>
          </n-gi>
          <n-gi>
            <InfoItem label="民族：" :value="patientInfo?.nationName" :label-width="LABEL_WIDTH.demography[1]" />
          </n-gi>
          <n-gi>
            <InfoItem label="证件类型：" :value="patientInfo?.patientIdCardType" :label-width="LABEL_WIDTH.demography[0]" />
          </n-gi>
          <n-gi>
            <InfoItem label="证件号：" :value="patientInfo?.patientIdCardNum" :label-width="LABEL_WIDTH.demography[1]" />
          </n-gi>
          <n-gi>
            <InfoItem label="手机号：" :value="patientInfo?.phone" :label-width="LABEL_WIDTH.demography[0]" />
          </n-gi>
          <n-gi>
            <InfoItem label="工作性质：" :value="patientInfo?.companyType" :label-width="LABEL_WIDTH.demography[1]" />
          </n-gi>
          <n-gi>
            <InfoItem label="紧急联系人：" :value="patientInfo?.contactsName" :label-width="LABEL_WIDTH.demography[0]" />
          </n-gi>
          <n-gi>
            <InfoItem label="紧急联系人电话：" :value="patientInfo?.contactsTel" :label-width="LABEL_WIDTH.demography[1]" />
          </n-gi>
          <n-gi>
            <InfoItem label="医保性质：" :value="patientInfo?.medicalInsuranceType" :label-width="LABEL_WIDTH.demography[0]" />
          </n-gi>
          <n-gi>
            <InfoItem label="医保卡号：" :value="patientInfo?.medicalInsuranceNo" :label-width="LABEL_WIDTH.demography[1]" />
          </n-gi>
          <n-gi>
            <InfoItem label="婚姻状况：" :value="patientInfo?.marriageName" :label-width="LABEL_WIDTH.demography[0]" />
          </n-gi>
          <n-gi>
            <InfoItem label="家庭成员：" :value="patientInfo?.familyMembers" :label-width="LABEL_WIDTH.demography[1]" />
          </n-gi>
          <n-gi>
            <InfoItem label="家庭年收入：" :value="patientInfo?.income" :label-width="LABEL_WIDTH.demography[0]" />
          </n-gi>
          <n-gi>
            <InfoItem label="户口所在地：" :value="patientInfo?.homeAddr" :label-width="LABEL_WIDTH.demography[1]" />
          </n-gi>
          <n-gi>
            <InfoItem label="常住地址：" :value="patientInfo?.liveAddr" :label-width="LABEL_WIDTH.demography[0]" />
          </n-gi>
        </n-grid>
      </section>
      <!-- 随访信息 -->
      <section v-if="patientInfo?.status !== 0">
        <SectionTitle class="my-14px">
          随访信息
        </SectionTitle>
        <n-grid x-gap="14" y-gap="14" :cols="2" class="bg-#F8F8F8 p-14px text-#333">
          <n-gi>
            <InfoItem label="加入随访日期：" :label-width="LABEL_WIDTH.follow[0]">
              <span>{{ followBaseInfo?.joinTime ? dayjs(followBaseInfo?.joinTime).format('YYYY-MM-DD') : '-' }}</span>
            </InfoItem>
          </n-gi>
          <n-gi>
            <InfoItem label="最近就诊日期：" :value="followBaseInfo?.visitTime" :label-width="LABEL_WIDTH.follow[1]">
              <span>{{ followBaseInfo?.visitTime ? dayjs(followBaseInfo?.visitTime).format('YYYY-MM-DD') : '-' }}</span>
            </InfoItem>
          </n-gi>
          <n-gi>
            <InfoItem label="下次随访日期：" :value="followBaseInfo?.nextFollowTime" :label-width="LABEL_WIDTH.follow[0]">
              <div v-if="followBaseInfo?.nextFollowTime && followBaseInfo?.planStatus === 'FOLLOW'">
                <span>
                  {{ followBaseInfo?.nextFollowTime ? dayjs(followBaseInfo?.nextFollowTime).format('YYYY-MM-DD') : '-' }}
                </span>
                <span v-if="followBaseInfo?.nextFollowTime">（{{ (new Date(followBaseInfo?.nextFollowTime).setHours(0, 0, 0, 0) - Math.floor(new Date().setHours(0, 0, 0, 0))) / (1000 * 60 * 60 * 24) }}天）</span>
              </div>
              <div v-else>
                -
              </div>
            </InfoItem>
          </n-gi>
          <n-gi>
            <InfoItem label="患者状态：" :label-width="LABEL_WIDTH.follow[1]">
              <span v-if="followBaseInfo?.planStatus" class="planStatus" :class="[`${followStatusMap[followBaseInfo?.planStatus].color}`]">{{ followStatusMap[followBaseInfo?.planStatus].name }}</span>
              <span v-else>-</span>
            </InfoItem>
          </n-gi>
          <n-gi>
            <InfoItem label="管理医生：" :value="followBaseInfo?.manageDoctorName" :label-width="LABEL_WIDTH.follow[0]" />
          </n-gi>
          <n-gi>
            <InfoItem label="管理医院：" :value="followBaseInfo?.manageOrganName" :label-width="LABEL_WIDTH.follow[1]" />
          </n-gi>
          <n-gi>
            <InfoItem label="随访计划：" :label-width="LABEL_WIDTH.follow[0]" align-items="center">
              <span>{{ planSortMap[followBaseInfo.planSort as string] }}-{{ formatNullValueToShortBar(followBaseInfo?.planName) }}</span>
            </InfoItem>
          </n-gi>
          <n-gi>
            <InfoItem label="患者标签：" :label-width="LABEL_WIDTH.follow[1]" align-items="center">
              <div flex="~ wrap" gap-10px>
                <template v-if="followBaseInfo?.tagName">
                  <div
                    v-for="(tag, index) in followBaseInfo?.tagName?.split(',')"
                    :key="index" border="1px solid #ff9b54 rd-11px" text="#FF9B54" px-10px
                    py-4px
                  >
                    {{ tag }}
                  </div>
                </template>
                <template v-else>
                  -
                </template>
              </div>
            </InfoItem>
          </n-gi>
        </n-grid>
      </section>
      <!-- 体格检查 -->
      <section class="pt-6px">
        <SectionTitle class="my-14px">
          体格检查<span class="ml-10px text-#666">(更新时间：{{ patientInfo?.phyUpdateTime ? dayjs(patientInfo?.phyUpdateTime).format('YYYY-MM-DD HH:mm:ss') : '-' }}，数据来源：{{ formatNullValueToShortBar(patientInfo?.phySource) }})</span>
        </SectionTitle>
        <n-grid x-gap="14" y-gap="14" :cols="2" class="bg-#F8F8F8 p-14px text-#333">
          <n-gi>
            <InfoItem label="血型：" :label-width="LABEL_WIDTH.physique[0]">
              <span>{{ formatNullValueToShortBar(patientInfo?.bloodType) }}型</span>
            </InfoItem>
          </n-gi>
          <n-gi>
            <InfoItem label="体重：" :label-width="LABEL_WIDTH.physique[1]">
              <span>{{ formatNullValueToShortBar(patientInfo?.weight) }}kg</span>
            </InfoItem>
          </n-gi>
          <n-gi>
            <InfoItem label="血压：" :label-width="LABEL_WIDTH.physique[0]">
              <span>{{ formatNullValueToShortBar(patientInfo?.bloodPressure) }}mmHg</span>
            </InfoItem>
          </n-gi>
          <n-gi>
            <InfoItem label="脉搏：" :label-width="LABEL_WIDTH.physique[1]">
              <span>{{ formatNullValueToShortBar(patientInfo?.pulse) }}次/分</span>
            </InfoItem>
          </n-gi>
          <n-gi>
            <InfoItem label="身高：" :label-width="LABEL_WIDTH.physique[0]">
              <span>{{ formatNullValueToShortBar(patientInfo?.height) }}cm</span>
            </InfoItem>
          </n-gi>
          <n-gi>
            <InfoItem label="BMI：" :label-width="LABEL_WIDTH.physique[1]">
              <span>{{ formatNullValueToShortBar(patientInfo?.bmi) }}kg/m²</span>
            </InfoItem>
          </n-gi>
          <n-gi>
            <InfoItem label="心率：" :label-width="LABEL_WIDTH.physique[0]">
              <span>{{ formatNullValueToShortBar(patientInfo?.heartRate) }}次/分</span>
            </InfoItem>
          </n-gi>
          <n-gi>
            <InfoItem label="呼吸率：" :label-width="LABEL_WIDTH.physique[1]">
              <span>{{ formatNullValueToShortBar(patientInfo?.respiratoryRate) }}次/分</span>
            </InfoItem>
          </n-gi>
        </n-grid>
      </section>
      <!-- 就诊信息 -->
      <section class="pt-6px">
        <SectionTitle class="my-14px">
          就诊信息<span class="ml-10px text-#666">(展示近半年最新数据，数据来源：{{ formatNullValueToShortBar(medicalHistory?.dataSource) }})</span>
        </SectionTitle>
        <div
          class="bg-#F8F8F8 p-14px leading-tight text-#333"
        >
          <InfoItem label="主诉：" :label-width="LABEL_WIDTH.visiting[0]" :value="medicalHistory?.chiefComplaint" class="mb-14px" align-items="start" />
          <InfoItem label="现病史：" :label-width="LABEL_WIDTH.visiting[0]" :value="medicalHistory?.presentHistory" class="mb-14px" align-items="start" />
          <InfoItem label="既往史：" :label-width="LABEL_WIDTH.visiting[0]" :value="medicalHistory?.pastHistory" class="mb-14px" align-items="start" />
          <InfoItem label="个人史：" :label-width="LABEL_WIDTH.visiting[0]" :value="medicalHistory?.personalHistory" class="mb-14px" align-items="start" />
          <InfoItem label="婚育史：" :label-width="LABEL_WIDTH.visiting[0]" :value="medicalHistory?.maritalHistory" class="mb-14px" align-items="start" />
          <InfoItem label="家族史：" :label-width="LABEL_WIDTH.visiting[0]" :value="medicalHistory?.familyHistory" class="mb-14px" align-items="start" />
        </div>
      </section>
    </div>
  </div>
</template>

<style scoped lang="scss">
.planStatus {
    display:flex;
    align-items:center;
    &::before {
        display: block;
        content: '';
        width: 6px;
        height: 6px;
        border-radius: 50%;
        margin-right: 6px;
    }
}
</style>
