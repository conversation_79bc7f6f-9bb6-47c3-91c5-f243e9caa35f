<script lang='ts' setup>
import InfoItem from './InfoItem.vue'
import { SectionTitle } from '@/components/Title'
import type { InPatientRecord } from '@/api/patient'
import { getInPatientRecordApi } from '@/api/patient'

const route = useRoute()
const patientId = route.query?.patientId as string
const recordId = route.query?.recordId as string

const medicalHistory = ref<InPatientRecord>({
  auxiliaryExam: '',
  bedNum: '',
  birthHistory: '',
  birthday: '',
  chiefComplaint: '',
  departmentId: '',
  departmentName: '',
  doctorId: '',
  doctorName: '',
  familyHistory: '',
  feedingHistory: '',
  homeAddr: '',
  id: '',
  liveAddr: '',
  maritalHistory: '',
  marriageName: '',
  menstrualHistory: '',
  narrator: '',
  nationName: '',
  pastHistory: '',
  patientId: '',
  patientName: '',
  patientRecordNo: '',
  personalHistory: '',
  physicalExam: '',
  presentHistory: '',
  professionName: '',
  recordId: '',
  sexName: '',
  slmcNo: '',
  specialExam: '',
  treatDate: '',
  wardName: '',
})

async function getInPatientRecord() {
  const res = await getInPatientRecordApi<InPatientRecord>({ patientId, recordId })

  if (res.data)
    medicalHistory.value = res.data
}

function init() {
  getInPatientRecord()
}
onMounted(() => {
  init()
})
// 标签宽度
const LABEL_WIDTH = {
  // 病历
  medical: ['70', '70', '70'],
  // 诊断
  diagnose: ['70', '77'],

}
</script>

<template>
  <div class="">
    <section class="mb-20px">
      <SectionTitle class="mb-10px">
        病历
      </SectionTitle>
      <div class="bg-#F8F8F8 p-14px">
        <n-grid :cols="3" :x-gap="14" :y-gap="14">
          <n-gi>
            <InfoItem label="病人姓名" :label-width="LABEL_WIDTH.medical[0]" :value="medicalHistory.patientName" />
          </n-gi>
          <n-gi>
            <InfoItem label="病区" :label-width="LABEL_WIDTH.medical[1]" :value="medicalHistory.wardName" />
          </n-gi>
          <n-gi>
            <InfoItem label="床号" :label-width="LABEL_WIDTH.medical[2]" :value="medicalHistory.bedNum" />
          </n-gi>
          <n-gi>
            <InfoItem
              label="病历号" :label-width="LABEL_WIDTH.medical[0]" :value="medicalHistory.patientRecordNo"
            />
          </n-gi>
          <n-gi>
            <InfoItem label="出生日期" :label-width="LABEL_WIDTH.medical[1]" :value="medicalHistory.birthday" />
          </n-gi>
          <n-gi>
            <InfoItem label="性别" :label-width="LABEL_WIDTH.medical[2]" :value="medicalHistory.sexName" />
          </n-gi>
          <n-gi>
            <InfoItem label="供史者" :label-width="LABEL_WIDTH.medical[0]" :value="medicalHistory.narrator" />
          </n-gi>
          <n-gi>
            <InfoItem label="入院日期" :label-width="LABEL_WIDTH.medical[1]" :value="medicalHistory.treatDate" />
          </n-gi>
          <n-gi>
            <InfoItem label="职业" :label-width="LABEL_WIDTH.medical[2]" :value="medicalHistory.professionName" />
          </n-gi>
          <n-gi>
            <InfoItem label="出生地" :label-width="LABEL_WIDTH.medical[0]" :value="medicalHistory.homeAddr" />
          </n-gi>
          <n-gi>
            <InfoItem label="民族" :label-width="LABEL_WIDTH.medical[1]" :value="medicalHistory.nationName" />
          </n-gi>
          <n-gi>
            <InfoItem label="婚姻" :label-width="LABEL_WIDTH.medical[2]" :value="medicalHistory.marriageName" />
          </n-gi>
          <n-gi span="3">
            <InfoItem label="主诉" :label-width="LABEL_WIDTH.medical[0]" class="leading-tight" :value="medicalHistory.chiefComplaint" align-items="start" />
          </n-gi>
          <n-gi span="3">
            <InfoItem label="现病史" :label-width="LABEL_WIDTH.medical[1]" class="leading-tight" :value="medicalHistory.presentHistory" align-items="start" />
          </n-gi>
          <n-gi span="3">
            <InfoItem label="既往史" :label-width="LABEL_WIDTH.medical[2]" class="leading-tight" :value="medicalHistory.pastHistory" align-items="start" />
          </n-gi>
          <n-gi span="3">
            <InfoItem label="个人史" :label-width="LABEL_WIDTH.medical[0]" class="leading-tight" :value="medicalHistory.personalHistory" align-items="start" />
          </n-gi>
          <n-gi span="3">
            <InfoItem label="婚育史" :label-width="LABEL_WIDTH.medical[1]" class="leading-tight" :value="medicalHistory.maritalHistory" align-items="start" />
          </n-gi>
          <n-gi span="3">
            <InfoItem label="家族史" :label-width="LABEL_WIDTH.medical[2]" class="leading-tight" :value="medicalHistory.familyHistory" align-items="start" />
          </n-gi>
        </n-grid>
      </div>
    </section>
    <section>
      <SectionTitle class="mb-10px">
        体格检查
      </SectionTitle>
      <div class="bg-#F8F8F8 p-14px">
        <!-- TODO:体格检查结构化 -->
        {{ medicalHistory.physicalExam }}
      </div>
    </section>
  </div>
</template>

<style scoped lang="scss">

</style>
