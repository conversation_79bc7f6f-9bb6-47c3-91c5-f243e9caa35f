<script lang='ts' setup>
import InfoItem from './InfoItem.vue'
import type { InTreatDiagnoseDTOList, OutHospitalRecord } from '@/api/patient'
import { getOutHospitalRecordApi } from '@/api/patient'

interface DischargeMedication {
  id: string
  col0: string
  drugName: string
  qty: string
  'dose-doseUnit/supplyName': string
  remark: string
  frequencyName: string

}

const route = useRoute()
const patientId = route.query?.patientId as string
const recordId = route.query?.recordId as string

const medicalHistory = ref<OutHospitalRecord>({
  bedNum: '',
  birthday: '',
  departmentId: '',
  departmentName: '',
  dischargeBloodPressure: '',
  dischargeBodyWeight: '',
  dischargeBreathe: '',
  dischargeMedication: '',
  dischargePulse: '',
  dischargeStatus: '',
  dischargeTemperature: '',
  dischargeTime: '',
  dischargeWhere: '',
  doctorId: '',
  doctorName: '',
  hospitalCourse: '',
  hospitalizedCase: '',
  hospitalizedCause: '',
  hospitalizedDays: '',
  id: '',
  inHospitalTime: '',
  inTreatDiagnoseDTOList: [],
  outTreatDiagnoseDTOList: [],
  patientId: '',
  patientName: '',
  patientRecordNo: '',
  recordId: '',
  sexName: '',
  slmcNo: '',
  transferDate: '',
  transferDoctor: '',
  transferInstitutions: '',
  transferPurpose: '',
  wardName: '',
})
async function getOutPatientRecord() {
  const res = await getOutHospitalRecordApi<OutHospitalRecord>({ patientId, recordId })

  if (res.data) {
    medicalHistory.value = res.data
    formatDischargeMedication(res.data?.dischargeMedication)
  }
}
const dischargeMedication = ref<DischargeMedication[]>([])

function formatStringToJson(value: string) {
  try {
    if (value)
      return JSON.parse(value)
  }
  catch (error) {
    console.log(error)
    return null
  }
}
// 格式化出院带药
function formatDischargeMedication(value: string) {
  const dischargeMedicationArr = formatStringToJson(value)
  dischargeMedication.value = dischargeMedicationArr ?? []
}
// 格式化入院诊断
function formatDiagnoseDTOList(diagnose: InTreatDiagnoseDTOList[]) {
  if (!diagnose?.length)
    return '-'

  const diagnoseSureType: Record<string, string> = {
    1: '待查',
    2: '疑似',
    3: '确诊',
  }

  const sortDiagnose = diagnose.sort((a, b) => a.diagnoseDetailSn - b.diagnoseDetailSn)
  const stringDiagnose = sortDiagnose.map((item) => {
    const stringItem = `${item.diagnoseDetailSn}、${diagnoseSureType[item.diagnosisType]} ${item.diagnoseName} ${item.diagnoseRemark}`
    return stringItem
  })
  return stringDiagnose.join(';')
}
function createColumns() {
  return [
    {
      title: '序号',
      render(_: DischargeMedication, index: number) {
        return index + 1
      },

    },
    {
      title: '药品名称',
      key: 'drugName',
      render(row: DischargeMedication) {
        const { drugName = '' } = row
        return drugName || '-'
      },
    },
    {
      title: '数量',
      key: 'qty',
      render(row: DischargeMedication) {
        const { qty = '' } = row
        return qty || '-'
      },
    },
    {
      title: '单次量/用法',
      key: 'dose-doseUnit/supplyName',
      render(row: DischargeMedication) {
        return row['dose-doseUnit/supplyName'] || '-'
      },
    },
    {
      title: '嘱托',
      key: 'frequencyName',
      render(row: DischargeMedication) {
        const { frequencyName = '' } = row
        return frequencyName || '-'
      },
    },
    {
      title: '备注',
      key: 'remark',
      render(row: DischargeMedication) {
        const { remark = '' } = row
        return remark || '-'
      },
    },
  ]
}
const medicationColumns = createColumns()

function init() {
  getOutPatientRecord()
}
onMounted(() => {
  init()
})
const labelWidth = '98'
</script>

<template>
  <div>
    <n-grid :cols="3" :x-gap="14" :y-gap="14">
      <n-gi>
        <InfoItem label="病人姓名" :label-width="labelWidth" :value="medicalHistory.patientName" />
      </n-gi>
      <n-gi>
        <InfoItem label="性别" :label-width="labelWidth" :value="medicalHistory.sexName" />
      </n-gi>
      <n-gi>
        <InfoItem label="出生年月" :label-width="labelWidth" :value="medicalHistory.birthday" />
      </n-gi>
      <n-gi>
        <InfoItem label="所属病区" :label-width="labelWidth" :value="medicalHistory.wardName" />
      </n-gi>
      <n-gi>
        <InfoItem label="床号" :label-width="labelWidth" :value="medicalHistory.bedNum" />
      </n-gi>
      <n-gi>
        <InfoItem label="病历号" :label-width="labelWidth" :value="medicalHistory.patientRecordNo" />
      </n-gi>
      <n-gi>
        <InfoItem label="主诊医生" :label-width="labelWidth" :value="medicalHistory.doctorName" />
      </n-gi>
      <n-gi>
        <InfoItem label="入院时间" :label-width="labelWidth" :value="medicalHistory.inHospitalTime" />
      </n-gi>
      <n-gi>
        <InfoItem label="出院时间" :label-width="labelWidth" :value="medicalHistory.dischargeTime" />
      </n-gi>
      <n-gi>
        <InfoItem label="住院天数" :label-width="labelWidth" :value="medicalHistory.hospitalizedDays" />
      </n-gi>
      <n-gi :span="3">
        <n-divider dashed margin="0px 0px 0px 0px" />
      </n-gi>
      <n-gi :span="3">
        <InfoItem label="入院原因" :label-width="labelWidth" :value="medicalHistory.hospitalizedCause" align-items="start" />
      </n-gi>
      <n-gi :span="3">
        <InfoItem label="入院诊断" :label-width="labelWidth" align-items="start">
          <span>      {{ formatDiagnoseDTOList(medicalHistory.inTreatDiagnoseDTOList) }}</span>
        </InfoItem>
      </n-gi>
      <n-gi :span="3">
        <InfoItem label="出院诊断" :label-width="labelWidth" align-items="start">
          <span>  {{ formatDiagnoseDTOList(medicalHistory.outTreatDiagnoseDTOList) }}</span>
        </InfoItem>
      </n-gi>
      <n-gi :span="3">
        <InfoItem label="入院情况" class="leading-tight" :label-width="labelWidth" align-items="start" :value="medicalHistory.hospitalizedCase" />
      </n-gi>
      <n-gi span="3">
        <InfoItem label="住院治疗经过" class="leading-tight" :label-width="labelWidth" :value="medicalHistory.hospitalCourse" align-items="start" />
      </n-gi>
      <n-gi span="3">
        <InfoItem label="出院情况" class="leading-tight" :label-width="labelWidth" align-items="start" :value="medicalHistory.dischargeStatus" />
      </n-gi>
      <n-gi span="3">
        <InfoItem label="出院去向" :label-width="labelWidth" align-items="start" :value="medicalHistory.dischargeWhere" />
      </n-gi>
      <n-gi span="3">
        <InfoItem label="出院带药" :label-width="labelWidth" align-items="start">
          <div>
            <n-data-table
              :bordered="false"
              :columns="medicationColumns"
              size="small"
              :data="dischargeMedication"
              :row-key="(row:DischargeMedication) => row.id"
              striped
            />
          </div>
        </InfoItem>
      </n-gi>
      <n-gi span="3">
        <InfoItem label="医生签名" :label-width="labelWidth" align-items="start" :value="medicalHistory.doctorName" />
      </n-gi>
      <n-gi span="3">
        <InfoItem label="记录日期" :label-width="labelWidth" align-items="start" :value="medicalHistory.dischargeTime" />
      </n-gi>
    </n-grid>
  </div>
</template>

<style scoped lang="scss">

</style>
