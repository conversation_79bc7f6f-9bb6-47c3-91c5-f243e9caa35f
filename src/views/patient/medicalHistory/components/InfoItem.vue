<script lang='ts' setup>
import { computed, useSlots } from 'vue'
import { formatNullValueToShortBar } from '@/utils'

const props = withDefaults(defineProps<Props>(), {
  label: '',
  value: '',
  labelWidth: 'auto',
  alignItems: 'center',
})
// 判断<slot/>是否有传值
const slotDefault = !!useSlots().default
interface Props {
  label: string
  value?: string | number
  labelWidth?: string
  alignItems?: 'center' | 'start'
}

const labelWidth = computed(() => {
  return `${props.labelWidth}px`
})
const alignItems = computed(() => {
  return `${props.alignItems}`
})
</script>

<template>
  <div class="itemBlock h-full flex">
    <div class="labelName inline-block text-right text-#666">
      <span>{{ label }}：</span>
    </div>
    <slot v-if="slotDefault" />
    <div v-else class="flex-1">
      {{ formatNullValueToShortBar(value) }}
    </div>
  </div>
</template>

<style scoped lang="scss">
.itemBlock{
    align-items: v-bind(alignItems);
}
.labelName {
    flex-basis:v-bind(labelWidth);
}
</style>
