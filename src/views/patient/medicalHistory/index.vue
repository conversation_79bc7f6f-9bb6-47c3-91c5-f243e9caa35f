<script lang='ts' setup>
import { useRoute } from 'vue-router'
import Outpatient from './Outpatient.vue'
import Inpatient from './Inpatient.vue'

const route = useRoute()
const type = route.query?.type as string

/**
 * 1:门诊，2住院
 */
const medicalType = ref(type)
</script>

<template>
  <div>
    <Outpatient v-if="medicalType === '1'" />
    <Inpatient v-if="medicalType === '2'" />
  </div>
</template>

<style scoped lang="scss">

</style>
