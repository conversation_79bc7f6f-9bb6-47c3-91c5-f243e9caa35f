<script lang='ts' setup>
import RecordIn from './components/RecordIn.vue'
import RecordOut from './components/RecordOut.vue'
import type { BreadLit } from '@/layouts/common'
import { Breadcrumb } from '@/layouts/common'

const route = useRoute()

const type = ref('In')

const renderBread = computed(() => {
  const patientId = route.query?.patientId as string
  const slmcNo = route.query?.slmcNo as string
  const patientName = route.query?.patientName as string
  const primaryIndex = route.query?.primaryIndex as string

  const list: BreadLit[] = [
    { title: '患者数据库', link: '/patient', key: 'patient' },
    { title: '患者列表', link: '/patient/list', key: 'patient_list' },
    { title: `${patientName}档案信息`, link: `/patient/file?patientId=${patientId}&patientName=${patientName}&primaryIndex=${primaryIndex}&slmcNo=${slmcNo}&tabName=MedicalRecord`, key: 'patient_file' },
    { title: '病例文书', link: null, key: 'patient_medicalHistory' },
  ]
  return list
})
</script>

<template>
  <div>
    <Breadcrumb :bread-list="renderBread" />
    <PageCard breadcrumb>
      <PageTitle class="mb-2px">
        病例文书
      </PageTitle>
      <n-tabs v-model:value="type" animated>
        <n-tab-pane name="In" tab="入院记录">
          <RecordIn />
        </n-tab-pane>
        <n-tab-pane name="Out" tab="出院记录">
          <RecordOut />
        </n-tab-pane>
      </n-tabs>
    </PageCard>
  </div>
</template>

<style scoped lang="scss">

</style>
