<script lang='ts' setup>
import { useRoute } from 'vue-router'
import dayjs from 'dayjs'
import InfoItem from './components/InfoItem.vue'
import { SectionTitle } from '@/components/Title'
import { getOutPatientRecordApi } from '@/api/patient'
import type { OutPatientRecord } from '@/api/patient'
import { formatNullValueToShortBar } from '@/utils'
import type { BreadLit } from '@/layouts/common'
import { Breadcrumb } from '@/layouts/common'

const route = useRoute()

const patientId = route.query?.patientId as string
const recordId = route.query?.recordId as string

const infectionType: Record<number, string> = {
  0: '否',
  1: '是',
}

const medicalHistory = ref<OutPatientRecord>({
  age: 0,
  ageUnit: '',
  attackDate: '',
  auxiliaryExam: '',
  birthday: '',
  chiefComplaint: '',
  chineseMedicalFourDiagnostic: '',
  company: '',
  contactsName: '',
  contactsTel: '',
  countryCode: '',
  countryName: '',
  departmentId: '',
  departmentName: '',
  doctorGroupId: '',
  doctorGroupName: '',
  doctorId: '',
  doctorName: '',
  familyHistory: '',
  goType: '',
  homeAddr: '',
  hospitalCode: '',
  hospitalName: '',
  id: '',
  liveAddr: '',
  maritalHistory: '',
  menstrualHistory: '',
  nation: '',
  nationCode: '',
  pastHistory: '',
  patientId: '',
  patientIdCardNum: '',
  patientName: '',
  patientNatureId: '',
  patientNatureName: '',
  patientTypeId: '',
  patientTypeName: '',
  personalHistory: '',
  phone: '',
  physicalExam: '',
  presentHistory: '',
  recordId: '',
  resolution: '',
  sex: '',
  slmcNo: '',
  specialExam: '',
  treatBeginDate: '',
  treatDate: '',
  treatDiagnoseDTOList: [],
  treatEndDate: '',
  treatTimes: 0,
  visitFlag: 0,
  infection: null,
})

/** 获取门诊病历 */
async function getOutPatientRecord() {
  const params = {
    patientId,
    recordId,
  }
  const res = await getOutPatientRecordApi<OutPatientRecord>(params)
  if (res?.data)
    medicalHistory.value = { ...res.data }
}
/** 初始化函数 */
function init() {
  getOutPatientRecord()
}
onMounted(() => {
  init()
})

const renderBread = computed(() => {
  const patientId = route.query?.patientId as string
  const slmcNo = route.query?.slmcNo as string
  const patientName = route.query?.patientName as string
  const primaryIndex = route.query?.primaryIndex as string

  const list: BreadLit[] = [
    { title: '患者数据库', link: '/patient', key: 'patient' },
    { title: '患者列表', link: '/patient/list', key: 'patient_list' },
    { title: `${patientName}档案信息`, link: `/patient/file?patientId=${patientId}&patientName=${patientName}&primaryIndex=${primaryIndex}&slmcNo=${slmcNo}&tabName=MedicalRecord`, key: 'patient_file' },
    { title: '病例文书', link: null, key: 'patient_medicalHistory' },
  ]
  return list
})
// 标签宽度
const LABEL_WIDTH = {
  // 病历
  medical: ['70', '90'],
  // 诊断
  diagnose: ['70', '77'],

}
</script>

<template>
  <div>
    <Breadcrumb :bread-list="renderBread" />
    <PageCard breadcrumb>
      <PageTitle class="mb-14px" border>
        病例文书
      </PageTitle>
      <section>
        <SectionTitle class="mb-10px">
          病历
        </SectionTitle>
        <div class="bg-#F8F8F8 p-14px">
          <n-grid :cols="3" :x-gap="14" :y-gap="14">
            <n-gi>
              <InfoItem label="发病日期" :label-width="LABEL_WIDTH.medical[0]">
                <span>{{ formatNullValueToShortBar(dayjs(medicalHistory?.attackDate).format('YYYY-MM-DD HH:mm')) }}</span>
              </InfoItem>
            </n-gi>
            <n-gi>
              <InfoItem label="是否传染病" :label-width="LABEL_WIDTH.medical[1]">
                <span>
                  {{ `${medicalHistory?.infection}` ? infectionType[medicalHistory?.infection] : '-' }}
                </span>
              </InfoItem>
            </n-gi>
            <n-gi>
              <InfoItem label="就诊去向" :label-width="LABEL_WIDTH.medical[0]" :value="medicalHistory.goType" />
            </n-gi>
            <n-gi span="3">
              <InfoItem label="主诉" :label-width="LABEL_WIDTH.medical[0]" class="leading-tight" :value="medicalHistory.chiefComplaint" />
            </n-gi>
            <n-gi span="3">
              <InfoItem label="现病史" :label-width="LABEL_WIDTH.medical[0]" class="leading-tight" :value="medicalHistory.presentHistory" />
            </n-gi>
            <n-gi span="3">
              <InfoItem label="既往史" :label-width="LABEL_WIDTH.medical[0]" class="leading-tight" :value="medicalHistory.pastHistory" />
            </n-gi>
            <n-gi span="3">
              <InfoItem label="个人史" :label-width="LABEL_WIDTH.medical[0]" class="leading-tight" :value="medicalHistory.personalHistory" />
            </n-gi>
            <n-gi span="3">
              <InfoItem label="婚育史" :label-width="LABEL_WIDTH.medical[0]" class="leading-tight" :value="medicalHistory.maritalHistory" />
            </n-gi>
            <n-gi span="3">
              <InfoItem label="家族史" :label-width="LABEL_WIDTH.medical[0]" class="leading-tight" :value="medicalHistory.familyHistory" />
            </n-gi>
          </n-grid>
        </div>
      </section>
      <section class="mt-20px">
        <SectionTitle class="mb-10px">
          体格检查
        </SectionTitle>
        <div class="bg-#F8F8F8 p-14px">
          {{ formatNullValueToShortBar(medicalHistory.physicalExam) }}
        </div>
      </section>
      <section class="mt-20px">
        <SectionTitle class="mb-10px">
          专科评估
        </SectionTitle>
        <div class="bg-#F8F8F8 p-14px">
          {{ formatNullValueToShortBar(medicalHistory.specialExam) }}
        </div>
      </section>
      <section class="mt-20px">
        <SectionTitle class="mb-10px">
          辅助检查
        </SectionTitle>
        <div class="bg-#F8F8F8 p-14px">
          {{ formatNullValueToShortBar(medicalHistory.auxiliaryExam) }}
        </div>
      </section>
      <section class="mt-20px">
        <SectionTitle class="mb-10px">
          处理意见
        </SectionTitle>
        <div class="bg-#F8F8F8 p-14px">
          {{ formatNullValueToShortBar(medicalHistory.resolution) }}
        </div>
      </section>
      <section class="mt-20px">
        <SectionTitle class="mb-10px">
          诊断
        </SectionTitle>
        <div v-for="(treatDiagnose, index) in medicalHistory.treatDiagnoseDTOList" :key="index" class="bg-#F8F8F8 p-14px">
          <n-grid :cols="2" :x-gap="14" :y-gap="14">
            <n-gi>
              <InfoItem label="诊断名称" :label-width="LABEL_WIDTH.diagnose[0]" :value="treatDiagnose.diagnoseName" />
            </n-gi>
            <n-gi>
              <InfoItem label="诊断编码" :label-width="LABEL_WIDTH.diagnose[1]" :value="treatDiagnose.diagnoseIcd10" />
            </n-gi>
            <n-gi>
              <InfoItem label="诊断分期" :label-width="LABEL_WIDTH.diagnose[0]" :value="treatDiagnose.diagnoseStage" />
            </n-gi>
            <n-gi>
              <InfoItem label="诊断说明" :label-width="LABEL_WIDTH.diagnose[1]" :value="treatDiagnose.diagnoseRemark" />
            </n-gi>
            <n-gi>
              <InfoItem label="诊断结果" :label-width="LABEL_WIDTH.diagnose[0]" :value="treatDiagnose.diagnoseType" />
            </n-gi>
            <n-gi>
              <InfoItem label="初诊/复诊" :label-width="LABEL_WIDTH.diagnose[1]">
                <span v-if="medicalHistory?.visitFlag">  {{ medicalHistory?.visitFlag === 1 ? '初诊' : '复诊' }}</span>
                <span v-else> - </span>
              </InfoItem>
            </n-gi>
          </n-grid>
        </div>
      </section>
    </PageCard>
  </div>
</template>

<style scoped lang="scss">

</style>
