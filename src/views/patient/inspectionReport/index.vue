<script lang='ts' setup>
import dayjs from 'dayjs'
import { useWindowSize } from '@vueuse/core'
import { computed } from 'vue'
import type { AssayDetail, AssayRecord, AssayResult, VisitRecordParams } from '@/api/patient'
import { getVisitAssayDetailApi, getVisitAssayListApi } from '@/api/patient'
import { BasicTable } from '@/components/Table'
import type { BreadLit } from '@/layouts/common'
import { Breadcrumb } from '@/layouts/common'

const route = useRoute()
const patientId = route.query?.patientId as string
const recordId = route.query?.recordId as string

function createColumns() {
  return [
    {
      title: '序号',
      width: 80,
      render(_: AssayRecord, index: number) {
        return index + 1
      },

    },
    {
      title: '项目名称',
      key: 'projectName',
      width: 200,
    },
    {
      title: '申请时间',
      key: 'requestTime',
      width: 150,
      render(row: AssayRecord) {
        const { requestTime } = row
        return requestTime ? dayjs(requestTime).format('YYYY-MM-DD') : '-'
      },
    },
    {
      title: '标本',
      key: 'sampleName',
      width: 200,
    },
    {
      title: '报告时间',
      key: 'reportTime',
      width: 150,
      render(row: AssayRecord) {
        const { reportTime } = row
        return reportTime ? dayjs(reportTime).format('YYYY-MM-DD') : '-'
      },
    },
    {
      title: '报告医生',
      key: 'reporter',
      width: 120,
    },
  ]
}

const tableRef = ref<InstanceType<typeof BasicTable> | null>(null)
const columns = createColumns()
/** 当前点击行的数据 */
const currentRecord = ref<AssayRecord>({
  auditTime: '',
  auditor: '',
  departmentId: '',
  departmentName: '',
  id: '',
  infantFlag: '',
  patientId: '',
  patientName: '',
  patientType: '',
  projectName: '',
  recordId: '',
  reportTime: '',
  reporter: '',
  requestTime: '',
  sampleName: '',
  sampler: '',
  samplingTime: '',
  slmcNo: '',
  testPurpose: '',
  testTime: '',
  testType: '',
  tester: '',
  wardId: '',
  wardName: '',
})
/** 当前点击行id */
const currentRowId = ref('')

// 分页器
const paginationReactive = reactive({
  page: 1,
  pageSize: 10,
  pageSizes: [10, 20, 50, 100],
})
const tableParams = reactive<VisitRecordParams>({
  size: 10,
  start: 1,
  patientId,
  recordId,
})
// 加载表格数据
async function loadDataTable(res: { size: number; start: number }) {
  const response = await getVisitAssayListApi<AssayResult>({ ...tableParams, ...res })
  if (response.data?.records) {
    currentRecord.value = response.data?.records[0]
    currentRowId.value = response.data?.records[0].id
    tableDetailFetch()
  }

  return response
}
/**
 * 自定义行类名
 * @param row 行数据
 */
function rowClassName(row: AssayRecord) {
  if (currentRowId.value === row.id)
    return 'clickActive'

  return ''
}
/**
 * 自定义行数据
 * @param row 行数据
 */
function rowProps(row: AssayRecord) {
  return {
    style: 'cursor: pointer;',
    onClick: () => {
      currentRowId.value = row.id
      currentRecord.value = { ...row }
      tableDetailFetch()
    },
  }
}
// 详情信息列
function createColumnsDetail() {
  return [
    {
      title: '序号',
      width: 80,
      render(_: AssayDetail, index: number) {
        return index + 1
      },
    },
    {
      title: '项目名称',
      key: 'chineseName',
    },
    {
      title: '结果',
      key: 'result',
      width: 150,
      render(row: AssayDetail) {
        const { result, hint } = row
        return `${result}${hint || ''}`
      },
    },
    {
      title: '单位',
      key: 'unit',
      width: 150,
    },
    {
      title: '参考范围',
      key: 'refValue',
      width: 150,
    },

  ]
}
const tableDetailRef = ref<InstanceType<typeof BasicTable> | null>(null)
const columnsDetail = createColumnsDetail()
// 加载检验详情
async function loadDataTableDetail() {
  return await getVisitAssayDetailApi<AssayDetail>(currentRowId.value)
}
/** 获取表格详情数据 */
function tableDetailFetch() {
  tableDetailRef.value?.fetch({ id: currentRowId.value, listField: 'results' })
}

const { height } = useWindowSize()
/** 表格最大高度 */
const tableMaxHeight = computed(() => {
  const maxHeight = {
    record: (unref(height) - 50 - 28) / 2 - 110,
    detail: (unref(height) - 50 - 28) / 2 - 110,
  }
  return maxHeight
})
const renderBread = computed(() => {
  const patientId = route.query?.patientId as string
  const slmcNo = route.query?.slmcNo as string
  const patientName = route.query?.patientName as string
  const primaryIndex = route.query?.primaryIndex as string

  const list: BreadLit[] = [
    { title: '患者数据库', link: '/patient', key: 'patient' },
    { title: '患者列表', link: '/patient/list', key: 'patient_list' },
    { title: `${patientName}档案信息`, link: `/patient/file?patientId=${patientId}&patientName=${patientName}&primaryIndex=${primaryIndex}&slmcNo=${slmcNo}&tabName=MedicalRecord`, key: 'patient_file' },
    { title: '检验报告', link: null, key: 'patient_inspection' },
  ]
  return list
})
</script>

<template>
  <div>
    <Breadcrumb :bread-list="renderBread" />
    <PageCard breadcrumb>
      <PageTitle class="mb-14px">
        检验报告
      </PageTitle>
      <BasicTable
        ref="tableRef"
        class="table"

        :columns="columns"
        :request="loadDataTable"
        :row-key="(row:AssayRecord) => row.id"
        :pagination="paginationReactive"
        :row-props="rowProps"
        :row-class-name="rowClassName"
        striped
        :max-height="tableMaxHeight.record"
        :scroll-x="800"
      />
      <SectionTitle class="mb-14px">
        详情信息
      </SectionTitle>
      <BasicTable
        ref="tableDetailRef"

        :columns="columnsDetail"
        :request="loadDataTableDetail"
        :row-key="(row:AssayDetail) => row.id"
        :pagination="false"
        :on-mounted-request="false"
        striped
        :max-height="tableMaxHeight.detail"
        :scroll-x="800"
      />
      <div />
    </PageCard>
  </div>
</template>

<style scoped lang="scss">
.table {
    :deep(.clickActive .n-data-table-td){
        background-color: #FFFBE0 !important;
    }
}
</style>
