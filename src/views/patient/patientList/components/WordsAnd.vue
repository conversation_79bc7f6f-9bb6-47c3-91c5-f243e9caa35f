<script lang='ts' setup>
interface Props {
  /** 条件个数 */
  conditionCount?: number
}

const props = withDefaults(defineProps<Props>(), {
  conditionCount: 0,
})

const lineHeight = computed(() => {
  const height = props.conditionCount <= 2 ? 46 : 46 + (props.conditionCount - 2) * 42

  return `${height}px`
})
</script>

<template>
  <div class="wordsAndBlock">
    <div class="wordsAnd">
      <span class="wordsAndText">且</span>
    </div>
  </div>
</template>

<style scoped lang="scss">
.wordsAndBlock {
    position: absolute;
    left: 0;
    top:50%;
    transform: translateY(-50%);
    margin-left: 14px;
    // margin-right: 14px;

    .wordsAnd {
        display: flex;
        align-items: center;
        justify-content: flex-start;

        .wordsAndText {
            display: inline-block;
            width: 20px;
            height: 20px;
            font-size: 12px;
            line-height: 20px;
            text-align: center;
            color: #fff;
            font-weight: 500;
            background: #06AEA6;
            border-radius: 3px;
            z-index: 1;
        }
        &::after {
            content: '';
            display: block;
            height: v-bind(lineHeight);
            width: 13px;
            margin-left: -10.5px;
            border: 1px solid #06AEA6;
            z-index: 0;
            border-right-color: transparent;
    }
    }
}
</style>
