<script lang='ts' setup>
import { useMessage } from 'wowjoy-vui'
import type { FormInst } from 'wowjoy-vui'
import { useDebounceFn } from '@vueuse/core'
import { BasicModal, useModalInner } from '@/components/Modal'
import { addPlan, nameIsAvailable } from '@/api/followCenter'
import { getDoctorListApi } from '@/api/doctor'
import { useAuthStore } from '@/store'

interface DoctorNameOption {
  doctorName: string
  doctorId: string
}

interface FormValue {
  doctorId: string | null
  doctorName: string
  planTemplateId: string | null
  planTemplateName: string
  planTemplateSort: string | null
  primaryIndex: string[]
}

const props = defineProps({
  currentPrimaryIndex: {
    type: String,
    default: '',
  },
  currentPrimaryId: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['onSuccess', 'register'])
const { userInfo } = useAuthStore()
const organId = userInfo.organId

const message = useMessage()

const formRef = ref<FormInst | null>(null)

const formValue = reactive<FormValue>({
  doctorId: null,
  doctorName: '',
  planTemplateId: null,
  planTemplateName: '',
  planTemplateSort: 'CHB',
  primaryIndex: [],

})

watch(() => props.currentPrimaryIndex, (val) => {
  formValue.primaryIndex = [val]
})

// 医生枚举
const optionsDoctorName = ref<DoctorNameOption[]>([])
// 随访路径
const planAOpsB = ref<any>({})
// 随访计划枚举
const planAOps = [
  { label: '乙肝', value: 'CHB' },
  { label: '脂肪肝', value: 'FLB' },
  { label: '丙肝', value: 'HCV' },
]
const rules = {
  doctorId: {
    required: true,
    trigger: ['blur'],
    message: '内容不能为空',
  },
  planTemplateId: {
    required: true,
    message: '内容不能为空',
    trigger: ['blur'],
  },
  planTemplateSort: {
    required: true,
    message: '内容不能为空',
    trigger: ['blur'],
  },

}
function resetFormValue() {
  formValue.doctorId = null
  formValue.doctorName = ''
  formValue.planTemplateId = null
  formValue.planTemplateName = ''
  formValue.planTemplateSort = 'CHB'
  formValue.primaryIndex = []
}

// 搜索做防抖
const getDoctorName = useDebounceFn(async (name = '') => {
  const res = await getDoctorListApi<DoctorNameOption[]>({ name, organId })
  if (res.data)
    optionsDoctorName.value = res.data
}, 300)
/**
 * 医生姓名
 * @param query 搜索key
 */
function handleSearchDoctorName(query: string) {
  getDoctorName(query)
}
/**
 * 医生名字打开/关闭回调
 * @param show 下拉打开/关闭
 */
function handleUpdateShowDoctorName(show: boolean) {
  if (show)
    getDoctorName('')
}
// 医生名字更新
function handleUpdateValueDoctorName(value: string, options: any) {
  if (value) {
    formValue.doctorId = value
    formValue.doctorName = options?.doctorName
  }
}
// 随访计划跟新
function handleUpdatePlan(value: string, options: any) {
  if (value) {
    formValue.planTemplateId = value
    formValue.planTemplateName = options?.planTemplateName
  }
}
function handleUpdatePlanSort(value: string) {
  if (value)
    formValue.planTemplateId = null
}
// 获取随访路径
async function getNamePlan() {
  const res = await nameIsAvailable({ sortType: '' })
  if (res.data)
    planAOpsB.value = res.data
}

const [register, { closeModal }] = useModalInner()

/**
 * 弹窗打开/关闭的callback
 * @param v true:打开，false:关闭
 */
async function visibleChange(v: boolean) {
  if (!v)
    resetFormValue()
  else
    getNamePlan()
}
/** 纳入随访 */
async function addPlanFn(params: any) {
  try {
    const { data } = await addPlan(params)
    if (data === 'success') {
      message.success('保存成功')
      // 如果继续新增就重置数据
      emit('onSuccess')
      resetFormValue()

      closeModal()
    }
  }
  catch (error) {
    console.log(error)
  }
}

// 保存
function handleConfirm() {
  formRef.value?.validate((errors: any) => {
    if (!errors) {
      const payload = {
        ...formValue,

      }

      addPlanFn(payload)
    }
    else { console.log(errors) }
  })
}

const sortMemo = computed(() => {
  if (formValue.planTemplateSort)
    return planAOpsB.value[formValue.planTemplateSort]?.find((v: any) => v.planTemplateId === formValue.planTemplateId)?.sortMemo

  return null
})

const planTemplateMemo = computed(() => {
  if (formValue.planTemplateId && formValue.planTemplateSort)
    return planAOpsB.value[formValue.planTemplateSort]?.find((v: any) => v.planTemplateId === formValue.planTemplateId)?.planTemplateMemo

  return null
})
</script>

<template>
  <div>
    <BasicModal
      v-bind="$attrs"
      title="纳入随访"
      :min-height="105"
      width="560"
      :footer-offset="110"
      @register="register"
      @ok="handleConfirm"
      @visible-change="visibleChange"
    >
      <n-form
        ref="formRef"
        label-placement="left"
        class="my-24px ml-17px mr-30px"
        require-mark-placement="left"
        :label-width="100"
        :model="formValue"
        :rules="rules"
      >
        <n-grid :cols="2">
          <n-form-item-gi label-style="color: #666" path="doctorId" label="管理医生" :span="2">
            <n-select
              v-model:value="formValue.doctorId"
              filterable placeholder="请选择"
              label-field="doctorName"
              value-field="doctorId"
              :options="optionsDoctorName"
              clearable
              remote
              @search="handleSearchDoctorName"
              @update:show="handleUpdateShowDoctorName"
              @update:value="handleUpdateValueDoctorName"
            />
          </n-form-item-gi>
          <n-form-item-gi label-style="color: #666" path="planTemplateSort" label="随访计划">
            <n-select
              v-model:value="formValue.planTemplateSort"
              placeholder="请输入"
              :options="planAOps"
              class="basis-130px"
              @update:value="handleUpdatePlanSort"
            />
            <div class="mx-10px basis-10px">
              —
            </div>
          </n-form-item-gi>
          <n-form-item-gi label-style="color: #666" path="planTemplateId">
            <n-select
              v-model:value="formValue.planTemplateId"
              placeholder="请输入"
              label-field="planTemplateName"
              value-field="planTemplateId"
              class="basis-260px"
              :options="formValue.planTemplateSort ? planAOpsB[formValue.planTemplateSort] : []"
              @update:value="handleUpdatePlan"
            />
          </n-form-item-gi>
          <n-form-item-gi v-if="sortMemo" label-style="color: #666" label="随访计划介绍" :span="2">
            <div class="w-full bg-#f5f5f5 p-9px" border="1px solid #d1d1d1 rd-3px">
              {{ sortMemo }}
            </div>
          </n-form-item-gi>
          <n-form-item-gi v-if="planTemplateMemo" label-style="color: #666" label="随访路径" :span="2" :show-feedback="false">
            <div class="w-full bg-#f5f5f5 p-9px" border="1px solid #d1d1d1 rd-3px">
              {{ planTemplateMemo }}
            </div>
          </n-form-item-gi>
        </n-grid>
      </n-form>
    </BasicModal>
  </div>
</template>

<style scoped lang="scss">

</style>
