<script lang='ts' setup>
</script>

<template>
  <div class="wordsOrBlock">
    <div class="wordsOr">
      <span class="wordsOrText">或</span>
    </div>
  </div>
</template>

<style scoped lang="scss">
.wordsOrBlock {
    position: relative;
    height: 40px;
    width: 100%;
    display: flex;
    justify-content: center;
    .wordsOr {
        display: flex;
        align-items: center;
        justify-items: center;
    }
    .wordsOrText{
        width: 20px;
        height: 20px;
        font-size: 12px;
        line-height: 20px;
        text-align: center;
        color: #fff;
        font-weight: 500;
        background: #fca415;
        border-radius: 3px;
        z-index: 1;
    }
    .wordsOr::after {
        content: '';
        display: block;
        height: 40px;
        // width: 1px;
        margin-left: -10px;
        z-index: 0;
        border: 1px dashed #fca415;

    }
}
</style>
