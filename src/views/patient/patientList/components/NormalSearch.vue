<script lang='ts' setup>
import { useDebounceFn } from '@vueuse/core'
import { useMessage } from 'wowjoy-vui'
import type { NormalParams } from '@/api/patient'
import { getPatientNameApi } from '@/api/patient'
import { useAuthStore } from '@/store'
import { isNumber } from '@/utils'
import { getSbOrganList } from '~/src/api/organization/organization'

const emit = defineEmits(['onSearch', 'onReset'])

const message = useMessage()

interface Options {
  patientName: {
    label: string
    value: string
  }[]
  sex: {
    label: string
    value: string
  }[]
  searchOrgan: {
    organName: string
    id: string
  }[]
}

const { userInfo } = useAuthStore()
const organId = userInfo.organId
const organList = userInfo.organList
/** 默认值 */
const defaultValue = {
  patientName: '',
  phone: '',
  startAge: null,
  endAge: null,
  sex: '',
  startV1Degree: null,
  endV1Degree: null,
  slmcNo: '',
  searchOrganId: '0',
  organId,
}
/** 搜索参数 */
const model = ref<NormalParams>({
  patientName: '',
  phone: '',
  startAge: null,
  endAge: null,
  sex: '',
  startV1Degree: null,
  endV1Degree: null,
  slmcNo: '',
  searchOrganId: '0',
  organId,
})

const options = ref<Options>({
  patientName: [
    { label: '全部', value: '' },
  ],
  sex: [
    { label: '全部', value: '' },
    { label: '男', value: '男' },
    { label: '女', value: '女' },
    { label: '未知', value: '未知' },
  ],
  searchOrgan: [
    { organName: '全部', id: '0' },
  ],
})
const loading = ref({
  patientName: false,
})
/**
 * 扁平化机构列表
 * @param arr 机构列表
 */
function flatten(arr: Auth.OrganList[]) {
  return arr.reduce((pre, cur) => {
    const { children, ...item } = cur
    return pre.concat([item], Array.isArray(children) ? flatten(children) : [])
  }, [])
}

// 搜索做防抖
const getPatientName = useDebounceFn(async (patientName = '') => {
  loading.value.patientName = true
  const res = await getPatientNameApi<string[]>({ patientName, organId })

  if (res.data) {
    const array = res.data.map((item) => {
      return {
        label: item,
        value: item,
      }
    })
    if (!patientName) {
      const defaultArray = [{ label: '全部', value: '' }]

      options.value.patientName = defaultArray.concat(array)
    }
    else {
      options.value.patientName = array
    }

    loading.value.patientName = false
  }
}, 300)
/**
 * 查找患者姓名
 * @param query 搜索key
 */
function handleSearchPatientName(query: string) {
  getPatientName(query)
}
/**
 * 患者名字打开/关闭回调
 * @param show 下拉打开/关闭
 */
function handleUpdateShowPatientName(show: boolean) {
  if (show)
    getPatientName('')
}
/** 搜索 */
function handleSearchClick() {
  const { startAge, endAge, endV1Degree, startV1Degree } = unref(model)
  if (!isFillInCorrectly(startAge, endAge)) {
    message.warning('请输入正确的年龄范围！')
    return
  }

  if (!isFillInCorrectly(startV1Degree, endV1Degree)) {
    message.warning('请输入正确的V1完成度范围！')
    return
  }

  emit('onSearch', unref(model))
}
/**
 * 判断两个值是否正确填写
 * @param prev 前一个值
 * @param next 后一个值
 */
function isFillInCorrectly(prev: number | null, next: number | null): boolean {
  if ((prev && !isNumber(prev)) || (next && !isNumber(next)))
    return false
  if (prev && prev < 0)
    return false
  if (next && next < 0)
    return false
  if ((prev && next) && (prev > next))
    return false
  return true
}
/** 参数重置 */
function handleResetClick() {
  model.value = { ...defaultValue }
  emit('onReset')
}
/** 初始化参数 */
function init() {
  const defaultArray = [
    {
      organName: '全部',
      id: '0',
    },
  ]

  getSbOrganList().then((res) => {
    const organListFlat = defaultArray.concat(flatten(res.data || []))
    options.value.searchOrgan = organListFlat
  })
}

onMounted(() => {
  init()
})

/// 机构选择
function organSelect(selId) {
  model.value.organId = selId.id
}
</script>

<template>
  <n-form
    label-placement="left"
    :model="model"
    :show-feedback="false"
  >
    <div class="flex flex-wrap gap-14px">
      <n-form-item label="患者姓名" path="patientName" class="basis-246px">
        <n-select
          v-model:value="model.patientName"
          filterable placeholder="请选择"
          :options="options.patientName"
          :loading="loading.patientName"
          clearable
          remote
          style="width: 180px;"
          @search="handleSearchPatientName"
          @update:show="handleUpdateShowPatientName"
        />
      </n-form-item>
      <n-form-item label="手机号" path="phone" class="basis-232px">
        <n-input v-model:value="model.phone" placeholder="请输入" style="width: 180px;" />
      </n-form-item>
      <n-form-item label="年龄" class="basis-268px">
        <n-input-number v-model:value="model.startAge" placeholder="请输入" :show-button="false" :max="999" :min="0" style="min-width:100px">
          <template #suffix>
            岁
          </template>
        </n-input-number>
        <div class="mx-10px">
          —
        </div>
        <n-input-number v-model:value="model.endAge" placeholder="请输入" :show-button="false" :max="999" :min="0" style="min-width:100px">
          <template #suffix>
            岁
          </template>
        </n-input-number>
      </n-form-item>
      <n-form-item label="性别" path="sex" class="basis-218px">
        <n-select v-model:value="model.sex" placeholder="请输入" :options="options.sex" style="width: 180px;" />
      </n-form-item>
      <!-- TIP:暂时不加，后续在加 -->
      <!-- <n-form-item label="机构" path="searchOrganId" class="basis-218px">
        <n-select
          v-model:value="model.searchOrganId" placeholder="请输入"
          label-field="organName"
          value-field="id"
          :options="options.searchOrgan" style="width: 180px;"
        />
      </n-form-item> -->
      <!-- <n-form-item label="机构选择">
        <JgSelectTree :default-value="model.organId" :options="organList" @value-select="organSelect" />
      </n-form-item> -->
      <n-form-item label="V1完成度" class="basis-300px">
        <n-input-number v-model:value="model.startV1Degree" :show-button="false" :max="999" :min="0" placeholder="请输入" style="min-width: 80px;">
          <template #suffix>
            %
          </template>
        </n-input-number>
        <div class="mx-10px">
          —
        </div>
        <n-input-number v-model:value="model.endV1Degree" :show-button="false" :max="999" :min="0" placeholder="请输入" style="min-width: 80px;">
          <template #suffix>
            %
          </template>
        </n-input-number>
      </n-form-item>
      <n-form-item label="SLMC编号" path="slmcNo" class="basis-257px">
        <n-input v-model:value="model.slmcNo" placeholder="请输入" style="width: 180px;" />
      </n-form-item>
      <n-form-item>
        <n-button type="primary" class="mr-10px" @click="handleSearchClick">
          查询
        </n-button>
        <n-button @click="handleResetClick">
          重置
        </n-button>
      </n-form-item>
    </div>
  </n-form>
</template>

<style scoped lang="scss">

</style>
