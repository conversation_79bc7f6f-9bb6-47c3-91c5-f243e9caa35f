<script lang='ts' setup>
import { NButton, useMessage } from 'wowjoy-vui'
import dayjs from 'dayjs'
import NormalSearch from './components/NormalSearch.vue'
import HighSearch from './components/HighSearch.vue'
import FollowModal from './components/FollowModal.vue'
import { useModal } from '@/components/Modal'
import { useAuthStore } from '@/store'
import { usePermission, useRouterPush } from '@/hooks'
import { BasicTable, RowAction } from '@/components/Table'
import type { HighSearchParams, PatientListParams, PatientRecord } from '@/api/patient'
import { getAdvancedSearchPatientListApi, getPatientListApi, getSynchronizeDataTimeApi, synchronizeDataUrlApi } from '@/api/patient'
import { SvgIcon } from '@/components/Icon'
import { formatRate } from '@/utils'
import { Breadcrumb } from '@/layouts/common'
import { iconName } from '@/utils/common'

/** 跳转时携带参数 */
interface JumpParams {
  patientId: string
  patientName: string
  primaryIndex: string
  slmcNo: string
  step?: string
  patientSex?: string
  age?: string
}

const message = useMessage()

const { routerPush } = useRouterPush()
const { hasPermission } = usePermission()

const { userInfo } = useAuthStore()
const organId = userInfo.organId

/** 最新同步时间 */
const updateTime = ref<string | null>(null)
/** 同步按钮loading */
const syncLoading = ref(false)

type SearchActionType = 'normal' | 'high' | 'null'
const searchAction = ref<SearchActionType>('null')
function getSearchActionOption() {
  const haveHigh = hasPermission('lddp:database:patient:adv_search')
  const searchType = [
    { value: 'normal', label: '普通搜索', isShow: true },
    { value: 'high', label: '高级筛选', isShow: haveHigh },
  ].filter(type => type.isShow)
  if (searchType.length)
    searchAction.value = searchType[0].value

  return searchType
}
const searchActionOption = getSearchActionOption()

/**
 * 渲染年龄和性别
 * @param age 年龄
 * @param sexName 性别
 */
function renderAgeAndSexStatus(age: string, ageUnit: string, sexName: string) {
  // const iconName: Record<string, string> = {
  //   男: 'slmc-icon-male_',
  //   女: 'slmc-icon-female_',
  //   未知: 'slmc-icon-unknown',
  // }
  return h('div', { class: 'flex items-center ' }, [
    h('span', { class: 'mr-2px' }, `${age ? `${age}${ageUnit}` : '-'}`),
    h(SvgIcon, {
      localIcon: iconName[sexName],
    }),

  ])
}
/**
 * 跳转患者信息
 * @param patientId 患者id
 * @param patientName 患者姓名
 * @param primaryIndex 患者主索引
 */
function handleGoPatientInfo(params: JumpParams) {
  const { patientId, patientName, primaryIndex, slmcNo, patientSex, age } = params
  const fileModel = [
    { name: 'BaseInfo', isShow: hasPermission('lddp:database:patientDetail:info') },
    { name: 'TreatmentPlan', isShow: hasPermission('lddp:database:patientDetail:treat') },
    { name: 'MedicalRecord', isShow: hasPermission('lddp:database:patientDetail:record') },
    { name: 'IndicatorTrend', isShow: hasPermission('lddp:database:patientDetail:main_target') },
    { name: 'SlmcInterview', isShow: hasPermission('lddp:database:patientDetail:slmc') },
  ]
  const jumpTabName = fileModel.filter(modal => modal.isShow)[0].name
  routerPush({
    name: 'patient_file',
    query: {
      patientId,
      patientName,
      primaryIndex,
      slmcNo,
      tabName: jumpTabName,
      patientSex,
      source: 'patient',
      age,
    },
  })
}
/**
 * 跳转问卷
 * @param primaryIndex 患者主索引
 */
function handleGoFollow(params: JumpParams) {
  const { patientId, patientName, primaryIndex, slmcNo, step, age } = params
  routerPush({
    name: 'follow_questionnaire',
    query: {
      patientId,
      patientName,
      primaryIndex,
      slmcNo,
      fromPage: 'patient',
      phaseTemplateCode: step,
      source: 'patient',
      age,
    },
  })
}
/**
 * 跳转slmc访视
 * @param patientName 患者姓名
 * @param primaryIndex 患者主索引
 */
function handleGoSLMCView(params: JumpParams) {
  const { patientId, patientName, primaryIndex, slmcNo, age } = params
  routerPush({
    name: 'patient_file',
    query: {
      patientName,
      primaryIndex,
      slmcNo,
      patientId,
      tabName: 'SlmcInterview',
      source: 'patient',
      age,
    },
  })
}
/**
 * 跳转患者就诊记录
 * @param patientId 患者id
 * @param patientName 患者姓名
 * @param primaryIndex 患者主索引
 */
function handleGoMedicalRecord(params: JumpParams) {
  const { patientId, patientName, primaryIndex, slmcNo, age } = params
  routerPush({
    name: 'patient_file',
    query: {
      patientName,
      primaryIndex,
      patientId,
      slmcNo,
      tabName: 'MedicalRecord',
      source: 'patient',
      age,
    },
  })
}

function createColumns() {
  return [
    {
      type: 'selection',
      width: 50,
    },
    {
      title: '序号',
      key: 'index',
      width: 70,
      render(_: PatientRecord, index: number) {
        return index + 1
      },
    },
    {

      title: '是否纳入随访',
      key: 'status',
      width: 124,
      render(row: PatientRecord) {
        const { status, primaryIndex, id } = row
        const statusType: Record<number, string> = {
          0: '未纳入',
          1: '已纳入',
        }
        return h('div', {
          class: `followStatus ${status === 1 ? 'bg-primary' : 'bg-#ccc cursor-pointer'}`,
          onClick: () => {
            if (!hasPermission('lddp:database:patient:into_slmc'))
              return
            showFollowModal(status, primaryIndex, id)
          },
        }, `${statusType[status] ?? '-'}`)
      },
    },
    {
      title: 'SLMC编号',
      key: 'slmcNo',
      width: 170,
    },
    {
      title: '患者姓名',
      key: 'patientName',
      width: 120,
      render(row: PatientRecord) {
        const { patientName, id, primaryIndex, slmcNo, sexName, age } = row
        const params = {
          patientName,
          primaryIndex,
          slmcNo,
          patientId: id,
          patientSex: sexName,
          age,
        }
        return h(NButton, {
          text: true,
          class: 'text-#3B8FD9',
          onClick: () => handleGoPatientInfo(params),
        }, `${patientName ?? '-'}`)
      },

    },
    {
      title: '年龄/性别',
      key: 'age/sexName',
      width: 120,
      render(row: PatientRecord) {
        const { age, ageUnit, sexName } = row
        return renderAgeAndSexStatus(age, ageUnit, sexName)
      },
    },
    {
      title: '手机号',
      key: 'phone',
      width: 150,

    },
    {
      title: '加入SLMC日期',
      key: 'createTime',
      width: 150,
      render(row: PatientRecord) {
        const { createTime } = row
        return createTime ? dayjs(createTime).format('YYYY-MM-DD') : '-'
      },

    },
    {
      title: 'V1完整度',
      key: 'v1Visit',
      width: 100,
      align: 'right',
      render(row: PatientRecord) {
        const { v1Visit, primaryIndex, patientName, id, slmcNo, age } = row
        const params = {
          patientName,
          primaryIndex,
          slmcNo,
          patientId: id,
          step: '1',
          age,
        }
        return v1Visit
          ? h(NButton, {
            text: true,
            class: 'text-#3B8FD9',
            disabled: !hasPermission('lddp:database:patient:visit1'),
            onClick: () => handleGoFollow(params),
          }, formatRate(v1Visit))
          : h(NButton, {
            text: true,
            class: 'cursor-not-allowed',
          }, '-')
      },

    },
    {
      title: '患者标签',
      key: 'tagName',
    },
    {
      title: '最新访视',
      key: 'lastVisit',
      width: 120,

      align: 'right',
      render(row: PatientRecord) {
        const { lastVisitRate, lastVisit, primaryIndex, patientName, id, slmcNo, age } = row
        const params = {
          patientName,
          primaryIndex,
          slmcNo,
          patientId: id,
          step: lastVisit,
          age,
        }
        return lastVisit
          ? h('div', {
            class: 'flex items-center gap-3px',
          },
          [
            h('span', {}, `V${lastVisit}`),
            h(NButton, {
              text: true,
              disabled: !hasPermission('lddp:database:patient:new_visit'),
              class: 'relative bottom-1px text-#3B8FD9',
              onClick: () => handleGoFollow(params),
            }, formatRate(lastVisitRate)),
          ],
          )
          : h(NButton, {
            text: true,
            class: 'cursor-not-allowed',
          }, '-')
      },
    },

  ]
}
function createActionColumns() {
  return {
    title: '操作',
    key: 'address',
    width: 200,
    fixed: 'right',
    render(row: PatientRecord) {
      const { primaryIndex, patientName, id, slmcNo, age } = row
      const params = {
        patientName,
        primaryIndex,
        slmcNo,
        patientId: id,
        age,

      }
      return h(RowAction, {
        actions: [
          {
            label: 'SLMC访视',
            onClick: () => handleGoSLMCView(params),
            type: 'primary',
            text: true,
          },
          {
            label: '就诊记录',
            onClick: () => handleGoMedicalRecord(params),
            type: 'primary',
            text: true,
          },

        ],
      })
    },
  }
}

const tableRef = ref<InstanceType<typeof BasicTable> | null>(null)
const columns = createColumns()
const actionColumns = createActionColumns()
// 分页器
const paginationReactive = reactive({
  page: 1,
  pageSize: 10,
  pageSizes: [10, 20, 50, 100],
})

let tableParams = reactive<any>({
  organId,
  //   organId: '1658736868559892482',
  searchOrganId: '0',
  size: 10,
  start: 1,

})
watch(searchAction, () => {
  tableParams = {
    organId,
    //   organId: '1658736868559892482',
    searchOrganId: '0',
    size: 10,
    start: 1,
  }
})
// 加载表格数据
async function loadDataTable(res: { size: number; start: number }) {
  if (searchAction.value === 'normal') {
    if (hasPermission('lddp:database:patient:search'))
      return await getPatientListApi<PatientRecord>({ ...tableParams as PatientListParams, ...res })
  }
  else { return await getAdvancedSearchPatientListApi<PatientRecord>({ ...tableParams as HighSearchParams, ...res }) }
}

// 搜索
function handleSearch(params: any) {
  tableParams = {
    ...tableParams,
    ...params,
  }

  const payload = { ...tableParams, ...params, start: 1 }
  tableRef?.value?.fetch(payload)
}
// 搜索重置
function handelReset() {
  tableParams = {
    endAge: null,
    endV1Degree: null,
    organId,
    //   organId: '1658736868559892482',
    searchOrganId: '0',
    patientName: '',
    phone: '',
    sex: '',
    size: 10,
    slmcNo: '',
    start: 1,
    startAge: null,
    startV1Degree: null,
  }
  tableRef?.value?.fetch({ start: 1, size: 10, organId })
}

// 数据同步
async function handleSyncClick() {
  syncLoading.value = true
  const res = await synchronizeDataUrlApi()
  if (res.data) {
    syncLoading.value = false
    getSynchronizeDataTime()
    message.success('同步成功')
  }
}

// 自定义导出
function customImport() {
  message.info('功能开发中')
}
// 模板导出
function templateImport() {
  message.info('功能开发中')
}

const [registerFollowModal, { openModal: openFollowModal }] = useModal()
const currentPrimaryIndex = ref('')
const currentPrimaryId = ref('')
function showFollowModal(status: number, primaryIndex: string, id: string) {
  if (status === 1)
    return

  openFollowModal()
  currentPrimaryIndex.value = primaryIndex
  currentPrimaryId.value = id
}
// 新增成功回调
function onSuccess() {
  tableRef.value?.reload()
}
/** 获取最新同步时间 */
async function getSynchronizeDataTime() {
  const res = await getSynchronizeDataTimeApi<string | null>()
  if (res.data)
    updateTime.value = res.data
}
/** 初始化函数 */
function init() {
  getSynchronizeDataTime()
}

onMounted(() => {
  init()
})
</script>

<template>
  <div>
    <Breadcrumb route-name="patient_list" />
    <PageCard class="relative pt-35px" breadcrumb padding="35px 14px 14px 14px">
      <n-alert type="info" class="headWidth text-#666">
        温馨提示：每天0点系统自动同步患者数据，您也可以手动同步。
      </n-alert>
      <div class="pt-14px">
        <PageTitle border class="flex">
          <div> 患者列表 <span class="ml-10px text-#666">(最近更新时间：{{ updateTime ?? '-' }})</span></div>
          <template #right>
            <NButton v-permission="'lddp:database:patient:syn'" type="primary" class="syncLoading" text @click="handleSyncClick">
              <template #icon>
                <n-spin type="uni" v-if="syncLoading" size="small" />
                <SvgIcon v-else local-icon="slmc-icon-tongbu" size="16" class="text-primary" />
              </template>
              <span class="pl-10px">数据同步</span>
            </NButton>
          </template>
        </PageTitle>
        <div v-if="searchAction !== 'null'" class="flex items-center">
          <span class="text-#666">搜索方式：</span>
          <n-radio-group v-model:value="searchAction" name="searchType" mode="ghost" class="my-14px">
            <n-space :size="[40, 20]">
              <n-radio
                v-for="action in searchActionOption"
                :key="action.value"
                :width="116"
                :value="action.value"
              >
                {{ action.label }}
              </n-radio>
            </n-space>
          </n-radio-group>
        </div>
        <NormalSearch
          v-if="searchAction === 'normal'"
          @on-search="handleSearch"
          @on-reset="handelReset"
        />
        <HighSearch
          v-if="searchAction === 'high' && hasPermission('lddp:database:patient:adv_search')"
          @on-search="handleSearch"
          @on-reset="handelReset"
        />
        <div class="my-14px">
          <n-space :size="[10, 10]">
            <NButton v-permission="'lddp:database:patient:custom_export'" type="primary" ghost @click="customImport">
              自定义导出
            </NButton>
            <NButton v-permission="'lddp:database:patient:template_export'" type="primary" ghost @click="templateImport">
              模板导出
            </NButton>
          </n-space>
        </div>
        <div class="w-full">
          <BasicTable
            ref="tableRef"
            class="table"
            :columns="columns"
            :request="loadDataTable"
            :row-key="(row:any) => row.id"
            :action-column="actionColumns"
            :pagination="paginationReactive"
            :scroll-x="1500"
            striped
          />
        </div>
      </div>
      <FollowModal
        :current-primary-index="currentPrimaryIndex"
        @register="registerFollowModal"
        @on-success="onSuccess"
      />
    </PageCard>
  </div>
</template>

<style scoped lang="scss">
.headWidth {
    width: 100%;
    position: absolute;
    left: 0;
    top: 0%;
}
.syncLoading {
    :deep(.n-button__icon){
        margin: 0px;
    }
}
.table {
    :deep(.followStatus){
        width: 56px;
        height: 18px;
        color: #fff;
        border-radius: 9px;
        text-align: center;
        line-height: 18px;

    }
}
</style>
