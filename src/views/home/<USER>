<script lang='ts' setup>
import { useWindowSize } from '@vueuse/core'
import { useMessage } from 'wowjoy-vui'
import type { ChildSystemType } from './type'
import { LayoutHeader } from '@/components/layouts'
import LocalCache from '@/utils/cache'
import type { UserInfo } from '@/store/modules/user/type'

import imgLog from '@/assets/images/home-log.jpg'
import imgPermission from '@/assets/images/home-permission.jpg'
import imgRole from '@/assets/images/home-role.jpg'
import imgSystem from '@/assets/images/home-system.jpg'
import imgUser from '@/assets/images/home-user.jpg'
import imgScientific from '@/assets/images/home-scientific.jpg'

const { width } = useWindowSize()
const message = useMessage()
const router = useRouter()

// 目前写死的渲染菜单
const childSystem: ChildSystemType[] = [
  { img: imgScientific, url: 'http://*************:31696', title: '专病科研系统', key: 'null' },
  { img: imgUser, url: '/user', title: '用户管理', key: 'system:user:list' },
  { img: imgPermission, url: '/permission', title: '权限管理', key: 'system:power:list' },
  { img: imgRole, url: '', title: '角色管理', key: 'null' },
  { img: imgLog, url: '/system', title: '系统日志', key: 'system:log:list' },
  { img: imgSystem, url: '', title: '系统设置', key: 'null' },
]

const childSystemRef = ref<ChildSystemType[]>([
  { img: '', url: '', title: '', key: '' }])

// 不同分辨率下 一行排列数量
const cols = computed(() => {
  const childSystemCount = childSystem.length
  const windowWidth = unref(width)
  if (windowWidth < 1280)
    return childSystemCount < 5 ? childSystemCount : 5
  if (windowWidth >= 1280 && windowWidth < 1600)
    return childSystemCount < 6 ? childSystemCount : 6
  if (windowWidth >= 1600 && windowWidth < 1920)
    return childSystemCount < 7 ? childSystemCount : 7
  if (windowWidth >= 1920)
    return childSystemCount < 8 ? childSystemCount : 8
})

// 获取菜单列表
function getMenuList() {
  // 临时demo版本方案，home页其实应该放的是各个子系统
  const powerArr = getLoginPermission()
  childSystemRef.value = childSystem.filter((menu: ChildSystemType) => powerArr.includes(menu.key) || menu.key === 'null')
}

// 获取登陆状态时候拿到的权限
function getLoginPermission() {
  // 目前的权限方案以登录缓存再local中为准
  const userInfo: UserInfo = LocalCache.getLocalStorage('userInfo')

  const powerList = userInfo?.user?.power ?? []
  const powerId = powerList.map(item => item.power)
  return powerId
}
// 跳转
function handGo(system: ChildSystemType) {
  const usUrl = ['system:user:list', 'system:power:list', 'system:log:list']
  if (!system.url) {
    message.info('功能开发中')
    return
  }
  if (system.title === '专病科研系统')
    window.location.replace(system.url)

  if (usUrl.includes(system.key))
    router.push(system.url)

  else
    router.replace(system.url)
}

function init() {
  getMenuList()
}
onMounted(() => {
  init()
})
</script>

<template>
  <div class="home">
    <LayoutHeader />
    <div class="home-container">
      <div>
        <n-grid :cols="cols" :x-gap="20" :y-gap="20" class="mt-56px">
          <n-gi v-for="system in childSystemRef" :key="system.key">
            <div class="home-block" @click="handGo(system)">
              <img :src="system.img" alt="" class="w-74px">
              <span class="text-16px">{{ system.title }}</span>
            </div>
          </n-gi>
        </n-grid>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.home {
    height: 100%;

    background: url('@/assets/images/home-bg.jpg') repeat center 100%/100% ;
    .home-container {
        display: flex;

        justify-content: center;
        height: calc(100% - 50px);

    }
    .home-block {
        width: 150px;
        height: 150px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        background: #ffffff;
        box-shadow: 0px 0px 20px 0px rgba(139,168,187,0.20);
        cursor: pointer;
    }
}
</style>
