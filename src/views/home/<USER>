<script lang='ts' setup>
import { useRouter } from 'vue-router'
import { useAuthStore, useRouteStore } from '@/store'
import GlobalHeader from '@/layouts/common/GlobalHeader/index.vue'
import { useBasicLayout } from '@/hooks'
import Home from '@/assets/images/home.png'

const router = useRouter()
const { headerProps } = useBasicLayout()
const { userInfo } = useAuthStore()

const useRoute = useRouteStore()

const menus = useRoute.menus || []

const loading = ref(true)
/** 跳转的路径 */
const jumpPath = computed(() => {
  if (menus.length && menus[0]?.routePath)
    return menus[0]?.routePath
  else
    return null
})
/** 是否可以跳转 */
const canJump = computed(() => {
  if (menus.length && menus[0]?.routePath)
    return true
  return false
})

/** 初始化 */
function init() {
  if (canJump.value && jumpPath.value) {
    router.replace({
      path: jumpPath.value,
    })
  }
  else {
    loading.value = false
  }
}
onMounted(() => {
  init()
})
</script>

<template>
  <div class="homeWrap">
    <GlobalHeader v-bind="headerProps" />
    <n-spin type="uni" :show="loading">
      <div class="welcomeWrap">
        <div>
          <img :src="Home" alt="" class="homeImg">
          <div class="welcome">
            欢迎登录SLMC管理平台
          </div>
        </div>
      </div>
    </n-spin>
  </div>
</template>

<style scoped lang="scss">
.homeWrap {
    height: 100%;
    background: linear-gradient(180deg,#f1f6ff, #ffffff);
    box-shadow: 0px 1px 4px 0px rgba(202,202,202,0.50);
    .welcomeWrap {
        display: flex;
        align-items: flex-start;
        height: calc(100% - 50px);
        width: 100%;
        justify-content: center;

    }
    .homeImg {
        display: block;
        width: 553px;
        margin-top: 15vh;

    }
    .welcome {
        margin-top: 40px;
        color: #8096B3;
        font-size: 24px;
        font-weight: 600;
        text-align: center;
    }
}
</style>
