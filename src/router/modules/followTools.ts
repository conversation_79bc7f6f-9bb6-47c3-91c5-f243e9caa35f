const ROUTER_NAME = 'toolsFollow'

export const FollowToolsRoute: AuthRoute.Route = {
  name: ROUTER_NAME,
  path: `/${ROUTER_NAME}`,
  component: 'basic',
  meta: {
    title: '随访工具',
    localIcon: 'slmc-icon-department',
    requiresAuth: false,
    order: 4,
  },
  children: [
    {
      name: `${ROUTER_NAME}_message`,
      path: `/${ROUTER_NAME}/message`,
      component: 'self',
      meta: {
        title: '短信模板',
      },
    },
    {
      name: `${ROUTER_NAME}_survey`,
      path: `/${ROUTER_NAME}/survey`,
      component: 'self',
      meta: {
        title: '问卷模板',
      },
    },
    {
      name: `${ROUTER_NAME}_survey_create`,
      path: `/${ROUTER_NAME}/survey/create`,
      component: 'self',
      meta: {
        title: '新增问卷',
        hide: true,
        activeMenu: `${ROUTER_NAME}_survey`,
      },
    },
    {
      name: `${ROUTER_NAME}_survey_edit`,
      path: `/${ROUTER_NAME}/survey/edit`,
      component: 'self',
      meta: {
        title: '编辑问卷',
        hide: true,
        activeMenu: `${ROUTER_NAME}_survey`,
      },
    },
    {
      name: `${ROUTER_NAME}_education`,
      path: `/${ROUTER_NAME}/education`,
      component: 'self',
      meta: {
        title: '患教模板',
      },
    },
    {
      name: `${ROUTER_NAME}_warning`,
      path: `/${ROUTER_NAME}/warning`,
      component: 'self',
      meta: {
        title: '预警模板',
        hide: true,
      },
    },
  ],
}

export default FollowToolsRoute
