const ROUTER_NAME = 'fullScreen'
export const FullScreenRoute: AuthRoute.Route = {
  name: ROUTER_NAME,
  path: `/${ROUTER_NAME}`,
  component: 'blank',
  meta: {
    title: '数据大屏',
    icon: 'ic:baseline-security',
    order: 5,
    hide: true,
  },
  children: [
    {
      name: `${ROUTER_NAME}_data`,
      path: `/${ROUTER_NAME}/data`,
      component: 'self',
      meta: {
        title: '数据大屏',
        requiresAuth: false,
        icon: 'ic:round-construction',
        breadcrumb: false,
        hide: true,
      },
    },
    {
      name: `${ROUTER_NAME}_zheYiDataFullScreen`,
      path: `/${ROUTER_NAME}/zheYiDataFullScreen`,
      component: 'self',
      meta: {
        title: '数据大屏',
        requiresAuth: false,
        icon: 'ic:round-construction',
        breadcrumb: false,
        hide: true,
      },
    },

  ],
}

export default FullScreenRoute
