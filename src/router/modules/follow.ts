const ROUTER_NAME = 'follow'
export const FollowRoute: AuthRoute.Route = {
  name: ROUTER_NAME,
  path: `/${ROUTER_NAME}`,
  component: 'basic',
  meta: {
    title: '随访中心',
    localIcon: 'slmc-icon-suifanggongzuotai',
    order: 3,
    // key: 'lddp:followup',
    requiresAuth: false,
    hide: true,
  },
  children: [
    {
      name: `${ROUTER_NAME}_workspace`,
      path: `/${ROUTER_NAME}/workspace`,
      component: 'self',
      meta: {
        title: '工作台',
        requiresAuth: false,
        breadcrumb: false,
        // key: 'lddp:followup:staging',
      },
    },
    {
      name: `${ROUTER_NAME}_manage`,
      path: `/${ROUTER_NAME}/manage`,
      component: 'self',
      meta: {
        title: '随访管理',
        requiresAuth: false,
        breadcrumb: false,
        // key: 'lddp:followup:main',
      },
    },
    {
      name: `${ROUTER_NAME}_questionnaire`,
      path: `/${ROUTER_NAME}/questionnaire`,
      component: 'self',
      meta: {
        title: '随访记录',
        requiresAuth: false,
        breadcrumb: false,
        hide: true,
      },
    },
  ],
}

export default FollowRoute
