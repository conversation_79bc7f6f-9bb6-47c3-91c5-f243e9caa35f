const ROUTER_NAME = 'exportdata'
export const InpatientData: AuthRoute.Route = {
  name: ROUTER_NAME,
  path: `/${ROUTER_NAME}`,
  component: 'basic',
  meta: {
    title: '数据导出',
    localIcon: 'slmc-icon-zhuanbingshujuku',
    order: 10,
    requiresAuth: false,
  },
  children: [
    {
      name: `${ROUTER_NAME}_inpatient`,
      path: `/${ROUTER_NAME}/inpatient`,
      component: 'self',
      meta: {
        title: '住院数据',
        requiresAuth: false,
        breadcrumb: false,
        // key: 'lddp:followup:staging',
      },
    },
    {
      name: `${ROUTER_NAME}_inpatient_record`,
      path: `/${ROUTER_NAME}/inpatient/record`,
      component: 'self',
      meta: {
        title: '导出记录',
        requiresAuth: false,
        breadcrumb: false,
        // key: 'lddp:followup:staging',
      },
    },
  ],
}

export default InpatientData
