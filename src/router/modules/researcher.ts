import reseacherIcon from '@/assets/svg-icon/left-nav-researcher.svg'

const ROUTER_NAME = 'researcher'

export const researcherRoute: AuthRoute.Route = {
  name: ROUTER_NAME,
  path: `/${ROUTER_NAME}`,
  component: 'basic',
  meta: {
    title: '科研管理',
    icon: reseacherIcon,
    order: 2,
  },
  children: [
    {
      name: `${ROUTER_NAME}_projectManage`,
      path: `/${ROUTER_NAME}/projectManage`,
      component: 'self',
      meta: {
        title: '课题管理',
      },
    },
    {
      name: `${ROUTER_NAME}_scaleManage`,
      path: `/${ROUTER_NAME}/scaleManage`,
      component: 'self',
      meta: {
        title: '量表管理',
      },
    },
    {
      name: `${ROUTER_NAME}_scaleDetail`,
      path: `/${ROUTER_NAME}/scaleDetail`,
      component: 'self',
      meta: {
        title: '量表查看',
        activeMenu: `${ROUTER_NAME}_scaleManage`,
        hide: true,
      },
    },
    {
      name: `${ROUTER_NAME}_workerManage`,
      path: `/${ROUTER_NAME}/workerManage`,
      component: 'self',
      meta: {
        title: '研究者管理',
        hide: true,
      },
    },
    {
      name: `${ROUTER_NAME}_projectManage_create`,
      path: `/${ROUTER_NAME}/projectManage/create`,
      component: 'self',
      meta: {
        title: '新增课题',
        activeMenu: `${ROUTER_NAME}_projectManage`,
        hide: true,
      },
    },
    {
      name: `${ROUTER_NAME}_projectManage_detail`,
      path: `/${ROUTER_NAME}/projectManage/detail`,
      component: 'self',
      meta: {
        title: '课题详情',
        activeMenu: `${ROUTER_NAME}_projectManage`,
        hide: true,
      },
    },
    {
      name: `${ROUTER_NAME}_projectManage_subjectDetail`,
      path: `/${ROUTER_NAME}/projectManage/subjectDetail`,
      component: 'self',
      meta: {
        title: '受试者详情',
        activeMenu: `${ROUTER_NAME}_projectManage`,
        hide: true,
      },
    },
    {
      name: `${ROUTER_NAME}_projectManage_subject_create`,
      path: `/${ROUTER_NAME}/projectManage/subject/addSubject`,
      component: 'self',
      meta: {
        title: '新增受试者',
        activeMenu: `${ROUTER_NAME}_projectManage_subject_create`,
        hide: true,
      },
    },
    {
      name: `${ROUTER_NAME}_groupManage`,
      path: `/${ROUTER_NAME}/groupManage`,
      component: 'self',
      meta: {
        title: '试验分组管理',
        activeMenu: `${ROUTER_NAME}_groupManage`,
        hide: true,
      },
    },
    {
      name: `${ROUTER_NAME}_subjectManage`,
      path: `/${ROUTER_NAME}/subjectManage`,
      component: 'self',
      meta: {
        title: '受试者管理',
        activeMenu: `${ROUTER_NAME}_subjectManage`,
        hide: true,
      },
    },
    {
      name: `${ROUTER_NAME}_subjectManage_subjectDetail`,
      path: `/${ROUTER_NAME}/subjectManage/subjectDetail`,
      component: 'self',
      meta: {
        title: '受试者详情',
        activeMenu: `${ROUTER_NAME}_subjectManage`,
        hide: true,
      },
    },
    {
      name: `${ROUTER_NAME}_historyManage`,
      path: `/${ROUTER_NAME}/historyManage`,
      component: 'self',
      meta: {
        title: '病史信息管理',
        activeMenu: `${ROUTER_NAME}_historyManage`,
        hide: true,
      },
    },
    {
      name: `${ROUTER_NAME}_historyManage_historyDetail`,
      path: `/${ROUTER_NAME}/historyManage/historyDetail`,
      component: 'self',
      meta: {
        title: '病史信息详情',
        activeMenu: `${ROUTER_NAME}_historyManage`,
        hide: true,
      },
    },
  ],
}

export default researcherRoute
