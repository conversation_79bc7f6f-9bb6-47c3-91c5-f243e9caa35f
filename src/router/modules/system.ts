const ROUTER_NAME = 'system'
export const SystemRoute: AuthRoute.Route = {
  name: ROUTER_NAME,
  path: `/${ROUTER_NAME}`,
  component: 'basic',
  meta: {
    title: '系统设置',
    localIcon: 'slmc-icon-setting',
    order: 8,
    key: 'system:config',
  },
  children: [
    {
      name: `${ROUTER_NAME}_userManage`,
      path: `/${ROUTER_NAME}/userManage`,
      component: 'self',
      meta: {
        title: '用户管理',
        requiresAuth: true,
        key: 'system:config:user',
        buttonAuth: [
          'system:config:user:search', // 查询
          'system:config:user:operate', // 基础管理
        ],
      },
    },
    {
      name: `${ROUTER_NAME}_baseSetting`,
      path: `/${ROUTER_NAME}/baseSetting`,
      component: 'self',
      meta: {
        title: '基础设置',
        requiresAuth: false,
        activeMenu: `${ROUTER_NAME}_baseSetting`,
      },
    },
    {
      name: `${ROUTER_NAME}_roleManage`,
      path: `/${ROUTER_NAME}/roleManage`,
      component: 'self',
      meta: {
        title: '角色管理',
        requiresAuth: true,
        key: 'system:config:role',
        buttonAuth: [
          'system:config:role:search', // 查询
          'system:config:role:add', // 新增角色
          'system:config:role:operate', // 基础管理
        ],
      },
    },
    {
      name: `${ROUTER_NAME}_memberManage`,
      path: `/${ROUTER_NAME}/memberManage`,
      component: 'self',
      meta: {
        title: '成员管理',
        requiresAuth: false,
        hide: true,
        key: '',
        activeMenu: `${ROUTER_NAME}_roleManage`,
      },
    },
    {
      name: `${ROUTER_NAME}_permissionManage`,
      path: `/${ROUTER_NAME}/permissionManage`,
      component: 'self',
      meta: {
        title: '权限设置',
        requiresAuth: true,
        key: '',
        hide: true,

      },
    },
    {
      name: `${ROUTER_NAME}_log`,
      path: `/${ROUTER_NAME}/log`,
      component: 'self',
      meta: {
        title: '系统日志',
        requiresAuth: true,
        breadcrumb: false,
        key: 'system:config:log',
        buttonAuth: [
          'system:config:log:search', // 查询
        ],
      },
    },

    /*    {
      name: `${ROUTER_NAME}_tag`,
      path: `/${ROUTER_NAME}/tag`,
      component: 'self',
      meta: {
        title: '标签管理',
        requiresAuth: false,
        breadcrumb: false,
        // key: 'lddp:followup:tag',
      },
    }, */
    // {
    //   name: `${ROUTER_NAME}_parameterSetting`,
    //   path: `/${ROUTER_NAME}/parameterSetting`,
    //   component: 'self',
    //   meta: {
    //     title: '参数设置',
    //     requiresAuth: true,
    //     icon: 'ic:round-construction',
    //     key: 'system:power:list',
    //   },
    // },
    {
      name: `${ROUTER_NAME}_permissionEdit`,
      path: `/${ROUTER_NAME}/permissionEdit`,
      component: 'self',
      meta: {
        title: '权限设置',
        requiresAuth: true,
        hide: true,
        activeMenu: `${ROUTER_NAME}_roleManage`,
      },
    },
    {
      name: `${ROUTER_NAME}_permissionLook`,
      path: `/${ROUTER_NAME}/permissionLook`,
      component: 'self',
      meta: {
        title: '查看权限',
        requiresAuth: true,
        hide: true,
        activeMenu: `${ROUTER_NAME}_roleManage`,
      },
    },
  ],
}

export default SystemRoute
