const ROUTER_NAME = 'analysis'
export const PatientRoute: AuthRoute.Route = {
  name: ROUTER_NAME,
  path: `/${ROUTER_NAME}`,
  component: 'basic',
  meta: {
    title: '统计分析',
    localIcon: 'slmc-icon-web_leftbar_tongji',
    order: 6,
    key: 'lddp:statistics',
  },
  children: [
    {
      name: `${ROUTER_NAME}_statement`,
      path: `/${ROUTER_NAME}/statement`,
      component: 'self',
      meta: {
        title: '统计报表',
        requiresAuth: true,
        key: 'lddp:statistics:main',
        buttonAuth: [
          'lddp:statistics:main:all', // SLMC患者总数
          'lddp:statistics:main:age_sex', // 年龄(性别)分布
          'lddp:statistics:main:follow', // 随访率
          'lddp:statistics:main:disease', // 疾病分类占比
        ],
      },
    },
    {
      name: `${ROUTER_NAME}_specialDisease`,
      path: `/${ROUTER_NAME}/specialDisease`,
      component: 'self',
      meta: {
        title: '专病报表',
        requiresAuth: true,
        key: 'lddp:statistics:speciality',
        buttonAuth: [
          'lddp:statistics:main:hepatitisBCured', // 乙肝-临床治愈
          'lddp:statistics:main:hepatitisBDna', // 乙肝-DNA转阴率
          'lddp:statistics:main:hepatitisCNucleic', // 丙肝-核酸检测率
          'lddp:statistics:main:hepatitisCCured', // 丙肝-临床治愈率
          'lddp:statistics:main:bmi', // 脂肪肝-BMI达标率
          'lddp:statistics:main:weightLoss', // 脂肪肝48周减重率
        ],
      },
    },
    {
      name: `${ROUTER_NAME}_antibacterial`,
      path: `/${ROUTER_NAME}/antibacterial`,
      component: 'self',
      meta: {
        title: '抗菌药使用情况报表',
        requiresAuth: true,
        hide: false,
      },
    },
  ],
}

export default PatientRoute
