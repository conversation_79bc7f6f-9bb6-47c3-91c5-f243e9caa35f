const ROUTER_NAME = 'specificDisease'

export const specificDiseaseRoute: AuthRoute.Route = {
  name: ROUTER_NAME,
  path: `/${ROUTER_NAME}`,
  component: 'basic',
  meta: {
    title: '专病数据库',
    localIcon: 'slmc-icon-user',
    order: 1,
    // key: 'lddp:database',
  },
  children: [
    {
      name: `${ROUTER_NAME}_list`,
      path: `/${ROUTER_NAME}/list`,
      component: 'self',
      meta: {
        title: '患者列表',
        requiresAuth: false,
        breadcrumb: false,
        // key: 'lddp:database:patient',

      },
    },
    {
      name: `${ROUTER_NAME}_detail`,
      path: `/${ROUTER_NAME}/detail`,
      component: 'self',
      meta: {
        title: '患者列表',
        requiresAuth: false,
        hide: true,
        activeMenu: `${ROUTER_NAME}_list`,
        // key: 'lddp:database:patientDetail',

      },
    },

  ],
}

export default specificDiseaseRoute
