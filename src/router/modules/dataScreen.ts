const ROUTER_NAME = 'dataScreen'
export const DataScreenRoute: AuthRoute.Route = {
  name: ROUTER_NAME,
  path: `/${ROUTER_NAME}`,
  component: 'basic',
  meta: {
    title: '数据大屏',
    localIcon: 'slmc-icon-suifangshujudaping',
    order: 1,
    key: 'lddp:dataScreen',
    hide: true,
  },
  children: [
    {
      name: `${ROUTER_NAME}_wane`,
      path: `/${ROUTER_NAME}/wane`,
      component: 'self',
      meta: {
        title: '数据大屏',
        requiresAuth: true,
        breadcrumb: false,
        hide: true,
        key: 'lddp:dataScreen',
        activeMenu: `${ROUTER_NAME}`,
        buttonAuth: [
          'lddp:dataScreen:show', // 查询
          'lddp:dataScreen:jump', // 跳转
        ],
      },
    },

  ],
}

export default DataScreenRoute
