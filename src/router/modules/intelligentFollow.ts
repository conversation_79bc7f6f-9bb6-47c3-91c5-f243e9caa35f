const ROUTER_NAME = 'intelligentFollow'

export const IntelligentRoute: AuthRoute.Route = {
  name: ROUTER_NAME,
  path: `/${ROUTER_NAME}`,
  component: 'basic',
  meta: {
    title: '智能随访',
    localIcon: 'slmc-icon-suifanggongzuotai',
    requiresAuth: false,
    order: 3,
  },
  children: [
    // {
    //   name: `${ROUTER_NAME}_registration`,
    //   path: `/${ROUTER_NAME}/registration`,
    //   component: 'self',
    //   meta: {
    //     title: '患者登记',
    //   },
    // },
    {
      name: `${ROUTER_NAME}_workbench`,
      path: `/${ROUTER_NAME}/workbench`,
      component: 'self',
      meta: {
        title: '我的工作台',
      },
    },
    {
      name: `${ROUTER_NAME}_todo2`,
      path: `/${ROUTER_NAME}/todo2`,
      component: 'self',
      meta: {
        title: '随访待办',
      },
    },
    {
      name: `${ROUTER_NAME}_tongJi`,
      path: `/${ROUTER_NAME}/tongJi`,
      component: 'self',
      meta: {
        title: '随访统计',
      },
    },
    {
      name: `${ROUTER_NAME}_confirm`,
      path: `/${ROUTER_NAME}/confirm`,
      component: 'self',
      meta: {
        title: '待入组患者',
      },
    },
    {
      name: `${ROUTER_NAME}_manage`,
      path: `/${ROUTER_NAME}/manage`,
      component: 'self',
      meta: {
        title: '随访管理',
      },
    },
    {
      name: `${ROUTER_NAME}_manage_create`,
      path: `/${ROUTER_NAME}/manage/create`,
      component: 'self',
      meta: {
        hide: true,
        title: '随访任务设置',
        activeMenu: `${ROUTER_NAME}_manage`,
      },
    },
    {
      name: `${ROUTER_NAME}_manage_detail`,
      path: `/${ROUTER_NAME}/manage/detail`,
      component: 'self',
      meta: {
        hide: true,
        title: '随访详情',
        activeMenu: `${ROUTER_NAME}_manage`,
      },
    },
    {
      name: `${ROUTER_NAME}_plan`,
      path: `/${ROUTER_NAME}/plan`,
      component: 'self',
      meta: {
        title: '专病方案',
      },
    },
    {
      name: `${ROUTER_NAME}_templateCenter`,
      path: `/${ROUTER_NAME}/templateCenter`,
      component: 'self',
      meta: {
        title: '模板中心',
      },
    },

  ],
}

export default IntelligentRoute
