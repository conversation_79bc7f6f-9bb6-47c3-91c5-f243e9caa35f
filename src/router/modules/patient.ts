const ROUTER_NAME = 'patient'
export const PatientRoute: AuthRoute.Route = {
  name: ROUTER_NAME,
  path: `/${ROUTER_NAME}`,
  component: 'basic',
  meta: {
    title: '患者数据库',
    localIcon: 'slmc-icon-user',
    order: 2,
    key: 'lddp:database',
    hide: true,
  },
  children: [
    {
      name: `${ROUTER_NAME}_list`,
      path: `/${ROUTER_NAME}/list`,
      component: 'self',
      meta: {
        title: '患者列表',
        requiresAuth: true,
        breadcrumb: false,
        key: 'lddp:database:patient',
        buttonAuth: [
          'lddp:database:patient:search', // 普通搜索
          'lddp:database:patient:adv_search', // 高级筛选
          'lddp:database:patient:syn', // 数据同步
          'lddp:database:patient:custom_export', // 自定义导出
          'lddp:database:patient:template_export', // 模版导出
          'lddp:database:patient:into_slmc', // 纳入随访
          'lddp:database:patient:visit1', // v1完整度
          'lddp:database:patient:new_visit', // 最新访视
        ],
      },
    },
    {
      name: `${ROUTER_NAME}_file`,
      path: `/${ROUTER_NAME}/file`,
      component: 'self',
      meta: {
        title: '患者档案',
        requiresAuth: false,
        hide: true,
        activeMenu: `${ROUTER_NAME}_list`,
        key: 'lddp:database:patientDetail',
        buttonAuth: [
          /* 基础信息 */
          'lddp:database:patientDetail:info', // 基础信息
          'lddp:database:patientDetail:search', // 基础信息——查询
          /* 就诊记录 */
          'lddp:database:patientDetail:record', // 就诊记录
          'lddp:database:patientDetail:record:search', // 就诊记录——查询
          'lddp:database:patientDetail:record:operate', // 就诊记录——操作
          /* 关键指标趋势图 */
          'lddp:database:patientDetail:main_target', // 关键指标趋势图
          /* 治疗方案 */
          'lddp:database:patientDetail:treat', // 治疗方案
          'lddp:database:patientDetail:treat:search', // 治疗方案-查询
          'lddp:database:patientDetail:add', // 治疗方案-新增药品
          /* SLMC访视 */
          'lddp:database:patientDetail:slmc', // SLMC访视
          'lddp:database:patientDetail:slmc:search', // SLMC访视——查询
          'lddp:database:patientDetail:slmc:operate', // SLMC访视——录入信息
        ],

      },
    },
    {
      name: `${ROUTER_NAME}_editFile`,
      path: `/${ROUTER_NAME}/editFile`,
      component: 'self',
      meta: {
        title: '编辑信息',
        requiresAuth: false,
        breadcrumb: false,
        hide: true,
        activeMenu: `${ROUTER_NAME}_list`,
        key: 'lddp:database:patientDetail:edit',
        buttonAuth: [
          'lddp:database:patientDetailEdit:search', // 查询
          'lddp:database:patientDetail:update', // 编辑
        ],

      },
    },
    {
      name: `${ROUTER_NAME}_inspection`,
      path: `/${ROUTER_NAME}/inspection`,
      component: 'self',
      meta: {
        title: '检验报告',
        requiresAuth: false,
        breadcrumb: false,
        hide: true,
        activeMenu: `${ROUTER_NAME}_list`,

      },
    },
    {
      name: `${ROUTER_NAME}_check`,
      path: `/${ROUTER_NAME}/check`,
      component: 'self',
      meta: {
        title: '检查报告',
        requiresAuth: false,
        breadcrumb: false,
        hide: true,
        activeMenu: `${ROUTER_NAME}_list`,

      },
    },
    {
      name: `${ROUTER_NAME}_medicalHistory`,
      path: `/${ROUTER_NAME}/medicalHistory`,
      component: 'self',
      meta: {
        title: '病历文书',
        requiresAuth: false,
        breadcrumb: false,
        hide: true,
        activeMenu: `${ROUTER_NAME}_list`,

      },
    },

  ],
}

export default PatientRoute
