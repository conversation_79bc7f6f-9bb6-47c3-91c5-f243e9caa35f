const ROUTER_NAME = 'dataCenter'
export const FullScreenRoute: AuthRoute.Route = {
  name: ROUTER_NAME,
  path: `/${ROUTER_NAME}`,
  component: 'basic',
  meta: {
    title: '数据中心',
    localIcon: 'slmc-icon-suifangshujudaping',
    order: 5,
    requiresAuth: false,
    hide: true,
  },
  children: [
    {
      name: `${ROUTER_NAME}`,
      path: `/${ROUTER_NAME}`,
      component: 'self',
      meta: {
        title: '数据中心',
        requiresAuth: false,
        breadcrumb: false,
        hide: true,
      },
    },
    {
      name: `${ROUTER_NAME}_records`,
      path: `/${ROUTER_NAME}/records`,
      component: 'self',
      meta: {
        title: '导出记录',
        requiresAuth: false,
        hide: true,
      },
    },
    {
      name: `${ROUTER_NAME}_zheYiDataCenter`,
      path: `/${ROUTER_NAME}/zheYiDataCenter`,
      component: 'self',
      meta: {
        title: '平台数据看板',
        requiresAuth: false,
        hide: false,
        breadcrumb: false,
      },
    },
  ],
}

export default FullScreenRoute
