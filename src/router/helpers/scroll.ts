import type { RouterScrollBehavior } from 'vue-router'

export const scrollBehavior: RouterScrollBehavior = (to, _) => {
  return new Promise((resolve) => {
    if (to.hash) {
      const el = document.querySelector(to.hash)
      if (el) {
        resolve({
          el,
          behavior: 'smooth',
        })
      }
    }

    setTimeout(() => {
      resolve({ left: 0, top: 0 })
    }, 200)
  })
}
