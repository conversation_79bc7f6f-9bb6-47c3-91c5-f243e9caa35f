import type { NavigationGuardNext, RouteLocationNormalized } from 'vue-router'
import { createDynamicRouteGuard } from './dynamic'
import { routeName } from '@/router'
import { exeStrategyActions, localStg } from '@/utils'
import { useAuthStore, useRouteStore } from '@/store'
import { hmaLoginApi } from '@/api/system/login'

/** 处理路由页面的权限 */
export async function createPermissionGuard(
  to: RouteLocationNormalized,
  from: RouteLocationNormalized,
  next: NavigationGuardNext,
) {
  // 免登录逻辑
  const queryHisNo = to.query.hisNo as string
  if (queryHisNo) {
    const localHisNo = localStg.get('hisNo')
    if (!localHisNo || localHisNo !== queryHisNo) {
      const { loginByToken } = useAuthStore()

      const { data } = await hmaLoginApi<Auth.UserInfo>({
        jobNum: queryHisNo,
        loginType: 'HMA',
      })

      if (data) {
        loginByToken({
          ...data,
          hisNo: queryHisNo,
        })
      }
    }
  }

  // 记录九宫格中的token
  const href = decodeURIComponent(to.fullPath)
  const regex = /token=([^&]+)/
  const match = href.match(regex)

  if (match) {
    const token = match[1]
    sessionStorage.setItem('his_token', token)
  }

  // 动态路由
  const isInitDynamicRoute = await createDynamicRouteGuard(to, from, next)
  if (!isInitDynamicRoute)
    return

  /// 缓存相关
  handleTheCacheRoutes(to, from)

  // 外链路由, 从新标签打开，返回上一个路由
  if (to.meta.href) {
    window.open(to.meta.href)
    next({ path: from.fullPath, replace: true, query: from.query })
    return
  }

  //   const auth = useAuthStore()
  const isLogin = Boolean(localStg.get('accessToken'))
  const userInfo: any = localStg.get('userInfo') || {}

  const userType: string = userInfo?.userType

  const permissionsPower = userInfo?.power || []

  const needLogin = Boolean(to.meta?.requiresAuth) || Boolean(to.meta?.key)

  const hasPermission = to.meta.key ? permissionsPower.includes(to.meta.key) : true

  const actions: Common.StrategyAction[] = [
    // 已登录状态跳转登录页，跳转至首页
    [
      isLogin && to.name === routeName('login'),
      () => {
        next({ name: routeName('root') })
      },
    ],
    // 不需要登录权限的页面直接通行
    [
      !needLogin,
      () => {
        next()
      },
    ],
    // 未登录状态进入需要登录权限的页面
    [
      !isLogin && needLogin,
      () => {
        const redirect = to.fullPath
        next({ name: routeName('login'), query: { redirect } })
      },
    ],
    // 登录状态进入需要登录权限的页面，有权限直接通行
    [
      isLogin && needLogin && hasPermission,
      () => {
        next()
      },
    ],
    [
      // 登录状态进入需要登录权限的页面，无权限，重定向到无权限页面
      isLogin && needLogin && !hasPermission,
      () => {
        next({ name: routeName('403') })
      },
    ], [
      // 普通用户仅展示欢迎页
      userType === '2',
      () => {
        next({ name: routeName('home') })
      },
    ],
  ]

  exeStrategyActions(actions)
}

/// 加一个处理缓存的
function handleTheCacheRoutes(to, from) {
  ///   目前就暂时先缓存   专病患者列表(缓存) -> 患者详情 这一场景
  console.log(to)
  console.log(from)
  if ((to.name === 'specificDisease_detail' && from.name === 'specificDisease_list') || (from.name === 'specificDisease_detail' && to.name === 'specificDisease_list'))
    useRouteStore().addCacheRoute('specificDisease_list')
  else
    useRouteStore().removeCacheRoute('specificDisease_list')
}
