// ele表格重置样式
.el-table th.el-table__cell {
  background-color: #E7F9F7 !important;
  box-shadow: 0px -1px 0px 0px rgba(204,204,204,0.50) inset;
  font-size: 14px;
  font-weight: 500;
}

.el-table__row.active .el-table__cell {
  background-color: #FFFBE0 !important;
}
.el-table__row--level-1 .el-table__cell {
  background-color: #F5F5F5 !important;
}
.el-table__row:hover .el-table__cell {
  background-color: #FFFBE0 !important;
}

.el-table .el-table__cell {
  font-size: 14px;
}

.el-table__expanded-cell[class*=cell] {
  padding: 0 !important;
}

.el-table .cell {
  color: #333333
}
.el-backtop{
  background-color:rgba($color: #000000, $alpha: 0.2);

  &:hover {
    background-color:rgba($color: #000000, $alpha: 0.3);
  }
}

.el-table__body tr.current-row>td.el-table__cell {
  background-color: #FFFBE0;
}

.el-checkbox__input.is-indeterminate .el-checkbox__inner,
.el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #06AEA6;
  border-color: #06AEA6;
}

.el-checkbox__inner {
  border: 2px solid #B3B3B3!important;
}

.el-checkbox__inner::after{
  top: 0!important;
  border-width: 2px!important;
  left: 3px!important;
}

.el-checkbox__input.is-indeterminate .el-checkbox__inner::before {
  border: 2px solid #fff;
  top: 4px!important;
}
