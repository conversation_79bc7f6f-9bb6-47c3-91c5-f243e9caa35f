.n-form-item  {
    //表单label
    .n-form-item-label {
        padding-right: 10px;
        color: #666666;
    }

}
/* 级联选择*/
.n-cascader-menu {
    .n-cascader-option {
        line-height: 1;
    }
    .n-cascader-option__prefix {
        display: flex;
        align-items: center;
        margin-right: 10px;
    }
}

.n-data-table {
    .n-data-table-thead {
        background-color: #E7F9F7;
    }
    .n-data-table-th {
        background-color: #E7F9F7;
        .n-data-table-sorter.n-data-table-sorter--active {
            color: #06AEA6;
        }
    }
    .n-data-table-td {
        font-size: 14px;
        // height: 32px;
    }
    .n-data-table-th {
        font-size: 14px;
        // height: 32px;
        // line-height: 32px;
    }
    .n-data-table-empty {
        border-bottom: 1px solid #d8d8d8;
    }

}

.n-tabs .n-tabs-nav.n-tabs-nav--line-type .n-tabs-nav-scroll-content {
    border-bottom: 1px solid #ccc
}
.n-tree {
    .n-tree-node-folder-content{
        margin-top: 0px;
    }
    .n-tree-node{
        display: flex;
        align-items: center;
    }
}

.n-popover {
    border-radius: 3px 3px 3px 3px;
}

.n-empty .n-empty-ic .n-empty-ic__description {
    font-size: 14px!important;
}

.n-menu .n-menu-item-content:not(.n-menu-item-content--disabled):hover::before{
    --n-item-color-hover: #E1F0EF !important;
    left:0  !important;
    right:0 !important;
    border-radius: 0 !important;
}