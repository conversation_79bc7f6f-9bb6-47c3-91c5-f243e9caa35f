::-webkit-scrollbar-button {
    display: none;
  }

  /* 滚动条整体部分 */
  ::-webkit-scrollbar {
    width: 12px;
    height: 12px;
  }

 /* 外层轨道 */
  ::-webkit-scrollbar-track-piece {
    background: #fafafa;
    border-radius: 0;
    border: 1px solid #dcdcdc;

    &:vertical {
        border-top: 0px;
        border-bottom: 0px;
    }

    &:horizontal {
        border-left: 0px;
        border-right: 0px;
    }
  }

 /* 滚动条里面的小方块 */
  ::-webkit-scrollbar-thumb:vertical {
    background-color: #c1c1c1;
    border-left: 3px solid transparent;
    border-right: 3px solid transparent;
    border-radius: 20px;
    background-clip: content-box;
  }
  ::-webkit-scrollbar-thumb:horizontal {
    background-color: #c1c1c1;
    border-top: 3px solid transparent;
    border-bottom: 3px solid transparent;
    border-radius: 20px;
    background-clip: content-box;
  }
  ::-webkit-scrollbar-thumb:hover {
    background: #999 !important;
  }

 /* 自动填充 */
  input:-webkit-autofill{
      -webkit-box-shadow:0 0 0 1000px white inset ;
  }

