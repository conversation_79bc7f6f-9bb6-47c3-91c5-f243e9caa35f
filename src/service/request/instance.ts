import axios from 'axios'

import type { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { router } from '@/router'
import {
  handleAxiosError,
  handleBackendError,
  handleResponseError,
  handleServiceResult,
  localStg,
  transformRequestData,
} from '@/utils'

// 登出
async function logOut() {
  document.cookie = 'slmcToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'

  window.localStorage.clear()
  window.sessionStorage.clear()
  router.replace({
    path: '/login',
  })
}

/**
 * 封装axios请求类
 * <AUTHOR>
 */
export default class CustomAxiosInstance {
  instance: AxiosInstance

  backendConfig: Service.BackendResultConfig

  /**
   *
   * @param axiosConfig - axios配置
   * @param backendConfig - 后端返回的数据配置
   */
  constructor(
    axiosConfig: AxiosRequestConfig,
    backendConfig: Service.BackendResultConfig = {
      codeKey: 'state',
      dataKey: 'results',
      msgKey: 'msg',
      subMsgKey: 'subMsg',
      successCode: 200,
    },
  ) {
    this.backendConfig = backendConfig
    this.instance = axios.create(axiosConfig)
    this.setInterceptor()
  }

  /** 设置拦截器 */
  setInterceptor() {
    /* 请求的拦截 */
    this.instance.interceptors.request.use(
      async (config) => {
        const handleConfig = { ...config }
        if (handleConfig.headers) {
          // 数据转换
          const contentType = handleConfig.headers['Content-Type'] as UnionKey.ContentType
          handleConfig.data = await transformRequestData(handleConfig.data, contentType)
          // 设置token
          //   handleConfig.headers.Authorization = `Bearer ${localStg.get('accessToken')}` || ''
          handleConfig.headers.slmcToken = localStg.get('accessToken') || ''
        }
        return handleConfig
      },
      (axiosError: AxiosError) => {
        const error = handleAxiosError(axiosError)
        return handleServiceResult(error, null)
      },
    )
    /* 响应的拦截 */
    this.instance.interceptors.response.use(
      (async (response) => {
        const { status } = response
        if (status === 200 || status < 300 || status === 304) {
          const backend = response.data
          const { codeKey, dataKey, successCode } = this.backendConfig

          if (backend.state === 203)
            logOut()

          /// 需要重新登录的话, 就跳转到登录页面
          if (backend.subMsg === '请重新登录!')
            logOut()

          /// 如果是blob数据, 那么直接返回
          if (backend instanceof Blob) {
            const info = response.headers['content-disposition']
            let filename = 'file'
            if (info) {
              const filenameMatch = info.match(/filename="?(.+)"?/)
              if (filenameMatch.length === 2)
                filename = filenameMatch[1]
            }
            backend.fileName = filename
            return handleServiceResult(null, backend)
          }
          // 请求成功
          if (backend[codeKey] === successCode)
            return handleServiceResult(null, backend[dataKey])

          //   // token失效, 刷新token
          //   if (REFRESH_TOKEN_CODE.includes(backend[codeKey])) {
          //     const config = await handleRefreshToken(response.config)
          //     if (config)
          //       return this.instance.request(config)
          //   }
          if (backend.code === 200)
            return handleServiceResult(null, backend.data)

          const error = handleBackendError(backend, this.backendConfig)
          return handleServiceResult(error, null)
        }

        const error = handleResponseError(response)
        return handleServiceResult(error, null)
      }) as (response: AxiosResponse<any, any>) => Promise<AxiosResponse<any, any>>,
      (axiosError: AxiosError) => {
        const error = handleAxiosError(axiosError)
        return handleServiceResult(error, null)
      },
    )
  }
}
