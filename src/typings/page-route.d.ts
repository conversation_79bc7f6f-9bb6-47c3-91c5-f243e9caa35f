declare namespace PageRoute {
  /**
   * the root route key
   * @translate 根路由
   */
  type RootRouteKey = 'root';

  /**
   * the not found route, which catch the invalid route path
   * @translate 未找到路由(捕获无效路径的路由)
   */
  type NotFoundRouteKey = 'not-found';

  /**
   * the route key
   * @translate 页面路由
   */
  type RouteKey =
    | '403'
    | '404'
    | 'not-found'
    | 'home'
    | 'login'
    | 'system'
    | 'system_log'
    | 'system_roleManage'
    | 'system_permissionManage'
    | 'system_parameterSetting'
    | 'system_permissionEdit'
    | 'system_permissionLook'
    | 'system_userManage'
    | 'system_memberManage'
    | 'system_baseSetting'
    | 'patient'
    | 'patient_list'
    | 'patient_file'
    | 'patient_editFile'
    | 'patient_check'
    | 'patient_inspection'
    | 'patient_medicalHistory'
    | 'dataScreen'
    | 'dataScreen_wane'
    | 'fullScreen'
    | 'fullScreen_data'
    | 'analysis'
    | 'analysis_specialDisease'
    | 'analysis_statement'
    | 'follow'
    | 'follow_workspace'
    | 'follow_manage'
    | 'system_tag'
    | 'follow_questionnaire'
    | 'intelligentFollow'
    | 'intelligentFollow_registration'
    | 'intelligentFollow_manage'
    | 'intelligentFollow_manage_detail'
    | 'intelligentFollow_manage_create'
    | 'intelligentFollow_plan'
    | 'intelligentFollow_templateCenter'
    | 'intelligentFollow_todo'
    | 'intelligentFollow_todo2'
    |  'intelligentFollow_tongJi'
    | 'intelligentFollow_workbench'
    | 'intelligentFollow_confirm'
    | 'toolsFollow'
    | 'toolsFollow_message'
    | 'toolsFollow_survey'
    | 'toolsFollow_survey_create'
    | 'toolsFollow_survey_edit'
    | 'toolsFollow_education'
    | 'toolsFollow_warning'
    | 'specificDisease'
    | 'specificDisease_list'
    | 'specificDisease_detail'
    | 'tag'
    | 'dataCenter'
    | 'dataCenter_records'
    | 'researcher_projectManage'
    | 'researcher'
    | 'researcher_projectManage_create'
    | 'researcher_projectManage_detail'
    | 'researcher_scaleManage'
    | 'researcher_scaleDetail'
    | 'researcher_workerManage'
    | 'researcher_projectManage_subjectDetail'
    | 'researcher_subjectManage_subjectDetail'
    | 'researcher_projectManage_subject_create'
    | 'researcher_groupManage'
    | 'researcher_subjectManage'
    | 'dataCenter_zheYiDataCenter'
    | 'fullScreen_zheYiDataFullScreen'
    | 'analysis_antibacterial'
    | 'knowledge_knowledgeInfo'
    | 'knowledge_knowledgeInfo_1638862590740656063'
    | 'exportdata'
    | 'exportdata_inpatient'
    | 'exportdata_inpatient_record'
    | 'researcher_historyManage'
    | 'researcher_historyManage_historyDetail'
  /**
   * last degree route key, which has the page file
   * @translate 最后一级路由(该级路有对应的页面文件)
   */
  type LastDegreeRouteKey = Extract<
    RouteKey,
    | '403'
    | '404'
    | 'not-found'
    | 'home'
    | 'login'
    | 'system_log'
    | 'system_roleManage'
    | 'system_userManage'
    | 'system_permissionManage'
    | 'system_permissionEdit'
    | 'system_permissionLook'
    | 'system_parameterSetting'
    | 'system_memberManage'
    | 'system_baseSetting'
    | 'patient_list'
    | 'patient_file'
    | 'patient_editFile'
    | 'patient_check'
    | 'patient_inspection'
    | 'patient_medicalHistory'
    | 'dataScreen_wane'
    | 'fullScreen_data'
    | 'analysis_specialDisease'
    | 'analysis_statement'
    | 'follow_workspace'
    | 'follow_manage'
    | 'system_tag',
    | 'follow_questionnaire'
    | 'intelligentFollow'
    | 'intelligentFollow_registration'
    | 'intelligentFollow_manage'
    | 'intelligentFollow_manage_create'
    | 'intelligentFollow_manage_detail'
    | 'intelligentFollow_plan'
    | 'intelligentFollow_templateCenter'
    | 'intelligentFollow_todo'
    | 'intelligentFollow_workbench'
    | 'intelligentFollow_confirm'
    | 'toolsFollow'
    | 'toolsFollow_message'
    | 'toolsFollow_survey'
    | 'toolsFollow_survey_create'
    | 'toolsFollow_survey_edit'
    | 'toolsFollow_education'
    | 'toolsFollow_warning'
    | 'specificDisease'
    | 'specificDisease_list'
    | 'specificDisease_detail'
    | 'tag'
    | 'dataCenter'
    | 'dataCenter_records'
    | 'researcher_projectManage'
    | 'researcher'
    | 'researcher_projectManage_create'
    | 'researcher_projectManage_detail'
    | 'researcher_scaleManage'
    | 'researcher_scaleDetail'
    | 'researcher_workerManage'
    | 'researcher_projectManage_subjectDetail'
    | 'researcher_subjectManage_subjectDetail'
    | 'researcher_projectManage_subject_create'
    | 'researcher_groupManage'
    | 'researcher_subjectManage'
    | 'dataCenter_zheYiDataCenter'
    | 'fullScreen_zheYiDataFullScreen'
    | 'analysis_antibacterial'
    | 'intelligentFollow_todo2'
    | 'intelligentFollow_tongJi'
    | 'knowledge_knowledgeInfo'
    | 'knowledge_knowledgeInfo_1638862590740656063'
    | 'exportdata'
    | 'exportdata_inpatient'
    | 'exportdata_inpatient_record'
    | 'researcher_historyManage'
    | 'researcher_historyManage_historyDetail'
  >;
}
