/** 用户相关模块 */
declare namespace Auth {
    /**
     * 用户角色类型(前端静态路由用角色类型进行路由权限的控制)
     * - super: 超级管理员(该权限具有所有路由数据)
     * - admin: 管理员
     * - user: 用户
     */
    type RoleType = 'super' | 'admin' | 'user';

    interface OrganList {
        id: string,
        parentId: string,
        organName: string,
        organGroup: null | string,
        organType: null|string,
        organDesc: null|string,
        status: null | string
        children: OrganList[]
        organLevel:number
    }

    interface User {
        phone: string
        id: string
        power: Power[]
        userName: string
        job: string
        title: string
        userNumber: string
        /**0主管理员 1子管理员 2普通用户*/
        userType: string
        organList: OrganList[]
        organId:string
        organName: string
        rsUserId:string
    }
    interface Power {
        id: number
        userId: number
        menuId: number
        menuName: string
        power: string
        powerRange: string
        isDeleted: number
    }

    /** 用户信息 */
    interface UserInfo {
        access_token: string
        token_type: string
        iat: number
        user: User
        jti: string
        hisNo?: string
    }
}

declare namespace UserManagement {
    interface User extends ApiUserManagement.User {
        /** 序号 */
        index: number;
        /** 表格的key（id） */
        key: string;
    }

    /**
     * 用户性别
     * - 0: 女
     * - 1: 男
     */
    type GenderKey = NonNullable<User['gender']>;

    /**
     * 用户状态
     * - 1: 启用
     * - 2: 禁用
     * - 3: 冻结
     * - 4: 软删除
     */
    type UserStatusKey = NonNullable<User['userStatus']>;
}
