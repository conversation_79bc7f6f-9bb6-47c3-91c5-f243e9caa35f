declare namespace StorageInterface {
  /** localStorage的存储数据的类型 */
  // interface Session {
  //   demoKey: string;
  //   token: string
  // }

  type Session = Record<string,string>

  /** localStorage的存储数据的类型 */
  interface Local {
    /** 主题颜色 */
    themeColor: string;
    /** 用户token */
    accessToken: string;
    /** 用户刷新token */
    refreshToken: string;
    /** 用户信息 */
    userInfo: Auth.User;
    /** 主题配置 */
    themeSettings: Theme.Setting;
    /** 多页签路由信息 */
    multiTabRoutes: App.GlobalTabRoute[];
    /** 本地语言缓存 */
    lang: I18nType.langType;
    /** his 工号 */
    hisNo: string
  }
}
