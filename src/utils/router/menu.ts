import { isArray } from 'lodash-es'
import { localStg } from '../storage'
import { useIconRender } from '@/hooks'

/**
 * 将权限路由转换成菜单
 * @param routes - 路由
 */
export function transformAuthRouteToMenu(
  routes: AuthRoute.Route[],
  menuKeys: string[],
  isChildren?: boolean,
): App.GlobalMenuOption[] {
  const globalMenu: App.GlobalMenuOption[] = []

  for (let index = 0; index < routes.length; index++) {
    const route = routes[index]
    const { name, path, meta } = route
    const routeName = name as string
    const key = meta?.key || ''
    let menuChildren: App.GlobalMenuOption[] | undefined
    if (route.children && route.children.length > 0) {
      menuChildren = transformAuthRouteToMenu(
        route.children,
        menuKeys,
        true,
      )
    }
    const menuItem: App.GlobalMenuOption = addPartialProps({
      menu: {
        key: routeName,
        label: meta.title,
        routeName,
        routePath: path,
        i18nTitle: meta.i18nTitle,
      },
      icon: meta.icon,
      localIcon: meta.localIcon,
      children: menuChildren,
    })
    /// TODO
    /// 特殊处理一下
    const userInfo: any = localStg.get('userInfo') || {}
    if (userInfo.roleList === '科研系统' && !isChildren) {
      if (meta.title === '科研管理') {
        /// 就要一个
        globalMenu.push(menuItem)
        return globalMenu
      }
      continue
    }
    /// 继续特殊处理下
    if (meta.title === '研究者管理') {
      route.meta.hide = !userInfo.isAdmin
      console.log('隐藏')
    }
    if (userInfo.userName !== '颜华东') {
      if (meta.title === '数据导出') {
        route.meta.hide = false
        console.log('隐藏数据导出')
        continue
      }
    }
    /// 登录的是树兰平台 那么隐藏掉科研平台
    if (isArray(userInfo.roleList) && meta.title === '科研管理')
      route.meta.hide = true

    if (!hideInMenu(route)) {
      if (!key)
        globalMenu.push(menuItem)
      else menuKeys.includes(key) && globalMenu.push(menuItem)
    }
  }
  return globalMenu
}

/**
 * 翻译菜单
 * @param menus
 * @returns
 */
export function translateMenuLabel(
  menus: App.GlobalMenuOption[],
): App.GlobalMenuOption[] {
  const globalMenu: App.GlobalMenuOption[] = []
  menus.forEach((menu) => {
    let menuChildren: App.GlobalMenuOption[] | undefined
    if (menu.children && menu.children.length > 0)
      menuChildren = translateMenuLabel(menu.children)
    const menuItem: App.GlobalMenuOption = {
      ...menu,
      children: menuChildren,
      label: () => h('n-ellipsis', null, { default: () => menu.label }),
    }
    globalMenu.push(menuItem)
  })
  return globalMenu
}

/**
 * 获取当前路由所在菜单数据的paths
 * @param activeKey - 当前路由的key
 * @param menus - 菜单数据
 */
export function getActiveKeyPathsOfMenus(
  activeKey: string,
  menus: App.GlobalMenuOption[],
) {
  const keys = menus
    .map(menu => getActiveKeyPathsOfMenu(activeKey, menu))
    .flat(1)
  return keys
}

function getActiveKeyPathsOfMenu(
  activeKey: string,
  menu: App.GlobalMenuOption,
) {
  const keys: string[] = []
  if (activeKey.startsWith(menu.routeName))
    keys.push(menu.routeName)

  if (menu.children) {
    keys.push(
      ...menu.children
        .map(item =>
          getActiveKeyPathsOfMenu(
            activeKey,
            item as App.GlobalMenuOption,
          ),
        )
        .flat(1),
    )
  }

  return keys
}

/** 路由不转换菜单 */
function hideInMenu(route: AuthRoute.Route) {
  return Boolean(route.meta.hide)
}

/** 给菜单添加可选属性 */
function addPartialProps(config: {
  menu: App.GlobalMenuOption
  icon?: string
  localIcon?: string
  children?: App.GlobalMenuOption[]
}) {
  const { iconRender } = useIconRender()

  const item = { ...config.menu }

  const { icon, localIcon, children } = config

  if (localIcon)
    Object.assign(item, { icon: iconRender({ localIcon }) })

  if (icon)
    Object.assign(item, { icon: iconRender({ icon }) })

  if (children)
    Object.assign(item, { children })

  return item
}
