class LocalCache {
  setLocalStorage(key: string, value: any) {
    window.localStorage.setItem(key, JSON.stringify(value))
  }

  getLocalStorage(key: string) {
    const value = window.localStorage.getItem(key)
    if (value)
      return JSON.parse(value)
  }

  deleteLocalStorage(key: string) {
    window.localStorage.removeItem(key)
  }

  clearLocalStorage() {
    window.localStorage.clear()
  }

  setSessionStorage(key: string, value: any) {
    window.sessionStorage.setItem(key, JSON.stringify(value))
  }

  getSessionStorage(key: string) {
    const value = window.sessionStorage.getItem(key)
    if (value)
      return JSON.parse(value)
  }

  deleteSessionStorage(key: string) {
    window.sessionStorage.removeItem(key)
  }

  clearSessionStorage() {
    window.sessionStorage.clear()
  }

  clearStorage() {
    this.clearLocalStorage()
    this.clearSessionStorage()
  }
}

export default new LocalCache()
