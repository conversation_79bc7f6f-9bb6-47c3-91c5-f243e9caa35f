import { isNull, isUndefined } from './is'

/**
 * 如果传入的值不存在，就格式化为‘-’
 * @param value 传入的值
 * @returns 原值 or '-'
 */
export function formatNullValueToShortBar(value: string | number | null | undefined): string {
  if (isUndefined(value) || isNull(value) || value === '' || value === ' ')
    return '-'
  else
    return `${value}`
}

/**
 * 手机号处理中间隐藏
 */
export function formartPhoneNumberMiss(phone: string) {
  if (phone?.length !== 11)
    return phone ?? '-'
  else
    return `${phone.substring(0, 3)}****${phone.substring(7)}`
}

export function convertToChinaNum(num: number) {
  const arr1 = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九']
  const arr2 = ['', '十', '百', '千', '万', '十', '百', '千', '亿', '十', '百', '千', '万', '十', '百', '千', '亿']// 可继续追加更高位转换值
  if (!num || Number.isNaN(num))
    return '零'

  const english = num.toString().split('')
  let result = ''
  for (let i = 0; i < english.length; i++) {
    const des_i = english.length - 1 - i// 倒序排列设值
    result = arr2[i] + result
    const arr1_index = english[des_i]
    result = arr1[arr1_index] + result
  }
  // 将【零千、零百】换成【零】 【十零】换成【十】
  result = result.replace(/零(千|百|十)/g, '零').replace(/十零/g, '十')
  // 合并中间多个零为一个零
  result = result.replace(/零+/g, '零')
  // 将【零亿】换成【亿】【零万】换成【万】
  result = result.replace(/零亿/g, '亿').replace(/零万/g, '万')
  // 将【亿万】换成【亿】
  result = result.replace(/亿万/g, '亿')
  // 移除末尾的零
  result = result.replace(/零+$/, '')
  // 将【零一十】换成【零十】
  // result = result.replace(/零一十/g, '零十');//貌似正规读法是零一十
  // 将【一十】换成【十】
  result = result.replace(/^一十/g, '十')
  return result
}

/// 取出字符串的数字
// 定义一个函数用于提取字符串中的连续数字序列
export function extractNumbers(inputString: string) {
  // 正则表达式以匹配整数和小数（包括带有前导零的小数）
  const re = /(?:\d+\.\d+|\.\d+|\d+)/g
  // 使用 match 方法提取所有匹配的数字
  const matches = inputString.match(re)
  // 如果没有匹配，返回空数组
  return matches || []
}

// 时间戳转化为时间
export function formatDate(str) {
  const date = new Date(str)
  const year = date.getFullYear()
  const month = date.getMonth() + 1
  // month = month < 10 ? (`0${month}`) : month
  const day = date.getDate()
  // day = day < 10 ? (`0${day}`) : day
  // let h = date.getHours()
  // h = h < 10 ? (`0${h}`) : h
  // let m = date.getMinutes()
  // m = m < 10 ? (`0${m}`) : m
  // let s = date.getSeconds()
  // s = s < 10 ? (`0${s}`) : s
  return `${year}-${month < 10 ? (`0${month}`) : month}-${day < 10 ? (`0${day}`) : day}`
}

// 创建一个计算年龄的方法
export function calculateAge(birthday: string) {
  const today = new Date() // 获取今天的日期
  const birthDate = new Date(birthday) // 将输入的日期转换为Date对象
  const diff = today.getFullYear() - birthDate.getFullYear() // 计算年份差异
  const monthDiff = today.getMonth() - birthDate.getMonth() // 计算月份差异
  const dayDiff = today.getDate() - birthDate.getDate() // 计算日期差异
  const ageVal = diff + (monthDiff > 0 ? 1 : 0) + (dayDiff > 0 ? 0.5 : 0) // 根据差异计算年龄，如果出生日期还没到，则年龄减0.5
  return Math.floor(ageVal) // 将计算结果赋值给age变量，并取整数部分
}
