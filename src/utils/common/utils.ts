import { isNumber, isObject } from './is'

export function deepMerge<T = any>(src: any = {}, target: any = {}): T {
  let key: string
  for (key in target) {
    src[key] = isObject(src[key])
      ? deepMerge(src[key], target[key])
      : (src[key] = target[key])
  }

  return src
}

// dynamic use hook props
export function getDynamicProps<T extends {}, U>(props: T): Partial<U> {
  const ret: Record<string, any> = {}

  Object.keys(props).forEach((key) => {
    ret[key] = unref((props as Record<string, any>)[key])
  })

  return ret as Partial<U>
}

// 比例
export function formatRate(value: any) {
  if (!value)
    return '-'
  const newV = Number(value)
  if (isNumber(newV))
    return `${String(newV.toFixed(2))}%`

  return '-'
}

export function flattenChildren(arr: any) {
  return arr.reduce((result: any, item: any) => {
    const { children, ...rest } = item
    const newItem = { ...rest, isSelect: false }
    result.push(newItem)
    if (children)
      result = result.concat(flattenChildren(children))
    return result
  }, [])
}

export function compareArray(originals: string[], currents: string[]) {
  const removed = originals?.filter(item => !currents.includes(item))
  const added = currents?.filter(item => !originals.includes(item))

  return {
    removed,
    added,
  }
}

/// 返回性别 icon
export function sexIcon(sex: string) {
  switch (sex) {
    case '男':
      return 'slmc-icon-nan'
    case '女':
      return 'slmc-icon-nv'
    case '未知':
      return 'slmc-icon-xingbieweizhi'
    case 'male':
      return 'slmc-icon-nan'
    case 'female':
      return 'slmc-icon-nv'
    default:
      return 'slmc-icon-xingbieweizhi'
  }
}

/// 返回性别 icon(俩种 icon,我也不知道为啥有俩)
export function iconName(sex: string) {
  switch (sex) {
    case '男':
      return 'slmc-icon-male'
    case '女':
      return 'slmc-icon-female_'
    case '未知':
      return 'slmc-icon-unknown'
    default:
      return 'slmc-icon-unknown'
  }
}

/// 输入限制 正数
export function onlyAllowNumber(value: string) {
  if (value.length === 0)
    return true
  return /^[1-9]\d*$/.test(value)
}

/// / 添加长按事件
export const vLongPress = {
  beforeMount(el, binding) {
    // 设定长按的时间阈值，默认为 5 秒
    const duration = binding.arg || 5000
    let pressTimer = null

    // 开始长按事件处理
    const start = () => {
      if (pressTimer === null) {
        pressTimer = setTimeout(() => {
          // 执行绑定的回调函数
          binding.value()
        }, duration)
      }
    }

    // 取消长按事件处理
    const cancel = () => {
      if (pressTimer !== null) {
        clearTimeout(pressTimer)
        pressTimer = null
      }
    }

    // 添加事件监听器
    el.addEventListener('mousedown', start)
    el.addEventListener('touchstart', start)
    // 取消事件监听器
    el.addEventListener('mouseup', cancel)
    el.addEventListener('mouseleave', cancel)
    el.addEventListener('touchend', cancel)
  },
}
