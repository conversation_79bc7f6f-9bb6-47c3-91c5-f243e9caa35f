import { extractNumbers } from '@/utils/common/format'

/**
 * 取出检查数据的值(取出规范值,里面有各种科学计数法,以及低于检测,还有约等于,大于小于奇葩值的处理)
 */
export function getCheckDataValue(data: string, ref: string) {
  if (data.includes('低于检测')) {
    if (ref) {
      const refValue = extractNumbers(ref)
      console.log('检测值范围=======', refValue)
      console.log(refValue[0])
      return (Number.parseInt(refValue[0] || '0') - 1).toString()
    }
    else {
      return '29'
    }
  }
  else if (data.includes('E+')) {
    const str = data.split('E+')
    return (Number.parseFloat(str[0]) * 10 ** Number.parseInt(str[1])).toString()
  }
  else if (data.includes('×10E')) {
    const str = data.split('×10E')
    return (Number.parseFloat(str[0]) ** Number.parseInt(str[1])).toString()
  }
  else {
    if (extractNumbers(data).length > 0)
      return extractNumbers(data)[0]

    else
      return '-'
  }
}

/***
 * 判断 y 轴是否只有阴性阳性
 */
export function isOnlyYAxis(data: any, selectName: string) {
  ///  至少有个值是阴性阳性(+1:{value}阳性的意思)
  const res = data.some((item: any) => {
    const str: string = item[selectName] || ''
    return str === '阴性' || str === '阳性' || str.includes('+1:')
  })
  /// 每个值都是阴阳,或者是暂无检测,或者是空
  const onlyYAxis = data.every((item: any) => {
    const str: string = item[selectName] || ''
    return str === '阴性' || str === '阳性' || str.includes('+1:') || str === '低于检测限' || str === ''
  })

  return res && onlyYAxis
}

/**
 * 取到数组前后(连续的)的空值的 index,返回[10, 20]
 */
export function getEmptyIndex(data: any[]) {
  const firstIndex = data.findIndex((item: any) => item !== '-')

  const res: any[] = data.slice().reverse()
  const lastIndex = res.findIndex((item: any) => item !== '-')

  return [firstIndex - 1 || 0, data.length - lastIndex + 1]
}
