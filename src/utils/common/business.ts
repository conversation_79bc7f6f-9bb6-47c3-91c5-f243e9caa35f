interface MeldScoreParams {
  etiology: number
  TBIL: { result: string ; unit: string | null }
  INR: { result: string }
  CR: { result: string; unit: string | null }
}

/**
 *根据总胆红素结果计算分数
 * @param result TBIL结果值 总胆红素
 * @returns 分数
 */
export function getTBILScore(result: string | number) {
  try {
    const scoreValue = typeof result === 'string' ? Number.parseFloat(result) : result
    if (scoreValue < 34)
      return 1

    else if (scoreValue >= 34 && scoreValue <= 51)
      return 2

    else if (scoreValue > 51)
      return 3

    return 0
  }
  catch (error) {
    console.error(error)
    return 0
  }
}

/**
  *根据凝血酶原时间延长结果计算分数
  * @param result PT结果值 凝血酶原时间延长 -11.2
  * @returns 分数
  */
export function getPTScore(result: string | number) {
  //  这个值等于凝血酶原时间减去凝血酶原时间正常对照(RPT)，rpt按11.2s算，也就是说=pt-11.2
  try {
    const scoreValue = typeof result === 'string' ? Number.parseFloat(result) : result

    if (scoreValue < 4)
      return 1

    else if (scoreValue >= 4 && scoreValue <= 6)
      return 2

    else if (scoreValue > 6)
      return 3

    return 0
  }
  catch (error) {
    console.error(error)
    return 0
  }
}

/**
  *根据白蛋白结果计算分数
  * @param result ALB结果值 白蛋白
  * @returns 分数
  */
export function getALBScore(result: string | number) {
  try {
    const scoreValue = typeof result === 'string' ? Number.parseFloat(result) : result

    if (scoreValue < 28)
      return 3

    else if (scoreValue >= 28 && scoreValue <= 35)
      return 2

    else if (scoreValue > 35)
      return 1

    return 0
  }
  catch (error) {
    console.error(error)
    return 0
  }
}

/**
 *根据child分数计算child评级
 * @param total child-pugh总分
 * @returns child-pugh评级
 */
export function getScoreRate(total: number) {
  if (total >= 5 && total <= 6)
    return 'A'

  else if (total >= 7 && total <= 9)
    return 'B'

  else if (total >= 10 && total <= 15)
    return 'C'

  return '-'
}
/**
 * meld评分中，TBIL需要根据单位进行分数计算
 * @param params {result:结果值，unit:单位}
 * @returns 分数
 */
function getTBILByUnit(params: { result: string; unit: string | null }) {
  const { result, unit } = params

  const TBILResult = ['umol/L', 'μmol/L'].includes(unit!) ? (Number.parseFloat(result) / 17.1) : Number.parseFloat(result)

  const score = 3.8 * Math.log(TBILResult)

  // 为防止评分出现负数，任何小于1的数值默认为1
  return score < 1 ? 1 : score
}
/**
 * meld评分，根据公式获取INR
 * @param params {result:结果值}
 * @returns 分数
 */
function getINRScore(params: { result: string }) {
  const { result } = params
  const score = 11.2 * Math.log(Number.parseFloat(result))
  // 为防止评分出现负数，任何小于1的数值默认为1
  return score < 1 ? 1 : score
}

/**
 * meld评分中，CR需要根据单位进行分数计算
 * @param params {result:结果值，unit:单位}
 * @returns 分数
 */
function getCRByUnit(params: { result: string; unit: string | null }) {
  const { result, unit } = params

  const CRResult = ['umol/L', 'μmol/L'].includes(unit!) ? (Number.parseFloat(result) / 88.4) : Number.parseFloat(result)

  const score = 9.6 * Math.log(CRResult)

  // 为防止评分出现负数，任何小于1的数值默认为1
  return score < 1 ? 1 : score
}

/**
 *计算meld评分
 * @param params 病因，TBIL,INR,CR
 * @returns meld评分
 */
export function getMeldScore(params: MeldScoreParams) {
  try {
    const { TBIL, INR, CR, etiology } = params

    const TBILScore = getTBILByUnit(TBIL)
    const INRScore = getINRScore(INR)
    const CRScore = getCRByUnit(CR)
    const etiologyScore = 6.4 * etiology
    console.log(TBILScore, INRScore, CRScore, etiologyScore)

    const totalScore = (TBILScore + INRScore + CRScore + etiologyScore).toFixed(1)

    return Number.parseFloat(totalScore)
  }
  catch (error) {
    console.error(error)
    return 0
  }
}
