import { ComponentCollection } from 'survey-core'

ComponentCollection.Instance.add({
  name: 'timeunit',
  title: '时间单位',
  elementsJSON: [
    {
      type: 'text',
      inputType: 'number',
      name: 'time',
      title: '时长',
      isRequired: true,
      minWidth: 200,
      min: 0,
      max: 10000,
    },
    {
      type: 'dropdown',
      name: 'unit',
      title: '单位',
      isRequired: true,
      minWidth: 200,
      startWithNewLine: false,
      choices: [
        {
          value: 'day',
          text: '天',
        },
        {
          value: 'week',
          text: '周',
        },
        {
          value: 'month',
          text: '月',
        },
        {
          value: 'year',
          text: '年',
        },
      ],
    },
  ],
})

ComponentCollection.Instance.add({
  name: 'monthyear',
  title: '年月填写',
  elementsJSON: [
    {
      type: 'text',
      inputType: 'number',
      name: 'year',
      title: '年',
      isRequired: true,
      minWidth: 200,
      min: 0,
      max: 10000,
    },
    {
      type: 'text',
      inputType: 'number',
      title: '月',
      isRequired: true,
      minWidth: 200,
      startWithNewLine: false,
    },
  ],
})
