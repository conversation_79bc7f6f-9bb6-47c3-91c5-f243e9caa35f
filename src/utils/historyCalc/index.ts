import dayjs from 'dayjs'
import { once } from 'lodash-es'

const diseaseTextMap = {
  肝病病史: '',
  肝病家族史: '',
  常见慢性病及肿瘤史: '',
  个人史: '',
  重大传染病及精神疾病史: '',
  // 体格检查: '',
  // 婚育史: '',
  // 过敏史: '',
}

export function clear() {
  diseaseTextMap.肝病病史 = ''
  diseaseTextMap.肝病家族史 = ''
  diseaseTextMap.常见慢性病及肿瘤史 = ''
  diseaseTextMap.个人史 = ''
  diseaseTextMap.重大传染病及精神疾病史 = ''
}

/// 病史计算
export function handleTheSelectedDiseaseText(array) {
  // 清空
  once(clear)
  const sarray = unref(array)
  if (!sarray)
    return
  for (let index = 0; index < sarray.length; index++) {
    const element = sarray[index]
    if (element.selected || element.topTitle === '体格检查') {
      // 到最后一层
      if (element.children)
        handleTheSelectedDiseaseText(element.children)
      else cacleTheTimeOfsbhistory(element)
    }
  }

  return diseaseTextMap
}

/// 计算这个傻逼病史选择的时间
function cacleTheTimeOfsbhistory(item) {
  /// 进来就是 已经选中的了
  let jb = item.title
  switch (item.topTitle) {
    case '肝病病史':
    case '常见慢性病及肿瘤史': {
      if (item.startTime) {
        if (item.title === '其他' && item.type === 'multiple') { jb = item.selectTags?.join('、') ?? '' }
        else {
          jb
              = (item.title === '其他肝病' || item.title === '其他')
              ? item.otherInput
              : item.title
        }
        jb += item.title === '乙肝' ? '患病' : '确诊'
        // 计算时长
        item.check = true
        const sc = dayjsDiffTimeSB(item.startTime)
        if (sc.length > 0 && !item.useDate) {
          jb += `${sc}`
        }
        else {
          if (item.title === '其他' && item.type === 'multiple') {
            const tag = item.selectTags?.join('、')
            jb
                = tag?.length > 0
                ? `${dayjs(item.startTime).format(
                    'YYYY年MM',
                  )}确诊${tag}`
                : ''
          }
          else {
            jb = `${dayjs(item.startTime).format('YYYY年MM')}确诊${item.title === '其他肝病'
                  ? item.otherInput
                  : item.title
                }`
          }
        }
        /// 有治疗方案的++
        const zl = treatFangAnString(item)
        jb += `${zl.length > 0
              ? `,${zl}\n`
              : item.title === '乙肝'
                ? ''
                : ';\n'
            }`
      }
      else {
        if (item.title === '其他' && item.type === 'multiple') { jb = item.selectTags?.join('、') ?? '' }
        else {
          jb
              = item.title === '其他肝病'
              ? item.otherInput
              : item.title
        }
        if (item.title !== '乙肝') {
          /// 有治疗方案的++
          const zl = treatFangAnString(item)
          jb += `${zl.length > 0
                ? `,${zl}\n`
                : item.title === '乙肝'
                  ? ''
                  : ';\n'
              }`
        }
        item.check = false
      }
      if (jb === ';\n')
        jb = ''
        /// 看看乙肝有没有 药品
      if (item.title === '乙肝') {
        const bb = handleTheUseMedicalString(item.medical, [], item)
        console.log(bb)
        const stt = bb.join(',')
        jb += `${stt?.length > 0 ? `,${stt};\n` : ';\n'}`
      }
      break
    }
    case '肝病家族史':
      if (item.chooses?.length > 0) {
        const qs = item.chooses.join('、')
        jb = `${qs}患有${item.title};\n`
        item.check = true
      }
      else {
        jb += ';\n'
      }
      break
    case '个人史':
      if (item.type === 'yj') {
        jb = `饮${item?.yjzl?.length > 0 ? item?.yjzl : '酒'
            }${dayjsDiffTimeSB(item.startTime, item.endTime)}${item?.pjmrycsrl > 0
              ? `,平均每日乙醇摄入${item.pjmrycsrl}g`
              : ''
            }${item.jj
              ? item.endTime
                ? `,于${dayjs(item.endTime).format(
                  'YYYY年MM',
                )}月戒酒`
                : ',已戒'
              : item.jj == null
                ? ''
                : ',未戒'
            };\n`
        item.check = item?.yjzl?.length > 0 && item?.pjmrycsrl > 0
      }
      else if (item.type === 'xy') {
        jb = `吸烟${dayjsDiffTimeSB(item.startTime, item.endTime)}${item?.pjmrxyl?.length > 0
              ? `,平均每日${item.pjmrxyl}根`
              : ''
            }${item.jy
              ? item.endTime
                ? `,于${dayjs(item.endTime).format(
                  'YYYY年MM',
                )}月戒烟`
                : ',已戒'
              : item.jy == null
                ? ''
                : ',未戒'
            };\n`
        item.check = item?.pjmrxyl?.length > 0
      }
      else if (item.type === 'ysj') {
        /// 傻逼
        jb = `服用味乐舒${dayjsDiffTimeSB(item.startTime, item.endTime)}${item.yfyl ? `,每天${item.yfyl}条` : ''}${item.stop
            ? `,于${dayjs(item.endTime).format(
              'YYYY年MM',
            )}月停用`
            : ''}`
      }
      break
    case '重大传染病及精神疾病史':
      if (item.startTime) {
        jb = `曾在${dayjs(item.startTime).format('YYYY年MM')}月确诊${item.title
            };\n`
        item.check = true
      }
      else {
        jb += ';\n'
      }
      break
    case '肝硬化':
      /// 处理肝硬化
      if (item?.title?.includes('肝硬化')) {
        jb = item.startTime
          ? `于${dayjs(item.startTime).format('YYYY年MM')}月确诊${item.title
            };\n`
          : `${item.title};\n`
        item.check = item.startTime != null
      }
      else {
        /// 并发症
        jb = item.startTime
          ? `于${dayjs(item.startTime).format('YYYY年MM')}月首次出现${item.title === '其他并发症状'
              ? item.otherInput
              : item.title
            }`
          : `出现${item.title === '其他并发症状'
              ? item.otherInput
              : item.title
            }`
        item.check = item.startTime != null
        /// 查看治疗方式
        const zl = treatFangAnString(item)
        jb += `${zl.length > 0 ? `,${zl}\n` : ';\n'}`
        if (
          item.title === '其他并发症状'
            && item?.otherInput?.trim().length === 0
        )
          jb = ''
      }
      break
    case '体格检查':
      jb = `${item.title.slice(0, item.title.length - 2)}${item.selected ? '有' : '未见'}${item.title.slice(item.title.length - 2)};\n`
      break
    case '婚育史':
      jb = item.chooses || ''
      break
    case '过敏史':
      jb = item?.content?.length > 0 ? `${item.content}过敏` : ''
      break
    default:
      break
  }
  /// 处理下肝硬化
  const key = item.topTitle === '肝硬化' ? '肝病病史' : item.topTitle
  /// 如果是肝硬化
  if (diseaseTextMap[key])
    diseaseTextMap[key] += jb
  else diseaseTextMap[key] = jb
}

/// 治疗方式字符串拼接
function treatFangAnString(item) {
  let temp = ''
  if (item.treats && item.treats?.length > 0) {
    item.treats.forEach((element) => {
      let sb = '有进行'
      if (element.type === 'radio') {
        /// 单选
        if (element?.zlfn?.length > 0 && element?.zlfn !== '无') {
          sb += `${element.zlfn === '有' ? '' : `${element.zlfn}的`}${element.title
              };`
        }
        else { sb = '' }
      }
      else {
        /// 多选
        if (element?.zlfn?.length > 0)
          sb += `${element.zlfn.join('、')}的${element.title};`
        else sb = ''
      }
      temp += sb
    })
  }
  return temp.length > 0 ? temp : ''
}

function dayjsDiffTimeSB(start, end?: any) {
  if (end == null)
    end = dayjs()
  else end = dayjs(end)
  const diffMon = end.diff(dayjs(start), 'month')
  const year = Math.trunc(diffMon / 12)
  const month = diffMon % 12

  return `${year === 0 ? '' : `${year}年`}${month === 0 ? '' : `${month}个月`
      }`
}

function handleTheUseMedicalString(medical, array, parentNode) {
  if (medical == null)
    return array
  for (let index = 0; index < medical.length; index++) {
    const element = medical[index]
    if (element.children?.length > 0 && element.select) {
      handleTheUseMedicalString(element.children, array, element)
    }
    else {
      if (element.select) {
        if (element.type === 'other') {
          array.push(
              `${element.drugInput == null
                || element?.drugInput?.trim()?.length === 0
                ? ''
                : `${element?.drugInput}`
              }${element.times == null
                || element?.times?.trim()?.length === 0
                ? ''
                : `${element?.times}年`
              }${element.stop ? '已停药' : ''}`,
          )
        }
        else {
          let sttr = `${element.startTime
                ? `${dayjs(element.startTime).format(
                  'YYYY年MM',
                )}月开始服用${element.drugName}${element.endTime && element.stop
                  ? dayjsDiffTimeSB(
                    element.startTime,
                    element.endTime,
                  )
                  : `至今${dayjsDiffTimeSB(
                    element.startTime,
                    element.endTime,
                  )}`
                }`
                : `服用${element.drugName}`
              }${element.endTime && element.stop
                ? `,于${dayjs(element.endTime).format(
                  'YYYY年MM',
                )}月停药`
                : ''
              }`
          if (element.drugName === 'Peg-IFN')
            sttr = sttr.replace('服用', '使用')
          if (element.drugName === '胸腺法新')
            sttr = sttr.replace('服用', '注射')
          array.push(sttr)
        }
        /// 看看填写是否完整
        if ((element.stop && element.endTime) || element?.startTime)
          parentNode.check = true
        else parentNode.check = false
      }
    }
  }

  return array
}
