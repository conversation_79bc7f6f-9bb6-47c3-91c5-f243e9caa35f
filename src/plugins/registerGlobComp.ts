import type { App } from 'vue'
import {
  // create naive ui
  create,
  // component
  // eslint-disable-next-line sort-imports
  NButton,
  NLayout,
  NLayoutHeader,
  NLayoutContent,
  NLayoutFooter,
  NDropdown,
  NSpin,
  NMessageProvider,
  NMenu,
  NIcon,
  NBreadcrumb,
  NBreadcrumbItem,
  NInput,
  NDatePicker,
  NSelect,
  NTree,
  NConfigProvider,
  NModal,
  NForm,
  NFormItem,
  NDataTable,
  NNotificationProvider,
  NCheckbox,
  NCheckboxGroup,
  NRadio,
  NRadioGroup,
  NSpace,
  NUpload,
  NDivider,
  NSkeleton,
  NSteps,
  NStep,
  NDialogProvider,
  NEllipsis,
  NTabs,
  NTabPane,
  NDescriptions,
  NDescriptionsItem,
} from 'wowjoy-vui'

// import { JoySearch, JoySearchBtn } from './SearchForm'
import { SvgIcon } from '@/components/Icon'

// import PageHeader from '@/components/PageHeader/index.vue'

const compList = [SvgIcon]
const vui = create({
  components: [
    NButton,
    NLayout,
    NLayoutHeader,
    NLayoutContent,
    NLayoutFooter,
    NDropdown,
    NSpin,
    NMessageProvider,
    NMenu,
    NBreadcrumb,
    NBreadcrumbItem,
    NIcon,
    NInput,
    NDatePicker,
    NSelect,
    NTree,
    NConfigProvider,
    NModal,
    NForm,
    NFormItem,
    NDataTable,
    NNotificationProvider,
    NCheckbox,
    NCheckboxGroup,
    NRadio,
    NRadioGroup,
    NSpace,
    NUpload,
    NDivider,
    NSkeleton,
    NSteps,
    NStep,
    NDialogProvider,
    NEllipsis,
    NTabs,
    NTabPane,
    NDescriptions,
    NDescriptionsItem,
  ],
})

export function registerGlobComp(app: App): void {
  compList.forEach((comp) => {
    app.component(comp.name || comp.displayName, comp)
  })
  app.use(vui)
}
