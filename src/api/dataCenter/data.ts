import type { CustomExportDataParams, ExportDataParams } from './type'
import http from '@/service'

const PREFIX = '/heper-api'

export enum patientAPI {
  patientListURL = `${PREFIX}/patientData/patient/patientList`,
  patientNameListURL = `${PREFIX}/patientData/patient/patientNameList`,
  doctorNameListURL = `${PREFIX}/manage/user/pageList`,
  planNameListURL = `${PREFIX}/api/hma/follow/v1/planTemplates/getAllPlanTmp`,
  ExportDataURL = `${PREFIX}/exportData/export`,
  GetSmsCodeURL = `${PREFIX}/exportData/getSmsCode`,
  dataField = `${PREFIX}/exportData/fields`,
  records = `${PREFIX}/exportData/records`,
  customExport = `${PREFIX}/exportData/c_export`,
  download = `${PREFIX}/exportData/down/file`,
  reDownload = `${PREFIX}/exportData/reDownload`,
}

/**
 * 获取患者列表数据
 */
export function getPatientList<T>(params: any) {
  return http.get<T>({
    url: `${patientAPI.patientListURL}`,
    params,
  })
}

/**
 * 获取筛选框医生数据
 */
export function getDoctorNameList<T>(params: any) {
  return http.get<T>({
    url: `${patientAPI.doctorNameListURL}`,
    params,
  })
}

/**
 * 获取筛选框患者数据
 */
export function getPatientNameList<T>(params: any) {
  return http.get<T>({
    url: `${patientAPI.patientNameListURL}`,
    params,
  })
}

/**
 * 获取随访方案数据
 */
export function getPlanNameList<T>() {
  return http.get<T>({
    url: `${patientAPI.planNameListURL}`,
  })
}

/**
 * 导出数据
 */
export function exportExcelApi<T>(params: ExportDataParams) {
  return http.post<T>({
    url: `${patientAPI.ExportDataURL}`,
    data: params,
  })
}

/**
 * 获取导出验证码
 */
export function getSmsCodeApi<T>(phone: string) {
  return http.get<T>({
    url: `${patientAPI.GetSmsCodeURL}/${phone}`,
  })
}

/**
 * 导出字段
 */
export function exportDataFieldsApi(name: string) {
  return http.get<Node[]>({
    url: `${patientAPI.dataField}`,
    params: {
      name,
    },
  })
}

/**
 * 导出记录
 */
export function exportDataRecordsApi<T>(params: any) {
  return http.get<T>({
    url: `${patientAPI.records}`,
    params,
  })
}

export function customExportApi<T>(data: CustomExportDataParams) {
  return http.post<T>({
    url: `${patientAPI.customExport}`,
    data,
  })
}

export function reDownloadApi<T>(recordId: string, userId: string) {
  return http.get<T>({
    url: `${patientAPI.reDownload}`,
    params: {
      userId,
      recordId,
    },
  })
}

/**
 *  知识图谱
 */
export function getAllEntitys(eid: string) {
  return http.get({
    url: '/heper-api/omaha/all',
    params: {
      value: eid,
    },
  })
}

export function queryEntitys(eid: string) {
  return http.get({
    url: '/heper-api/omaha/query',
    params: {
      eid,
    },
  })
}

export function searchEntitys(text: string) {
  return http.get({
    url: '/heper-api/omaha/search',
    params: {
      text,
    },
  })
}
