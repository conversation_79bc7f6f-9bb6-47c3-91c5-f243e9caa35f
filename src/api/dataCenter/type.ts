export interface ExportDataParams {
  dataRange: string
  exportWay: string
  patientList: string[]
  technique: string
}

export interface CustomExportDataParams {
  code: string
  dataRange: string
  fieldIds: string[]
  patientList: string[]
  phone: string
  userId: string
  userName: string
  allExport: boolean
  searchParam: string
}

export interface FieldNode {
  name: string
  id: string
  children?: Node[]
}
