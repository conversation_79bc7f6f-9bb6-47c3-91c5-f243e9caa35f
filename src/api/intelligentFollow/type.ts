export interface Response<T> {
  msg: string
  results: T
  state: number
  subCode: string
  subMsg: string
  total: number
}
/**	推荐随访方案 */
export interface SuggestPlan {
  additionalProp1: string
  additionalProp2: string
  additionalProp3: string
}
/** 患者随访查询列表 */
export interface Record {
  age: string
  alarmGive: string
  appUser: boolean
  brithDay: string
  department: string
  diagnosis: string
  followMethod: string
  manageDoctorId: string
  manageDoctorName: string
  overdue: string
  patientId: string
  patientNm: string
  patientTag: string[]
  phone: string
  planId: string
  planState: string
  planTemplateId: string
  planTemplateName: string
  recordId: string
  sexName: string
  slmcNo: string
  suggestPlan: SuggestPlan
  taskList: string[]
  visitDate: string
}
/** 患者随访查询 */
export interface PlanPatient {
  current: number
  pages: number
  records: Record[]
  size: number
  total: number
}
/** 随访患者查询 */
export interface PatientFollowModal {
  followedUp: number
  overdue: number
  planPatient: PlanPatient
  revisited: number
  waitEnteringGroup: number
  waitFollowUp: number
  waitReturnVisit: number
}

/** 患者随访查询 */
export interface PatientFollowParams {
  alarmGive?: number
  authPlanTmpIdList?: any[]
  manageDoctor?: string
  manageDoctorList?: any[]
  operateId: string
  organId?: string
  page?: number
  patientNm?: string
  patientNmList?: any[]
  planState?: any[]
  planTemplateId?: string
  queryType?: string
  sexName?: string
  size?: number
}
