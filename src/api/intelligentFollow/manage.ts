import type { PatientFollowParams } from './type'
import http from '@/service'
import type { FollowPlan } from '@/views/intelligentFollow/type'

export function getPlanListApi(params: {
  search: string
  operateId: string
}) {
  return http.get<any[]>({
    url: '/heper-api/api/hma/follow/v1/planTemplates/queryPlanTemplate',
    params,
  })
}

export function getPlanListApi2(params: {
  search: string
  operateId: string
}) {
  return http.get<any[]>({
    url: '/heper-api/api/hma/follow/v1/planTemplates/hmaPlanTemplate',
    params,
  })
}

export function getPatientFollowList(data) {
  return http.post({
    url: '/heper-api/api/hma/follow/v1/follow/waitFollow',
    data,
  })
}

export function getFollowWorkbenchData(data) {
  return http.post({
    url: '/heper-api/api/hma/follow/v1/follow/staging',
    data,
  })
}

export function getFollowPatientData(data) {
  return http.post({
    url: '/heper-api/api/hma/follow/v1/follow/agency',
    data,
  })
}

export function deletePlanApi(params: {
  planTmpId: string
  operateId: string
}) {
  return http.get({
    url: '/heper-api/api/hma/follow/v1/planTemplates/deletePlanTmp',
    params,
  })
}

export function getPlanDetailApi(planTmpId: string) {
  return http.get<FollowPlan>({
    url: '/heper-api/api/hma/follow/v1/planTemplates/getPlanTmpById',
    params: {
      planTmpId,
    },
  })
}

export function editPlanDetailApi(data: FollowPlan) {
  return http.post({
    url: '/heper-api/api/hma/follow/v1/planTemplates/editPlanTmp',
    data,
  })
}

export function createPlanApi(data: any) {
  return http.post<string>({
    url: '/heper-api/api/hma/follow/v1/planTemplates/addPlanTmp',
    data,
  })
}

export function updatePlanStateAPI(params: {
  planTmpId: string
  tmpState: number
}) {
  return http.get<boolean>({
    url: '/heper-api/api/hma/follow/v1/planTemplates/updatePlanState',
    params,
  })
}

export function saveToNewTemplate(data: any) {
  return http.post({
    url: '/heper-api/api/hma/follow/v1/planTemplates/saveNewMuBan',
    data,
  })
}

/// 随访下患者管理 (患者名)
export function getPatientSelects(data: any) {
  return http.post<any>({
    url: '/heper-api/api/hma/follow/v1/planPatient/queryPlanPatientName',
    data,
  })
}

/// 随访下患者管理 (管理医生)
export function getMangeDoctSelects(data: any) {
  return http.post<any>({
    url: '/heper-api/api/hma/follow/v1/planPatient/queryPlanDoctorName',
    data,
  })
}

/// 查询 随访下患者管理数据
export function queryFollowPlanPatients(data: any) {
  return http.post({
    url: '/heper-api/api/hma/follow/v1/planPatient/queryPlanPatient',
    data,
  })
}

/// 移除随访
export function removeFollowPlanPatient(data: any) {
  return http.post({
    url: '/heper-api/api/hma/follow/v1/planPatient/removePlan',
    data,
  })
}

/// 导入患者, 患者名查询
export function queryImportPatientsNames(patientNm: string) {
  return http.post<any>({
    url: '/heper-api/api/hma/follow/v1/planPatient/queryImportPatientName',
    data: {
      patientNm,
      size: 10000,
    },
  })
}

/// 导入患者, 管理医生查询
export function queryImportDoctorsNames(params: any) {
  return http.get<any>({
    url: '/heper-api/dic/doctor/doctorList',
    params,
  })
}

/// 导入患者列表查询
export function queryImportPatients(data: any) {
  return http.post<any>({
    url: '/heper-api/api/hma/follow/v1/planPatient/queryImportPatientList',
    data,
  })
}

/// 批量导入患者
export function batchImportPatients(data: any) {
  return http.post({
    url: '/heper-api/api/hma/follow/v1/plan/batchIntoPlan',
    data,
  })
}

export function checkPlanTempNameAPI(params: {
  planTempName: string
  planTmpId?: string
}) {
  return http.get<boolean>({
    url: '/heper-api/api/hma/follow/v1/planTemplates/checkPlanTempName',
    params,
  })
}

export function getOwnTemplate(operateId: string) {
  return http.get<string[]>({
    url: '/heper-api/api/hma/follow/v1/planTemplates/queryMyMuBan',
    params: {
      operateId,
    },
  })
}

export function getCommonTemplate(operateId: string) {
  return http.get<Record<string, any[]>>({
    url: '/heper-api/api/hma/follow/v1/planTemplates/queryCommonMuBan',
    params: {
      operateId,
    },
  })
}

export function getLabelByTypeAPI(category: string) {
  return http.get<any[]>({
    url: '/heper-api/api/hma/follow/v1/label/tree',
    params: {
      category,
    },
  })
}

export function getSpecPlanAPI(params: {
  queryName: string
}) {
  return http.get<any[]>({
    url: '/heper-api/api/hma/follow/v1/specPlan/querySpecPlan',
    params,
  })
}

// 旧的
export function getFollowPatientAPI<T>(params: PatientFollowParams) {
  return http.post<T>({
    url: '/heper-api/api/hma/follow/v1/planPatient/patientPlanList',
    data: params,
  })
}

// 2024-10-29 新的 liukun
export function getFollowPatientNewAPI<T>(params: PatientFollowParams) {
  return http.post<T>({
    url: '/heper-api/api/hma/follow/v1/follow/waitFollow',
    data: params,
  })
}

export function getMedicalProject(params: {
  type: 'INSPECT' | 'IMAGE'
  projectName: string
}) {
  return http.get<any[]>({
    url: '/heper-api/api/hma/common/v1/medical-project/list',
    params,
  })
}

export function getMedicalProjectItemAPI(itemName: string) {
  return http.get<any[]>({
    url: '/heper-api/api/hma/common/v1/medical-project-item/list',
    params: {
      itemName,
    },
  })
}

export function addToPlanAPI(data: {
  operatorId: string
  operatorName: string
  createId: string
  createName: string
  patientId: string
  planTmpId: string
}) {
  return http.post({
    url: '/heper-api/api/hma/follow/v1/plan/addIntoPlan',
    data,
  })
}

export function getCheckDataAPI(taskId: string) {
  return http.get<any>({
    url: `/heper-api/api/hma/follow/v1/task/check/${taskId}`,
  })
}

export function getInspectDataAPI(taskId: string) {
  return http.get<any>({
    url: `/heper-api/api/hma/follow/v1/task/inspect/${taskId}`,
  })
}

export function setHmaAPI(params: {
  planTmpId: string
  operatorId: string
  isHma: boolean
}) {
  return http.get<boolean>({
    url: '/heper-api/api/hma/follow/v1/planTemplates/setHma',
    params,
  })
}

export function refuseFollowAPI(data: any) {
  return http.post<boolean>({
    url: '/heper-api/api/hma/follow/v1/refuse/save',
    data,
  })
}

// AI电话
export function aiccAPI(params: {
  patientId: string
  planPhaseId: string
}) {
  return http.get<boolean>({
    url: '/heper-api/api/hma/aicc/v1/call/manualCallReviewReminder',
    params,
  })
}
