import http from '@/service'

// 新增随访记录
export function addFollowRecordInfo(data: any) {
  return http.post({
    url: '/heper-api/api/hma/follow/v1/recordInfo/add',
    data,
  })
}

// 获取随访记录列表
export function getFollowRecordList(params: {
  patientId: string
}) {
  return http.get<any[]>({
    url: '/heper-api/api/hma/follow/v1/recordInfo/getRecordList',
    params,
  })
}

// 获取随访记录详情
export function getFollowRecordDetail(params: {
  recordId: string
}) {
  return http.get<any[]>({
    url: '/heper-api/api/hma/follow/v1/recordInfo/getRecordDetail',
    params,
  })
}

// 修改随访记录
export function updateFollowRecordInfo(data: any) {
  return http.post({
    url: '/heper-api/api/hma/follow/v1/recordInfo/upd',
    data,
  })
}

// 变更任务为已完成
export function finishFollowRecord(params: {
  recordId: string
}) {
  return http.get<any[]>({
    url: '/heper-api/api/hma/follow/v1/recordInfo/changeStatus',
    params,
  })
}

// 删除任务
export function deleteFollowRecord(params: {
  recordId: string
}) {
  return http.get<any[]>({
    url: '/heper-api/api/hma/follow/v1/recordInfo/dtl',
    params,
  })
}
