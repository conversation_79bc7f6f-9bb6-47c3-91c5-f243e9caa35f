import http from '@/service'

// 获取患者计划的阶段及任务信息
export function getPlanPhaseAPI(id: string) {
  return http.get<any[]>({
    url: `/heper-api/api/hma/follow/v1/plan-phase/list/${id}`,
  })
}

// 获取患者的当前随访计划
export function getPlanByPatientid<T>(params: any) {
  return http.get<T>({
    url: '/heper-api/api/hma/follow/v1/plan/getPlanByPatientId',
    params,
  })
}

// 获取一键随访的计划
export function queryOneFollow<T>(params: any) {
  return http.get<T>({
    url: '/heper-api/api/hma/follow/v1/planTemplates/queryOneFollow',
    params,
  })
}

export function addToPlan<T>(data: any) {
  return http.post<T>({
    url: '/heper-api/api/hma/follow/v1/plan/addIntoPlan',
    data,
  })
}

/// 随访工作台

// 获取患者标签列表
export function getFollowMarks(search?: string) {
  return http.get({
    url: '/heper-api/api/hma/follow/v1/mark/list',
    params: { search },
  })
}

// 获取统计信息
export function getFollowSearchAnysis(data) {
  return http.post({
    url: '/heper-api/api/hma/follow/v1/staging/stagingCount',
    data,
  })
}

// 获取随访记录
export function getFollowRecords(data) {
  return http.post({
    url: '/heper-api/api/hma/follow/v1/staging/stagingList',
    data,
  })
}

/// 新增患者标签
export function addNewPatientMark(markName) {
  return http.post({
    url: '/heper-api/api/hma/follow/v1/mark/saveUpdate',
    data: {
      markName,
    },
  })
}

/// 更新患者标签
export function updatePatientMarks(data) {
  return http.post({
    url: '/heper-api/api/hma/follow/v1/patientMark/saveUpdate',
    data,
  })
}

/// 查询患者标签
export function getPatientMarks(patientId) {
  return http.get({
    url: '/heper-api/api/hma/follow/v1/patientMark/getByPatientId',
    params: {
      patientId,
    },
  })
}
