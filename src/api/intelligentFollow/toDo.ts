import http from '@/service'

// 获取患者计划的阶段及任务信息
export function getFollowTODOList(data: any) {
  return http.post({
    url: '/heper-api/api/hma/follow/v1/planPatient/patientPlanList',
    data,
  })
}

// 获取患者计划的阶段及任务信息
export function getFollowTODOList2(data: any) {
  return http.post({
    url: '/heper-api/api/hma/follow/v1/planPatient/followStating',
    data,
  })
}

/// / 获取随访统计相关的数据
export function getTheFollowTongJiCount(data) {
  return http.post({
    url: '/heper-api/api/hma/follow/v1/planPatient/followCount',
    data,
  })
}
