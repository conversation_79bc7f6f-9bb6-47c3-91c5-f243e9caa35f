export interface Response<T> {
  msg: string
  results: T
  state: number
  subCode: string
  subMsg: string
  total: number
}

export interface CreateOrganParams {
  organDesc: string
  organGroup: string | null
  organName: string
  organType: string | null
  status?: number
}

export interface GetOrganParams {
  size: number
  start: number
  searchName?: string
  level?: number
}

export interface OrganizationRes {
  id: string
  parentId: string
  organName: string
  organGroup: null | string
  organType: null | string
  organDesc: null | string
  status: null | string
  children: OrganizationRes[]
  organLevel: number

}
