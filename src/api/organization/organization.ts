import type { CreateOrganParams, GetOrganParams } from './type'
import http from '@/service'

const PREFIX = '/heper-api'

enum Organ {
  Insert = `${PREFIX}/manage/organ/insert`,
  Get = `${PREFIX}/manage/organ/list`,
  Delete = `${PREFIX}/manage/organ/delete`,
  UpdateSort = `${PREFIX}/manage/organ/update`,
  OrganDataList = `${PREFIX}/manage/organ/dataList`,
  SearchOrgan = `${PREFIX}/manage/organ/search`,


}

// 新增机构
export function createOrganApi<T>(data: CreateOrganParams) {
  return http.post<T>({
    url: `${Organ.Insert}`,
    data,
  })
}

// 获取机构
export function getOrganApi<T>(data: GetOrganParams) {
  return http.get<T>({
    url: `${Organ.Get}`,
    params: data,
  })
}

/// 获取机构
export function getSbOrganList(params: any = {}) {
  return http.get({
    url: `${PREFIX}/manage/organ/dataList`,
    params,
  })
}

// 删除机构
export function deleteOrganApi<T>(id: string) {
  return http.delete<T>({
    url: `${Organ.Delete}/${id}`,

  })
}
// 更新机构排序
export function updateSortApi<T>(data: any) {
  return http.put<T>({
    url: `${Organ.UpdateSort}`,
    data,
  })
}
// 获取机构-平铺展开
export function getOrganDataListApi<T>() {
  return http.get<T>({
    url: `${Organ.OrganDataList}`,

  })
}
// 搜索机构-分类展示
export function searchOrganListApi<T>(params: { searchName?: string }) {
  return http.get<T>({
    url: `${Organ.SearchOrgan}`,
    params,
  })
}
