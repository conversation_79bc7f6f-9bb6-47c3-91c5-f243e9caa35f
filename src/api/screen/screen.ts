import http from '@/service'

const PREFIX = '/heper-api'

export function newPatientCount<T>(data: any) {
  return http.post<T>({
    url: `${PREFIX}/dataScreen/allPatientCount`,
    data,
  })
}

export function ageAndSexCount<T>(data: any) {
  return http.post<T>({
    url: `${PREFIX}/dataScreen/ageAndSexCount`,
    data,
  })
}

export function newPlanPatientCount<T>(data: any) {
  return http.post<T>({
    url: `${PREFIX}/dataScreen/newPlanPatientCount`,
    data,
  })
}

export function planPatientCount<T>(data: any) {
  return http.post<T>({
    url: `${PREFIX}/dataScreen/newPatientCount`,
    data,
  })
}

export function diseasesCount<T>(data: any) {
  return http.post<T>({
    url: `${PREFIX}/dataScreen/diseases`,
    data,
  })
}

export function rateCount<T>(data: any) {
  return http.get<T>({
    url: `${PREFIX}/api/slmc/follow/v1/count/rate`,
    params: data,
  })
}

export function getMapData<T>(data: any) {
  return http.post<T>({
    url: `${PREFIX}/dataScreen/patientCountByProvince`,
    data,
  })
}

export function getCountIndex<T>(data: any) {
  return http.get<T>({
    url: `${PREFIX}/api/slmc/follow/v1/count/index`,
    params: data,
  })
}
