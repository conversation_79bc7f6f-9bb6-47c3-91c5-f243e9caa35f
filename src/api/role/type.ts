export interface Response<T> {
  msg: string
  results: T
  state: number
  subCode: string
  subMsg: string
  total: number
}
export interface Results<T> {
  records: T
  total: string
  size: string
  current: string
  orders: any[]
  optimizeCountSql: boolean
  searchCount: boolean
  maxLimit?: any
  countId?: any
  pages: string
}
/** 创建角色请求参数 */
export interface CreateRole {
  createTime?: string
  des: string
  id?: string
  name: string
  remark?: string
  roleMenuList?: string[]
  sort?: number | null
  status?: number | null
  updateTime?: string
}
/** 角色列表请求 */
export interface RoleListParams {
  size: number
  start: number
  searchName?: string
  sort?: string
  order?: string
}
/** 角色列表 */
export interface RoleListData {
  createTime: string
  des: string
  id: string
  name: string
  remark?: string
  roleMenuList?: string[]
  sort: number | null
  /** 0正常 1停用 */
  status: number | null
  updateTime: string
  roleUserCount: string
}
/** 成员列表请求参数 */
export interface MemberListParams {
  size: number
  start: number
  searchName?: string
  roleId: string
  sort?: string
  order?: string
}

export interface UserPowerList {
  id: string
  isDeleted: number
  menuId: string
  menuName: string
  power: string
  powerRange: string
  userId: string
}
/** 成员列表 */
export interface MemberListData {
  category: string
  createBy: string
  createTime: string
  hospitalId: string
  hospitalName: string
  id: string
  job: string
  loginTime: string
  medicalTeam: string
  name: string
  organId: string
  organName: string
  phone: string
  remark: string
  status: number
  title: string
  updateTime: string
  updateType: string
  userName: string
  userNumber: string
  userPowerList: UserPowerList[]
  userRoleList: any[]
  userType: string
  visibleApplication: string
}

export interface RoleOtherUserParams {
  size?: number
  start?: number
  searchName?: string
  roleId: string
  sort?: string
  order?: string
}
export interface RoleOtherUserData {
  children: RoleOtherUserData[]
  id: string
  organDesc: string
  organGroup: string
  organLevel: string
  organName: string
  organType: string
  parentId: string
  status: number
}
/** 新增角色下用户 */
export interface AddRoleMember {
  order?: string
  roleId: string
  searchName?: string
  size?: number
  sort?: string
  start?: number
  userIds: string[]
}

export interface RemoveRoleMember {
  order?: string
  roleId: string
  searchName?: string
  size?: number
  sort?: string
  start?: number
  userIds: string[]
}
