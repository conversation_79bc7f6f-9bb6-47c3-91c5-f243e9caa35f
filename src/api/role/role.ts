import type { AddRoleMember, CreateRole, MemberListParams, RemoveRoleMember, RoleListData, RoleListParams, RoleOtherUserParams } from './type'
import http from '@/service'

const PREFIX = '/heper-api'

enum RoleAPI {
  CreateRoleUrl = `${PREFIX}/manage/role/insert`,
  RoleListUrl = `${PREFIX}/manage/role/pageList`,
  UpdateStatusUrl = `${PREFIX}/manage/role/updateStatus`,
  DeleteRoleUrl = `${PREFIX}/manage/role/deleteById`,
  CheckRoleNameUrl = `${PREFIX}/manage/role/checkName`,
  UpdateRoleUrl = `${PREFIX}/manage/role/updateById`,
  RoleMemberUrl = `${PREFIX}/manage/role/userPage`,
  RoleOtherUserUrl = `${PREFIX}/manage/role/otherUserList`,
  AddRoleMemberUrl = `${PREFIX}/manage/role/addUser`,
  RemoveMemberUrl = `${PREFIX}/manage/role/removeUser`,
  GetRoleDetailUrl = `${PREFIX}/manage/role/detail`,
  GetRoleMenuIdUrl = `${PREFIX}/manage/role/menuList`,
  UpdateRoleMenuIdApi = `${PREFIX}/manage/role/updateMenuList`,

}
/**
 *
 * 新增角色
 */
export function createRoleApi<T>(data: CreateRole) {
  return http.post<T>({
    url: `${RoleAPI.CreateRoleUrl}`,
    data,
  })
}
/** 编辑角色 */
export function updateRoleApi<T>(data: RoleListData) {
  return http.put<T>({
    url: `${RoleAPI.UpdateRoleUrl}`,
    data,
  })
}

/** 获取角色列表 */
export function getRoleListApi<T>(params: RoleListParams) {
  return http.get<T>({
    url: `${RoleAPI.RoleListUrl}`,
    params,
  })
}
/** 改变角色状态 */
export function updateStatusApi<T>(data: { idList: string[]; status: number }) {
  return http.post<T>({
    url: `${RoleAPI.UpdateStatusUrl}`,
    data,
  })
}
/** 删除角色 */
export function deleteRoleApi<T>(id: string) {
  return http.delete<T>({
    url: `${RoleAPI.DeleteRoleUrl}/${id}`,
  })
}
/** 新增，编辑时校验角色名字 */
export function checkRoleNameRoleApi<T>(data: { id?: string; value: string }) {
  return http.post<T>({
    url: `${RoleAPI.CheckRoleNameUrl}`,
    data,
  })
}
/** 获取成员列表 */
export function getRoleMemberApi<T>(params: MemberListParams) {
  return http.get<T>({
    url: `${RoleAPI.RoleMemberUrl}`,
    params,
  })
}
/** 获取角色-机构-用户列表 */
export function getRoleOtherUserApi<T>(params: RoleOtherUserParams) {
  return http.get<T>({
    url: `${RoleAPI.RoleOtherUserUrl}`,
    params,
  })
}
/** 新增角色下用户 */
export function addRoleMemberApi<T>(data: AddRoleMember) {
  return http.post<T>({
    url: `${RoleAPI.AddRoleMemberUrl}`,
    data,
  })
}
/** 移除角色下用户-支持批量 */
export function removeMemberApi<T>(data: RemoveRoleMember) {
  return http.post<T>({
    url: `${RoleAPI.RemoveMemberUrl}`,
    data,
  })
}
/** 获取角色详情 */
export function getRoleDetailApi<T>(id: string) {
  return http.get<T>({
    url: `${RoleAPI.GetRoleDetailUrl}/${id}`,

  })
}

/** 获取角色拥有的菜单id */
export function getRoleMenuIdApi<T>(id: string) {
  return http.get<T>({
    url: `${RoleAPI.GetRoleMenuIdUrl}/${id}`,
  })
}

/** 修改角色拥有的菜单id */
export function updateRoleMenuIdApi<T>(data: any) {
  return http.post<T>({
    url: `${RoleAPI.UpdateRoleMenuIdApi}`,
    data,
  })
}
