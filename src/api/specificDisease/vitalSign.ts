import type { VitalSignParams } from './type'
import http from '@/service'

const PREFIX = '/heper-api'

enum VitalSignAPI {
  bodyListUrl = `${PREFIX}/vitalSign/heightWeight/list`,
  bodyAddUrl = `${PREFIX}/vitalSign/heightWeight/insert`,
  normalLifeListUrl = `${PREFIX}/vitalSign/generalSign/list`,
  normalLifeAddUrl = `${PREFIX}/vitalSign/generalSign/insert`,
  updateBodyCheck = `${PREFIX}/vitalSign/heightWeight/update`,
  deleteBodyCheck = `${PREFIX}/vitalSign/heightWeight/delete`,
  updateNormalCheck = `${PREFIX}/vitalSign/generalSign/update`,
  deleteNormalCheck = `${PREFIX}/vitalSign/generalSign/delete`,
}
/**
 * 生命体征接口数据
 * @param params 查询参数
 *
 */
export function getBodyListApi<T>(params: VitalSignParams) {
  return http.get<T>({
    url: `${VitalSignAPI.bodyListUrl}`,
    params,
  })
}

export function addBodyDataApi<T>(params: any) {
  return http.post<T>({
    url: `${VitalSignAPI.bodyAddUrl}`,
    data: params,
  })
}

export function getNormaLifeListApi<T>(params: VitalSignParams) {
  return http.get<T>({
    url: `${VitalSignAPI.normalLifeListUrl}`,
    params,
  })
}

export function addNormaLifeDataApi<T>(params: any) {
  return http.post<T>({
    url: `${VitalSignAPI.normalLifeAddUrl}`,
    data: params,
  })
}

/// 修改 一般检查 就是 身高体重什么的
export function updateBodyCheck(data) {
  return http.post({
    url: `${VitalSignAPI.updateBodyCheck}`,
    data,
  })
}

export function deleteBodyCheck(id) {
  return http.delete({
    url: `${VitalSignAPI.deleteBodyCheck}/${id}`,
  })
}

/// / 修改生命体征
export function updateTheNormalCheck(data) {
  return http.post({
    url: `${VitalSignAPI.updateNormalCheck}`,
    data,
  })
}

/// 删除生命体征
export function deleteTheNormalCheck(id) {
  return http.delete({
    url: `${VitalSignAPI.deleteNormalCheck}/${id}`,
  })
}
