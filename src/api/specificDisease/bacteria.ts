import http from '@/service'

export function addBacteriaAPI(data: any) {
  return http.post({
    url: '/heper-api/enterobacterialTransplantationRecord/insert',
    data,
  })
}

export function getBacteriaAPI(params: any) {
  return http.get<any>({
    url: '/heper-api/enterobacterialTransplantationRecord/list',
    params,
  })
}

export function updateBacteriaAPI(data: any) {
  return http.post<boolean>({
    url: '/heper-api/enterobacterialTransplantationRecord/update',
    data,
  })
}

export function deleteBacteriaAPI(id: string) {
  return http.delete<boolean>({
    url: `/heper-api/enterobacterialTransplantationRecord/delete/${id}`,
  })
}

export function addScoreAPI(data: any) {
  return http.post({
    url: '/heper-api/enterobacterialTransplantationRecord/insertScore',
    data,
  })
}

export function getScoreAPI(params: any) {
  return http.get<any>({
    url: '/heper-api/enterobacterialTransplantationRecord/scoreList',
    params,
  })
}

export function updateScoreAPI(data: any) {
  return http.post<any>({
    url: '/heper-api/enterobacterialTransplantationRecord/updateScore',
    data,
  })
}

export function deleteScoreAPI(id: string) {
  return http.delete<any>({
    url: `/heper-api/enterobacterialTransplantationRecord/deleteScore/${id}`,
  })
}

export function getSelectEnumsAPI(data: any) {
  return http.post<any>({
    url: '/heper-api/enterobacterialTransplantationRecord/assayDetail',
    data,
  })
}
