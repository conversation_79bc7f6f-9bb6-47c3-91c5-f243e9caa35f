import type { PatientListParams, UpdatePatientInfoParams } from './type'
import http from '@/service'

const PREFIX = '/heper-api'

enum PatientAPI {
  InfoUrl = `${PREFIX}/patientData/patient/patientInfo`,
  UpdateInfoUrl = `${PREFIX}/patientData/patient/updatePatientInfo`,
  PatientListUrl = `${PREFIX}/patientData/patient/patientList`,
  PatientNameUrl = `${PREFIX}/patientData/patient/patientNameList`,
  ManageDoctorListUrl = `${PREFIX}/manage/user/pageList`,

}

/**
 * 获取患者用户列表
 * @param patientId 患者id
 *
 */
export function getPatientListApi<T>(params: PatientListParams) {
  return http.get<T>({
    url: `${PatientAPI.PatientListUrl}`,
    params,
  })
}

/**
 * 获取患者基本信息
 * @param patientId 患者id
 *
 */
export function getPatientInfoApi<T>(patientId: string) {
  return http.get<T>({
    url: `${PatientAPI.InfoUrl}/${patientId}`,
  })
}

/**
 * 修改基本信息
 * @param data 患者信息
 *
 */
export function UpdateInfoApi<T>(data: UpdatePatientInfoParams) {
  return http.put<T>({
    url: `${PatientAPI.UpdateInfoUrl}`,
    data,
  })
}
/**
 * 获取患者姓名
 * @param patientName 患者姓名
 *
 */
export function getPatientNameApi<T>(params: { patientName?: string; organId: string }) {
  return http.get<T>({
    url: `${PatientAPI.PatientNameUrl}`,
    params,
  })
}

/**
 * 获取管理医生
 * @param patientName 医生姓名
 *
 */
export function getManageDoctorListApi<T>(params: { searchName: string; size: number }) {
  return http.get<T>({
    url: `${PatientAPI.ManageDoctorListUrl}`,
    params,
  })
}
