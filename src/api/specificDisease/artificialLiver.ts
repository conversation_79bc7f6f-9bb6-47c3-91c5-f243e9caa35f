import type { AssayDetailResultParams, GetSpecialScoreParams, UpdateBacteriaResultDetailParams, UpdateMedicalRecordParams, UpdateResultDetailParams, UpdateSpecialScoreParams } from './type'
import http from '@/service'

const PREFIX = '/heper-api'

enum ArtificialLiverAPI {
  HaveDataUrl = `${PREFIX}/artificialLiver/haveData`,
  AssayDetailListUrl = `${PREFIX}/artificialLiver/assayDetailList`,
  AssayDetailResultListUrl = `${PREFIX}/artificialLiver/assayDetailResultList`,
  AssayDetailTableUrl = `${PREFIX}/artificialLiver/assayDetailTable`,
  MedicalRecordUrl = `${PREFIX}/artificialLiver/medicalRecord`,
  UpdateMedicalRecordUrl = `${PREFIX}/artificialLiver/medicalRecord/update`,
  SpecialtyScoreUrl = `${PREFIX}/artificialLiver/scoreList`,
  AssayDetailUrl = `${PREFIX}/artificialLiver/assayDetail`,
  UpdateScoreUrl = `${PREFIX}/artificialLiver/updateScore`,
  ScoreDetailUrl = `${PREFIX}/artificialLiver/scoreDetail`,
  UpdateScoreDetailUrl = `${PREFIX}/artificialLiver/saveOrUpdateScoreDetail`,
  BacteriaSBcoreDetailUrl = `${PREFIX}/enterobacterialTransplantationRecord/scoreDetail`,
  UpdateBacteriaSBcoreDetailUrl = `${PREFIX}/enterobacterialTransplantationRecord/saveOrUpdateScoreDetail`,

}
/**
 * 判断患者是否作过人工肝 有人工肝记录
 * @param patientId 患者id
 *
 */
export function getHaveDataApi<T>(patientId: string) {
  return http.get<T>({
    url: `${ArtificialLiverAPI.HaveDataUrl}/${patientId}`,
  })
}

/**
 * 关键性指标集合
 */
export function getAssayDetailListApi<T>() {
  return http.get<T>({
    url: `${ArtificialLiverAPI.AssayDetailListUrl}`,

  })
}
/**
 * 检验明细项结果值
 * @param params 查询参数
 *
 */
export function getAssayDetailResultListApi<T>(params: AssayDetailResultParams) {
  return http.post<T>({
    url: `${ArtificialLiverAPI.AssayDetailResultListUrl}`,
    data: params,
  })
}

/**
 * 关键指标结果表
 * @param patientId 患者id
 *
 */
export function getAssayDetailTableApi<T>(patientId: string) {
  return http.get<T>({
    url: `${ArtificialLiverAPI.AssayDetailTableUrl}/${patientId}`,

  })
}
/**
 * 病历记录
 * @param patientId 患者id
 *
 */
export function getMedicalRecordApi<T>(patientId: string) {
  return http.get<T>({
    url: `${ArtificialLiverAPI.MedicalRecordUrl}/${patientId}`,

  })
}
/**
 * 修改病历记录
 * @param params 查询参数
 *
 */
export function updateMedicalRecordApi<T>(params: UpdateMedicalRecordParams) {
  return http.post<T>({
    url: `${ArtificialLiverAPI.UpdateMedicalRecordUrl}`,
    data: params,
  })
}

/**
 * 人工肝专科评分
 * @param patientId 患者id
 *
 */
export function getSpecialtyScoreApi<T>(patientId: string) {
  return http.get<T>({
    url: `${ArtificialLiverAPI.SpecialtyScoreUrl}/${patientId}`,

  })
}

/**
 * 人工肝评分指标数据
 * @param params 修改参数
 *
 */
export function getAssayDetailApi<T>(params: GetSpecialScoreParams) {
  return http.post<T>({
    url: `${ArtificialLiverAPI.AssayDetailUrl}`,
    data: params,
  })
}

/**
 * 人工肝评分指标数据
 * @param params 修改参数
 *
 */
export function updateScoreApi<T>(params: UpdateSpecialScoreParams) {
  return http.post<T>({
    url: `${ArtificialLiverAPI.UpdateScoreUrl}`,
    data: params,
  })
}
/**
 * 人工肝专科评分明细
 * @param params
 *
 */
export function getScoreDetailApi<T>(params: { scoreId: string; scoreType: string; scoreOrder: string }) {
  return http.post<T>({
    url: `${ArtificialLiverAPI.ScoreDetailUrl}`,
    data: params,
  })
}
/**
 * 保存/修改人工肝专科评分明细
 * @param params
 *
 */
export function updateScoreDetailApi<T>(params: UpdateResultDetailParams) {
  return http.post<T>({
    url: `${ArtificialLiverAPI.UpdateScoreDetailUrl}`,
    data: params,
  })
}

/**
 * 肠菌专科评分明细
 * @param params
 *
 */
export function getBacteriaSBcoreDetailApi<T>(params: { scoreId: string; scoreType: string }) {
  return http.post<T>({
    url: `${ArtificialLiverAPI.BacteriaSBcoreDetailUrl}`,
    data: params,
  })
}
/**
 * 保存/修改肠菌专科评分明细
 * @param params
 *
 */
export function updateBacteriaScoreDetailApi<T>(params: UpdateBacteriaResultDetailParams) {
  return http.post<T>({
    url: `${ArtificialLiverAPI.UpdateBacteriaSBcoreDetailUrl}`,
    data: params,
  })
}
