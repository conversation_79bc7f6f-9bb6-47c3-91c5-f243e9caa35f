import http from '@/service'

const PREFIX = '/heper-api'

enum labExamineAPI {
  assayDetailListURL = `${PREFIX}/labAssay/assayDetailList`,
  assayDetailResultListURL = `${PREFIX}/labAssay/assayDetailResultList`,
}

/**
 * 获取实验室检查表头检查项数组(有序)
 */
export function getAssayDetailList<T>(params: any) {
  return http.get<T>({
    url: `${labExamineAPI.assayDetailListURL}`,
    params,
  })
}

/**
 * 获取实验室检查具体数据
 */
export function getAssayDetailResultList<T>(params: any) {
  return http.get<T>({
    url: `${labExamineAPI.assayDetailResultListURL}`,
    params,
  })
}
