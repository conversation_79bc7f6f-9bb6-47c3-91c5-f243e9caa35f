import type { ExamListParams, LsmListParams } from './type'
import http from '@/service'

const PREFIX = '/heper-api'

enum ExamProjectAPI {
  ExamListUrl = `${PREFIX}/examProject/examList`,
  lsmUrl = `${PREFIX}/examProject/lsmList`,
  lsmAllUrl = `${PREFIX}/examProject/lsmListAll`,

}
/**
 * 除肝硬度测定的检查记录
 * @param params 查询参数
 *
 */
export function getExamListApi<T>(params: ExamListParams) {
  return http.get<T>({
    url: `${ExamProjectAPI.ExamListUrl}`,
    params,
  })
}
/**
 * 肝硬度测定的检查记录
 * @param params 查询参数
 *
 */
export function getLsmListApi<T>(params: LsmListParams) {
  return http.get<T>({
    url: `${ExamProjectAPI.lsmUrl}`,
    params,
  })
}

/**
 * 肝硬度测定的检查记录
 * @param params 查询参数
 *
 */
export function getLsmAllListApi<T>(params: LsmListParams) {
  return http.get<T>({
    url: `${ExamProjectAPI.lsmAllUrl}`,
    params,
  })
}
