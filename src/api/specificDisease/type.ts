/** 除肝硬度测定检查记录请求参数 */
export interface ExamListParams {
  /** 检查项目分组名称 */
  examProjectName: string
  patientId: string
  size?: number
  start?: number
}
/** 危急0否1是 */
type Emergency = 0 | 1
/** 除肝硬度测定检查记录 */
export interface ExamListModel {
  /** 审核时间 */
  auditTime: string
  /** 审核人姓名 */
  auditor: string
  /** 危急0否1是 */
  emergency: Emergency
  /** 检查部位 */
  examBodypart: string
  /** 检查项目 */
  examItem: string
  /** 检查类型 */
  examType: string
  id: string
  /** 影像诊断 */
  imageDiagnosis: string
  /** 影像所见 */
  imageSight: string
  /** 病人slmc唯一标识 */
  patientId: string
  /** 病人姓名 */
  patientName: string
  /** 摄片时间（检查时间） */
  performTime: string
  /** 检查医生姓名 */
  performer: string
  /** 就诊id 对应一次就诊 */
  recordId: string
  /** 登记人姓名 */
  register: string
  /** 登记时间 */
  registerTime: string
  /** 报告时间 */
  reportTime: string
  /** 报告人姓名 */
  reporter: string
  /** 申请时间 */
  requestTime: string
  /** 申请人姓名 */
  requester: string
  /** 修订时间 */
  reviseTime: string
  /** 修订人姓名 */
  reviser: string
  /** SLMC编号 */
  slmcNo: string
}

/** 肝硬度测定检查记录请求参数 */
export interface LsmListParams {
  /** 检查项目分组名称 */
  examProjectName: string
  patientId: string
  size?: number
  start?: number
}

export interface LsmListModal {
  capUnit: string
  capValue: number
  eunit: string
  evalue: number
  reportTime: string
}
/** 关键性指标集合 */
export interface KeyPointModel {
  assayProjectDetailName: string
  assayProjectName: string
  createTime: string
  hisAssayDetailCode: string
  hisAssayDetailName: string
  id: number
  isDeleted: boolean
  orderNum: number
  refValue: string
  sex: string
  unit: string
  updateTime: string
}
/** 关键性指标枚举 */
export interface KeyPointOption {
  // TODO:1.ts体操从KeyPointModel计算出来
  value: string
  label: string
  assayProjectName: string
  createTime: string
  id: number
  isDeleted: boolean
  orderNum: number
  refValue: string
  sex: string
  unit: string
  updateTime: string
  disabled: boolean
}

/** 查询检验明细项结果值 */
export interface AssayDetailResultParams {
  assayDetailName: string[]
  patientId: string
  sex: string
}

export interface AssayDetailItem {
  assayProjectId: string
  chineseName: string
  defaultStatus: string
  emergency: number
  englishName: string
  hint: string
  id: string
  itemId: string
  negativePositive: number
  patientId: string
  recordId: string
  refValue: string
  reportTime: string
  result: string
  unit: string
}

/** 检验明细项结果值 */
export interface AssayDetailResultModal {
  [key: string]: AssayDetailItem[]
}
export interface VitalSignParams {
  patientId: string
  size: number
  start: number
}
/** 病历记录 */
export interface MedicalRecordModal {
  basicEtiology: string
  cirrhosis: number
  complication: string
  currentEtiology: string
  firstArtificialLiverTime: string
  id: string
  inHospitalDiagnose: string
  inHospitalTime: string
  outHospitalDiagnose: string
  outHospitalMainDiagnose?: string
  patientId: string
  timeInterval: string
  basicDiagnose: string
  historyA: number | null
  historyB: number | null
  historyC: number | null
}
/** 修改病历记录 */
export interface UpdateMedicalRecordParams {
  basicEtiology: string
  cirrhosis: number | null
  complication: string
  currentEtiology: string
  firstArtificialLiverTime: string | null
  id: string
  inHospitalDiagnose: string
  inHospitalTime: string | null
  outHospitalDiagnose: string
  outHospitalMainDiagnose?: string
  patientId: string
}
/** 患者列表 */
export interface PatientRecord {
  age: string
  ageUnit: string
  birthday: string
  bloodPressure: string
  bloodType: string
  bmi: number
  companyName: string
  companyTel: string
  companyType: string
  contactsAddr: string
  contactsName: string
  contactsRelation: string
  contactsTel: string
  countryName: string
  createOrganId: string
  createTime: string
  culturalName: string
  diastolicPressure: number
  familyMembers: string
  heartRate: number
  height: number
  homeAddr: string
  id: string
  income: string
  lastVisit: string
  lastVisitRate: string
  liveAddr: string
  liveProvince: string
  liverComplaintType: number
  manageDoctorId: string
  manageDoctorName: string
  manageOrganizationId: string
  marriageName: string
  medicalInsuranceNo: string
  medicalInsuranceType: string
  nationCode: string
  nationName: string
  patientIdCardNum: string
  patientIdCardType: string
  patientName: string
  patientRecordNo: string
  patientSource: string
  patientStatus: string
  phone: string
  phySource: string
  phyUpdateTime: string
  planName: string
  primaryIndex: string
  professionName: string
  pulse: number
  respiratoryRate: number
  saplingScore: string
  sexCode: string
  sexName: string
  slmcNo: string
  status: number
  systolicPressure: number
  tagName: string
  temperature: number
  v1Visit: string
  weight: number
}

/** 查询患者列表 */
export interface PatientListParams {
  organId?: string
  patientName?: string
  plan?: string
  manageDoctorId?: string
  sex: string
  size: number
  slmcNo?: string
  start: number
  tagName: string
}
/** 用户信息 */
export interface PatientInfoModal {
  age: string
  birthday: string
  bloodPressure: string
  bloodType: string
  bmi: string
  companyName: string
  companyTel: string
  companyType: string
  contactsAddr: string
  contactsName: string
  contactsRelation: string
  contactsTel: string
  countryName: string
  createTime: string
  culturalName: string
  familyMembers: string
  heartRate: string
  height: string
  homeAddr: string
  id: string
  income: string
  lastVisit: string
  liveAddr: string
  liverComplaintType: number
  marriageName: string
  medicalInsuranceNo: string
  medicalInsuranceType: string
  nationCode: string
  nationName: string
  patientIdCardNum: string
  patientIdCardType: string
  patientName: string
  patientRecordNo: string
  patientSource: string
  phone: string
  phySource: string
  primaryIndex: string
  professionName: string
  pulse: string
  respiratoryRate: string
  sexCode: string
  sexName: string
  slmcNo: string
  status: number
  tag: string
  v1Visit: string
  weight: string
  phyUpdateTime: string
}

/** 编辑用户信息参数 */
export interface UpdatePatientInfoParams {
  age: string
  birthday: string
  bloodPressure: string
  bloodType: string
  bmi: number | null
  companyName: string
  companyTel: string
  companyType: string
  contactsAddr: string
  contactsName: string
  contactsRelation: string
  contactsTel: string
  countryName: string
  createTime: string
  culturalName: string
  familyMembers: string
  heartRate: string
  height: string
  homeAddr: string
  id: string
  income: string
  lastVisit: string
  liveAddr: string
  liverComplaintType: number
  marriageName: string
  medicalInsuranceNo: string
  medicalInsuranceType: string
  nationCode: string
  nationName: string
  patientIdCardNum: string
  patientIdCardType: string
  patientName: string
  patientRecordNo: string
  patientSource: string
  phone: string
  phySource: string
  primaryIndex: string
  professionName: string
  pulse: string
  respiratoryRate: string
  sexCode: string
  sexName: string
  slmcNo: string
  status: number
  tag: string
  v1Visit: string
  weight: string
}

/** 查询专科评分指标 */
export interface GetSpecialScoreParams {
  calTime: string
  patientId: string
  scoreOrder: string
  scoreType: string
}
/** 修改专科评分 */
export interface UpdateSpecialScoreParams {
  calTime: string
  childPughScoreAfter?: string
  childPughScoreBefore?: string
  id: string
  meldScoreAfter?: string
  meldScoreBefore?: string
  patientId: string
  recordId: string
  recordTime: string
}

export interface GetResultDetailParams {
  scoreId: string
  scoreOrder: string
  scoreType: string

}

export interface GetResultDetailModel {
  alb: string
  ascites: string
  calTime: string
  hepaticComa: string
  id: string
  pt: string
  scoreId: string
  scoreOrder: string
  scoreType: string
  tbil: string
  etiology: string
  inr: string
  scr: string
}

export interface UpdateResultDetailParams {
  alb?: string
  ascites?: string
  calTime: string
  hepaticComa?: string
  id?: string
  pt?: string
  scoreId?: string
  scoreOrder: string
  scoreType: string
  tbil?: string
  etiology?: string
  scr?: string
  inr?: string
}

export interface UpdateBacteriaResultDetailParams {
  alb?: string
  alt?: string
  ascites?: string
  ast?: string
  calTime: string | null
  etiology?: string
  hepaticComa?: string
  inr?: string
  plt?: string
  pt?: string
  scoreId: string | undefined
  scoreType: string
  scr?: string
  tbil?: string
}
