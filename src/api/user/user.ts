import type {
  CheckPhonParams, CreateUserParams, DeleteUserParams,
  EditUserParams, GetUserParams, GetVerifyCodeParams,
  InsertAdminParams, TransferMasterAdminParams, UpdateUserStatusParams,
  VerifyCodeParams,
} from './type'
import http from '@/service'

const PREFIX = '/heper-api'

enum UserAPI {
  GetUserList = `${PREFIX}/manage/user/pageList`,
  CreateUser = `${PREFIX}/manage/user/insert`,
  GetUserNumber = `${PREFIX}/manage/user/getUserNumber`,
  UpdateUserStatus = `${PREFIX}/manage/user/updateStatus`,
  DeleteUser = `${PREFIX}/manage/user/deleteById`,
  BatchDelete = `${PREFIX}/manage/user/batchDelete`,
  DeleteAdmin = `${PREFIX}/manage/user/deleteAdminById`,
  EditUser = `${PREFIX}/manage/user/updateById`,
  CheckPhone = `${PREFIX}/manage/user/checkPhone`,
  VerifyCode = `${PREFIX}/manage/user/verifySmsCode`,
  GetVerifyCode = `${PREFIX}/manage/user/getSmsCode`,
  TransferMasterAdmin = `${PREFIX}/manage/user/transferMainAdmin`,
  InsertAdmin = `${PREFIX}/manage/user/insertAdmin`,
  UserDetail = `${PREFIX}/manage/user/detail`,
  UserSort = `${PREFIX}/manage/user/orderNum`,

}

// 获取用户列表
export function getUserApi<T>(data: GetUserParams) {
  return http.get<T>({
    url: `${UserAPI.GetUserList}`,
    params: data,
  })
}

// 新增用户
export function createUserApi<T>(data: CreateUserParams) {
  return http.post<T>({
    url: `${UserAPI.CreateUser}`,
    data,
  })
}
// 获取用户名
export function getUserNumberApi<T>() {
  return http.get<T>({
    url: `${UserAPI.GetUserNumber}`,

  })
}
// 更新用户状态
export function updateUserStatusApi<T>(data: UpdateUserStatusParams) {
  return http.post<T>({
    url: `${UserAPI.UpdateUserStatus}`,
    data,
  })
}
// 删除用户
export function deleteUserApi<T>(data: DeleteUserParams) {
  return http.delete<T>({
    url: `${UserAPI.DeleteUser}/${data.id}`,
  })
}
// 编辑用户
export function editUserApi<T>(data: EditUserParams) {
  return http.put<T>({
    url: `${UserAPI.EditUser}`,
    data,
  })
}
// 手机号重复校验
export function checkPhoneApi<T>(data: CheckPhonParams) {
  return http.post<T>({
    url: `${UserAPI.CheckPhone}`,
    data,
  })
}
// 获取验证码
export function getVerifyCodeApi<T>(params: GetVerifyCodeParams) {
  return http.get<T>({
    url: `${UserAPI.GetVerifyCode}/${params.phone}`,
  })
}
// 验证验证码
export function verifyCodeApi<T>(params: VerifyCodeParams) {
  return http.get<T>({
    url: `${UserAPI.VerifyCode}`,
    params,
  })
}

// 转让
export function transferMasterAdminApi<T>(params: TransferMasterAdminParams) {
  return http.get<T>({
    url: `${UserAPI.TransferMasterAdmin}`,
    params,
  })
}
// 新增子管理员权限
export function insertAdminApi<T>(data: InsertAdminParams) {
  return http.post<T>({
    url: `${UserAPI.InsertAdmin}`,
    data,
  })
}
// 用户详情
export function userDetailApi<T>(id: string) {
  return http.get<T>({
    url: `${UserAPI.UserDetail}/${id}`,
  })
}
// 删除管理员
export function deleteAdminApi<T>(id: string) {
  return http.delete<T>({
    url: `${UserAPI.DeleteAdmin}/${id}`,
  })
}

// 删除管理员
export function batchDeleteApi<T>(data: { idList: string[] }) {
  return http.post<T>({
    url: `${UserAPI.BatchDelete}`,
    data,
  })
}
// 用户排序
export function userSortApi<T>(data: { id: string; orderNum: number }) {
  return http.post<T>({
    url: `${UserAPI.UserSort}`,
    data,
  })
}
