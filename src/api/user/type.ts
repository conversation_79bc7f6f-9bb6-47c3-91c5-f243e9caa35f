export interface Response<T> {
  msg: string
  results: T
  state: number
  subCode: string
  subMsg: string
  total: number
}

export interface GetUserParams {
  searchName?: string
  size: number
  start: number
  userType: string
}

export interface CreateUserParams {
  createBy: string
  createTime?: string
  id?: string
  job: string
  loginTime?: string
  organId: string
  organName: string
  phone: string
  remark?: string
  status: number
  title: string
  updateTime?: string
  userName: string
  userPowerList?: UserPowerList[]
  visibleApplication?: string
}

// 用户拥有的权限
export interface UserPowerList {
  id: string
  isDeleted: number
  menuId: string
  menuName: string
  power: string
  powerRange: string
  userId: string
}

export interface UpdateUserStatusParams {
  idList: string[]
  status: number
}
export interface DeleteUserParams {
  id: string
}
export interface EditUserParams {
  createBy: string
  createTime: string
  id: number
  job: string
  loginTime: string
  organId: number
  organName: string
  phone: string
  remark: string
  status: number
  title: string
  updateTime: string
  userName: string
  userNumber: string
  userPowerList: UserPowerList[]
  userType: string
  visibleApplication: string
}

export interface CheckPhonParams {
  value: string
}

export interface GetVerifyCodeParams {
  phone: string
}

export interface VerifyCodeParams {
  code: string
  phone: string
}

export interface TransferMasterAdminParams {
  code: string
  id: string
  newId: string
}

// 用户列表
export interface UserPageListRecord {
  createBy: string
  createTime: string
  id: string
  job: string
  loginTime: string
  organId: string
  organName: string
  phone: string
  remark: string
  status: number
  title: string
  updateTime: string
  userName: string
  userNumber: string
  userPowerList: UserPowerList[]
  userType: string
  visibleApplication: string
}

export interface Order {
  asc: boolean
  column: string
}
export interface UserPageListRes {
  countId: string
  current: number
  hitCount: boolean
  maxLimit: number
  optimizeCountSql: boolean
  orders: Order[]
  pages: number
  records: UserPageListRecord[]
  searchCount: boolean
  size: number
  total: number
}

// 用户详情
export interface UserDetailRes {
  createBy: string
  createTime: string
  id: string
  job: string
  loginTime: string
  organId: string
  organName: string
  phone: string
  remark: string
  status: number
  title: string
  updateTime: string
  userName: string
  userNumber: string
  userPowerList: UserPowerList[]
  userType: string
  visibleApplication: string
}

export interface UserPowerListParams {
  menuId: string
  menuName: string
  power: string
  powerRange: string
  userId: string
}
export interface InsertAdminParams {
  id: string
  userPowerList: UserPowerListParams[]
}
