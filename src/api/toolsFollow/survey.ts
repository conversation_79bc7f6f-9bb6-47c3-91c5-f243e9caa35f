import http from '@/service'

export function saveSurveyAPI(data: {
  content: string
  createId: string
  createName: string
  introduce: string
  scaleName: string
  isTemplate: boolean
  visibleRange: string
  relations: string[]
}) {
  return http.post<any>({
    url: '/heper-api/api/hma/follow/v1/scale/save',
    data,
  })
}

export function updateSurveyAPI(data: {
  updateId: string
  updateName: string
  scaleId: string
  content: string
  introduce: string
  scaleName: string
  isTemplate: boolean
  visibleRange: string
  relations: string[]
}) {
  return http.put<any>({
    url: '/heper-api/api/hma/follow/v1/scale/update',
    data,
  })
}

export function getSurveyListAPI(search: string) {
  return http.get<any>({
    url: '/heper-api/api/hma/follow/v1/scale/list',
    params: {
      search,
    },
  })
}

export function deleteSurveyAPI(scaleId: string) {
  return http.delete<boolean>({
    url: `/heper-api/api/hma/follow/v1/scale/remove?scaleId=${scaleId}`,
  })
}

export function getSurveyDetail(scaleId: string) {
  return http.get<any>({
    url: `/heper-api/api/hma/follow/v1/scale/detail/${scaleId}`,
  })
}

export function isSurveyAvailableAPI(params: {
  scaleId?: string
  name: string
}) {
  return http.get<boolean>({
    url: '/heper-api/api/hma/follow/v1/scale/available',
    params,
  })
}

export function getSurveyDetailBySeriesAPI(id: string) {
  return http.get<any>({
    url: `/heper-api/api/hma/follow/v1/scale/series/${id}`,
  })
}

export function getQuestionLibAPI(params: {
  labelId: string
  topic: string
}) {
  return http.get<any[]>({
    url: '/heper-api/api/hma/follow/v1/question/listByLabel',
    params,
  })
}

export function getAIScale(data: {
  labels: string[]
  nums: number
}) {
  return http.post<any[]>({
    url: '/heper-api/api/hma/follow/v1/scale/ai-scale',
    data,
  })
}

export function getScaleOnTask(taskId: string) {
  return http.get<any>({
    url: `/heper-api/api/hma/follow/v1/task/scale/${taskId}`,
  })
}

export function saveAnswerAPI(data: {
  answer: string
  taskId: string
  writeId: string
  writeType: 'PATIENT' | 'MEDICAL' | 'SYSTEM'
}) {
  return http.post<boolean>({
    url: '/heper-api/api/hma/follow/v1/answer/save',
    data,
  })
}
