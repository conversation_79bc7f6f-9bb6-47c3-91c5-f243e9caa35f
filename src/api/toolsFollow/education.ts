import http from '@/service'

// 获取分类
export function getEduTreeAPI() {
  return http.get<any>({
    url: '/heper-api/api/hma/follow/v1/label/education/tree',
  })
}

// 保存
export function saveEduAPI(data: any) {
  return http.post<any>({
    url: '/heper-api/api/hma/follow/v1/education/save',
    data,
  })
}

export function getEduPageAPI(data: any) {
  return http.post<any>({
    url: '/heper-api/api/hma/follow/v1/education/page',
    data,
  })
}

export function isEduNameAvailableAPI(params: any) {
  return http.get<boolean>({
    url: '/heper-api/api/hma/follow/v1/education/available',
    params,
  })
}

export function getEduDetailAPI(id: string) {
  return http.get<any>({
    url: `/heper-api/api/hma/follow/v1/education/detail/${id}`,
  })
}

export function deleteEduAPI(id: string) {
  return http.get<boolean>({
    url: `/heper-api/api/hma/follow/v1/education/delete?educationId=${id}`,
  })
}

export function updateEduAPI(data: any) {
  return http.put<boolean>({
    url: '/heper-api/api/hma/follow/v1/education/update',
    data,
  })
}

export function getEduListAPI() {
  return http.get<any>({
    url: '/heper-api/api/hma/follow/v1/education/list',
  })
}

export function getEduBySeriesAPI(id: string) {
  return http.get<any>({
    url: `/heper-api/api/hma/follow/v1/education/series/${id}`,
  })
}
