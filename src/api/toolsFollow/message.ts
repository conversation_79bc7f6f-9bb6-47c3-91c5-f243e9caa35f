import http from '@/service'

// 保存/新增短信
export function saveSmsAPI(data: any) {
  return http.post<boolean>({
    url: '/heper-api/api/hma/follow/v1/smsTemplate/saveSmsTemplate',
    data,
  })
}

// 短信列表
export function getSmsListAPI(params: {
  operateId: string
  queryName?: string
}) {
  return http.get<any>({
    url: '/heper-api/api/hma/follow/v1/smsTemplate/querySmsTmp',
    params,
  })
}

export function deleteSmsAPI(params: {
  operateId: string
  smsTmpId: string
}) {
  return http.get<boolean>({
    url: '/heper-api/api/hma/follow/v1/smsTemplate/deleteSmsTmp',
    params,
  })
}
