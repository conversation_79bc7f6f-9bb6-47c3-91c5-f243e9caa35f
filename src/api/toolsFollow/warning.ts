import http from '@/service'

export interface WarningData {
  warningName: string
  content: string
  createId?: string
  createName?: string
  warningId: string
  updateId?: string
  updateName?: string
}

// 获取分类
export function saveWarningAPI(data: WarningData) {
  return http.post<boolean>({
    url: '/heper-api/api/hma/follow/v1/warning/save',
    data,
  })
}

export function getWarningPageAPI(data: any) {
  return http.post<any>({
    url: '/heper-api/api/hma/follow/v1/warning/page',
    data,
  })
}

export function checkWarningName(params: {
  warningName: string
  warningId: string
}) {
  return http.get({
    url: '/heper-api/api/hma/follow/v1/warning/available',
    params,
  })
}

export function updateWarningAPI(data: WarningData) {
  return http.put<boolean>({
    url: '/heper-api/api/hma/follow/v1/warning/update',
    data,
  })
}

export function deleteWarningAPI(warningId: string) {
  return http.delete<any[]>({
    url: `/heper-api/api/hma/follow/v1/warning/remove?warningId=${warningId}`,
  })
}

export function getWarningListAPI(search: string) {
  return http.get<any>({
    url: '/heper-api/api/hma/follow/v1/warning/list',
    params: {
      search,
    },
  })
}
