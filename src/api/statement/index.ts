import http from '@/service'

export function CountFollowDateAPI(data: any) {
  return http.post<any>({
    url: '/heper-api/api/hma/follow/v1/plan/count/countFollowDate',
    data,
  })
}

export function countDocAPI(data: any) {
  return http.post<any>({
    url: '/heper-api/api/hma/follow/v1/plan/count/countDoctor',
    data,
  })
}

export function getDocGroupListAPI(params: {
  organId: string
  name?: string
}) {
  return http.get<any>({
    url: '/heper-api/manage/doctorGroup/doctorGroupList',
    params,
  })
}

export function getSaplingTotalAPI(data) {
  return http.post<number>({
    url: '/heper-api/sapling/total',
    data,
  })
}

export function getDepartTotalAPI(data) {
  return http.post<any>({
    url: '/heper-api/sapling/depart_total',
    data,
  })
}
