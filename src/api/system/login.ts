import type { LoginParams } from './type'
import http from '@/service'

const PREFIX = '/heper-api'

enum LoginAPI {
  loginUrl = `${PREFIX}/login/do`,
  logout = `${PREFIX}/login/out`,
  ResetPassword = `${PREFIX}/login/reset_password`,
  GetLoginVerifyCode = `${PREFIX}/login/sms/send/retrieve`,
  hmaLogin = `${PREFIX}/hma/login`,
  codeLogin = `${PREFIX}/login/sms/send/login`,
}

// 登录
export function loginApi<T>(data: LoginParams) {
  return http.post<T>({
    url: `${LoginAPI.loginUrl}`,
    data,
  })
}
// 登出
export function logOutApi<T>() {
  return http.get<T>({
    url: `${LoginAPI.logout}`,
  })
}
// 重置密码
export function resetPasswordApi<T>(data: { smsCode: string; password: string; phone: string }) {
  return http.post<T>({
    url: `${LoginAPI.ResetPassword}`,
    data,
  })
}
// 获取验证码
export function getVerifyCodeApi<T>(params: { phone: string }) {
  return http.get<T>({
    url: `${LoginAPI.GetLoginVerifyCode}`,
    params,
  })
}

// hma登录
export function hmaLoginApi<T>(data: {
  jobNum: string
  loginType: 'HMA'
}) {
  return http.post<T>({
    url: `${LoginAPI.hmaLogin}`,
    data,
  })
}

/// 科研平台验证吗登录
export function getEDCLoginCode(phone) {
  return http.get({
    url: `${LoginAPI.codeLogin}`,
    params: { phone },
  })
}
