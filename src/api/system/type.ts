export interface Response<T> {
  msg: string
  results: T
  state: number
  subCode: string
  subMsg: string
  total: number
}

export interface LoginParams {
  loginType: string
  password: string
  phone: string
  smsCode?: string
}

export interface LogRecordParams {
  begin: string
  end: string
  size: number
  start: number
  systemId: number
  userId: number
  eventType: string
}

export interface MenuParams {
  size: number
  start: number
  status: number
}
export interface MenuRes {
  id: string
  menuName: string
  power: string
  menuType: string
  orderNum: number
  parentId: string
  children?: MenuRes[]
  powerRange?: string
}
