import type { LogRecordParams, MenuParams } from './type'
import http from '@/service'

const PREFIX = '/heper-api'
enum LogAPI {
  RecordUrl = `${PREFIX}/log/record/list`,

}
enum SystemAPI {
  MenuUrl = `${PREFIX}/manage/menu/list`,
  EventTypeUrl = `${PREFIX}/log/config/eventTypes`,
  SystemsUrl = `${PREFIX}/log/config/systems `,
  OperatorUrl = `${PREFIX}/log/config/users`,
}

// 日志记录
export function getLogListApi<T>(data: LogRecordParams) {
  return http.post<T>({
    url: `${LogAPI.RecordUrl}`,
    data,
  })
}

// 获取菜单
export function getMenusApi<T>(params: MenuParams) {
  return http.get<T>({
    url: `${SystemAPI.MenuUrl}`,
    params,
  })
}

// 获取事件类型
export function getEventTypesApi<T>() {
  return http.get<T>({
    url: `${SystemAPI.EventTypeUrl}`,
  })
}
// 获取应用名称
export function getAppNameApi<T>() {
  return http.get<T>({
    url: `${SystemAPI.SystemsUrl}`,
  })
}

// 获取操作者
export function getOperatorApi<T>() {
  return http.get<T>({
    url: `${SystemAPI.OperatorUrl}`,
  })
}
