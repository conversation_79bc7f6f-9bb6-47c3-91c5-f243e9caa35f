import http from '@/service'

const PREFIX = '/heper-api'

enum DoctorAPI {
  DoctorGroupListUrl = `${PREFIX}/manage/doctorGroup/doctorGroupList`,
  DoctorListUrl = `${PREFIX}/dic/doctor/doctorList`,

}
/**
 * 获取医生组
 * @param params 查询参数
 *
 */
export function getDoctorGroupListApi<T>(params: { organId: string; name: string }) {
  return http.get<T>({
    url: `${DoctorAPI.DoctorGroupListUrl}`,
    params,
  })
}
/**
 * 获取医生
 * @param params 查询参数
 *
 */
export function getDoctorListApi<T>(params: { organId: string; name: string }) {
  return http.get<T>({
    url: `${DoctorAPI.DoctorListUrl}`,
    params,
  })
}
