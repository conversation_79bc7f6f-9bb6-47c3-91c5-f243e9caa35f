import http from '~/src/service'

const PREFIX = '/heper-api'

export enum statisticsAPI {
  allPatients = `${PREFIX}/statistics/patient/all_patient`,
  ageSex = `${PREFIX}/statistics/patient/age_sex`,
  diseases = `${PREFIX}/statistics/patient/diseases`,
  hepatitisBCured = `${PREFIX}/statistics/hepatitisBCured`, // 乙肝治愈率
  hepatitisBDna = `${PREFIX}/statistics/hepatitisBDna`, // 乙肝DNA转阴
  hepatitisCCured = `${PREFIX}/statistics/hepatitisCCured`, // 丙肝治愈率
  hepatitisCNucleic = `${PREFIX}/statistics/hepatitisCNucleic`, // 丙肝核酸检测
  bmi = `${PREFIX}/statistics/patient/bmi`, // bmi达标率
  weightLoss = `${PREFIX}/statistics/patient/weightLoss`, // 减重
  sfRate = `${PREFIX}/api/slmc/follow/v1/count/rate`, // 随访率
  zyAgeSex = `${PREFIX}/zy_s/age_sex`,
  zyAls = `${PREFIX}/zy_s/als`,
  zyDb = `${PREFIX}/zy_s/db`,
  zyDisease = `${PREFIX}/zy_s/diseases`,
  zyFollowComplete = `${PREFIX}/zy_s/followComplete`,
  zyHealing = `${PREFIX}/zy_s/healing`,
  zyOrgans = `${PREFIX}/zy_s/organs`,
  zyOverview = `${PREFIX}/zy_s/overview`,
  zyRegion = `${PREFIX}/zy_s/region`,
  dddOverview = `${PREFIX}/hpt-antichange/overview`,
  dddDoctorGroup = `${PREFIX}/hpt-antichange/doctorGroup`,
  dddTimeGroup = `${PREFIX}/hpt-antichange/timeGroup`,
  dddDetailInfo = `${PREFIX}/hpt-antichange/groupPatients`,
}

export function getStatisticsDatas<T>(url: statisticsAPI, method: string, data?: any | undefined, params?: any) {
  if (method === 'GET') {
    return http.get<T>({
      url,
      params,
    })
  }
  else {
    return http.post<T>({
      url,
      data,
      config: {
        params,
      },
    })
  }
}

/// 获取关键指标数据
export function getKeyItemStatisticsDatas(data: any) {
  return http.post({
    url: `${PREFIX}/assayProjectDetail/assayProjectDetailList`,
    data,
  })
}

/// 浙一数据统计

/// 患者年龄性别分布
export function getZyAgeSexStatistics() {
  return http.get({
    url: statisticsAPI.zyAgeSex,
  })
}
/// 人工肝数据
export function getZyAlsStatistics() {
  return http.get({
    url: statisticsAPI.zyAls,
  })
}
/// 数据库总条数
export function getZyDbStatistics() {
  return http.get({
    url: statisticsAPI.zyDb,
  })
}
/// 病种分布
export function getZyDiseaseStatistics() {
  return http.get({
    url: statisticsAPI.zyDisease,
  })
}
/// 随访完成率
export function getZyFollowCompleteStatistics() {
  return http.get({
    url: statisticsAPI.zyFollowComplete,
  })
}
/// 肝病治愈率
export function getZyhealingStatistics() {
  return http.get({
    url: statisticsAPI.zyHealing,
  })
}
/// 机构分布
export function getZyOrgansStatistics() {
  return http.get({
    url: statisticsAPI.zyOrgans,
  })
}
/// 地域分布
export function getZyRegionsStatistics() {
  return http.get({
    url: statisticsAPI.zyRegion,
  })
}
/// overView
export function getZyOverviewStatistics() {
  return http.get({
    url: statisticsAPI.zyOverview,
  })
}

/// / 抗菌药物使用记录
export function getThedddOverViewData(data) {
  return http.post({
    url: statisticsAPI.dddOverview,
    data,
  })
}

///  医生组统计
export function getThedddDoctorGroup(data) {
  return http.post({
    url: statisticsAPI.dddDoctorGroup,
    data,
  })
}
/// 时间统计
export function getThedddTimeGroup(data) {
  return http.post({
    url: statisticsAPI.dddTimeGroup,
    data,
  })
}

/// 医生组内抗菌药物详情
export function getTheDddDetailInfo(data) {
  return http.post({
    url: statisticsAPI.dddDetailInfo,
    data,
  })
}
