import http from '@/service'

const PREFIX = '/heper-api'

export function getTagList<T>(data: any) {
  return http.post<T>({
    url: `${PREFIX}/api/slmc/follow/v1/tag/page`,
    data,
  })
}

export function getTagListAll<T>(data: any) {
  return http.get<T>({
    url: `${PREFIX}/api/slmc/follow/v1/tag/list`,
    params: data,
  })
}

export function addTagList<T>(data: any) {
  return http.post<T>({
    url: `${PREFIX}/api/slmc/follow/v1/tag/add`,
    data,
  })
}

export function updateTag<T>(data: any) {
  return http.put<T>({
    url: `${PREFIX}/api/slmc/follow/v1/tag/update`,
    data,
  })
}

export function deleteTagList<T>(tagId: any) {
  return http.delete<T>({
    url: `${PREFIX}//api/slmc/follow/v1/tag/delete/${tagId}`,
  })
}

export function pageTagList(data: any) {
  return http.post<any>({
    url: `${PREFIX}/api/slmc/follow/v1/tag/page`,
    data,
  })
}

export function tagIsUsedAPI<T>(params: {
  tagId: string
  tagName: string
}) {
  return http.get<T>({
    url: `${PREFIX}/api/slmc/follow/v1/tag/tagNameIsUsable`,
    params,
  })
}
