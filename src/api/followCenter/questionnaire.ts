import http from '@/service'

const PREFIX = '/heper-api'

// 药品字典
export function medicineDictAPI<T>(module: string) {
  return http.get<T>({
    url: `${PREFIX}/api/slmc/follow/v1/dict/medicine/tree/${module}`,
  })
}

// SLMC访视列表
export function planPhaseAPI<T>(primaryIndex: string) {
  return http.get<T>({
    url: `${PREFIX}/planTemplate/getAllPhase?primaryIndex=${primaryIndex}`,
  })
}

export type Tabs = 'BASE_INFO' | 'MEDICAL_HISTORY' | 'INSPECT' | 'ICONOGRAPHY'

interface TaskPhaseReq {
  planId: string
  planPhaseId: string
  planTemplateId: string
  showPosition: Tabs
}
// 患者阶段任务
export function taskPhaseAPI<T>(data: TaskPhaseReq) {
  return http.post<T>({
    url: `${PREFIX}/api/slmc/follow/v1/task/phases`,
    data,
  })
}

// V0患者阶段任务
export function taskPhaseV0API<T>(params: {
  showPosition: Tabs
  primaryIndex: string
}) {
  return http.get<T>({
    url: `${PREFIX}/api/slmc/follow/v1/task/v0`,
    params,
  })
}

interface ReportReq {
  reportNo: string
  organId: string
}

// 获取图文报告信息
export function iconographyReportAPI<T>(params: ReportReq) {
  return http.get<T>({
    url: `${PREFIX}/api/slmc/follow/v1/task/report/iconography`,
    params,
  })
}

// 获取实验室检验信息
export function examinationReportAPI<T>(params: ReportReq) {
  return http.get<T>({
    url: `${PREFIX}/api/slmc/follow/v1/task/report/examination`,
    params,
  })
}

// 保存问卷答案
export function saveAnswerAPI<T>(data: any) {
  return http.post<T>({
    url: `${PREFIX}/api/slmc/follow/v1/answer/save`,
    data,
  })
}

// 保存问卷答案
export function saveAnswerV0API<T>(data: any) {
  return http.post<T>({
    url: `${PREFIX}/api/slmc/follow/v1/answer/phase0/save`,
    data,
  })
}

// 获取任务详情
export function taskDetailAPI<T>(taskId: string) {
  return http.get<T>({
    url: `${PREFIX}/api/slmc/follow/v1/task/${taskId}`,
  })
}

// 查询访视阶段参数
export function phaseParamAPI<T>(params: {
  primaryIndex: string
  phaseCode: string
}) {
  return http.get<T>({
    url: `${PREFIX}/api/slmc/follow/v1/plan-phase/getPhaseParam`,
    params,
  })
}

// 重新发送
export function resendAPI<T>(phaseId: string) {
  return http.get<T>({
    url: `${PREFIX}/taskSend/reSend`,
    params: {
      phaseId,
    },
  })
}
