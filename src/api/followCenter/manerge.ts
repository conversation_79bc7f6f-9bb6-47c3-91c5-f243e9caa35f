import http from '@/service'

const PREFIX = '/heper-api'

// 纳入随访计划
export function addPlan<T>(data: any) {
  return http.post<T>({
    url: `${PREFIX}/plan/addPlan`,
    data,
  })
}

// 变更随访计划
export function editPlan<T>(data: any) {
  return http.post<T>({
    url: `${PREFIX}/plan/editPlan`,
    data,
  })
}

// 变成管理医生
export function editPatient<T>(data: any) {
  return http.post<T>({
    url: `${PREFIX}/plan/editPatient`,
    data,
  })
}

export function queryFollowPatient<T>(data: any) {
  return http.post<T>({
    url: `${PREFIX}/planTemplate/queryFollowPatient`,
    data,
  })
}
/**
 * 查询随访的随访计划
 * @param params 查询key
 * @returns
 */
export function nameIsAvailable<T>(params: { sortType: string }) {
  return http.get<T>({
    url: `${PREFIX}/planTemplate/nameIsAvailable`,
    params,
  })
}

export function doctorList(params: any) {
  return http.get({
    url: `${PREFIX}/dic/doctor/doctorList`,
    params,
  })
}

export function editorPlan(data: any) {
  return http.post({
    url: `${PREFIX}/plan/editPlan`,
    data,
  })
}

export function batchUpdate(data: any) {
  return http.put({
    url: `${PREFIX}/api/slmc/follow/v1/patient-tag/batchUpdate`,
    data,
  })
}

/**
 * 查询随访基本信息
 * @param params 患者主索引
 */
export function getFollowBaseInfoApi<T>(params: { primaryIndex: string }) {
  return http.get<T>({
    url: `${PREFIX}/plan/getFollowBaseInfo`,
    params,
  })
}

export function insertPatient<T>(data: any) {
  return http.post<T>({
    url: `${PREFIX}/plan/insertPatient`,
    data,
  })
}

// 发送单条短信
export function getSmsTemplateAPI<T>() {
  return http.get<T>({
    url: `${PREFIX}/smsTemplate/getAllTemplate`,
  })
}

// 批量发送短信
export function sendSms(data: any) {
  return http.post({
    url: `${PREFIX}/smsTemplate/sendSms`,
    data,
  })
}
