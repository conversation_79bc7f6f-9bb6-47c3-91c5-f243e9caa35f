/** 随访基本信息 */
export interface FollowBaseInfo {
  joinTime: string | null
  manageDoctorId: string | null
  manageDoctorName: string | null
  nextFollowTime: string | null
  patientId: string | null
  planName: string | null
  planSort: string | null
  planStatus: string | null
  primaryIndex: string | null
  planSortName: string | null
  tagIds: string[] | null | string
  tagName: string | null
  visitTime: string | null
  planTemplateId: string | null
  manageOrganName: string | null
  manageOrganId: string | null
}

export interface AdditionalProp1 {
  beforeOpenDays: number
  beforeRemindDays: number
  createTime: string
  incrementId: number
  isDeleted: boolean
  planTemplateId: string
  planTemplateMemo: string
  planTemplateName: string
  planTemplateSort: string
  referPhase: string
  sortMemo: string
  updateTime: string
}

export interface AdditionalProp2 {
  beforeOpenDays: number
  beforeRemindDays: number
  createTime: string
  incrementId: number
  isDeleted: boolean
  planTemplateId: string
  planTemplateMemo: string
  planTemplateName: string
  planTemplateSort: string
  referPhase: string
  sortMemo: string
  updateTime: string
}

export interface AdditionalProp3 {
  beforeOpenDays: number
  beforeRemindDays: number
  createTime: string
  incrementId: number
  isDeleted: boolean
  planTemplateId: string
  planTemplateMemo: string
  planTemplateName: string
  planTemplateSort: string
  referPhase: string
  sortMemo: string
  updateTime: string
}
/** 随访的随访计划 */
export interface NameIsAvailable {
  additionalProp1: AdditionalProp1[]
  additionalProp2: AdditionalProp2[]
  additionalProp3: AdditionalProp3[]
}
