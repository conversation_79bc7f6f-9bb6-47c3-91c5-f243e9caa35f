import http from '@/service'

// 获取标签列表
export function getTagPageAPI(data: any) {
  return http.post<any>({
    url: '/heper-api/api/hma/follow/v1/tag/page',
    data,
  })
}

export function getTagListAPI(tagName: string) {
  return http.get<any>({
    url: '/heper-api/api/hma/follow/v1/tag/list',
    params: {
      tagName,
    },
  })
}

export function addTagAPI(data: any) {
  return http.post<boolean>({
    url: '/heper-api/api/hma/follow/v1/tag/add',
    data,
  })
}

export function updateTagAPI(data: any) {
  return http.put<boolean>({
    url: '/heper-api/api/hma/follow/v1/tag/update',
    data,
  })
}

export function deleteTagAPI(id: string) {
  return http.delete<boolean>({
    url: `/heper-api/api/hma/follow/v1/tag/delete/${id}`,
  })
}

export function checkTagName(params: {
  tagId: string
  tagName: string
}) {
  return http.get<boolean>({
    url: '/heper-api/api/hma/follow/v1/tag/tagNameIsUsable',
    params,
  })
}

/** 患者标签修改 */
export function updatePatientTagAPI(data: any) {
  return http.put<boolean>({
    url: '/heper-api/api/hma/follow/v1/patient-tag/update',
    data,
  })
}
