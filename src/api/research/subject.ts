import http from '@/service'

const PREFIX = '/heper-api'

export enum ProjectApi {
  addSubject = `${PREFIX}/examinee/insert`,
  editSubject = `${PREFIX}/examinee/update`,
  subjectList = `${PREFIX}/examinee/list`,
  deleteSubject = `${PREFIX}/examinee/delete/`,
  subjectFrom = `${PREFIX}/rsUser/organNameList`,
  subjectDetail = `${PREFIX}/examinee/detail/`,
  subjectEditLog = `${PREFIX}/examinee/logList/`,
  subjectVisitList = `${PREFIX}/api/rs/visit/node/plan`,
  subjectAnswers = `${PREFIX}/api/rs/visit/task/scale/answer`,
  subjectQuestions = `${PREFIX}/api/rs/scale/detail/`,
  subjectScaleChart = `${PREFIX}/api/rs/answer/indicatorTrends`,
  subjectScaleRecord = `${PREFIX}/api/rs/answer/save`,
  stopSubject = `${PREFIX}/examinee/stop/`,
  updateScaleAnswer = `${PREFIX}/api/rs/answer/update`,
  exportPatientList = `${PREFIX}/examinee/patientList`,
  exportPatient = `${PREFIX}/examinee/batchImport/`,
  subjectGroup = `${PREFIX}/examinee/groupList/`,
  uploadTheFileWithNodeId = `${PREFIX}/file/upload_v2/`,
  getFileList = `${PREFIX}/file/list/v2/`,
  deleteFile = `${PREFIX}/file/delete/`,
  downloadFile = `${PREFIX}/file/download/`,
  previewFile = `${PREFIX}/file/preview/`,
  getToDoListApi = `${PREFIX}/api/rs/visit/task/unfinished`,
  handleTheNodeApi = `${PREFIX}/api/rs/visit/task/removeNodeTasks`,
  tipApi = `${PREFIX}/api/rs/visit/task/reminder`,
  interveneRecords = `${PREFIX}/api/rs/visit/intervene/list`,
  addInterveneRecord = `${PREFIX}/api/rs/visit/intervene/save`,
  updateInterveneRecord = `${PREFIX}/api/rs/visit/intervene/update`,
  deleteInterveneRecord = `${PREFIX}/api/rs/visit/intervene/delete`,
  efficacyEvaluation = `${PREFIX}/rsData/efficacyEvaluation`,
  patientSource = `${PREFIX}/rsData/patientSource`,
  sexAgeGroup = `${PREFIX}/rsData/sexAgeGroup`,
  adverseEventChart = `${PREFIX}/rsData/adverseEvent`,
  fileId = `${PREFIX}/hma/id`,
  subjectList2 = `${PREFIX}/examinee/list2`,
}

/**
 * 新增受试者
 */
export function addSubject<T>(data: any) {
  return http.post<T>({
    url: ProjectApi.addSubject,
    data,
  })
}

/**
 * 编辑受试者
 */
export function editSubject<T>(data: any) {
  return http.post<T>({
    url: ProjectApi.editSubject,
    data,
  })
}

/**
 * 受试者管理列表
 */
export function getSubjectList<T>(data: any) {
  return http.get<T>({
    url: ProjectApi.subjectList,
    params: data,
  })
}

/**
 * 受试者来源
 */
export function getSubjectResourceList<T>() {
  return http.get<T>({
    url: ProjectApi.subjectFrom,
  })
}

/**
 * 受试者分组
 */
export function getSubjectGroupList<T>(topicId: string) {
  return http.get<T>({
    url: ProjectApi.subjectGroup + topicId,
  })
}

/**
 * 受试者详情
 */
export function getSubjectDetail<T>(subjectId: string) {
  return http.get<T>({
    url: ProjectApi.subjectDetail + subjectId,
  })
}

/**
 * 受试者修改日志
 */
export function getSubjectEditLog<T>(subjectId: string) {
  return http.get<T>({
    url: ProjectApi.subjectEditLog + subjectId,
  })
}

/**
 * 删除受试者
 */
export function deleteSubject<T>(subjectId: string) {
  return http.delete<T>({
    url: ProjectApi.deleteSubject + subjectId,
  })
}

/**
 * 删除受试者
 */
export function stopSubject<T>(subjectId: string) {
  return http.delete<T>({
    url: ProjectApi.stopSubject + subjectId,
  })
}

/**
 * 受试者 访视计划
 */
export function getVisitPlan(params: any) {
  return http.get({
    url: ProjectApi.subjectVisitList,
    params,
  })
}

/**
 * 量表详情接口(问卷问题)
 */
export function getScaleDetail(scaleId: string) {
  return http.get({
    url: `${ProjectApi.subjectQuestions}/${scaleId}`,
  })
}

/**
 * 获取量表记录数据
 */
export function getScaleAnswers(params) {
  return http.get({
    url: ProjectApi.subjectAnswers,
    params,
  })
}

/**
 * 获取趋势图数据
 */
export function getScaleChartData(params) {
  return http.get({
    url: ProjectApi.subjectScaleChart,
    params,
  })
}

/**
 * 提交量表某一项的数据
 */
export function commitScaleDataRecord(data) {
  return http.post({
    url: ProjectApi.subjectScaleRecord,
    data,
  })
}

/**
 * 量表填写值的修改
 */
export function editScaleAnswer(data) {
  return http.put({
    url: ProjectApi.updateScaleAnswer,
    data,
  })
}

/**
 * 导入患者列表数据
 */
export function exportPatientList(data) {
  return http.get({
    url: ProjectApi.exportPatientList,
    params: data,
  })
}

/**
 * 批量导入受试者
 */
export function exportPatients(rsUserId, topicId, data) {
  return http.post({
    url: `${ProjectApi.exportPatient}${rsUserId}/${topicId}`,
    data,
  })
}

/**
 * 批量上传文件
 */

export function uploadTheFilesWithScaleData(options, type) {
  return http.post({
    config: {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    },
    url: `${ProjectApi.uploadTheFileWithNodeId + options?.nodeId}/${type}`,
    data: options?.data,
  })
}

export function getFilesListWithScaleId(id, type) {
  return http.get({
    url: `${ProjectApi.getFileList + id}/${type}`,
  })
}

export function deleteTheFileWithId(id) {
  return http.delete({
    url: ProjectApi.deleteFile + id,
  })
}

export function downloadFileWithId(id) {
  return http.get({
    url: ProjectApi.downloadFile + id,
  })
}

/**
 * 获取待办任务
 */
export function getToDoList(params) {
  return http.get({
    url: ProjectApi.getToDoListApi,
    params,
  })
}

/**
 * 删除节点下检查项目
 */
export function handelNodeTaskDelete(data) {
  return http.post({
    url: ProjectApi.handleTheNodeApi,
    data,
  })
}

/**
 * 提醒受试者
 */
export function tipTheWaitToDo(params) {
  return http.get({
    url: ProjectApi.tipApi,
    params,
  })
}

/**
 * 干预措施记录
 */
export function getInterveneRecordList(params) {
  return http.get({
    url: ProjectApi.interveneRecords,
    params,
  })
}

/**
 * 保存干预措施记录
 */
export function addNewInterveneRecord(data) {
  return http.post({
    url: ProjectApi.addInterveneRecord,
    data,
  })
}

/**
 * 修改干预措施记录
 */
export function updateInterveneRecord(data) {
  return http.put({
    url: ProjectApi.updateInterveneRecord,
    data,
  })
}

/**
 *  删除干预措施
 */
export function deleteInterveneRecord(id) {
  return http.get({
    url: `${ProjectApi.deleteInterveneRecord}/${id}`,
  })
}

/**
 *  疗效评估分析
 */
export function efficiencyEvaluationApi(topicId) {
  return http.get({
    url: `${ProjectApi.efficacyEvaluation}/${topicId}`,
  })
}

/**
 *  患者来源分析
 */
export function patientSourceFromApi(topicId) {
  return http.get({
    url: `${ProjectApi.patientSource}/${topicId}`,
  })
}

/**
 *  患者性别年龄分析
 */
export function patientSexAgeApi(topicId) {
  return http.get({
    url: `${ProjectApi.sexAgeGroup}/${topicId}`,
  })
}

/**
 *  不良时间数据分析
 */
export function adverseEventAnalysis(topicId) {
  return http.get({
    url: `${ProjectApi.adverseEventChart}/${topicId}`,
  })
}

/**
 * 生成文件 id
*/
export function generateFileId() {
  return http.get({
    url: `${ProjectApi.fileId}`,
  })
}

/**
 * 受试者管理列表外面单独页面的
 */
export function getSubjectList2<T>(data: any) {
  return http.get<T>({
    url: ProjectApi.subjectList2,
    params: data,
  })
}
