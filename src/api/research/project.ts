import http from '@/service'

const PREFIX = '/heper-api'

enum ProjectApi {
  rsProcedureInfo = `${PREFIX}/rsProcedure/info/`,
  rsProcedureSave = `${PREFIX}/rsProcedure/saveForm`,
  topicList = `${PREFIX}/topic/list`,
  postTopic = `${PREFIX}/topic/insert`,
  updateTopic = `${PREFIX}/topic/update`,
  deleteTopic = `${PREFIX}/topic/delete`,
  topicDetail = `${PREFIX}/topic/detail`,
  topicNameList = `${PREFIX}/topic/rsTopicNameList`,
  stopTopic = `${PREFIX}/topic/stop`,
  researcherList = `${PREFIX}/rsUser/list`,
  researcherStop = `${PREFIX}/rsUser/stop`,
  researcherUpdate = `${PREFIX}/rsUser/update`,
  researcherInsert = `${PREFIX}/rsUser/insert`,
  researcherDetail = `${PREFIX}/rsUser/detail`,
  researcherNameList = `${PREFIX}/rsUser/rsUserNameList`,
  organNameList = `${PREFIX}/rsUser/organAllList`,
  scaleList = `${PREFIX}/api/rs/scale/page`,
  scaleCreaterList = `${PREFIX}/api/rs/scale/creates`,
  scaleDetail = `${PREFIX}/api/rs/scale/detail`,
  scaleSimpleList = `${PREFIX}/api/rs/scale/listSimple`,
  examinees = `${PREFIX}/rsData/examinees`,
  examGroup = `${PREFIX}/rsData/examGroup`,
  examQuit = `${PREFIX}/rsData/quit`,
  exportApi = `${PREFIX}/exportData/fmt/`,
  publishTopic = `${PREFIX}/topic/publish/`,
  postTopicGroupApi = `${PREFIX}/api/rs/exam/group/update`,
  queryTopicGroupsApi = `${PREFIX}/api/rs/exam/group/page`,
}

/**
 * 获取课题设计信息
 * @param topicId
 * @returns
 */
export function getProcedureInfo<T>(topicId: string) {
  return http.get<T>({
    url: ProjectApi.rsProcedureInfo + topicId,
  })
}

/**
 * 提交课题设计结果
 * @param data
 * @returns
 */
export function saveProcedure<T>(data: any) {
  return http.post<T>({
    url: ProjectApi.rsProcedureSave,
    data,
  })
}

/**
 * 获取科研课题列表
 */
export function getTopicList(params?: any) {
  return http.get({
    url: ProjectApi.topicList,
    params,
  })
}

/**
 * 提交新增科研课题
 * @param data
 * @returns
 */
export function addNewTopic(data: any) {
  return http.post({
    url: ProjectApi.postTopic,
    data,
  })
}
/**
 * 更新课题
 * @param data
 * @returns
 */
export function updateTheTopic(data: any) {
  return http.post({
    url: ProjectApi.updateTopic,
    data,
  })
}
/**
 * 删除课题
 * @param id
 * @returns
 */
export function deleteTheTopic(id: any) {
  return http.delete({
    url: `${ProjectApi.deleteTopic}/${id}`,
  })
}
/**
 * 终止课题
 * @param id
 * @returns
 */
export function stopTheTopic(id: any) {
  return http.delete({
    url: `${ProjectApi.stopTopic}/${id}`,
  })
}
/**
 * 获取课题名称列表
 * @returns
 */

export function getTopicNameList(params: any) {
  return http.get({
    url: ProjectApi.topicNameList,
    params,
  })
}

/**
 * 获取课题详情
 */

export function getTheTopicDetail(id: string) {
  return http.get({
    url: `${ProjectApi.topicDetail}/${id}`,
  })
}

/**
 * 获取研究者列表
 */
export function getReseacherList(params?: any) {
  return http.get({
    url: ProjectApi.researcherList,
    params,
  })
}

/**
 * 禁用研究员
 */
export function stopReseacher(id: any) {
  return http.delete({
    url: `${ProjectApi.researcherStop}/${id}`,
  })
}

/**
 *  修改研究员
 */
export function updateTheReseacher(data: any) {
  return http.post({
    url: ProjectApi.researcherUpdate,
    data,
  })
}

/**
 * 新增研究员
 */
export function addTheReseacher(data: any) {
  return http.post({
    url: ProjectApi.researcherInsert,
    data,
  })
}

/**
 * 研究员详情
 */
export function getTheReseacherDetail(id: any) {
  return http.get({
    url: `${ProjectApi.researcherDetail}/${id}`,
  })
}

/**
 * 研究员姓名列表
 */
export function getReseacherNameList(userName) {
  return http.get({
    url: ProjectApi.researcherNameList,
    params: {
      start: 1,
      size: 100,
      userName,
    },
  })
}

/**
 * 所属机构字典
 */
export function getOrganAllList() {
  return http.get({
    url: ProjectApi.organNameList,
  })
}

/**
 * 获取量表列表
 */
export function getScaleList(data: any) {
  return http.post({
    url: ProjectApi.scaleList,
    data,
  })
}

/**
 * 获取量表创建人列表
 */
export function getScaleCreaterList() {
  return http.get({
    url: ProjectApi.scaleCreaterList,
  })
}
/**
 * 获取简单量表名
 * @param id
 * @returns
 */
export function getScaleSimpleList() {
  return http.get({
    url: ProjectApi.scaleSimpleList,
  })
}

/// /统计
/**
 * 获取受试者人数分布
 * @param id
 * @returns
 */
export function getExaminees(topicId: string) {
  return http.get({
    url: `${ProjectApi.examinees}/${topicId}`,
  })
}

/**
 * 获取分组人数信息
 * @param id
 * @returns
 */
export function getExamGroup(topicId: string) {
  return http.get({
    url: `${ProjectApi.examGroup}/${topicId}`,
  })
}

/**
 * 累计退出人数
 * @param id
 * @returns
 */
export function getQuitCount(topicId: string) {
  return http.get({
    url: `${ProjectApi.examQuit}/${topicId}`,
  })
}

/**
 * 导出数据
 */
export function exportThePatientData(topicId, userId) {
  return http.post({
    url: `${ProjectApi.exportApi}/${topicId}/${userId}`,
    config: {
      headers: {
        'Content-Type': 'application/vnd.ms-excel;charset=utf-8',
      },
      responseType: 'blob',
    },
  })
}

/**
 * 发布课题
 */
export function publishTheTopic(data) {
  return http.post({
    url: ProjectApi.publishTopic,
    data,
  })
}

/**
 * 课题分组提交
 */
export function postTopicGroups(data) {
  return http.post({
    url: ProjectApi.postTopicGroupApi,
    data,
  })
}

/**
 * 课题分组分页查询
 */
export function queryTopicGroups(data) {
  return http.post({
    url: ProjectApi.queryTopicGroupsApi,
    data,
  })
}
