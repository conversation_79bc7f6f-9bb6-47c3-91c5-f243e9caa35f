import type { VisitRecordParams } from './type'
import http from '@/service'

const PREFIX = '/heper-api'

enum VisitRecordAPI {
  ListUrl = `${PREFIX}/visitRecord/list`,
  ExamListUrl = `${PREFIX}/visitRecord/examList`,
  AssayListUrl = `${PREFIX}/visitRecord/assayList`,
  AssayDetailUrl = `${PREFIX}/visitRecord/assayDetail`,
  OutPatientRecordUrl = `${PREFIX}/visitRecord/outPatientRecord`,
  OutHospitalRecordUrl = `${PREFIX}/visitRecord/outHospitalRecord`,
  InPatientRecordUrl = `${PREFIX}/visitRecord/inPatientRecord`,

}

/**
 * 获取患者就诊记录
 * @param params 查询参数
 *
 */
export function getVisitRecordListApi<T>(params: VisitRecordParams) {
  return http.get<T>({
    url: `${VisitRecordAPI.ListUrl}`,
    params,
  })
}
/**
 * 获取患者检查记录
 * @param params 查询参数
 *
 */
export function getVisitExamListApi<T>(params: VisitRecordParams) {
  return http.get<T>({
    url: `${VisitRecordAPI.ExamListUrl}`,
    params,
  })
}
/**
 * 获取患者检验记录
 * @param params 查询参数
 *
 */
export function getVisitAssayListApi<T>(params: VisitRecordParams) {
  return http.get<T>({
    url: `${VisitRecordAPI.AssayListUrl}`,
    params,
  })
}
/**
 * 获取患者检验记录详情
 * @param params 查询参数
 *
 */
export function getVisitAssayDetailApi<T>(id: string) {
  return http.get<T>({
    url: `${VisitRecordAPI.AssayDetailUrl}/${id}`,
  })
}
/**
 * 获取患者门诊病历
 * @param params 查询参数
 *
 */
export function getOutPatientRecordApi<T>(params: { patientId: string; recordId: string }) {
  return http.get<T>({
    url: `${VisitRecordAPI.OutPatientRecordUrl}`,
    params,
  })
}
/**
 * 获取患者出院记录
 * @param params 查询参数
 *
 */
export function getOutHospitalRecordApi<T>(params: { patientId: string; recordId: string }) {
  return http.get<T>({
    url: `${VisitRecordAPI.OutHospitalRecordUrl}`,
    params,
  })
}
/**
 * 获取患者入院记录
 * @param params 查询参数
 *
 */
export function getInPatientRecordApi<T>(params: { patientId: string; recordId: string }) {
  return http.get<T>({
    url: `${VisitRecordAPI.InPatientRecordUrl}`,
    params,
  })
}
