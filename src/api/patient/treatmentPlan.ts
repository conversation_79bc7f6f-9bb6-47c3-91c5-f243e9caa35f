import type { CreateTreatPlanParams, TreatMentPlanParams, UpdateTreatPlanParams } from './type'
import http from '@/service'

const PREFIX = '/heper-api'

enum TreatMentAPI {
  ListUrl = `${PREFIX}/treatmentPlan/list`,
  StandardDrugListUrl = `${PREFIX}/treatmentPlan/standardDrugList`,
  TreatMentDetailUrl = `${PREFIX}/treatmentPlan/detail`,
  FrequencyUrl = `${PREFIX}/treatmentPlan/frequency`,
  SupplyUrl = `${PREFIX}/treatmentPlan/supply`,
  CreateTreatPlanUrl = `${PREFIX}/treatmentPlan/insert`,
  UpdateTreatPlanUrl = `${PREFIX}/treatmentPlan/update`,
  DeleteTreatPlanUrl = `${PREFIX}/treatmentPlan/delete`,

}

/**
 * 获取治疗方案列表
 * @param params 查询参数
 *
 */
export function getTreatMentApi<T>(params: TreatMentPlanParams) {
  return http.get<T>({
    url: `${TreatMentAPI.ListUrl}`,
    params,
  })
}

/**
 * 获取标准列表
 *
 */
export function getStandardDrugListApi<T>() {
  return http.get<T>({
    url: `${TreatMentAPI.StandardDrugListUrl}`,
  })
}

/**
 * 获取治疗方案详情
 * @param id 治疗方案id
 */
export function getTreatMentDetailApi<T>(id: string) {
  return http.get<T>({
    url: `${TreatMentAPI.TreatMentDetailUrl}/${id}`,
  })
}
/**
 * 获取频次
 */
export function getFrequencyApi<T>() {
  return http.get<T>({
    url: `${TreatMentAPI.FrequencyUrl}`,
  })
}
/**
 * 获取用法，给药方式
 */
export function getSupplyApi<T>() {
  return http.get<T>({
    url: `${TreatMentAPI.SupplyUrl}`,
  })
}
/**
 * 新增治疗方案
 */
export function createTreatPlanApi<T>(data: CreateTreatPlanParams) {
  return http.post<T>({
    url: `${TreatMentAPI.CreateTreatPlanUrl}`,
    data,
  })
}

/**
 * 修改治疗方案
 */
export function updateTreatPlanApi<T>(data: UpdateTreatPlanParams) {
  return http.put<T>({
    url: `${TreatMentAPI.UpdateTreatPlanUrl}`,
    data,
  })
}
/**
 * 删除治疗方案
 */
export function deleteTreatPlanApi<T>(id: string) {
  return http.delete<T>({
    url: `${TreatMentAPI.DeleteTreatPlanUrl}/${id}`,
  })
}
