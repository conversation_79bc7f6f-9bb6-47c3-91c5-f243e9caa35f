export interface Response<T> {
  msg: string
  results: T
  state: number
  subCode: string
  subMsg: string
  total: number
}
/** 查询患者列表 */
export interface PatientListParams {
  endAge: null | number
  endV1Degree: null | number
  organId: string
  patientName: string
  phone: string
  searchOrganId: string
  sex: string
  size: number
  slmcNo: string
  start: number
  startAge: null | number
  startV1Degree: null | number
}
/** 普通搜索参数 */
export interface NormalParams {
  patientName: string
  phone: string
  startAge: number | null
  endAge: number | null
  sex: string
  startV1Degree: number | null
  endV1Degree: number | null
  slmcNo: string
  searchOrganId: string
  organId: string
}
export type ConditionType = '0' | '1' | '2' | '3' | '4' | '5'
/** 高级搜索条件列表 */
export interface HighConditions {
  code: string
  field: string
  group: string
  id: string
  name: string
  type: ConditionType
}

export interface SearchItem {
  code: string
  field: string
  group: string
  id: string
  name: string
  relation: string
  type: string
  value: string[]
}

export interface SearchList {
  list: SearchItem[]
}
/** 高级搜索参数 */
export interface HighSearchParams {
  endV1Degree?: string
  list: SearchList[]
  organId: string
  patientName?: string
  phone?: string
  searchOrganId: string
  size: number
  slmcNo?: string
  start: number
  startV1Degree?: string
}

/** 患者列表 */
export interface PatientRecord {
  age: string
  ageUnit: string
  birthday: string
  bloodPressure: string
  bloodType: string
  bmi: string
  companyName: string
  companyTel: string
  companyType: string
  contactsAddr: string
  contactsName: string
  contactsRelation: string
  contactsTel: string
  countryName: string
  createTime: string
  culturalName: string
  familyMembers: string
  heartRate: string
  height: string
  homeAddr: string
  id: string
  income: string
  lastVisit: string
  liveAddr: string
  liverComplaintType: number
  marriageName: string
  medicalInsuranceNo: string
  medicalInsuranceType: string
  nationCode: string
  nationName: string
  patientIdCardNum: string
  patientIdCardType: string
  patientName: string
  patientRecordNo: string
  patientSource: string
  phone: string
  phySource: string
  phyUpdateTime: string
  primaryIndex: string
  professionName: string
  pulse: string
  respiratoryRate: string
  sexCode: string
  sexName: string
  slmcNo: string
  status: number
  tag: string
  v1Visit: string
  weight: string
  lastVisitRate: string
}
/** 患者列表返回 */
export interface PatientRecordResult {
  countId: string
  current: number
  hitCount: boolean
  maxLimit: number
  optimizeCountSql: boolean
  orders: Order[]
  pages: number
  records: PatientRecord[]
  searchCount: boolean
  size: number
  total: number
}
/** 用户信息 */
export interface PatientInfoModal {
  age: string
  birthday: string
  bloodPressure: string
  bloodType: string
  bmi: string
  companyName: string
  companyTel: string
  companyType: string
  contactsAddr: string
  contactsName: string
  contactsRelation: string
  contactsTel: string
  countryName: string
  createTime: string
  culturalName: string
  familyMembers: string
  heartRate: string
  height: string
  homeAddr: string
  id: string
  income: string
  lastVisit: string
  liveAddr: string
  liverComplaintType: number
  marriageName: string
  medicalInsuranceNo: string
  medicalInsuranceType: string
  nationCode: string
  nationName: string
  patientIdCardNum: string
  patientIdCardType: string
  patientName: string
  patientRecordNo: string
  patientSource: string
  phone: string
  phySource: string
  primaryIndex: string
  professionName: string
  pulse: string
  respiratoryRate: string
  sexCode: string
  sexName: string
  slmcNo: string
  status: number
  tag: string
  v1Visit: string
  weight: string
  phyUpdateTime: string
  healthCardNum: string
}
/** 病史信息 */
export interface MedicalHistoryModal {
  allergyHistory: string
  chiefComplaint: string
  drugHistory: string
  familyHistory: string
  id: string
  maritalHistory: string
  menstrualHistory: string
  operationHistory: string
  pastHistory: string
  patientId: string
  patientName: string
  patientRecordNo: string
  personalHistory: string
  presentHistory: string
  slmcNo: string
  dataSource: string
  dataUpdateTime: string
}
/** 编辑用户信息参数 */
export interface UpdatePatientInfoParams {
  age: string
  birthday: string
  bloodPressure: string
  bloodType: string
  bmi: number | null | string
  companyName: string
  companyTel: string
  companyType: string
  contactsAddr: string
  contactsName: string
  contactsRelation: string
  contactsTel: string
  countryName: string
  createTime: string
  culturalName: string
  familyMembers: string
  heartRate: string
  height: string
  homeAddr: string
  id: string
  income: string
  lastVisit: string
  liveAddr: string
  liverComplaintType: number
  marriageName: string
  medicalInsuranceNo: string
  medicalInsuranceType: string
  nationCode: string
  nationName: string
  patientIdCardNum: string
  patientIdCardType: string
  patientName: string
  patientRecordNo: string
  patientSource: string
  phone: string
  phySource: string
  primaryIndex: string
  professionName: string
  pulse: string
  respiratoryRate: string
  sexCode: string
  sexName: string
  slmcNo: string
  status: number
  tag: string
  v1Visit: string
  weight: string
}

/** 药品分组 */
export type DrugGroup = '抗炎保肝类药物' | '抗纤维化类药物' | '抗病毒类药物' | '降脂类药物' | '降糖类药物' | '降压类药物' | '肝损伤类药物'
export interface TreatMentPlanParams {
  drugGroup: DrugGroup
  patientId: string
  size: number
  start: number
}

export interface Order {
  asc: boolean
  column: string
}
/** 治疗方案列表 */
export interface TreatMentRecord {
  dose: number
  doseUnit: string
  drugChannel: string
  drugGroup: string
  drugName: string
  endDate: string
  frequencyName: string
  id: string
  patientId: string
  patientName: string
  prescriptionDate: string
  qty: number
  slmcNo: string
  source: string
  specName: string
  startDate: string
  status: number
  stopDate: string
  supplyName: string
}
/** 治疗方案列表 */
export interface TreatMentModal {
  countId: string
  current: number
  hitCount: boolean
  maxLimit: number
  optimizeCountSql: boolean
  orders: Order[]
  pages: number
  records: TreatMentRecord[]
  searchCount: boolean
  size: number
  total: number
}

/** 新增药品 */
export interface CreateTreatPlan {
  dose: number
  doseUnit: string
  drugChannel: string
  drugGroup: string
  drugName: string
  drugType: string
  endDate: string
  frequencyName: string
  id: string
  patientId: string
  patientName: string
  prescriptionDate: string
  qty: number
  slmcNo: string
  source: string
  specName: string
  startDate: string
  status: number
  stopDate: string
  supplyName: string
}
/** 标准药品列表 */
export interface StandardDrugList {
  dose: number
  doseUnit: string
  drugGroup: string
  drugName: string
  drugType: string
  frequencyName: string
  id: string
  qty: number
  specName: string
  supplyName: string
}
/** 治疗方案详情 */
export interface TreatPlanDetail {
  dose: number
  doseUnit: string
  drugChannel: string
  drugGroup: string
  drugName: string
  drugType: string
  endDate: string
  frequencyName: string
  id: string
  patientId: string
  patientName: string
  prescriptionDate: string
  qty: number
  slmcNo: string
  source: string
  specName: string
  startDate: string
  status: 0 | 1 | null
  stopDate: string
  supplyName: string
}

/** 药品频次 */
export interface Frequency {
  frequencyCode: string
  frequencyName: string
  id: string
}
/** 用法，给药方式 */
export interface supply {
  id: string
  supplyName: string
}
export type FormattedValue = string | [string, string]
/** 新增治疗方案 */
export interface CreateTreatPlanParams {
  dose: string | null
  doseUnit: string
  drugChannel: null | string
  drugGroup: string
  drugName: string
  drugType: string
  endDate?: string
  frequencyName: string
  id?: string
  patientId: string
  patientName: string
  prescriptionDate: FormattedValue | null | undefined
  qty?: number
  slmcNo: string
  source: string
  specName: null | string
  startDate?: string
  status: 0 | 1 | null
  stopDate?: string
  supplyName: string
}

/** 修改治疗方案 */
export interface UpdateTreatPlanParams {
  dose: string | null
  doseUnit: string
  drugChannel: null | string
  drugGroup: string
  drugName: string
  drugType: string
  endDate?: string
  frequencyName: string
  id: string
  patientId: string
  patientName: string
  prescriptionDate: FormattedValue | null | undefined
  qty?: string
  slmcNo: string
  source: string
  specName: null | string
  startDate?: string
  status: 0 | 1 | null
  stopDate?: string
  supplyName: string
}
/** 就诊记录查询 */
export interface VisitRecordParams {
  patientId: string
  recordId?: string
  size: number
  start: number
}
/** 就诊记录列表 */
export interface VisitRecord {
  departmentId: string
  departmentName: string
  hospital: string
  patientId: string
  patientName: string
  recordId: string
  treatDate: string
  type: number
}
/** 就诊记录返回 */
export interface VisitRecordResult {
  countId: string
  current: number
  hitCount: boolean
  maxLimit: number
  optimizeCountSql: boolean
  orders: Order[]
  pages: number
  records: VisitRecord[]
  searchCount: boolean
  size: number
  total: number
}
/** 检查报告 */
export interface ExamRecord {
  auditTime: string
  auditor: string
  emergency: number
  examBodypart: string
  examItem: string
  examType: string
  id: string
  imageDiagnosis: string
  imageSight: string
  patientId: string
  patientName: string
  performTime: string
  performer: string
  recordId: string
  register: string
  registerTime: string
  reportTime: string
  reporter: string
  requestTime: string
  requester: string
  reviseTime: string
  reviser: string
  slmcNo: string
}
/** 检查报告返回 */
export interface ExamListResult {
  countId: string
  current: number
  hitCount: boolean
  maxLimit: number
  optimizeCountSql: boolean
  orders: Order[]
  pages: number
  records: ExamRecord[]
  searchCount: boolean
  size: number
  total: number
}
/** 检验记录 */
export interface AssayRecord {
  auditTime: string
  auditor: string
  departmentId: string
  departmentName: string
  id: string
  infantFlag: string
  patientId: string
  patientName: string
  patientType: string
  projectName: string
  recordId: string
  reportTime: string
  reporter: string
  requestTime: string
  sampleName: string
  sampler: string
  samplingTime: string
  slmcNo: string
  testPurpose: string
  testTime: string
  testType: string
  tester: string
  wardId: string
  wardName: string
}
/** 检验报告返回 */
export interface AssayResult {
  countId: string
  current: number
  hitCount: boolean
  maxLimit: number
  optimizeCountSql: boolean
  orders: Order[]
  pages: number
  records: AssayRecord[]
  searchCount: boolean
  size: number
  total: number
}
/** 检验记录详情 */
export interface AssayDetail {
  assayProjectId: string
  chineseName: string
  emergency: number
  englishName: string
  hint: string
  id: string
  itemId: string
  negativePositive: number
  patientId: string
  recordId: string
  refValue: string
  reportTime: string
  result: string
  unit: string
}
/** 诊断列表 */
export interface TreatDiagnoseDTOList {
  diagnoseIcd10: string
  diagnoseName: string
  diagnoseType: number
  diagnosisType: string
  isMain: boolean
  diagnoseRemark: string
}
/** 门诊病历 */
export interface OutPatientRecord {
  age: number
  ageUnit: string
  attackDate: string
  auxiliaryExam: string
  birthday: string
  chiefComplaint: string
  chineseMedicalFourDiagnostic: string
  company: string
  contactsName: string
  contactsTel: string
  countryCode: string
  countryName: string
  departmentId: string
  departmentName: string
  doctorGroupId: string
  doctorGroupName: string
  doctorId: string
  doctorName: string
  familyHistory: string
  goType: string
  homeAddr: string
  hospitalCode: string
  hospitalName: string
  id: string
  liveAddr: string
  maritalHistory: string
  menstrualHistory: string
  nation: string
  nationCode: string
  pastHistory: string
  patientId: string
  patientIdCardNum: string
  patientName: string
  patientNatureId: string
  patientNatureName: string
  patientTypeId: string
  patientTypeName: string
  personalHistory: string
  phone: string
  physicalExam: string
  presentHistory: string
  recordId: string
  resolution: string
  sex: string
  slmcNo: string
  specialExam: string
  treatBeginDate: string
  treatDate: string
  treatDiagnoseDTOList: TreatDiagnoseDTOList[]
  treatEndDate: string
  treatTimes: number
  visitFlag: number
  infection: number | null
}
/** 入院记录 */
export interface InPatientRecord {
  auxiliaryExam: string
  bedNum: string
  birthHistory: string
  birthday: string
  chiefComplaint: string
  departmentId: string
  departmentName: string
  doctorId: string
  doctorName: string
  familyHistory: string
  feedingHistory: string
  homeAddr: string
  id: string
  liveAddr: string
  maritalHistory: string
  marriageName: string
  menstrualHistory: string
  narrator: string
  nationName: string
  pastHistory: string
  patientId: string
  patientName: string
  patientRecordNo: string
  personalHistory: string
  physicalExam: string
  presentHistory: string
  professionName: string
  recordId: string
  sexName: string
  slmcNo: string
  specialExam: string
  treatDate: string
  wardName: string
}
/** 入院诊断 */
export interface InTreatDiagnoseDTOList {
  diagnoseDetailSn: number
  diagnoseIcd10: string
  diagnoseName: string
  diagnoseRemark: string
  diagnoseSureType: number
  diagnosisType: string
  isMain: boolean
}
/** 出院诊断 */
export interface OutTreatDiagnoseDTOList {
  diagnoseDetailSn: number
  diagnoseIcd10: string
  diagnoseName: string
  diagnoseRemark: string
  diagnoseSureType: number
  diagnosisType: string
  isMain: boolean
}
/** 出院记录 */
export interface OutHospitalRecord {
  bedNum: string
  birthday: string
  departmentId: string
  departmentName: string
  dischargeBloodPressure: string
  dischargeBodyWeight: string
  dischargeBreathe: string
  dischargeMedication: string
  dischargePulse: string
  dischargeStatus: string
  dischargeTemperature: string
  dischargeTime: string
  dischargeWhere: string
  doctorId: string
  doctorName: string
  hospitalCourse: string
  hospitalizedCase: string
  hospitalizedCause: string
  hospitalizedDays: string
  id: string
  inHospitalTime: string
  inTreatDiagnoseDTOList: InTreatDiagnoseDTOList[]
  outTreatDiagnoseDTOList: OutTreatDiagnoseDTOList[]
  patientId: string
  patientName: string
  patientRecordNo: string
  recordId: string
  sexName: string
  slmcNo: string
  transferDate: string
  transferDoctor: string
  transferInstitutions: string
  transferPurpose: string
  wardName: string
}

export interface AddPatient {
  addPatientType?: string
  doctorId?: string
  doctorName?: string
  medicalInsuranceNo?: string
  patientName: string
  operateType?: string
  patientIdCardNum: string
  phone: string
  patientRecordNo: string
  patientIdCardType?: string
  createOrganId: string
  userId?: string
  primaryIndex?: string
}
