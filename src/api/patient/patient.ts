import type { AddPatient, PatientListParams, UpdatePatientInfoParams } from './type'
import http from '@/service'

const PREFIX = '/heper-api'

enum PatientAPI {
  InfoUrl = `${PREFIX}/patientData/patient/patientInfo`,
  MedicalHistoryUrl = `${PREFIX}/patientData/patient/patientMedicalHistory`,
  UpdateInfoUrl = `${PREFIX}/patientData/patient/updatePatientInfo`,
  PatientListUrl = `${PREFIX}/patientData/patient/patientList`,
  PatientNameUrl = `${PREFIX}/patientData/patient/patientNameList`,
  PatientAddUrl = `${PREFIX}/patientData/patient/insertPatient`,
  SynchronizeDataTimeUrl = `${PREFIX}/patientData/patient/synchronizeDataTime`,
  SynchronizeDataUrl = `${PREFIX}/patientData/patient/synchronizeData`,
  AdvancedSearchListUrl = `${PREFIX}/patientData/patient/advancedSearchList`,
  AdvancedSearchPatientListUrl = `${PREFIX}/patientData/patient/advancedSearchPatientList`,
  DrugHistory = `${PREFIX}/patientData/patient/drugHistory`,
  UpdateDiseaseHistory = `${PREFIX}/patientData/patient/updatePatientMedicalHistory2`,
  getPatientDiseaseHistory = `${PREFIX}/patientData/patient/patientMedicalHistory2`,
  otherDisease = `${PREFIX}/patientData/patient/diseaseHistory`,
  patientHealthSummary = `${PREFIX}/hma/healthSummary/2`,
  patientHealthSummary2 = `${PREFIX}/hma/healthReport/1`,
  patientImportantAssay = `${PREFIX}/patientData/patient/importantAssay/`,
  inpatientRecordist = `${PREFIX}/manage/qiguai/pullData/queryInpatientRecord`,
  exportPatientData = `${PREFIX}/manage/qiguai/pullData/exportInpatientRecord`,
  exportDataRecord = `${PREFIX}/manage/qiguai/pullData/records`,
  reExportData = `${PREFIX}/manage/qiguai/pullData/reDownload`,
}
/**
 * 获取高级搜索的条件
 */
export function getAdvancedSearchListApi<T>() {
  return http.get<T>({
    url: `${PatientAPI.AdvancedSearchListUrl}`,
  })
}
/**
 * 获取患者用户列表
 * @param patientId 患者id
 *
 */
export function getPatientListApi<T>(params: PatientListParams) {
  return http.get<T>({
    url: `${PatientAPI.PatientListUrl}`,
    params,
  })
}
/**
 * 获取患者健康小结
 * @param patientId 患者id
 *
 */
export function getPatientHealthSummary<T>(patientId) {
  return http.get<T>({
    url: `${PatientAPI.patientHealthSummary}/${patientId}`,
  })
}

/**
 * 获取患者健康小结2
 * @param patientId 患者id
 *
 */
export function getPatientHealthSummary2<T>(patientId) {
  return http.get<T>({
    url: `${PatientAPI.patientHealthSummary2}/${patientId}`,
  })
}

/**
 * 高级搜搜索获取患者用户列表
 * @param data 搜索参数
 *
 */
export function getAdvancedSearchPatientListApi<T>(data: any) {
  return http.post<T>({
    url: `${PatientAPI.AdvancedSearchPatientListUrl}`,
    data,
  })
}
/**
 * 获取患者基本信息
 * @param patientId 患者id
 *
 */
export function getPatientInfoApi<T>(patientId: string) {
  return http.get<T>({
    url: `${PatientAPI.InfoUrl}/${patientId}`,
  })
}
/**
 * 获取患者病史信息
 * @param patientId 患者id
 *
 */
export function getMedicalHistoryInfoApi<T>(patientId: string) {
  return http.get<T>({
    url: `${PatientAPI.MedicalHistoryUrl}/${patientId}`,
  })
}
/**
 * 修改基本信息
 * @param data 患者信息
 *
 */
export function UpdateInfoApi<T>(data: UpdatePatientInfoParams) {
  return http.put<T>({
    url: `${PatientAPI.UpdateInfoUrl}`,
    data,
  })
}
/**
 * 获取患者姓名
 * @param patientName 患者姓名
 *
 */
export function getPatientNameApi<T>(params: { patientName: string; organId: string }) {
  return http.get<T>({
    url: `${PatientAPI.PatientNameUrl}`,
    params,
  })
}

export function addPatientAPI<T>(data: AddPatient) {
  return http.post<T>({
    url: `${PatientAPI.PatientAddUrl}`,
    data,
  })
}
/**
 * 获取最新同步时间
 *
 */
export function getSynchronizeDataTimeApi<T>() {
  return http.get<T>({
    url: `${PatientAPI.SynchronizeDataTimeUrl}`,
  })
}
/**
 * 同步数据
 */
export function synchronizeDataUrlApi<T>() {
  return http.get<T>({
    url: `${PatientAPI.SynchronizeDataUrl}`,
  })
}

/// 用药调查表
export function getPatientDiseaseUseDrug(name: string) {
  return http.get({
    url: PatientAPI.DrugHistory,
    params: {
      type: name,
    },
  })
}

/**
 * 提交病史信息
 */
export function updateThePatientDiseaseHistory(data: any) {
  return http.post({
    url: PatientAPI.UpdateDiseaseHistory,
    data,
  })
}

/**
 * 提交病史信息
 */
export function getPatientDiseaseHistory(patientId: string) {
  return http.get({
    url: `${PatientAPI.getPatientDiseaseHistory}/${patientId}`,
  })
}

/**
 * 其他疾病下拉选择
 */
export function getOtherDiseaseForHistory(type) {
  return http.get({
    url: PatientAPI.otherDisease,
    params: {
      type,
    },
  })
}

/**
 * 获取最近的重要检查
 */
export function getEarlySeriousChecks(patientId) {
  return http.get({
    url: PatientAPI.patientImportantAssay + patientId,
  })
}

/// 获取住院患者数据
export function getInpatientListApi<T>(data: any) {
  return http.post<T>({
    url: `${PatientAPI.inpatientRecordist}`,
    data,
  })
}

/// 导出患者数据
export function exportInpatientData<T>(data: any) {
  return http.post<T>({
    url: `${PatientAPI.exportPatientData}`,
    data,
  })
}

/// 导出数据记录
export function exportDataRecord<T>(data: any) {
  return http.post<T>({
    url: `${PatientAPI.exportDataRecord}`,
    data,
  })
}

/// 重新导出数据
export function reExportData<T>(recordId: string) {
  return http.get<T>({
    url: `${PatientAPI.reExportData}`,
    params: {
      recordId,
    },
  })
}
