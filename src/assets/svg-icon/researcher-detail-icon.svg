<?xml version="1.0" encoding="UTF-8"?>
<svg width="96px" height="99px" viewBox="0 0 96 99" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>编组 2备份</title>
    <defs>
        <filter x="-27.8%" y="-26.4%" width="155.6%" height="159.7%" filterUnits="objectBoundingBox" id="filter-1">
            <feOffset dx="0" dy="4" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="6" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0.288763033   0 0 0 0 0.604451915   0 0 0 0 0.834998532  0 0 0 0.3 0" type="matrix" in="shadowBlurOuter1" result="shadowMatrixOuter1"></feColorMatrix>
            <feMerge>
                <feMergeNode in="shadowMatrixOuter1"></feMergeNode>
                <feMergeNode in="SourceGraphic"></feMergeNode>
            </feMerge>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-2">
            <stop stop-color="#ECECEC" offset="0%"></stop>
            <stop stop-color="#E6E6E6" offset="100%"></stop>
        </linearGradient>
        <filter x="-25.6%" y="-25.6%" width="151.1%" height="151.2%" filterUnits="objectBoundingBox" id="filter-3">
            <feGaussianBlur stdDeviation="1.5" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="50%" x2="0%" y2="88.0105001%" id="linearGradient-4">
            <stop stop-color="#ECECEC" offset="0%"></stop>
            <stop stop-color="#F4F4F4" offset="100%"></stop>
        </linearGradient>
        <filter x="-47.4%" y="-50.0%" width="194.7%" height="200.0%" filterUnits="objectBoundingBox" id="filter-5">
            <feGaussianBlur stdDeviation="4.5" in="SourceGraphic"></feGaussianBlur>
        </filter>
        <linearGradient x1="50%" y1="0%" x2="50%" y2="100%" id="linearGradient-6">
            <stop stop-color="#50B2FA" offset="0%"></stop>
            <stop stop-color="#1B7CED" offset="100%"></stop>
        </linearGradient>
    </defs>
    <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="04-课题详情" transform="translate(-202, -105)">
            <g id="编组-2" transform="translate(194, 96)">
                <g id="编组-2备份" filter="url(#filter-1)" transform="translate(20, 20)">
                    <rect id="矩形" fill="#D8D8D8" opacity="0" x="0" y="0" width="72" height="72"></rect>
                    <g id="编组-10" transform="translate(3, 0)">
                        <path d="M7.5,65.5020576 L66,65.5020576 L66,67.5 C66,69.9852814 63.9852814,72 61.5,72 L12,72 C9.51471863,72 7.5,69.9852814 7.5,67.5 L7.5,65.5020576 L7.5,65.5020576 Z" id="矩形" fill="#C6D7DE"></path>
                        <path d="M12,3.12638804e-13 L47.9624658,3.12638804e-13 L47.9624658,3.12638804e-13 L66,18.0091046 L66,66 C66,68.4852814 63.9852814,70.5 61.5,70.5 L12,70.5 C9.51471863,70.5 7.5,68.4852814 7.5,66 L7.5,4.5 C7.5,2.01471863 9.51471863,3.13095343e-13 12,3.12638804e-13 Z" id="矩形" fill="url(#linearGradient-2)"></path>
                        <path d="M28.875,28.5 L56.625,28.5 C57.6605339,28.5 58.5,29.3394661 58.5,30.375 C58.5,31.4105339 57.6605339,32.25 56.625,32.25 L28.875,32.25 C27.8394661,32.25 27,31.4105339 27,30.375 C27,29.3394661 27.8394661,28.5 28.875,28.5 Z M28.875,37.5 L56.625,37.5 C57.6605339,37.5 58.5,38.3394661 58.5,39.375 C58.5,40.4105339 57.6605339,41.25 56.625,41.25 L28.875,41.25 C27.8394661,41.25 27,40.4105339 27,39.375 C27,38.3394661 27.8394661,37.5 28.875,37.5 Z M28.875,46.5 L56.625,46.5 C57.6605339,46.5 58.5,47.3394661 58.5,48.375 C58.5,49.4105339 57.6605339,50.25 56.625,50.25 L28.875,50.25 C27.8394661,50.25 27,49.4105339 27,48.375 C27,47.3394661 27.8394661,46.5 28.875,46.5 Z M34.875,55.5 L50.625,55.5 C51.6605339,55.5 52.5,56.3394661 52.5,57.375 C52.5,58.4105339 51.6605339,59.25 50.625,59.25 L34.875,59.25 C33.8394661,59.25 33,58.4105339 33,57.375 C33,56.3394661 33.8394661,55.5 34.875,55.5 Z" id="形状结合备份" fill-opacity="0.6" fill="#C0CED5"></path>
                        <path d="M46.745835,1.5 L46.745835,15.3376763 C46.745835,17.4087441 48.4247672,19.0876763 50.495835,19.0876763 L64.3491886,19.0876763 L64.3491886,19.0876763 L46.745835,1.5 Z" id="路径-2" fill="#D6DBE2" filter="url(#filter-3)"></path>
                        <path d="M48.245835,0 L48.245835,13.8376763 C48.245835,15.9087441 49.9247672,17.5876763 51.995835,17.5876763 L65.8491886,17.5876763 L65.8491886,17.5876763 L48.245835,0 Z" id="路径-2" fill="url(#linearGradient-4)"></path>
                        <rect id="矩形" fill-opacity="0.5" fill="#1E7FEE" filter="url(#filter-5)" x="12" y="30" width="28.5" height="27" rx="4.5"></rect>
                        <rect id="矩形" fill="#0060D9" x="0" y="16.5" width="37.5" height="39" rx="4.5"></rect>
                        <rect id="矩形" fill="url(#linearGradient-6)" x="0" y="16.5" width="37.5" height="37.5" rx="4.5"></rect>
                        <path d="M19.6754918,32.4622039 C19.347746,29.6440887 18.6307552,27.6326969 17.5860186,26.6381954 C17.0385256,26.1239724 16.4310334,25.8784165 15.7020428,25.9015277 L15.6172938,25.9015277 L14.2073119,25.9593055 C12.6285822,26.041639 11.3040992,26.4518618 10.2593626,27.2116407 C9.42087335,27.8197527 8.76463177,28.6380317 8.29063786,29.6556443 C7.4761483,31.4214802 7.50007367,33.1526494 7.50007367,33.1757605 L7.50007367,33.1880383 C7.50007367,35.7020975 7.81589395,38.0760454 8.42413613,40.0527705 C8.98287896,41.8893843 9.74861913,43.3042197 10.6471076,44.1694432 C11.2186003,44.7190551 11.8140926,45 12.3975852,45 C12.9443281,45 13.3575728,44.7775552 13.7700675,44.2864434 C14.1713124,43.8068871 14.4990582,43.1287195 14.8395538,42.4036073 C15.447046,41.1404388 16.1280373,39.7024923 17.4645201,39.0236024 C18.1567613,38.6726019 19.3597458,37.9243786 19.7122413,36.8135992 C19.7258418,36.7879626 19.7340006,36.759973 19.736241,36.7312657 C19.8097401,36.5557654 19.8577394,36.357154 19.8577394,36.1700982 C19.8704893,35.5273195 19.8577394,34.0648174 19.6754918,32.4629261 L19.6754918,32.4622039 Z M31.4870902,27.3163631 C31.4503407,27.047696 30.9395972,26.1586392 25.6544151,25.8784165 C24.7664264,25.8321942 24.2211834,25.7960831 23.3339448,25.7960831 C22.6814532,25.7960831 21.5339679,25.6913607 20.5507306,25.5750827 C19.5682432,25.459527 18.3502588,25.3100268 18.8460024,26.5421397 C19.2329408,27.5081028 19.6149405,28.4758937 19.9919877,29.4454773 C20.2349846,30.2991452 20.4172323,31.2813689 20.5507306,32.3921483 C20.7082285,33.7376502 20.7329782,34.6960405 20.7329782,35.4688194 L20.7329782,35.5966529 C20.7329782,35.8306533 20.9392256,36.0299869 21.1829724,36.0177091 C22.1909595,35.9830424 23.8311884,35.749042 25.8366627,34.6144293 C27.3186437,33.7607614 28.8013747,32.4622039 29.9068605,31.0358129 C31.0130963,29.6086997 31.5958388,28.2523644 31.4870902,27.3163631 L31.4870902,27.3163631 Z" id="形状" fill="#A0D5FF" fill-rule="nonzero"></path>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>