<?xml version="1.0" encoding="UTF-8"?><svg version="1.1" width="249px" height="114px" viewBox="0 0 249.0 114.0" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink"><defs><clipPath id="i0"><path d="M1920,0 L1920,1401 L0,1401 L0,0 L1920,0 Z"></path></clipPath><clipPath id="i1"><path d="M239,0 L239,48.06 L248.947217,56.1361293 L239,66.083 L239,114 L0,114 L0,0 L239,0 Z"></path></clipPath><filter id="i2" x="-32.5203252%" y="-51.2820513%" filterUnits="userSpaceOnUse" width="132.520325%" height="151.282051%"><feGaussianBlur stdDeviation="15" in="SourceAlpha" result="i3"></feGaussianBlur><feOffset dx="0" dy="0" in="i3" result="i4"></feOffset><feColorMatrix values="0 0 0 0.1042873475609756 0 0 0 0 0.4401795922256098 0 0 0 0 1.0 0 0 0 0 0.5 0" type="matrix" in="i4"></feColorMatrix></filter><linearGradient id="i5" x1="124.473609px" y1="0px" x2="124.473609px" y2="114px" gradientUnits="userSpaceOnUse"><stop stop-color="rgba(27, 143, 233, 0.5)" offset="0%"></stop><stop stop-color="rgba(27, 143, 233, 0)" offset="54.763986%"></stop><stop stop-color="rgba(27, 143, 233, 0.5)" offset="100%"></stop></linearGradient><clipPath id="i6"><path d="M2,41.193808 L2,42.4421053 L0,42.4421053 L0,41.193808 L2,41.193808 Z M2,37.4489164 L2,38.6972136 L0,38.6972136 L0,37.4489164 L2,37.4489164 Z M2,33.7040248 L2,34.952322 L0,34.952322 L0,33.7040248 L2,33.7040248 Z M2,29.9591331 L2,31.2074303 L0,31.2074303 L0,29.9591331 L2,29.9591331 Z M2,26.2142415 L2,27.4625387 L0,27.4625387 L0,26.2142415 L2,26.2142415 Z M2,22.4693498 L2,23.7176471 L0,23.7176471 L0,22.4693498 L2,22.4693498 Z M2,18.7244582 L2,19.9727554 L0,19.9727554 L0,18.7244582 L2,18.7244582 Z M2,14.9795666 L2,16.2278638 L0,16.2278638 L0,14.9795666 L2,14.9795666 Z M2,11.2346749 L2,12.4829721 L0,12.4829721 L0,11.2346749 L2,11.2346749 Z M2,7.48978328 L2,8.7380805 L0,8.7380805 L0,7.48978328 L2,7.48978328 Z M2,3.74489164 L2,4.99318885 L0,4.99318885 L0,3.74489164 L2,3.74489164 Z M2,0 L2,1.24829721 L0,1.24829721 L0,0 L2,0 Z"></path></clipPath><linearGradient id="i7" x1="1px" y1="0px" x2="1px" y2="42.4421053px" gradientUnits="userSpaceOnUse"><stop stop-color="rgba(0, 194, 255, 0)" offset="0%"></stop><stop stop-color="#39DBF1" offset="50.7730551%"></stop><stop stop-color="rgba(0, 143, 253, 0)" offset="100%"></stop></linearGradient><clipPath id="i8"><path d="M36,0 L36,41 L33.9313364,41 L34.3868578,2.04859441 L0,2.04859441 L0,0 L36,0 Z"></path></clipPath><linearGradient id="i9" x1="36px" y1="-1.28022593e-15px" x2="21.9271373px" y2="16.5641099px" gradientUnits="userSpaceOnUse"><stop stop-color="#64F9FF" offset="0%"></stop><stop stop-color="rgba(0, 233, 194, 0)" offset="100%"></stop></linearGradient></defs><g transform="translate(-836.0 -547.0)"><g clip-path="url(#i0)"><g transform="translate(623.0 258.0)"><g transform="translate(213.0 289.0)"><g clip-path="url(#i1)"><polygon points="0,0 248.947217,0 248.947217,114 0,114 0,0" stroke="none" fill="rgba(0, 21, 51, 0.95)"></polygon><path d="M279,-30 L279,144 L-30,144 L-30,-30 L279,-30 Z M239,0 L0,0 L0,114 L239,114 L239,66.083 L248.947217,56.1361293 L239,48.06 L239,0 Z" stroke="none" fill="#000000" filter="url(#i2)"></path></g><g clip-path="url(#i1)"><polygon points="239,0 239,48.06 248.947217,56.1361293 239,66.083 239,114 0,114 0,0 239,0 239,0" stroke="url(#i5)" stroke-width="4" fill="none" stroke-miterlimit="5"></polygon></g><g transform="translate(140.22105263157894 7.494736842105377) rotate(90.0)"><g clip-path="url(#i6)"><polygon points="0,0 2,0 2,42.4421053 0,42.4421053 0,0" stroke="none" fill="url(#i7)"></polygon></g></g><g transform="translate(37.0 114.0) rotate(-180.0)"><g clip-path="url(#i8)"><polygon points="0,0 36,0 36,41 0,41 0,0" stroke="none" fill="url(#i9)"></polygon></g></g><g transform="translate(36.0 0.0) scale(-1.0 1.0)"><g clip-path="url(#i8)"><polygon points="0,0 36,0 36,41 0,41 0,0" stroke="none" fill="url(#i9)"></polygon></g></g><g transform="translate(203.0 114.0) scale(1.0 -1.0)"><g clip-path="url(#i8)"><polygon points="0,0 36,0 36,41 0,41 0,0" stroke="none" fill="url(#i9)"></polygon></g></g><g transform="translate(203.0 0.0)"><g clip-path="url(#i8)"><polygon points="0,0 36,0 36,41 0,41 0,0" stroke="none" fill="url(#i9)"></polygon></g></g></g></g></g></g></svg>