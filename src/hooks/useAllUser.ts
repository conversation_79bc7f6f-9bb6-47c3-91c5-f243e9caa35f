import type { UserPageListRes } from '../api/user/type'
import { getUserApi } from '../api/user/user'

export function userAllUser() {
  const userList = ref<{
    label: string
    value: string
  }[]>([])

  async function getUserList() {
    try {
      const { data } = await getUserApi<UserPageListRes>({
        start: 1,
        size: 1000,
        userType: '',
      })

      if (data) {
        userList.value = data?.records?.map((item) => {
          return {
            value: item.id,
            label: item.userName,
          }
        }) || []
      }
    }
    catch (error) {
      console.log(error)
    }
  }
  onMounted(() => {
    getUserList()
  })
  return {
    userList,
  }
}
