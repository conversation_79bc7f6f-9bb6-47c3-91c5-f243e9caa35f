import { useAuthStore } from '@/store'
import { isArray, isMasterAdmin } from '@/utils'

/** 权限判断 */
export function usePermission() {
  const auth = useAuthStore()

  function hasPermission(permissionKey: string) {
    const { userInfo } = auth
    const permissionsPower = userInfo?.power || []
    const roleList = userInfo?.roleList || []
    let has = roleList.some(role => isMasterAdmin(role?.remark))

    if (!has) {
      if (isArray(permissionsPower))
        has = permissionsPower.includes(permissionKey)

      /*       if (isString(permission))
        has = (permission as Auth.RoleType) === userRole */
    }
    return has
  }

  return {
    hasPermission,
  }
}
