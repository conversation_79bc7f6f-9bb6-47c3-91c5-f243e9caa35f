import { useRoute } from 'vue-router'
import type { Answer } from '@/views/follow/questionnaire/types'
import { CurrentTaskAnswersKey, CurrentTaskKey, HandleNextTaskKey, QuestionStatusKey, getTasksKey } from '@/views/follow/questionnaire/types'
import { useAuthStore } from '@/store'
import { saveAnswerAPI, saveAnswerV0API } from '@/api/followCenter'

export interface Question {
  name?: string
  content: any
  id: string
  type?: 'input' | 'placeholder' | 'text' | 'inputText'
  label?: string
  unit?: string | null
  required?: boolean
  options?: string[]
  formula?: string
  optionKey?: string
  precision?: number
}

export function useQuestion() {
  const route = useRoute()
  const questionStatus = inject(QuestionStatusKey)
  const currentTask = inject(CurrentTaskKey)
  const handleNextTask = inject(HandleNextTaskKey)
  const getTasks = inject(getTasksKey)!
  const pageLoading = inject('pageLoading') as Ref<boolean>
  const questions = ref<Question[]>([])

  const currentTaskAnswers = inject(CurrentTaskAnswersKey)!

  watch(currentTaskAnswers, (val) => {
    try {
      if (val) {
        questions.value.forEach((question) => {
          const questionId = question.id
          const answer = val.find(item => item.questionId === questionId)
          if (answer) {
            try {
              question.content = JSON.parse(answer.content)
            }
            catch (error) {
              question.content = answer
            }
          }
        })
      }
    }
    catch (error) {
      console.log(error)
    }
  }, {
    immediate: true,
  })

  function resetQuestions() {
    if (currentTaskAnswers.value) {
      questions.value.forEach((question) => {
        const questionId = question.id
        const answer = currentTaskAnswers.value!.find(item => item.questionId === questionId)
        if (answer)
          question.content = JSON.parse(answer.content)
      })
    }
  }

  function toggleStatus() {
    questionStatus!.value = questionStatus!.value === 'check' ? 'edit' : 'check'
  }

  function setStatus(status: 'check' | 'edit') {
    questionStatus!.value = status
  }

  const isEdit = computed(() => {
    return questionStatus!.value === 'edit'
  })

  async function saveAnswer(questionAnswers: Answer[], scaleConclusion: string, isNext = false) {
    try {
      pageLoading.value = true
      const { userInfo } = useAuthStore()
      const phaseTemplateCode = route.query.phaseTemplateCode
      const { planId, planPhaseId, primaryIndex, taskId, scaleId, planTemplateId, taskTemplateId } = currentTask!.value!
      const params = {
        planId,
        planPhaseId,
        primaryIndex,
        questionAnswers,
        scaleConclusion,
        scaleId,
        taskId,
        planTemplateId,
        taskTemplateId,
        writeId: userInfo.id,
        writeType: 'MEDICAL',
      }

      let saveResults = false

      if (phaseTemplateCode === '0') {
        const { data } = await saveAnswerV0API<any>(params)
        saveResults = data
      }
      else {
        const { data } = await saveAnswerAPI<any>(params)
        saveResults = data
      }
      if (saveResults) {
        getTasks(null, false)

        window.$message.success('保存成功')
        setStatus('check')
        if (isNext)
          handleNextTask!()
      }
      else {
        window.$message.error('保存失败')
      }
    }
    catch (error) {
      console.log(error)
    }
    finally {
      pageLoading.value = false
    }
  }

  return {
    questionStatus,
    toggleStatus,
    setStatus,
    isEdit,
    saveAnswer,
    questions,
    resetQuestions,
    currentTask,
  }
}
