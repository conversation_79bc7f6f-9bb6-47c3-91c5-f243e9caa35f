<script setup lang="ts">
import { SurveyCreator } from 'survey-creator-knockout'
import 'survey-core/defaultV2.min.css'
import 'survey-creator-core/survey-creator-core.min.css'
import 'survey-creator-core/survey-creator-core.i18n'
import 'survey-core/survey.i18n.js'
import '@/utils/survey/localization'
import '@/utils/survey/customFun'
import '@/utils/survey/customComponent'
import { JsonObject, Serializer } from 'survey-core'
import { settings } from 'survey-creator-core'
import { storeToRefs } from 'pinia'
import { useAppStore } from '@/store'

defineOptions({
  name: 'SurveyEditor',
})

const isLibShow = inject<Ref<boolean>>('isLibShow')!

const { siderCollapse } = storeToRefs(useAppStore())

const editorWidth = computed(() => {
  if (isLibShow.value) {
    if (siderCollapse.value)
      return 'w-[calc(100vw-300px)]'
    return 'w-[calc(100vw-530px)]'
  }
  return ''
})

const surveyContent = defineModel<string>('content')

const surveyCreatorRef = ref<HTMLElement>()

const customChoices = [
  {
    value: '1',
    text: '选项1',
  },
  {
    value: '2',
    text: '选项2',
  },
  {
    value: '3',
    text: '选项3',
  },
]

settings.toolbox.defaultJSON.radiogroup.choices = customChoices as any
settings.toolbox.defaultJSON.dropdown.choices = customChoices as any
settings.toolbox.defaultJSON.tagbox.choices = customChoices as any
settings.toolbox.defaultJSON.checkbox.choices = customChoices as any
settings.toolbox.defaultJSON.ranking.choices = customChoices as any

let creator: SurveyCreator | null = null

function renderSurvey(canEdit: boolean) {
  creator = null
  Serializer.addProperty('survey', {
    name: 'introduce',
    type: 'text',
    displayName: '适用场景',
    category: 'general',
    visibleIndex: 2,
  })

  JsonObject.metaData.addProperty('itemvalue', {
    name: 'score:number',
  })

  creator = new SurveyCreator({
    showLogicTab: canEdit,
    showDesignerTab: canEdit,
    showJSONEditorTab: !!__IS_DEV__,
    isAutoSave: true,
    showObjectTitles: true,
    showTitlesInExpressions: true,
    showThemeTab: !!__IS_DEV__,
    pageEditMode: 'single',
    previewOrientation: 'portrait',
    showSidebar: false,
  })

  creator.text = surveyContent.value!

  creator.saveSurveyFunc = (saveNo: any, callback: any) => {
    surveyContent.value = creator!.text
    callback(saveNo, true)
  }

  surveyCreatorRef.value && creator.render(surveyCreatorRef.value)
}

async function handleAdd(elements: Array<any>) {
  let originalData = creator!.JSON
  if (JSON.stringify(originalData) === '{}') {
    originalData = {
      elements: [
        ...elements,
      ],
    }
  }
  else {
    originalData.elements.push(...elements)
  }

  creator!.changeText(JSON.stringify(originalData))
  surveyContent.value = creator!.text

  // 编辑器滚动到底部
  // setTimeout(() => {
  //   const editerEl = document.querySelector('.sd-root-modern')

  //   if (editerEl)
  //     editerEl.scrollTop = editerEl.scrollHeight
  // }, 300)
}

if (import.meta.hot) {
  nextTick(() => {
    renderSurvey(true)
  })
}

defineExpose({
  renderSurvey,
  handleAdd,
})
</script>

<template>
  <div id="surveyCreator" ref="surveyCreatorRef" transition-all :class="editorWidth" />
</template>

<style>
#surveyCreator {
  height: 100vh
}

.svc-creator__area.svc-creator__area--with-banner {
  height: 100% !important;
}

.svc-creator__banner {
  height: 0 !important;
}
</style>
