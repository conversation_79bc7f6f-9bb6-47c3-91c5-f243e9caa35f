<script setup  lang="ts">
import { computed } from 'vue'

import { basicProps } from '../props'

const props = defineProps({
  okButtonProps: basicProps.okButtonProps,
  showOkBtn: basicProps.showOkBtn,
  okType: basicProps.okType,
  okText: basicProps.okText,
  footerButton: basicProps.footerButton,
  footerOffset: basicProps.footerOffset,
  showCancelBtn: basicProps.showCancelBtn,
  cancelButtonProps: basicProps.cancelButtonProps,
  cancelText: basicProps.cancelText,

})
const emit = defineEmits(['ok', 'cancel'])

function handleOk(e: Event) {
  emit('ok', e)
}

function handleCancel(e: Event) {
  emit('cancel', e)
}

const getFooterType = computed(() => props.footerButton)
</script>

<template>
  <div class="basicModalFooter" :style="{ marginRight: `${props.footerOffset}px` }">
    <div v-if="getFooterType === 'footerGroup'" class="basicModalFooter-buttonGroup">
      <slot name="insertFooter" />
      <n-button
        v-if="showOkBtn"
        :type="okType"

        v-bind="okButtonProps"
        class="saveButton"
        @click="handleOk"
      >
        {{ okText }}
      </n-button>
      <slot name="centerFooter" />
      <n-button
        v-if="showCancelBtn"
        v-bind="cancelButtonProps"
        class="cancelButton"
        @click="handleCancel"
      >
        {{ cancelText }}
      </n-button>
      <slot name="appendFooter" />
    </div>
    <div v-if="getFooterType === 'singleClose'" class="basicModalFooter-singleClose">
      <n-button class="closeButton" @click="handleCancel">
        关闭
      </n-button>
    </div>
    <div v-if="getFooterType === 'barFooter'" class="basicModalFooter-barFooter">
      <slot name="insertFooter" />
      <n-button
        v-if="showOkBtn"
        :type="okType"

        v-bind="okButtonProps"
        class="saveButton"
        @click="handleOk"
      >
        {{ okText }}
      </n-button>
      <slot name="centerFooter" />
      <n-button
        v-if="showCancelBtn"
        v-bind="cancelButtonProps"
        class="cancelButton"
        @click="handleCancel"
      >
        {{ cancelText }}
      </n-button>
      <slot name="appendFooter" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.basicModalFooter {

    &-buttonGroup {
        padding: 0px 20px 24px 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        .saveButton,
        .cancelButton {
            width: 100px;
            letter-spacing: 4px;
            text-indent: 4px;
        }
        .cancelButton {
            margin-left: 16px;
        }
    }
    &-singleClose {
        display: flex;
        align-items: center;
        justify-content: center;

        border-radius: 0px 0px 3px 3px;
        padding-bottom: 28px;

        .closeButton {
            width: 100px;
            letter-spacing: 4px;
            text-indent: 4px;
            background: #fff;
        }
    }
    &-barFooter {
        padding: 14px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #f9f9f9;
        border-radius: 0px 0px 3px 3px;
        box-shadow: 0px 1px 0px 0px rgba(232,232,232,0.50) inset;
        .saveButton,
        .cancelButton {
            width: 100px;
            letter-spacing: 4px;
            text-indent: 4px;
        }
        .cancelButton {
            margin-left: 16px;
        }
    }
}
</style>
