<script setup  lang="ts">
// import { ScrollContainer } from '@/components/Container'
import type { CSSProperties } from 'vue'
import { computed, ref } from 'vue'

const props = defineProps({
  loading: { type: Boolean },
  minHeight: { type: Number, default: 100 },
  height: { type: Number },
  visible: { type: Boolean },
  loadingTip: { type: String },
})

// const wrapperRef = ref<ComponentRef>(null)
const spinRef = ref<ElRef>(null)

// async function scrollTop() {
//   nextTick(() => {
//     const wrapperRefDom = unref(wrapperRef)
//     if (!wrapperRefDom)
//       return
//     ;(wrapperRefDom as any)?.scrollTo?.(0)
//   })
// }

const spinStyle = computed((): CSSProperties => {
  // 内容的高度
  return {
    minHeight: `${props.minHeight}px`,
    height: `${props.height}px`,
  }
})
</script>

<template>
  <div>
    <div ref="spinRef" :style="spinStyle">
      <n-spin type="uni" :show="loading" :description="loadingTip">
        <slot />
      </n-spin>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
