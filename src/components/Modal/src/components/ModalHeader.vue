<script lang="ts" setup>
defineProps({
  title: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['cancel'])
function handleCancel(e: Event) {
  emit('cancel', e)
}
</script>

<template>
  <div class="basicModal-headerWrap flex flex-col">
    <div class="basicModal-header">
      <span v-if="title" class="basicModal-headerTitle">{{ title }}</span>
      <span class="closeIconBox" @click="handleCancel">
        <SvgIcon local-icon="slmc-icon-deletex2" size="14" />
      </span>
    </div>
  </div>
</template>

<style scoped lang="scss">
.basicModal-headerWrap {
    padding: 0px 10px 0px 10px;
    &::after {
        content: '';
        display: block;
        width: 100%;
        height: 1px;
        background-color:  #EBEBEB;

    }
}
.basicModal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    padding: 12px 0px 12px 0px;
    width: 100%;
    height: 38px;
    background: #ffffff;
    // border-bottom: 1px solid #EBEBEB;
    color: #333333;
    border-radius: 3px 3px 0 0;

    .closeIconBox {
        display: block;
        width: 14px;
        height: 14px;
        cursor: pointer;
        margin-right: 6px;
    }
}
.basicModal-headerTitle {

    font-weight: 500;
}
</style>
