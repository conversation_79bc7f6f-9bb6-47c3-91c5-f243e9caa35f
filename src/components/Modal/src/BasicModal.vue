<script lang="ts" setup>
import {
  computed,
  getCurrentInstance,
  unref,
  useAttrs,
} from 'vue'
import type { CSSProperties } from 'vue'
import { omit } from 'lodash-es'

import type { ModalMethods, ModalProps } from './type'

import { basicProps } from './props'

import ModalHeader from './components/ModalHeader.vue'
import ModalFooter from './components/ModalFooter.vue'
import ModalWrapper from './components/ModalWrapper.vue'

import { isFunction } from '@/utils/common'
import { deepMerge } from '@/utils'

const props = defineProps({
  ...basicProps,
  loading: {
    type: Boolean,
    default: false,
  },
})
const emit = defineEmits(['visibleChange', 'cancel', 'ok', 'register', 'update:visible'])
const attrs = useAttrs()

const visibleRef = ref(true)
const propsRef = ref<Partial<ModalProps> | null>(null)
const modalWrapperRef = ref<any>(null)

const modalMethods: ModalMethods = {
  setModalProps,
  emitVisible: undefined,
}

// 去触发组件的注册，再useModal hooks中去执行
const instance = getCurrentInstance()
if (instance)
  emit('register', modalMethods, instance.uid)

// 合并props
const getMergeProps = computed(() => {
  return {
    ...props,
    ...(unref(propsRef) as any),

  }
})
// 获取props
const getProps = computed(() => {
  const option = {
    ...unref(getMergeProps),
    visible: unref(visibleRef),
  }
  return {
    ...option,
  }
})

const getBindValue = computed(() => {
  const attr = {
    ...attrs,
    ...unref(getMergeProps),
    show: unref(visibleRef),
  }

  return omit(attr, 'title')
})

const getWrapperHeight = computed(() => {
  return unref(getProps).height
})
watchEffect(() => {
  visibleRef.value = !!props.visible
})
watch(
  () => unref(visibleRef),
  (v) => {
    emit('visibleChange', v)
    emit('update:visible', v)
    instance && modalMethods.emitVisible?.(v, instance.uid)
    // nextTick(() => {
    //   if (props.scrollTop && v && unref(modalWrapperRef))
    //     (unref(modalWrapperRef) as any).scrollTop()
    // })
  },
  {
    immediate: false,
  },
)
/**
 * @description: 设置modal参数
 */
function setModalProps(props: Partial<ModalProps>): void {
  // Keep the last setModalProps

  propsRef.value = deepMerge(unref(propsRef) || ({} as any), props)
  if (Reflect.has(props, 'show'))
    visibleRef.value = !!props.show
}

const modalStyle = computed((): CSSProperties => {
  // 内容的高度
  return {
    width: `${props.width}px`,
  }
})
function handleOk(e: Event) {
  emit('ok', e)
}

// 取消事件
async function handleCancel(e: Event) {
  e?.stopPropagation()

  if (props.closeFunc && isFunction(props.closeFunc)) {
    const isClose: boolean = await props.closeFunc()
    visibleRef.value = !isClose
    return
  }

  visibleRef.value = false
  emit('cancel', e)
}
</script>

<template>
  <n-modal v-bind="getBindValue" :style="modalStyle" :auto-focus="false" :to="getProps?.to || 'body'">
    <div class="basicModal-body">
      <n-spin :show="loading" description="正在导入">
        <ModalHeader :title="getProps.title" @cancel="handleCancel" />
        <ModalWrapper
          v-bind="omit(getProps.wrapperProps, 'visible', 'height')" ref="modalWrapperRef"
          :visible="visibleRef" :loading="getProps.loadingModal" :loading-tip="getProps.loadingTip"
          :min-height="getProps.minHeight" :height="getWrapperHeight"
        >
          <slot />
        </ModalWrapper>
        <ModalFooter v-bind="getBindValue" @ok="handleOk" @cancel="handleCancel">
          <template v-for="item in Object.keys($slots)" #[item]="data">
            <slot :name="item" v-bind="data || {}" />
          </template>
        </ModalFooter>
      </n-spin>
    </div>
  </n-modal>
</template>

<style lang="scss">
.basicModal {
  &-body {
    background: #fff;
    border-radius: 3px;
  }
}
</style>
