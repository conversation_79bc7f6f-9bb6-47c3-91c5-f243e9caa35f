import type { CSSProperties } from 'vue'
import type { GridItemProps, GridProps } from 'wowjoy-vui/lib/grid'
import type { ButtonProps } from 'wowjoy-vui/lib/button'
import type { ComponentType } from './index'

export interface Options {
  label: string
  value: string | number | null
  disabled?: boolean
}

export interface ComponentProps {
  placeholder?: string
  options?: Options[]
  // options?: any
  maxlength?: number
  multiple?: boolean
  remote?: boolean
  filterable?: boolean
  autosize?: boolean | { minRows?: number; maxRows?: number }
  callBack?: (key?: string) => Promise<Options[]>
  onChange?: (e?: string) => void
  search?: (key: string) => void
  maxTagCount?: number
}

export interface FormSchema {
  // 表单ID
  field: string
  // 宽度
  width: number
  label: string
  labelMessage?: string
  labelMessageStyle?: object | string
  defaultValue?: unknown
  component: ComponentType
  componentProps?: ComponentProps
  slot?: string
  rules?: object | object[]
  giProps?: GridItemProps
  isFull?: boolean
  suffix?: string
}

export interface FormProps {
  model?: Recordable
  labelWidth?: number | string
  schemas?: FormSchema[]
  inline: boolean
  layout?: string
  size: string
  labelPlacement: string
  isFull: boolean
  showActionButtonGroup?: boolean
  showResetButton?: boolean
  resetButtonOptions?: Partial<ButtonProps>
  showSubmitButton?: boolean
  showAdvancedButton?: boolean
  submitButtonOptions?: Partial<ButtonProps>
  submitButtonText?: string
  resetButtonText?: string
  gridProps?: GridProps
  giProps?: GridItemProps
  resetFunc?: () => Promise<void>
  submitFunc?: () => Promise<void>
  submitOnReset?: boolean
  baseGridStyle?: CSSProperties
  collapsedRows?: number
}

export interface FormActionType {
  submit: () => Promise<any>
  setProps: (formProps: Partial<FormProps>) => Promise<void>
  setFieldsValue: (values: Recordable) => Promise<void>
  clearValidate: (name?: string | string[]) => Promise<void>
  getFieldsValue: () => Recordable
  resetFields: () => Promise<void>
  validate: (nameList?: any[]) => Promise<any>
}

export type RegisterFn = (formInstance: FormActionType) => void

export type UseFormReturnType = [RegisterFn, FormActionType]
