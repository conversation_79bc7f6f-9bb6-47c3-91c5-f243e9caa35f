<script lang="ts" setup>
import {
  computed,
  onMounted,
  reactive,
  ref,
  unref,
  useAttrs,
  watch,
} from 'vue'
import type { GridProps } from 'wowjoy-vui/lib/grid'
import type { Ref } from 'vue'
import { useDebounceFn } from '@vueuse/core'
import { basicProps } from './props'
import { createPlaceholderMessage } from './helper'
import { useFormValues } from './hooks/useFormValues'
import { useFormEvents } from './hooks/useFormEvents'

import type { ComponentProps, FormActionType, FormProps, FormSchema } from './types/form'
import { deepMerge } from '@/utils'
import { isArray } from '@/utils/common'

const props = defineProps({ ...basicProps })
const emit = defineEmits(['reset', 'submit', 'register'])
const attrs = useAttrs()

const defaultFormModel = ref<Recordable>({})
const formModel = reactive<Recordable>({})
const propsRef = ref<Partial<FormProps>>({})
const schemaRef = ref<Nullable<FormSchema[]>>(null)
const formElRef = ref<Nullable<FormActionType>>(null)
const gridCollapsed = ref(true)
// fix:组件库button loading 类型有问题
const loadingSub = ref('none')
const isUpdateDefaultRef = ref(false)

// 查询/保存 按钮参数
const getSubmitBtnOptions = computed(() => {
  return Object.assign(
    {
      size: props.size,
      type: 'primary',
      minWidth: '80px',
    },
    props.submitButtonOptions,
  )
})
// 重置按钮参数
const getResetBtnOptions = computed(() => {
  return Object.assign(
    {
      size: props.size,
      type: 'default',
      minWidth: '80px',
    },
    props.resetButtonOptions,
  )
})

// 获取props
const getProps = computed((): FormProps => {
  const formProps = { ...props, ...unref(propsRef) }
  const rulesObj: any = {
    rules: {},
  }
  const schemas: any = formProps.schemas || []
  schemas.forEach((item: any) => {
    if (item.rules && isArray(item.rules))
      rulesObj.rules[item.field] = item.rules
  })

  return { ...formProps, ...unref(rulesObj) }
})

// 是否是水平模式
const isInline = computed(() => {
  const { layout } = unref(getProps)
  return layout === 'inline'
})

// 表单的grid布局
const getGrid = computed((): GridProps => {
  const { gridProps } = unref(getProps)
  return {
    ...gridProps,
    collapsed: false,
    responsive: 'self',
  }
})

// 获取表单项组件的props
function getComponentProps(schema: FormSchema) {
  const compProps = schema.componentProps ?? {}
  const component = schema.component

  return {
    clearable: true,
    placeholder: createPlaceholderMessage(unref(component)),
    ...compProps,
  }
}

const getBindValue = computed(() => ({ ...attrs, ...props, ...unref(getProps) } as Recordable))

// 获取schema
const getSchema = computed(() => {
  const schemas = unref(schemaRef) || (unref(getProps).schemas as any)
  for (const schema of schemas) {
    const { defaultValue } = schema
    // 有默认值就赋值默认值
    if (defaultValue)
      schema.defaultValue = defaultValue
  }
  return schemas as FormSchema[]
})

const { handleFormValues, initDefault } = useFormValues({
  defaultFormModel,
  getSchema,
  formModel,
})

const { handleSubmit, validate, resetFields, getFieldsValue, clearValidate, setFieldsValue }
    = useFormEvents({
      emit,
      getProps,
      formModel,
      getSchema,
      formElRef: formElRef as Ref<FormActionType>,
      defaultFormModel,
      loadingSub,
      handleFormValues,
    })

function unfoldToggle() {
  gridCollapsed.value = !gridCollapsed.value
}

async function setProps(formProps: Partial<FormProps>): Promise<void> {
  propsRef.value = deepMerge(unref(propsRef) || {}, formProps)
}
const formActionType: Partial<FormActionType> = {
  getFieldsValue,
  setFieldsValue,
  resetFields,
  validate,
  clearValidate,
  setProps,
  submit: handleSubmit,
}

watch(
  () => getSchema.value,
  (schema) => {
    if (unref(isUpdateDefaultRef))
      return

    if (schema?.length) {
      initDefault()
      isUpdateDefaultRef.value = true
    }
  },
)

onMounted(() => {
  initDefault()
  emit('register', formActionType)
})

// select的下拉搜索回调
const onSelectSearch = useDebounceFn(
  (e: string, field: string, componentProps?: ComponentProps) => {
    if (componentProps) {
      const { remote, search } = componentProps
      if (!remote)
        return

      search && search(e)
    }
  },
  1000,
)
</script>

<template>
  <n-form v-bind="getBindValue" ref="formElRef" :model="formModel">
    <n-grid v-bind="getGrid">
      <n-gi v-for="schema in getSchema" v-bind="schema.giProps" :key="schema.field">
        <n-form-item :label="schema.label" :path="schema.field">
          <!-- NInput -->
          <template v-if="schema.component === 'NInput'">
            <n-input
              v-model:value="formModel[schema.field]"
              v-bind="getComponentProps(schema)"
              :class="{ isFull: schema.isFull !== false && getProps.isFull }"
              :style="{
                width: `${schema.width}px`,

              }"
            >
              <!-- <template #prefix>
                <component :is="schema.componentProps?.prefixSlot" />
              </template> -->
            </n-input>
          </template>

          <!-- NSelect 下拉 -->
          <template v-else-if="schema.component === 'NSelect'">
            <n-select
              v-model:value="formModel[schema.field]"
              v-bind="getComponentProps(schema)"
              :style="{
                width: `${schema.width}px`,

              }"
              @search="(e:string) => onSelectSearch(e, schema.field, schema?.componentProps)"
            />
          </template>
          <template v-else-if="schema.component === 'NRangePicker'">
            <n-date-picker
              v-model:value="formModel[schema.field]"
              :style="{ width: `${schema.width}px` }"
              type="daterange"
              clearable
            />
          </template>
          <template v-else-if="schema.component === 'NDateRangeMultiple'">
            <n-date-picker
              v-model:value="formModel[schema.field.split(',')[0]]"
              type="date"
              :style="{ width: `${schema.width}px` }"
              clearable
            />
            <span class="line" />
            <n-date-picker
              v-model:value="formModel[schema.field.split(',')[1]]"
              type="date"
              :style="{ width: `${schema.width}px` }"
              clearable
            />
          </template>
          <!--  @update:value="(e:any) => onSelectChange(e, schema?.componentProps)" -->
          <!-- 动态渲染表单组件 -->
          <!-- <component
            :is="schema.component"
            v-else
            v-bind="getComponentProps(schema)"
            v-model:value="formModel[schema.field]"
            :class="{ isFull: schema.isFull !== false && getProps.isFull }"
          /> -->
        </n-form-item>
      </n-gi>
      <!-- 提交 重置 展开 收起 按钮 -->
      <n-gi
        v-if="getProps.showActionButtonGroup"
      >
        <n-space
          align="center"
          justify="start"
          :style="{ 'margin-left': `${isInline ? 0 : getProps.labelWidth}px` }"
        >
          <n-button
            v-if="getProps.showSubmitButton"
            v-bind="getSubmitBtnOptions"
            :loading="loadingSub"
            @click="handleSubmit"
          >
            {{ getProps.submitButtonText }}
          </n-button>

          <n-button
            v-if="getProps.showResetButton"
            v-bind="getResetBtnOptions"
            @click="resetFields"
          >
            {{ getProps.resetButtonText }}
          </n-button>
          <!--  <n-button
                      v-if="isInline && getProps.showAdvancedButton"
                      type="primary"
                      text
                      icon-placement="right"
                      @click="unfoldToggle"
                  >
                      <template #icon>
                          <n-icon v-if="overflow" size="14" class="unfold-icon">
                              <DownOutlined />
                          </n-icon>
                          <n-icon v-else size="14" class="unfold-icon">
                              <UpOutlined />
                          </n-icon>
                      </template>
                      {{ overflow ? '展开' : '收起' }}
                  </n-button> -->
        </n-space>
      </n-gi>
    </n-grid>
  </n-form>
</template>

<style lang="scss" scoped>
.is-full {
    justify-content: flex-start;
    width: 100%;
}

.unfold-icon {
    display: flex;
    align-items: center;
    margin-left: -3px;
    height: 100%;
}
</style>
