import { unref } from 'vue'
import type { ComputedRef, Ref } from 'vue'
import { set } from 'lodash-es'
import type { FormSchema } from '../types/form'
import { isArray, isFunction, isNullOrUnDef, isObject, isString } from '@/utils/common'

interface UseFormValuesContext {
  defaultFormModel: Ref<any>
  getSchema: ComputedRef<FormSchema[]>
  formModel: Recordable
}
export function useFormValues({ defaultFormModel, getSchema, formModel }: UseFormValuesContext) {
  // 加工 form values
  function handleFormValues(values: Recordable) {
    if (!isObject(values))
      return {}

    const res: Recordable = {}
    for (const item of Object.entries(values)) {
      let [, value] = item
      const [key] = item
      if (
        !key
                || (isArray(value) && value.length === 0)
                || isFunction(value)
                || isNullOrUnDef(value)
      )
        return

      // 删除空格
      if (isString(value))
        value = value.trim()

      set(res, key, value)
    }
    return res
  }

  // 初始化默认值
  function initDefault() {
    const schemas = unref(getSchema)
    const obj: Recordable = {}
    schemas.forEach((item) => {
      const { defaultValue } = item
      // 如果不是null或者undefined就赋值默认值
      if (!isNullOrUnDef(defaultValue)) {
        obj[item.field] = defaultValue
        formModel[item.field] = defaultValue
      }
    })
    defaultFormModel.value = obj
  }

  return { handleFormValues, initDefault }
}
