<script setup lang="ts">
const props = defineProps<{
  phone: string
}>()

const isHidden = ref(true)

const newPhone = computed(() => {
  if (props.phone) {
    if (isHidden.value)
      return props.phone.replace(props.phone.substring(3, 7), '****')

    return props.phone
  }

  return '-'
})
</script>

<template>
  <n-popover v-if="props.phone">
    <template #trigger>
      <span cursor-pointer @click="isHidden = !isHidden">{{ newPhone }}</span>
    </template>
    <span>
      {{ isHidden ? '点击显示完整手机号' : '点击隐藏手机号' }}
    </span>
  </n-popover>
  <div v-else>
    -
  </div>
</template>
