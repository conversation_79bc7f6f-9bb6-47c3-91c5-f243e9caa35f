<script lang='ts' setup>
import { computed } from 'vue'
import type { LayoutProps } from './types'
import { LAYOUT_MAX_Z_INDEX, LAYOUT_SCROLL_EL_ID, createLayoutCssVars } from './shared'

const props = withDefaults(defineProps<LayoutProps>(), {
  mode: 'vertical',
  scrollMode: 'content',
  scrollElId: LAYOUT_SCROLL_EL_ID,
  commonClass: 'transition-all-300',
  fixedTop: true,
  maxZIndex: LAYOUT_MAX_Z_INDEX,
  headerVisible: true,
  headerHeight: 56,
  tabVisible: true,
  tabHeight: 48,
  siderVisible: true,
  siderCollapse: false,
  siderWidth: 220,
  siderCollapsedWidth: 64,
  footerVisible: true,
  footerHeight: 48,
  rightFooter: false,
})

const slots = defineSlots<Slots>()

type SlotFn = (props?: Record<string, unknown>) => any

interface Slots {
  /** 插槽：主体 */
  default?: SlotFn
  /** 插槽：头部 */
  header?: SlotFn
  /** 插槽：页签 */
  tab?: SlotFn
  /** 插槽：侧边栏 */
  side?: SlotFn
  /** 插槽：底部 */
  footer?: SlotFn
}
/** CSS 变量 */
const cssVars = computed(() => createLayoutCssVars(props))

// 各部分的可见性
const showHeader = computed(() => Boolean(slots.header) && props.headerVisible)
const showSide = computed(() => !props.isMobile && Boolean(slots.side) && props.siderVisible)
const showFooter = computed(() => Boolean(slots.footer) && props.footerVisible)

// 滚动模式
const isWrapperScroll = computed(() => props.scrollMode === 'wrapper')
const isContentScroll = computed(() => props.scrollMode === 'content')

// 布局模式：水平、垂直
const isVertical = computed(() => props.mode === 'vertical')
const isHorizontal = computed(() => props.mode === 'horizontal')

/** 固定头部和页签 */
const fixedHeaderAndTab = computed(() => props.fixedTop || (isHorizontal.value && isWrapperScroll.value))

// class样式

const leftGapClass = computed(() => {
  if (!props.fullContent && showSide.value)
    return props.siderCollapse ? 'left-gap_collapsed' : 'left-gap'

  return ''
})

const headerLeftGapClass = computed(() => (isVertical.value ? leftGapClass.value : ''))

const footerLeftGapClass = computed(() => {
  const condition1 = isVertical.value
  const condition2 = isHorizontal.value && isWrapperScroll.value && !props.fixedFooter
  const condition3 = Boolean(isHorizontal.value && props.rightFooter)

  if (condition1 || condition2 || condition3)
    return leftGapClass.value

  return ''
})

const siderPaddingClass = computed(() => {
  let cls = ''

  if (showHeader.value && !headerLeftGapClass.value)
    cls += 'sider-padding-top'

  if (showFooter.value && !footerLeftGapClass.value)
    cls += 'sider-padding-bottom'

  return cls
})
</script>

<template>
  <div class="relative h-full transition-all-300" :style="cssVars">
    <div class="h-full flex flex-col transition-all-300">
      <!-- 头部 -->
      <template v-if="showHeader">
        <header
          v-show="!fullContent"
          class=":slmc: layout-header flex-shrink-0" :class="[
            commonClass,
            headerClass,
            headerLeftGapClass,
            { ':slmc: absolute top-0 left-0 w-full': fixedHeaderAndTab },
          ]"
        >
          <slot name="header" />
        </header>
        <div
          v-show="!fullContent && fixedHeaderAndTab"
          class=":slmc: layout-header-placement flex-shrink-0 overflow-hidden"
        />
      </template>
      <!-- 侧边栏 -->
      <template v-if="showSide">
        <aside
          v-show="!fullContent"
          class=":slmc: absolute left-0 top-0 h-full" :class="[
            commonClass,
            siderClass,
            siderPaddingClass,
            siderCollapse ? 'layout-sider_collapsed' : 'layout-sider',
          ]"
        >
          <slot name="side" />
        </aside>
      </template>
      <!-- 主体 -->
      <main
        :id="isContentScroll ? scrollElId : undefined"
        class=":slmc: flex flex-col flex-grow" :class="[
          commonClass,
          contentClass,
          leftGapClass,
          { ':slmc: overflow-y-auto': isContentScroll },
        ]"
      >
        <slot />
      </main>
    </div>
  </div>
</template>

<style scoped lang="scss">
.layout-header,
.layout-header-placement {
  height: var(--slmc-header-height);
}

.layout-header {
  z-index: var(--slmc-header-z-index);
}

.layout-tab {
  top: var(--slmc-header-height);
  height: var(--slmc-tab-height);
  z-index: var(--slmc-tab-z-index);
}

.layout-tab-placement {
  height: var(--slmc-tab-height);
}

.layout-sider {
  width: var(--slmc-sider-width);
  z-index: var(--slmc-sider-z-index);
}

.layout-mobile-sider {
  z-index: var(--slmc-sider-z-index);
}

.layout-mobile-sider-mask {
  z-index: var(--slmc-mobile-sider-z-index);
}

.layout-sider_collapsed {
  width: var(--slmc-sider-collapsed-width);
  z-index: var(--slmc-sider-z-index);
}

.layout-footer,
.layout-footer-placement {
  height: var(--slmc-footer-height);
}

.layout-footer {
  z-index: var(--slmc-footer-z-index);
}

.left-gap {
  padding-left: var(--slmc-sider-width);
}

.left-gap_collapsed {
  padding-left: var(--slmc-sider-collapsed-width);
}

.sider-padding-top {
  padding-top: var(--slmc-header-height);
}

.sider-padding-bottom {
  padding-bottom: var(--slmc-footer-height);
}
</style>
