<script lang='ts' setup>
import { SvgIcon } from '@/components/Icon'

interface Props {
  tag: string[]

}
const props = defineProps<Props>()
/** 优先展示的标签 */
const priorityTag = computed(() => {
  return props.tag[0]
})
/** 是否有更多标签 */
const haveMoreTag = computed(() => {
  return props.tag && props.tag?.length > 1
})

/** 剩余展示的标签 */
const remainTags = computed(() => {
  return haveMoreTag ? props.tag.slice(1) : []
})
/*

TODO:
标签是否优先级展示
*/
</script>

<template>
  <div class="flex justify-start">
    <span class="mr-6px">
      <n-ellipsis style="max-width: 140px">
        {{ priorityTag }}
      </n-ellipsis>

    </span>

    <n-popover
      v-if="haveMoreTag" trigger="hover"
      style="max-height: 150px;max-width: 148px;"
      content-style="padding: 10px 14px 15px 10px;"

      scrollable
    >
      <template #trigger>
        <SvgIcon v-if="haveMoreTag" class="cursor-pointer" local-icon="slmc-icon-gengduo1" size="16" />
      </template>
      <div class="flex flex-col gap-10px">
        <span v-for="(tagName, index) in remainTags" :key="index" class="truncate">{{ tagName }}</span>
      </div>
    </n-popover>
  </div>
</template>

<style scoped lang="scss">

</style>
