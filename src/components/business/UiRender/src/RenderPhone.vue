<script lang='ts' setup>
interface Props {
  phone: string

}
const props = withDefaults(defineProps<Props>(), {
  phone: '',
})

const isHidden = ref(true)

function handleClickHidden(hidden: boolean) {
  isHidden.value = hidden
}

const hiddenPhoneStr = computed(() => {
  if (props.phone) {
    const phonePrev = props.phone?.slice(0, 3)
    const phoneNext = props.phone?.slice(7)
    return `${phonePrev}****${phoneNext}`
  }
  else {
    return null
  }
})
</script>

<template>
  <n-popover trigger="hover">
    <template #trigger>
      <div class="cursor-pointer select-none">
        <span v-if="isHidden" @click="handleClickHidden(false)">{{ hiddenPhoneStr }}</span>
        <span v-else @click="handleClickHidden(true)">{{ phone }}</span>
      </div>
    </template>
    <span>点击{{ isHidden ? '显示完整' : '隐藏' }}手机号</span>
  </n-popover>
</template>

<style scoped lang="scss">

</style>
