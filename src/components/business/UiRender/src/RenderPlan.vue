<script lang='ts' setup>
import { SvgIcon } from '@/components/Icon'

interface Props {
  suggestPlan: Record<string, string>
}
const props = defineProps<Props>()

const emits = defineEmits(['updatePlan'])

const isEditState = ref(false)

const options = Object.entries(props.suggestPlan).map(([key, value]) => {
  return {
    label: value,
    value: key,
  }
})

const planId = ref(options[0].value)

const planName = computed(() => {
  return options.find(item => item.value === planId.value)?.label
})

function changeEditState(changeState: boolean) {
  isEditState.value = changeState
}

function changePlanState(changeState: boolean) {
  if (changeState) {
    // 调接口保存
  }
  else {
    // 放弃保存
  }
  changeEditState(false)
}

function handlePlanUpdate(val: any) {
  emits('updatePlan', val)
}
</script>

<template>
  <div class="w-full">
    <div v-if="isEditState" class="flex-start flex items-center">
      <n-select v-model:value="planId" :options="options" style="width:180px" @update:value="handlePlanUpdate" />
      <SvgIcon class="mx-10px cursor-pointer" local-icon="slmc-icon-done_line" size="16" @click="changePlanState(true)" />
      <SvgIcon class="cursor-pointer" local-icon="slmc-icon-wrong_line" size="16" @click="changePlanState(false)" />
    </div>
    <div v-else class="flex-start flex items-center">
      <span class="mr-6px">{{ planName }}</span><SvgIcon class="cursor-pointer" local-icon="slmc-icon-edit1" size="16" @click="changeEditState(true)" />
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>
