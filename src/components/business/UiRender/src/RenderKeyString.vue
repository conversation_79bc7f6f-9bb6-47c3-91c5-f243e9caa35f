<script lang='ts' setup>
const props = defineProps({
  text: {
    type: String,
    default: '',
  },
  searchKey: {
    type: String,
    default: '',
  },
  ellipsis: {
    type: Boolean,
    default: true,
  },
  maxWidth: {
    type: Number,
    default: 200,
  },

})

const formatText = computed(() => {
  const { text, searchKey } = props
  const searchKeyLen = searchKey.length
  const index = text.indexOf(searchKey)
  let str1 = ''
  let str2 = ''
  let str3 = ''

  if (index > -1) {
    str1 = text.slice(0, index)
    str2 = text.slice(index, index + searchKeyLen)
    str3 = text.slice(index + searchKeyLen)
    return {
      str1,
      str2,
      str3,
    }
  }
  else {
    return null
  }
})
</script>

<template>
  <div class="">
    <n-ellipsis
      :style="`max-width: ${maxWidth}px`"
    >
      <template v-if="formatText">
        <span>{{ formatText?.str1 }}</span><span style="color:#06AEA6">
          {{ formatText?.str2 }}</span><span>{{ formatText?.str3 }}</span>
      </template>
      <template v-else>
        <span>
          {{ text }}
        </span>
      </template>
    </n-ellipsis>
  </div>
</template>

<style scoped lang="scss">

</style>
