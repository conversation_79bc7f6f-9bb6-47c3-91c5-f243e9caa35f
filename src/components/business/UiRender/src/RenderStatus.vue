<script lang='ts' setup>
defineProps({
  text: String,
  color: String,
})
</script>

<template>
  <div class="status">
    <span class="text">{{ text }}</span>
  </div>
</template>

<style scoped lang="scss">
.status {

.text {
    position: relative;
    display: flex;
    align-items: center;
    padding-left: 16px;
}
.text::before{
    content: '';
    position: absolute;
    width: 6px;
    height: 6px;
    background:v-bind(color);
    border-radius: 50%;
    left:0px

}
}
</style>
