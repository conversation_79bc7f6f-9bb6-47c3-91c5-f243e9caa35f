<script lang='ts' setup>
import { computed } from 'vue'

interface Props {
  /** 距离底部偏移距离 */
  offsetBottom?: string
  footer?: boolean
  breadcrumb?: boolean
  bottomZero?: boolean
  /** padding */
  padding?: string
  customH?: number

}
defineOptions({
  inheritAttrs: false,
})
const props = withDefaults(defineProps<Props>(), {
  offsetBottom: '',
  footer: false,
  breadcrumb: false,
  bottomZero: false,
  padding: '14px',
  customH: 0,
})
const attrs = useAttrs()

const cardHeight = computed(() => {
  const breadcrumbH = props.breadcrumb ? 32 : 0
  const footerH = props.footer ? 60 : 0
  const cardH = breadcrumbH + footerH + 50 + props.customH

  return `calc(100vh - ${cardH}px)`
})
const getPadding = computed(() => {
  const paddingPx = props.padding ? props.padding : '14px'

  return `${paddingPx}`
})

const bindAttrs = computed<{ class: string; style: string }>(() => ({
  class: (attrs.class as string) || '',
  style: (attrs.style as string) || '',
}))
</script>

<template>
  <div class="pageCard bg-#F4F5F6 p-14px">
    <div class="pageCard-content h-full overflow-auto bg-#fff" :class="bottomZero ? '!pb-0 !pr-0' : ''" v-bind="bindAttrs">
      <slot />
    </div>
  </div>
</template>

<style scoped lang="scss">
.pageCard {
    // height: calc(100% - 0px)
    height: v-bind(cardHeight);
    .pageCard-content {
        padding:v-bind(getPadding)
    }
}
</style>
