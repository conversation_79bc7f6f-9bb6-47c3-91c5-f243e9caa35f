<script lang="ts" setup>
import { ref } from 'vue'
import { SvgIcon } from '@/components/Icon'

defineProps({
  width: {
    type: String,
    default: '240',
  },
  placeholder: {
    type: String,
    default: '请输入',
  },

})
const emit = defineEmits(['emitSearch'])
const key = ref('')
const showClearIcon = ref(false)
function handleSearch() {
  showClearIcon.value = key.value !== '' && true
  emit('emitSearch', key.value.trim())
}
function handleClear() {
  key.value = ''
  showClearIcon.value = false
  emit('emitSearch', '')
}
function handleInput() {
  showClearIcon.value = false
}
</script>

<template>
  <div>
    <n-input
      v-model:value="key"
      :placeholder="placeholder"
      :style="{ width: `${width}px` }"
      @update:value="handleInput"
      @keyup.enter="handleSearch"
    >
      <template #suffix>
        <SvgIcon
          v-if="showClearIcon"
          local-icon="slmc-icon-delete_1"
          size="16"
          style="color: #ccc; cursor: pointer;opacity: 0.3;"
          @click="handleClear"
        />
        <SvgIcon
          v-else
          local-icon="slmc-icon-search"
          size="16"
          style="color: #ccc; cursor: pointer"
          @click="handleSearch"
        />
      </template>
    </n-input>
  </div>
</template>

<style scoped lang="scss">

</style>
