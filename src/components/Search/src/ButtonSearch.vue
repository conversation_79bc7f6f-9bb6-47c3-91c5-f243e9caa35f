<script lang="ts" setup>
import { ref } from 'vue'
import { SvgIcon } from '@/components/Icon'

defineProps({
  width: {
    type: String,
    default: '240px',
  },
  placeholder: {
    type: String,
    default: '搜索',
  },
})

const emit = defineEmits(['emitSearch'])
const key = ref('')

const showClearIcon = ref(false)
function handleSearch() {
  showClearIcon.value = key.value !== '' && true
  emit('emitSearch', key.value.trim())
}
function handleClear() {
  key.value = ''
  showClearIcon.value = false
  emit('emitSearch', key.value.trim())
}
function handleInput() {
  showClearIcon.value = false
}
</script>

<template>
  <div class="buttonSearch">
    <n-input
      v-model:value="key"
      :placeholder="placeholder"
      :style="{ width }"
      @update:value="handleInput"
      @keyup.enter="handleSearch"
    >
      <template #suffix>
        <SvgIcon
          v-if="showClearIcon"
          local-icon="slmc-icon-delete_1"
          size="16"
          class="clearIcon"
          style="cursor: pointer;opacity: 0.3;"
          @click="handleClear"
        />
      </template>
    </n-input>
    <div class="searchBox bg-primary">
      <SvgIcon
        local-icon="slmc-icon-search"
        size="16"
        style="color: #fff; cursor: pointer;"
        @click="handleSearch"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.buttonSearch {
    display: flex;
    align-items: center;
    .searchBox {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0 3px 3px 0;
        cursor: pointer;

    }
    .clearIcon {
        color: #ccc;
        cursor: pointer;
        margin-right: 10px;
    }
    :deep(.n-input-wrapper) {
        padding-right: 0px;
    }
}
</style>
