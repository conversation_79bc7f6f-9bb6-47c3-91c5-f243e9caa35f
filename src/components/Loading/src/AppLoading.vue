<script setup lang="ts">
import { useAppInfo } from '@/hooks'
import { getRgbOfColor, localStg } from '@/utils'
import themeSettings from '@/settings/theme.json'

const { title } = useAppInfo()

const lodingClasses = [
  'left-0 top-0',
  'left-0 bottom-0 animate-delay-500',
  'right-0 top-0 animate-delay-1000',
  'right-0 bottom-0 animate-delay-1500',
]

function addThemeColorCssVars() {
  const defaultColor = themeSettings.themeColor
  const themeColor = localStg.get('themeColor') || defaultColor

  const { r, g, b } = getRgbOfColor(themeColor)

  const cssVars = `--primary-color: ${r},${g},${b}`
  document.documentElement.style.cssText = cssVars
}

addThemeColorCssVars()
</script>

<template>
  <div class="fixed-center flex-col">
    <div class="my-36px h-56px w-56px">
      <div class="relative h-full animate-spin">
        <div
          v-for="(item, index) in lodingClasses"
          :key="index"
          class="absolute h-16px w-16px animate-pulse rounded-8px bg-primary"
          :class="item"
        />
      </div>
    </div>
    <h2 class="text-28px font-500 text-#646464">
      {{ title }}
    </h2>
  </div>
</template>

<style scoped></style>
