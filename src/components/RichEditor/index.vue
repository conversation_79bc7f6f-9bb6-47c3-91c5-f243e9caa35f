<script setup lang="ts">
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import '@wangeditor/editor/dist/css/style.css'
import type { IEditorConfig, IToolbarConfig } from '@wangeditor/editor'

defineOptions({
  name: 'RichEditor',
})

withDefaults(defineProps<{
  width?: string
  height?: string
}>(), {
  width: '532px',
  height: '190px',
})

const html = defineModel('html')
const isEmpty = ref(true)
const editorRef = shallowRef()

const editorConfig: Partial<IEditorConfig> = {
  placeholder: '请输入内容',
  MENU_CONF: {
    uploadImage: {
      fieldName: 'file',
      server: '/heper-api/app/file/upload',
      allowedFileTypes: ['image/gif', 'image/jpeg', 'image/png', 'image/bmp'],
      maxFileSize: 3 * 1024 * 1024,
      customInsert(res: any, insertFn: (url: string, alt: string, href: string) => void) {
        if (res.results)
          insertFn(`/heper-api/app/file/preview/${res.results}`, '', '')
      },
      onError() {
        window.$message.error('图片上传失败')
      },

      base64LimitSize: 5 * 1024,
    },
  },
}

const toolbarConfig: Partial<IToolbarConfig> = {
  excludeKeys: ['fullScreen', 'insertVideo', 'emotion', 'codeBlock', 'group-image'],
}

defineExpose({
  isEmpty,
})
</script>

<template>
  <div
    :style="{
      'border': '1px solid #d1d1d1',
      'border-radius': '3px',
      width,
    }"
  >
    <Toolbar
      :style="{
        'border-bottom': '1px solid #d1d1d1',
        'border-radius': '3px 3px 0 0',
      }"
      :editor="editorRef"
      :default-config="toolbarConfig"
      mode="simple"
    />
    <Editor
      v-model="html"
      :style="{
        overflow: 'auto',
        height,
      }"
      :default-config="editorConfig"
      mode="simple"
      @onCreated="(e) => editorRef = e"
      @onChange="(e) => {
        isEmpty = e.isEmpty()
      }"
    />
  </div>
</template>

<style scoped>
:deep(.w-e-toolbar) {
  border-radius: 3px 3px 0 0;
  border-color:#d1d1d1;
}

:deep(.w-e-text-container) {
  border-radius: 0 0 3px 3px;
  border-color:#d1d1d1;
}
</style>
