<script setup lang="ts">
import { storeToRefs } from 'pinia'
import { useAppStore } from '@/store'

const props = withDefaults(
  defineProps<{
    rightZero: boolean
  }>(), {
    rightZero: false,
  },
)

const { siderCollapse } = storeToRefs(useAppStore())
const classList = computed(() => {
  if (!props.rightZero)
    return siderCollapse.value ? 'w-[calc(100vw-28px)] right-14px' : 'w-[calc(100vw-248px)] right-14px'

  else
    return siderCollapse.value ? 'w-[calc(100vw-14px)] right-0' : 'w-[calc(100vw-234px)] right-0'
})
</script>

<template>
  <div :class="classList" class="fixed-footer z-100">
    <slot />
  </div>
</template>

<style lang="scss" scoped>
.fixed-footer {
  height: 60px;
  position: fixed;
  box-shadow: 0px -1px 0px 0px #e8e8e8 inset, 0px 1px 0px 0px #e8e8e8 inset;
  background: #f9f9f9;
  bottom: 0px;
  justify-content: center;
  align-items: center;
  display: flex;
  gap: 20px;
}
</style>
