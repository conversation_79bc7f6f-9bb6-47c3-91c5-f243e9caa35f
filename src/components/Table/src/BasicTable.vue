<script lang='ts' setup>
import { computed, nextTick, onMounted, ref, toRaw, unref } from 'vue'
import type { BasicTableProps } from './types/table'
import { useLoading } from './hooks/useLoading'
import { useColumns } from './hooks/useColumns'
import { useDataSource } from './hooks/useDataSource'
import { usePagination } from './hooks/usePagination'
import { createTableContext } from './hooks/useTableContext'
import { basicProps } from './props'
import DataEmpty from './components/DataEmpty.vue'
import { useWindowSizeFn } from '@/hooks/event/useWindowSizeFn'
import { isBoolean } from '@/utils/common'
import { getViewportOffset } from '@/utils/domUtils'

const props = defineProps({ ...basicProps })
const emit = defineEmits([
  'fetch-success',
  'fetch-error',
  'update:checked-row-keys',
  'edit-end',
  'edit-cancel',
  'edit-row-end',
  'edit-change',
])

const deviceHeight = ref(150)
const tableElRef: any = ref(null)
const wrapRef = ref<null>(null)
let paginationEl: HTMLElement | null

const tableData = ref<unknown[]>([])
const innerPropsRef = ref<Partial<BasicTableProps>>()

const getProps = computed(() => {
  return { ...props, ...unref(innerPropsRef) } as BasicTableProps
})

const { getPaginationInfo, setPagination, setShowPagination } = usePagination(getProps)

const { getLoading, setLoading } = useLoading(getProps)

const { getDataSourceRef, getRowKey, reload, getIsSearch, fetch } = useDataSource(
  getProps,
  {
    getPaginationInfo,
    setPagination,
    tableData,
    setLoading,
    setShowPagination,
  },
  emit,
)
const { getPageColumns, setColumns, getColumns, getCacheColumns, setCacheColumnsField }
            = useColumns(getProps)
// 页码切换
function updatePage(page: number) {
  setPagination({ page })
  reload({
    isPageChange: true,
  })
}

// 分页数量切换
function updatePageSize(size: number) {
  setPagination({ page: 1, pageSize: size })
  reload()
}
// 组装表格信息
const getBindValues = computed(() => {
  const tableData = unref(getDataSourceRef)
  const maxHeight = tableData.length ? `${unref(deviceHeight)}px` : 'auto'
  return {
    ...unref(getProps),
    // loading: unref(getLoading),
    'columns': toRaw(unref(getPageColumns)),
    'rowKey': unref(getRowKey),
    'data': tableData,
    'bordered': false,
    // size: unref(getTableSize),
    'remote': true,
    'max-height': maxHeight,
  }
})
// 获取分页信息
const pagination = computed(() => toRaw(unref(getPaginationInfo)))

function setProps(props: Partial<BasicTableProps>) {
  innerPropsRef.value = { ...unref(innerPropsRef), ...props }
}

const tableAction = {
  reload,
  setColumns,
  setLoading,
  setProps,
  getColumns,
  getPageColumns,
  getCacheColumns,
  setCacheColumnsField,
  emit,
}

const getCanResize = computed(() => {
  const { canResize } = unref(getProps)
  return canResize
})
async function computeTableHeight() {
  const table = unref(tableElRef)
  if (!table)
    return
  if (!unref(getCanResize))
    return
  const tableEl: any = table?.$el
  const headEl = tableEl.querySelector('.n-data-table-thead')
  const { bottomIncludeBody } = getViewportOffset(headEl)
  const headerH = 64
  let paginationH = 2
  const marginH = 24
  if (!isBoolean(pagination)) {
    paginationEl = tableEl.querySelector('.n-data-table__pagination') as HTMLElement
    if (paginationEl) {
      const offsetHeight = paginationEl.offsetHeight
      paginationH += offsetHeight || 0
    }
    else {
      paginationH -= 22
    }
  }
  let height = bottomIncludeBody - (headerH + paginationH + marginH + (props.resizeHeightOffset || 0))
  const maxHeight = props.maxHeight
  height = maxHeight ?? maxHeight < height ? maxHeight : height
  deviceHeight.value = height
}

useWindowSizeFn<Promise<void>>(computeTableHeight, 280)

onMounted(() => {
  nextTick(() => {
    computeTableHeight()
  })
})

createTableContext({ ...tableAction, wrapRef, getBindValues })

defineExpose({
  reload,
  fetch,
  computeTableHeight,
})
</script>

<template>
  <div class="BasicTable">
    <n-spin h-full type="uni" size="large" :show="getLoading" description="正在加载中，请稍后">
      <n-data-table
        ref="tableElRef"
        v-bind="getBindValues"
        :pagination="pagination"
        @update:page="updatePage"
        @update:page-size="updatePageSize"
      >
        <template #empty>
          <DataEmpty v-show="!getLoading" :is-search="getIsSearch" />
        </template>
      </n-data-table>
    </n-spin>
  </div>
</template>

<style scoped lang="scss">

</style>
