import type { TableBaseColumn } from 'wowjoy-vui/lib/data-table/src/interface'
import type { ComponentType } from './componentType'

export interface BasicColumn extends TableBaseColumn {
  // 编辑表格
  edit?: boolean
  editRow?: boolean
  editable?: boolean
  editComponent?: ComponentType
  editComponentProps?: Recordable
  editRule?: boolean | ((text: string, record: Recordable) => Promise<string>)
  editValueMap?: (value: any) => string
  onEditRow?: () => void
  // 权限编码控制是否显示
  auth?: string[]
  // 业务控制是否显示
  ifShow?: boolean | ((column: BasicColumn) => boolean)
}

export interface TableActionType {
  reload: (opt: any) => Promise<void>
  emit?: any
  getColumns: (opt?: any) => BasicColumn[]
  setColumns: (columns: BasicColumn[] | string[]) => void
}

export interface BasicTableProps {
  title?: string
  // dataSource: Function
  dataSource: any
  columns: any[]
  // pagination: object
  pagination: any
  showPagination: boolean
  actionColumn: any[]
  canResize: boolean
  resizeHeightOffset: number
  loading: boolean
}

export interface FetchReload {
  start?: number
  size?: number
  isSearch?: boolean
  isRemove?: boolean
  isPageChange?: boolean
}
