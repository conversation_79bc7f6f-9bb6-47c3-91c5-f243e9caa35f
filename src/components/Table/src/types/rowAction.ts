// import { NButton } from 'wowjoy-vui'
// import { PermissionsEnum } from '@/enums/permissionsEnum';
export interface ActionItem {
  // onClick?: Fn
  onClick?: any // 暂时写any
  label?: string
  color?: 'success' | 'error' | 'warning'
  icon?: string
  // popConfirm?: PopConfirm
  disabled?: boolean
  divider?: boolean
  // 权限编码控制是否显示
  // auth?: PermissionsEnum | PermissionsEnum[] | string | string[]
  // 业务控制是否显示
  ifShow?: boolean | ((action: ActionItem) => boolean)
}
