import { computed, onMounted, ref, unref, watch, watchEffect } from 'vue'
import type { ComputedRef, Ref } from 'vue'
import type { BasicTableProps, FetchReload } from '../types/table'
import type { PaginationProps } from '../types/pagination'
import { APISETTING } from '../const'
import { isBoolean } from '@/utils/common'

export function useDataSource(
  propsRef: ComputedRef<BasicTableProps>,
  { getPaginationInfo, setPagination, tableData, setLoading, setShowPagination }: any,
  emit: any,
) {
  const dataSourceRef = ref([])
  const isSearch: Ref<boolean | undefined> = ref(false)

  watchEffect(() => {
    tableData.value = unref(dataSourceRef)
  })

  watch(
    () => unref(propsRef).dataSource,
    () => {
      const { dataSource }: any = unref(propsRef)
      dataSource && (dataSourceRef.value = dataSource)
    },
    {
      immediate: true,
    },
  )

  const getRowKey = computed(() => {
    const { rowKey }: any = unref(propsRef)
    return rowKey || (() => {
      return 'key'
    })
  })

  const getDataSourceRef = computed(() => {
    const dataSource = unref(dataSourceRef)

    if (!dataSource || dataSource.length === 0)
      return unref(dataSourceRef)

    return unref(dataSourceRef)
  })
  async function fetch(opt?: any) {
    try {
      setLoading(true)

      const { request, pagination }: any = unref(propsRef)
      // 组装分页信息
      const pageField = APISETTING.pageField
      const sizeField = APISETTING.sizeField
      const totalField = APISETTING.totalField
      const listField = opt?.listField ? opt?.listField : APISETTING.listField

      let pageParams: Record<string, number> = {}
      const { page = 1, pageSize = 10 } = unref(getPaginationInfo) as PaginationProps
      if ((isBoolean(pagination) && !pagination) || isBoolean(getPaginationInfo)) {
        pageParams = {}
      }
      else {
        pageParams.start = (opt && opt.start) || page
        pageParams.size = (opt && opt.size) || pageSize
      }

      const params = {
        ...opt,
        ...pageParams,
      }

      const { data: results } = await request(params)

      const resultTotal = results[totalField] || 0
      const currentPage = results.current

      // 如果数据异常，需获取正确的页码再次执行
      // if (resultTotal) {
      // if (pageSize > resultTotal) {
      //     setPagination({
      //         [pageField]: resultTotal
      //     })
      //     fetch(opt)
      // }
      // }
      // debugger
      const resultInfo = listField === 'results'
        ? results
        : results[listField]
          ? results[listField]
          : []

      dataSourceRef.value = resultInfo
      setPagination({
        [pageField]: Number(currentPage),
        [totalField]: Number(resultTotal),
        [sizeField]: Number(results.size),
      })
      if (opt && opt[pageField]) {
        setPagination({
          [pageField]: opt[pageField] || 1,
        })
      }
      emit('fetch-success', {
        records: unref(resultInfo),
        resultTotal,
      })
    }
    catch (error) {
      console.log(error)
      emit('fetch-error', error)
      dataSourceRef.value = []
      setPagination({
        pageCount: 0,
      })
    }
    finally {
      const isShowPagination = getDataSource().length > 0
      setShowPagination(isShowPagination)
      setLoading(false)
    }
  }
  onMounted(() => {
    const { onMountedRequest = true }: any = unref(propsRef)
    if (!onMountedRequest)
      return
    fetch()
  })
  function setTableData(values: any) {
    dataSourceRef.value = values
  }

  function getDataSource(): any[] {
    return getDataSourceRef.value
  }

  const getIsSearch = computed(() => isSearch.value)

  // 重新加载数据
  async function reload(opt?: FetchReload) {
    const isGoPrevPage = dataSourceRef.value.length === 1
    const { page = 1 } = unref(getPaginationInfo) as PaginationProps
    const params = { ...opt }

    // 是否是搜索操作
    isSearch.value = opt?.isSearch && unref(opt.isSearch)

    // 删除后回到上一页
    if (isGoPrevPage && params.isRemove)
      params.start = page === 1 ? 1 : page - 1

    await fetch(params)
  }
  return {
    fetch,
    getRowKey,
    getDataSourceRef,
    getDataSource,
    setTableData,
    reload,
    getIsSearch,
  }
}
