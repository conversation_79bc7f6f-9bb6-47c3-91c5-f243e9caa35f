import type { ComputedRef, Ref } from 'vue'
import { computed, h, ref, toRaw, unref, watch } from 'vue'
import { cloneDeep, isEqual } from 'lodash-es'
import { NEllipsis, NTooltip } from 'wowjoy-vui'
import type { BasicColumn, BasicTableProps } from '../types/table'
import { isArray, isBoolean, isFunction, isString } from '@/utils/common'
import type { ActionItem } from '@/components/Table'

export function useColumns(propsRef: ComputedRef<BasicTableProps>) {
  const columnsRef = ref(unref(propsRef).columns) as unknown as Ref<BasicColumn[]>
  let cacheColumns = unref(propsRef).columns

  const getColumnsRef = computed(() => {
    const columns = cloneDeep(unref(columnsRef))
    handleActionColumn(propsRef, columns)
    if (!columns)
      return []
    return columns
  })
  function isIfShow(action: ActionItem): boolean {
    const ifShow = action.ifShow

    let isIfShow = true

    if (isBoolean(ifShow))
      isIfShow = ifShow

    if (isFunction(ifShow))
      isIfShow = ifShow(action)

    return isIfShow
  }
  const renderTooltip = (trigger: any, content: any) => {
    return h(NTooltip, null, {
      trigger: () => trigger,
      default: () => content,
    })
  }

  const getPageColumns = computed(() => {
    const pageColumns = unref(getColumnsRef)
    const columns = cloneDeep(pageColumns)
    return (
      columns
      // .filter((column) => {
      //     return hasPermission(column.auth as string[]) && isIfShow(column)
      // })
        .map((column) => {
          // 默认 ellipsis 为true

          column.ellipsis
                        = typeof column.ellipsis === 'undefined' ? { tooltip: true } : column.ellipsis
          const { edit, render } = column
          if (!render) {
            column.render = (row) => {
              const haveValue = row?.[column?.key]
              return h(NEllipsis, { ...column.ellipsis }, {
                default: () => (haveValue ? row?.[column?.key] : '-'),
              })
              // return h('div', null, {
              //     default: () => (haveValue ? row?.[column?.key] : '-')
              // })
            }
          }
          if (edit) {
            // column.render = renderEditCell(column)
            /* 头部tooltip提示  */
            //     if (edit) {
            //         const title: any = column.title
            //         column.title = () => {
            //             return renderTooltip(
            //                 h('span', {}, [
            //                     h('span', { style: { 'margin-right': '5px' } }, title),
            //                     h(
            //                         NIcon,
            //                         {
            //                             size: 14
            //                         },
            //                         {
            //                             default: () => h(FormOutlined)
            //                         }
            //                     )
            //                 ]),
            //                 '该列可编辑'
            //             )
            //         }
            //     }
          }
          return column
        })
    )
  })
  watch(
    () => unref(propsRef).columns,
    (columns) => {
      columnsRef.value = columns
      cacheColumns = columns
    },
  )

  function handleActionColumn(propsRef: ComputedRef<BasicTableProps>, columns: BasicColumn[]) {
    const { actionColumn } = unref(propsRef)

    if (!actionColumn)
      return
    !columns.find(col => col.key === 'action')
            && columns.push({
              ...(actionColumn as any),
            })
  }

  // 设置
  function setColumns(columnList: string[]) {
    const columns: any[] = cloneDeep(columnList)
    if (!isArray(columns))
      return

    if (!columns.length) {
      columnsRef.value = []
      return
    }
    const cacheKeys = cacheColumns.map(item => item.key)
    // 针对拖拽排序
    if (!isString(columns[0])) {
      columnsRef.value = columns
    }
    else {
      const newColumns: any[] = []
      cacheColumns.forEach((item) => {
        if (columnList.includes(item.key))
          newColumns.push({ ...item })
      })
      if (!isEqual(cacheKeys, columns)) {
        newColumns.sort((prev, next) => {
          return cacheKeys.indexOf(prev.key) - cacheKeys.indexOf(next.key)
        })
      }
      columnsRef.value = newColumns
    }
  }
  // 获取
  function getColumns(): BasicColumn[] {
    const columns = toRaw(unref(getColumnsRef))
    return columns.map((item) => {
      return { ...item, title: item.title, key: item.key, fixed: item.fixed || undefined }
    })
  }

  // 获取原始
  function getCacheColumns(isKey?: boolean): any[] {
    return isKey ? cacheColumns.map(item => item.key) : cacheColumns
  }
  // 更新原始数据单个字段
  function setCacheColumnsField(key: string | undefined, value: Partial<BasicColumn>) {
    if (!key || !value)
      return

    cacheColumns.forEach((item) => {
      if (item.key === key)
        Object.assign(item, value)
    })
  }
  return {
    getColumnsRef,
    getCacheColumns,
    setCacheColumnsField,
    setColumns,
    getColumns,
    getPageColumns,
  }
}
