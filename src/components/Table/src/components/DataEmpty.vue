<script lang="ts" setup>
import noDataEmpty from '@/assets/images/comp_NoData.png'
import noSearchEmpty from '@/assets/images/page-noSearch.png'

defineProps({
  isSearch: {
    type: Boolean,
    default: false,
  },
  showText: {
    type: String,
    default: '无数据',
  },
})
</script>

<template>
  <div class="dataEmpty">
    <img v-if="isSearch" :src="noSearchEmpty" alt="没有搜索结果" class="dataEmpty-img">
    <img v-else :src="noDataEmpty" alt="无数据" class="dataEmpty-img">
    <div mt-14px class="dataEmpty-text">
      {{ isSearch ? '没有搜索结果' : showText }}
    </div>
  </div>
</template>

<style scoped lang="scss">
.dataEmpty {
  display: flex;
  flex-direction: column;
  align-items: center;
    &-img {
        display: block;
        width: 120px;
        height: 120px;
    }
    &-text {
        color: #999;
        text-align: center;
        transform: translateY(-22px);
    }
}
</style>
