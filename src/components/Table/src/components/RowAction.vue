<script lang='ts' setup>
import { computed, toRaw } from 'vue'
import type { ActionItem } from '../types/rowAction'
import { useThemeStore } from '@/store'

// import { usePermission } from '@/hooks/web/usePermission'
import { isBoolean, isFunction } from '@/utils/common'

const props = defineProps(['actions'])
// const { hasPermission } = usePermission()

/*       const actionType =
            props.style === 'button' ? 'default' : props.style === 'text' ? 'primary' : 'default'
        const actionText =
            props.style === 'button' ? undefined : props.style === 'text' ? true : undefined */

// const getMoreProps = computed(() => {
//     return {
//         text: actionText,
//         type: actionType,
//         size: 'small'
//     }
// })
/*
        const getDropdownList = computed(() => {
            return (toRaw(props.dropDownActions) || [])
                .filter((action) => {
                    return hasPermission(action.auth) && isIfShow(action)
                })
                .map((action) => {
                    const { popConfirm } = action
                    return {
                        size: 'small',
                        text: actionText,
                        type: actionType,
                        ...action,
                        ...popConfirm,
                        onConfirm: popConfirm?.confirm,
                        onCancel: popConfirm?.cancel
                    }
                })
        })
*/
function isIfShow(action: ActionItem): boolean {
  const ifShow = action.ifShow

  let isIfShow = true

  if (isBoolean(ifShow))
    isIfShow = ifShow

  if (isFunction(ifShow))
    isIfShow = ifShow(action)

  return isIfShow
}

const getActions = computed(() => {
  return (
    (toRaw(props.actions) || [])
    /*   .filter((action) => {
                    return hasPermission(action.auth) && isIfShow(action)
                }) */
      .filter((action) => {
        return isIfShow(action)
      })
      .map((action: any) => {
        // const { popConfirm } = action
        // 需要展示什么风格，自己修改一下参数
        return {
          size: 'small',
          text: props.actions.text,
          type: props.actions.type,
          ...action,
          // ...(popConfirm || {}),
          // onConfirm: popConfirm?.confirm,
          // onCancel: popConfirm?.cancel,
          // enable: !!popConfirm
        }
      })
  )
})

const theme = useThemeStore()
const themeColor = computed(() => theme.themeColor)
</script>

<template>
  <div class="rowAction">
    <template v-for="(action, index) in getActions" :key="`${index}-${action.label}`">
      <n-button :text-color="action?.textColor ?? '#3B8FD9'" v-bind="action">
        {{ action.label }}
      </n-button>
      <n-divider v-if="action.text && index !== getActions.length - 1" vertical />
    </template>
    <!-- 暂时未分装完 -->
    <!--         <n-dropdown
            v-if="dropDownActions && getDropdownList.length"
            trigger="hover"
            :options="getDropdownList"
            @select="select"
        >
            <slot name="more"></slot>
            <n-button v-bind="getMoreProps" v-if="!$slots.more" icon-placement="right">
                <div class="flex items-center">
                    <span>更多</span>
                </div>
            </n-button>
        </n-dropdown> -->
  </div>
</template>

<style scoped lang="scss">
.rowAction {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    :deep(.n-divider.n-divider--vertical) {
        background-color:  #3B8FD9;
        height: 14px;
    }
    :deep(.n-button__content) {
        // color: v-bind(themeColor);
        font-size: 14px;
    }
}
</style>
