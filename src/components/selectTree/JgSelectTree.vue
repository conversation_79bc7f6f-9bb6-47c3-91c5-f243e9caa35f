<script setup lang='ts'>
const props = defineProps({
  width: {
    type: String,
    default: '280px',
  },
  options: {
    type: Array<any>,
    default: [],
  },
  defaultValue: {
    type: String,
    default: '',
  },
})

const emit = defineEmits(['valueSelect'])

const customIcon = ref()
const currentV = ref(props.defaultValue)

const isFocus = ref(false)

function selectFocus() {
  isFocus.value = true
}

function selectBlur() {
  isFocus.value = false
}

function valueChange(v, op) {
  // console.log(op)
  isFocus.value = false
  currentV.value = op.key
  emit('valueSelect', op)
  if (op.prefix)
    customIcon.value = op.prefix()
  else
    customIcon.value = null
}

function trigerInput() {
  const dom = document.getElementById('jg-select')
  try {
    dom?.children[0].children[0].children[0]?.click()
  }
  catch (error) {

  }
}

watch(() => props.defaultValue, (o, n) => {
  currentV.value = o
})
</script>

<template>
  <div class="content">
    <div class="sb-icon" @click="trigerInput">
      <img
        v-if="!customIcon"
        src="@/assets/images/selectLevel.png"
        mode="scaleToFill"
        :style="{ width: '16px', height: '16px' }"
      >
      <customIcon v-else />
    </div>
    <n-tree-select id="jg-select" :value="currentV" filterable :options="props.options" key-field="id" label-field="organName" :style="{ width: props.width }" @update:value="valueChange" @focus="selectFocus" @blur="selectBlur">
      <template #arrow>
        <SvgIcon
          v-if="!isFocus" local-icon="slmc-icon-jiantou" class="text-#999" size="7" color="#999" @click.self="() => {
            console.log('xxx')
          }"
        />
        <SvgIcon v-else local-icon="slmc-icon-search" class="text-16px text-#999;" size="16" color="#999" />
      </template>
    </n-tree-select>
  </div>
</template>

<style>
.n-tree .n-tree-node{
  height: 32px;
  line-height: 32px;
  align-items: center;
}
</style>

<style scoped lang="scss">
.content{
  position:relative
}
:deep(.n-base-selection .n-base-suffix.n-base-suffix--active .n-base-suffix__arrow){
  transform: unset;
}
.sb-icon{
    position:absolute;
    top:0;
    bottom: 0;
    display: flex;
    align-items: center;
    left:10px;
    z-index:1;
}

:deep(.n-base-selection-input){
  padding-left: 30px  !important;
}

:deep(.n-base-selection .n-base-selection-overlay){
  padding-left: 30px  ;
}
:deep(.n-base-selection .n-base-suffix .n-base-suffix__arrow){
  width: 100%;
  display: flex;
  align-items: center;
}
</style>
