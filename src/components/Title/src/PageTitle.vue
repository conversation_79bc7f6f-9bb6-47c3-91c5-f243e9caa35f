<script lang="ts" setup>
import { SvgIcon } from '@/components/Icon'

defineProps({
  border: {
    type: Boolean,
    default: false,
  },
})
</script>

<template>
  <div class="pageHeader" :class="border ? 'bottom-border' : ''">
    <div class="w-full flex items-center justify-start">
      <SvgIcon local-icon="slmc-icon-title1" flex-shrink-0 size="16" class="text-primary" />
      <span class="pageHeader-title">
        <slot />
      </span>
    </div>
    <slot name="right" />
  </div>
</template>

<style scoped lang="scss">
.pageHeader {
    display: flex;
    align-items: center;
    justify-content: space-between;

    &-title {
        margin-left: 10px;
        color: #4a4a4a;
        height: 16px;
        line-height: 16px;
        width: 100%;
    }

}
.bottom-border {
        padding-bottom: 10px ;
        border-bottom: 1px solid #ccc;
    }
</style>
