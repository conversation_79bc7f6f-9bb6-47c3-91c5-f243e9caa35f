<script lang='ts' setup>
import { routeName } from '@/router'
import image404 from '@/assets/images/page-404.png'
import image403 from '@/assets/images/page-403.png'
import image404Text from '@/assets/images/page-404_text.png'
import image403Text from '@/assets/images/page-403_text.png'

defineOptions({ name: 'ExceptionBase' })

defineProps<Props>()

interface Props {
  /** 异常类型 403 404 500 */
  type: ExceptionType
}

type ExceptionType = '404' | '403'
const routeHomePath = routeName('root')
const TYPE_TEXT: Record<ExceptionType, string> = {
  404: '服务器查询不到信息',
  403: '非常抱歉，您没有权限访问此页面...',
}
const TYPE_IMAGE: Record<ExceptionType, string> = {
  404: image404,
  403: image403,
}
const TYPE_IMAGE_TEXT: Record<ExceptionType, string> = {
  404: image404Text,
  403: image403Text,
}
</script>

<template>
  <div class="w-full">
    <div class="flex items-center">
      <img :src="TYPE_IMAGE[type]" alt="" class="mr-20px w-300px">
      <div>
        <img :src="TYPE_IMAGE_TEXT[type]" alt="" class="w-118px">
        <div class="mb-14px mt-16px text-16px text-#999">
          {{ TYPE_TEXT[type] }}
        </div>
        <router-link :to="{ name: routeHomePath }">
          <n-button type="primary" ghost>
            回到首页
          </n-button>
        </router-link>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss"></style>
