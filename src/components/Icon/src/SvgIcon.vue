<script setup lang="ts">
import { computed, useAttrs } from 'vue'
import type { CSSProperties } from 'vue'

defineOptions({ name: 'SvgIcon' })

const props = defineProps<Props>()

/**
 * 图标组件
 * - 支持iconify和本地svg图标
 * - 同时传递了icon和localIcon，localIcon会优先渲染
 */
interface Props {
  /** 图标名称 */
  icon?: string
  /** 本地svg的文件名 */
  localIcon?: string
  size?: number | string
  color?: string
  minWidth?: number
}

const attrs = useAttrs()

const bindAttrs = computed<{ class: string; style: string }>(() => ({
  class: (attrs.class as string) || '',
  style: (attrs.style as string) || '',
}))

const symbolId = computed(() => {
  const defaultLocalIcon = 'no-icon'

  const icon = props.localIcon || defaultLocalIcon

  return `#${icon}`
})
const getStyle = computed((): CSSProperties => {
  const { size, color, minWidth } = props
  let s = `${size}`
  s = `${s.replace('px', '')}px`
  return {
    width: s,
    height: s,
    color,
    minWidth,
  }
})

/** 渲染本地icon */
const renderLocalIcon = computed(() => props.localIcon || !props.icon)
</script>

<template>
  <template v-if="renderLocalIcon">
    <svg aria-hidden="true" class="svgIcon" v-bind="bindAttrs" :style="getStyle">
      <use :xlink:href="symbolId" />
    </svg>
  </template>
  <template v-else>
    <!-- <Icon v-if="icon" :icon="icon" v-bind="bindAttrs" :style="getStyle" /> -->
    <!-- <img v-if="icon" class="svgIcon" :src="icon" :style="getStyle" v-bind="bindAttrs"> -->
    <!-- <object class="svgIcon" :src="icon" :style="getStyle" v-bind="bindAttrs" type="image/svg+xml" :data="icon" /> -->
    <img :src="icon" aria-hidden="true" class="svgIcon" v-bind="bindAttrs" :style="getStyle">
  </template>
</template>

<style scoped lang="scss">
.svgIcon {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
}
</style>
