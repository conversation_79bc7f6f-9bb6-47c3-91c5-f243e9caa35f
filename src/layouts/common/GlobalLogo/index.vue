<script setup lang="ts">
import huazhuologo from '@/assets/images/logo-header_white.png'

defineOptions({ name: 'GlobalLogo' })

defineProps<Props>()

interface Props {
  /** 显示名字 */
  showTitle: boolean
}

function goHisPage() {
  const token = sessionStorage.getItem('his_token')
  window.location.replace(`http://***********:30788/#/?token=${token}`)
}

// const routeHomePath = routePath('root')
</script>

<template>
  <div class="w-full flex-y-center nowrap-hidden">
    <img :src="huazhuologo" alt="" class="mr-20px w-260px cursor-pointer select-none" @click="goHisPage">
    <!-- <h2 v-show="showTitle" class="text-primary pl-8px text-16px font-bold transition duration-300 ease-in-out">
      SLMC管理系统
    </h2> -->
  </div>
</template>

<style scoped></style>
