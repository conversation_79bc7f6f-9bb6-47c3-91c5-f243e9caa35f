<script lang='ts' setup>
import bread from './constant'
import type { BreadLit, BreadRouteName } from './constant'
import { router } from '~/src/router'
import { useRouterPush } from '@/hooks'

interface Props {
  /** 当前路由名字 */
  routeName?: BreadRouteName
  /** 传入的面包屑列表 */
  breadList?: BreadLit[]

}

const props = defineProps<Props>()

const getBreadList = computed(() => {
  if (props.breadList && props.breadList.length)
    return props.breadList
  if (props.routeName)
    return bread[props.routeName]
})

const { routerPush } = useRouterPush()
function onClick(routeName: PageRoute.RouteKey, routeLink: string | null) {
  if (!routeLink)
    return
  const host = window.location.host
  const protocol = window.location.protocol

  const query: Record<string, string> = {

  }

  const searchParams = new URL(`${protocol}//${host}${routeLink}`).searchParams
  // 显示键/值对
  for (const pair of searchParams.entries())
    query[pair[0]] = pair[1]

  // routerPush({ name: routeName, query: { ...query } })
  /// 直接返回,不再 push
  router.back()
}
</script>

<template>
  <div class="breadcrumb">
    <n-breadcrumb>
      <n-breadcrumb-item
        v-for="item in getBreadList" :key="item.key"
      >
        <n-ellipsis max-w-100px>
          <span class="text-14px" @click="onClick(item.key, item.link)">{{ item.title }}</span>
        </n-ellipsis>
      </n-breadcrumb-item>
    </n-breadcrumb>
  </div>
</template>

<style scoped lang="scss">
.breadcrumb {
    position: relative;
    z-index: 3;
    height: 32px;
    line-height: 32px;
    padding-left: 20px;
    background: #ffffff;
    box-shadow: 0px 4px 5px -3px rgba(202, 202, 202, 0.5);
}
</style>
