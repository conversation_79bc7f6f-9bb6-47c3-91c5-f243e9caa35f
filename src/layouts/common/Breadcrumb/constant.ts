export type BreadRouteName =
    | 'system_permissionLook'
    | 'system_permissionEdit'
    | 'analysis_statement'
    | 'analysis_specialDisease'
    | 'system_userManage'
    | 'system_permissionManage'
    | 'system_log'
    | 'patient_list'
    | 'system_roleManage'
    | 'system_memberManage'
    | 'intelligentFollow_registration'
    | 'intelligentFollow_plan'
    | 'intelligentFollow_todo'
    | 'intelligentFollow_confirm'
    | 'specificDisease_list'
    | 'specificDisease_detail'
    | 'systemBaseSetting'

export interface BreadLit {
  link: string | null
  title: string
  key: PageRoute.RouteKey
}
/** 查看权限 */
const system_permissionLook: BreadLit[] = [
  { title: '系统设置', link: '/system', key: 'system' },
  { title: '权限管理', link: '/system/permissionManage', key: 'system_permissionManage' },
  { title: '查看', link: null, key: 'system_permissionLook' },
]
/** 添加子管理员 */
// const system_permissionEdit: BreadLit[] = [
//   { title: '系统设置', link: '/system', key: 'system' },
//   { title: '权限管理', link: '/system/permissionManage', key: 'system_permissionManage' },
//   { title: '添加子管理员', link: null, key: 'system_permissionEdit' },
// ]
/** 统计报表 */
const analysis_statement: BreadLit[] = [
  { title: '统计分析', link: '/analysis', key: 'analysis' },
  { title: '统计报表', link: null, key: 'analysis_statement' },
]
/** 专病报表 */
const analysis_specialDisease: BreadLit[] = [
  { title: '统计分析', link: '/analysis', key: 'analysis' },
  { title: '专病报表', link: null, key: 'analysis_specialDisease' },
]

/** 用户管理 */
const system_userManage: BreadLit[] = [
  { title: '系统设置', link: '/system', key: 'system' },
  { title: '用户管理', link: null, key: 'system_userManage' },
]
/** 权限管理 */
const system_permissionManage: BreadLit[] = [
  { title: '系统设置', link: '/system', key: 'system' },
  { title: '权限管理', link: null, key: 'system_permissionManage' },
]
/** 权限管理 */
const system_log: BreadLit[] = [
  { title: '系统设置', link: '/system', key: 'system' },
  { title: '系统日志', link: null, key: 'system_log' },
]
/** 患者列表 */
const patient_list: BreadLit[] = [
  { title: '患者数据库', link: '/patient', key: 'patient' },
  { title: '患者列表', link: null, key: 'patient_list' },
]

/** 角色管理 */
const system_roleManage: BreadLit[] = [
  { title: '系统设置', link: '/system', key: 'system' },
  { title: '角色管理', link: null, key: 'system_roleManage' },
]
/** 权限设置 */
const system_permissionEdit: BreadLit[] = [
  { title: '系统设置', link: '/system', key: 'system' },
  { title: '角色管理', link: '/system/roleManage', key: 'system_roleManage' },
  { title: '权限设置', link: null, key: 'system_permissionEdit' },
]

/** 成员管理 */
const system_memberManage: BreadLit[] = [
  { title: '系统设置', link: '/system', key: 'system' },
  { title: '角色管理', link: '/system/roleManage', key: 'system_roleManage' },
  { title: '成员管理', link: null, key: 'system_memberManage' },
]

/** 智能随访 */
const intelligentFollow_registration: BreadLit[] = [
  { title: '智能随访', link: '/intelligentFollow', key: 'intelligentFollow' },
  { title: '患者登记', link: null, key: 'intelligentFollow_registration' },
]

/** 专病管理方案 */
const intelligentFollow_plan: BreadLit[] = [
  { title: '智能随访', link: '/intelligentFollow', key: 'intelligentFollow' },
  { title: '专病管理方案', link: null, key: 'intelligentFollow_plan' },
]

/** 智能随访-随访待办 */
const intelligentFollow_todo: BreadLit[] = [
  { title: '智能随访', link: '/intelligentFollow', key: 'intelligentFollow' },
  { title: '随访待办', link: null, key: 'intelligentFollow_todo' },
]

/** 智能随访-待入组患者 */
const intelligentFollow_confirm: BreadLit[] = [
  { title: '智能随访', link: '/intelligentFollow', key: 'intelligentFollow' },
  { title: '待入组患者', link: null, key: 'intelligentFollow_confirm' },
]
/** 专病数据库-患者列表 */
const specificDisease_list: BreadLit[] = [
  { title: '专病数据库', link: '/specificDisease', key: 'specificDisease' },
  { title: '患者列表', link: null, key: 'specificDisease_list' },
]
/** 专病数据库-患者列表-小树苗患者档案 */
const specificDisease_detail: BreadLit[] = [
  { title: '专病数据库', link: '/specificDisease', key: 'specificDisease' },
  { title: '患者列表', link: '/specificDisease/list', key: 'specificDisease_list' },
  { title: '小树苗患者档案', link: null, key: 'specificDisease_detail' },
]

/** 专病数据库-患者列表-小树苗患者档案 */
const systemBaseSetting: BreadLit[] = [
  { title: '系统设置', link: '', key: 'system' },
  { title: '基础设置', link: '/specificDisease/list', key: 'specificDisease_list' },
]

/** 科研管理-课题管理 */
// const researcher: BreadLit[] = [
//   { title: '科研管理', link: '/researcher', key: 'researcher' },
//   { title: '课题管理', link: '/researcher/projectManage', key: 'researcher_projectManage' },
//   { title: '新增课题', link: '/researcher/projectManage/create', key: 'researcher_projectManage_create' },
// ]

/** 科研管理-课题管理 */
// const researcher_projectManage: BreadLit[] = [
//   { title: '科研管理', link: '/researcher', key: 'researcher' },
//   { title: '课题管理', link: '/researcher/projectManage', key: 'researcher_projectManage' },
// ]

const bread: Record<BreadRouteName, BreadLit[]> = {
  system_permissionLook,
  system_permissionEdit,
  analysis_statement,
  analysis_specialDisease,
  system_userManage,
  system_permissionManage,
  system_log,
  patient_list,
  system_roleManage,
  system_memberManage,
  intelligentFollow_registration,
  intelligentFollow_plan,
  intelligentFollow_todo,
  intelligentFollow_confirm,
  specificDisease_list,
  specificDisease_detail,
  systemBaseSetting,
  // researcher,
  // researcher_projectManage,
}

export default bread
