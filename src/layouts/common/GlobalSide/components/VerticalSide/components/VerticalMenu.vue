<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import type { MenuOption } from 'wowjoy-vui'
import { useAppStore, useRouteStore, useThemeStore } from '@/store'
import { useRouterPush } from '@/hooks'
import { getActiveKeyPathsOfMenus, translateMenuLabel } from '@/utils'

defineOptions({ name: 'VerticalMenu' })

const route = useRoute()
const app = useAppStore()
const theme = useThemeStore()
const routeStore = useRouteStore()
const { routerPush } = useRouterPush()

const menus = computed(() => translateMenuLabel(routeStore.menus as App.GlobalMenuOption[]))

const activeKey = computed(() => (route.meta?.activeMenu ? route.meta.activeMenu : route.name) as string)
const expandedKeys = ref<string[]>([])

function handleUpdateMenu(_key: string, item: MenuOption) {
  const menuItem = item as App.GlobalMenuOption
  routerPush(menuItem.routePath)
}

function handleUpdateExpandedKeys(keys: string[]) {
  expandedKeys.value = keys
}

watch(
  () => route.name,
  () => {
    expandedKeys.value = getActiveKeyPathsOfMenus(activeKey.value, menus.value)
  },
  { immediate: true },
)
</script>

<template>
  <n-scrollbar class="flex-1-hidden">
    <n-menu
      class="layoutSide"
      :value="activeKey"
      :collapsed="app.siderCollapse"
      :collapsed-width="theme.sider.collapsedWidth"
      :icon-size="16"
      :collapsed-icon-size="22"
      :options="menus"
      :expanded-keys="expandedKeys"
      :indent="14"
      :inverted="!theme.darkMode && theme.sider.inverted"
      @update:value="handleUpdateMenu"
      @update:expanded-keys="handleUpdateExpandedKeys"
    />
  </n-scrollbar>
</template>

<style scoped lang="scss">
.layoutSide {
    :deep(.n-menu-item-content.n-menu-item-content--child-active ){
        .n-menu-item-content-header {
            color:#333333;
            font-weight: 500;
        }
        .n-menu-item-content__icon {
            color:#666666;
         }
        .n-menu-item-content__arrow {
            color:#333333;
        }

    }

    :deep(.n-menu-item-content--selected::before){

            background: #06AEA6;
            width: 4px;
            height: 26px;
            right: 0;
            left: 0;
            top: 7px;
            z-index: 1;
            border-radius:0

    }
    :deep(.n-menu-item-content::before) {
            transition: none;
    }
    :deep(.n-menu-item-content-header){
        color: #333333;

        font-weight: 500;
    }
    :deep(.n-submenu-children){
        background: #f5f7f8;
        .n-menu-item-content--selected::before {
            background: #06AEA6;
            width: 4px;
            height: 26px;
            right: 0;
            left: 0;
            top: 7px;
            z-index: 1;
            border-radius:0

        }

        .n-menu-item-content::before {
            transition: none;
        }
        .n-menu-item-content-header {
            color: #333333;
            font-weight: normal;
        }
        .n-menu-item-content--selected .n-menu-item-content-header {
            color: #06AEA6;

        }

    }

    :deep(.n-menu-item-content__icon ){
            color:#666666;
    }
    :deep(.n-menu-item-content:not(.n-menu-item-content--disabled):hover .n-menu-item-content__icon){
        color: #06AEA6;
    }
    :deep(.n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--selected:hover .n-menu-item-content__icon ){
        color: #06AEA6;
    }
    :deep(.n-menu-item-content:not(.n-menu-item-content--disabled).n-menu-item-content--selected:hover::before){
        background-color: #06AEA6;
    }
    :deep(.n-menu-item-content:not(.n-menu-item-content--disabled):hover .n-menu-item-content-header){
      color: #06AEA6;
    }

}

:deep(.n-submenu-children)  {
  .n-menu-item-content {
    padding-left: 42px!important;
  }
}
</style>
