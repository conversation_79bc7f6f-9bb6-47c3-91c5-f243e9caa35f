<script setup lang="ts">
import { computed } from 'vue'
import { VerticalMenu } from './components'
import { useAppStore, useThemeStore } from '@/store'
import { GlobalLogo } from '@/layouts/common'

defineOptions({ name: 'VerticalSider' })

const app = useAppStore()
const theme = useThemeStore()

const isHorizontalMix = computed(() => theme.layout.mode === 'horizontal-mix')
const showTitle = computed(() => !app.siderCollapse && theme.layout.mode !== 'vertical-mix')
</script>

<template>
  <div class="h-full flex-col-stretch bg-white">
    <GlobalLogo v-if="!isHorizontalMix" :show-title="showTitle" :style="{ height: `${theme.header.height}px` }" />
    <VerticalMenu />
  </div>
</template>

<style scoped></style>
