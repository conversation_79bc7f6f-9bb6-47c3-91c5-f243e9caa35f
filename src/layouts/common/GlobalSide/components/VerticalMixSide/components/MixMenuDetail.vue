<script setup lang="ts">
import { computed } from 'vue'
import type { Component } from 'vue'
import { useBoolean } from '@/hooks'

defineOptions({ name: 'MixMenuDetail' })

const props = withDefaults(defineProps<Props>(), {
  icon: undefined,
  isMini: false,
})

interface Props {
  /** 路由名称 */
  routeName: string
  /** 路由名称文本 */
  label: string
  /** 当前激活状态的理由名称 */
  activeRouteName: string
  /** 路由图标 */
  icon?: Component
  /** mini尺寸的路由 */
  isMini?: boolean
}

const { bool: isHover, setTrue, setFalse } = useBoolean()

const isActive = computed(() => props.routeName === props.activeRouteName)
</script>

<template>
  <div class="mb-6px cursor-pointer px-4px" @mouseenter="setTrue" @mouseleave="setFalse">
    <div
      class="flex-center flex-col rounded-2px bg-transparent py-12px transition-colors duration-300 ease-in-out"
      :class="{ 'text-primary !bg-primary_active': isActive, 'text-primary': isHover }"
    >
      <component :is="icon" :class="[isMini ? 'text-16px' : 'text-20px']" />
      <p
        class="w-full ellipsis-text text-center text-12px transition-height duration-300 ease-in-out"
        :class="[isMini ? 'h-0 pt-0' : 'h-24px pt-4px']"
      >
        {{ label }}
      </p>
    </div>
  </div>
</template>

<style scoped></style>
