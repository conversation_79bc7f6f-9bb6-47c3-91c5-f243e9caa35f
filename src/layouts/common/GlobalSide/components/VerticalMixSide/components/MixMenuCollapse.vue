<script lang="ts" setup>
import { useAppStore } from '@/store'

defineOptions({ name: 'MixMenuCollapse' })

const app = useAppStore()
</script>

<template>
  <n-button :text="true" class="h-36px" @click="app.toggleSiderCollapse">
    <SvgIcon
      v-if="app.siderCollapse"
      local-icon="slmc-icon-takeup"
      size="18"
      style="cursor: pointer"
    />
    <SvgIcon
      v-else
      local-icon="slmc-icon-open"
      size="18"
      style="cursor: pointer"
    />
  </n-button>
</template>

<style scoped></style>
