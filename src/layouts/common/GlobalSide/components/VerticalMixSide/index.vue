<script setup lang="ts">
import { computed, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { MixMenuCollapse, MixMenuDetail, MixMenuDrawer } from './components'
import { useAppStore, useRouteStore, useThemeStore } from '@/store'
import { useBoolean, useRouterPush } from '@/hooks'
import { translateMenuLabel } from '@/utils'
import { GlobalLogo } from '@/layouts/common'

defineOptions({ name: 'VerticalMixSider' })

const route = useRoute()
const app = useAppStore()
const theme = useThemeStore()
const routeStore = useRouteStore()
const { routerPush } = useRouterPush()
const { bool: drawerVisible, setTrue: openDrawer, setFalse: hideDrawer } = useBoolean()

const activeParentRouteName = ref('')

function setActiveParentRouteName(routeName: string) {
  activeParentRouteName.value = routeName
}

const firstDegreeMenus = computed(() =>
  routeStore.menus.map((item) => {
    const { routeName, label } = item
    const icon = item?.icon
    const hasChildren = Boolean(item.children && item.children.length)

    return {
      routeName,
      label,
      icon,
      hasChildren,
    }
  }),
)

function getActiveParentRouteName() {
  firstDegreeMenus.value.some((item) => {
    const routeName = (route.meta?.activeMenu ? route.meta.activeMenu : route.name) as string
    const flag = routeName?.includes(item.routeName)
    if (flag)
      setActiveParentRouteName(item.routeName)

    return flag
  })
}

function handleMixMenu(routeName: string, hasChildren: boolean) {
  setActiveParentRouteName(routeName)
  if (hasChildren)
    openDrawer()
  else
    routerPush({ name: routeName })
}

function resetFirstDegreeMenus() {
  getActiveParentRouteName()
  hideDrawer()
}

const activeChildMenus = computed(() => {
  const menus: App.GlobalMenuOption[] = []
  routeStore.menus.some((item) => {
    const flag = item.routeName === activeParentRouteName.value && Boolean(item.children?.length)
    if (flag)
      menus.push(...translateMenuLabel((item.children || []) as App.GlobalMenuOption[]))

    return flag
  })
  return menus
})

watch(
  () => route.name,
  () => {
    getActiveParentRouteName()
  },
  { immediate: true },
)
</script>

<template>
  <div class="h-full flex" @mouseleave="resetFirstDegreeMenus">
    <div class="h-full flex-col-stretch flex-1-hidden">
      <GlobalLogo :show-title="false" :style="{ height: `${theme.header.height}px` }" />
      <n-scrollbar class="flex-1-hidden">
        <MixMenuDetail
          v-for="item in firstDegreeMenus"
          :key="item.routeName"
          :route-name="item.routeName"
          :active-route-name="activeParentRouteName"
          :label="item.label"
          :icon="item.icon"
          :is-mini="app.siderCollapse"
          @click="handleMixMenu(item.routeName, item.hasChildren)"
        />
      </n-scrollbar>
      <MixMenuCollapse />
    </div>
    <MixMenuDrawer :visible="drawerVisible" :menus="activeChildMenus" />
  </div>
</template>

<style scoped></style>
