<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'
import { routePath } from '@/router'
import { useRouteStore, useThemeStore } from '@/store'
import { useRouterPush } from '@/hooks'
import { getBreadcrumbByRouteKey } from '@/utils'

defineOptions({ name: 'GlobalBreadcrumb' })

const route = useRoute()
const theme = useThemeStore()
const routeStore = useRouteStore()
const { routerPush } = useRouterPush()

const breadcrumbs = computed(() =>
  getBreadcrumbByRouteKey(route.name as string, routeStore.menus as App.GlobalMenuOption[], routePath('root')).map(
    item => ({
      ...item,
      label: item.label,
      options: item.options?.map(oItem => ({ ...oItem, label: oItem.label })),
    }),
  ),
)

function dropdownSelect(key: string) {
  routerPush({ name: key })
}
</script>

<template>
  <n-breadcrumb v-if="route?.meta?.breadcrumb" class="h-30px flex-y-center px-10px">
    <template v-for="breadcrumb in breadcrumbs" :key="breadcrumb.key">
      <n-breadcrumb-item>
        <n-dropdown v-if="breadcrumb.hasChildren" :options="breadcrumb.options" @select="dropdownSelect">
          <span>
            <component
              :is="breadcrumb.icon"
              v-if="theme.header.crumb.showIcon"
              class="mr-4px inline-block align-text-bottom text-16px"
            />
            <span>{{ breadcrumb.label }}</span>
          </span>
        </n-dropdown>
        <template v-else>
          <component
            :is="breadcrumb.icon"
            v-if="theme.header.crumb.showIcon"
            class="mr-4px inline-block align-text-bottom text-16px"
            :class="{ 'text-#BBBBBB': theme.header.inverted }"
          />
          <span :class="{ 'text-#BBBBBB': theme.header.inverted }">
            {{ breadcrumb.label }}
          </span>
        </template>
      </n-breadcrumb-item>
    </template>
  </n-breadcrumb>
</template>

<style scoped></style>
