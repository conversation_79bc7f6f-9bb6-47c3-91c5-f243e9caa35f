<script lang='ts' setup>
const emit = defineEmits(['cancel', 'confirm'])

function onConfirm() {
  emit('confirm')
}
function onCancel() {
  emit('cancel')
}
</script>

<template>
  <div class="layout-footer">
    <n-space :size="[16, 16]">
      <n-button type="primary" min-width="100px" class="tracking-4px" @click="onConfirm">
        确定
      </n-button>
      <n-button min-width="100px" class="tracking-4px" @click="onCancel">
        取消
      </n-button>
    </n-space>
  </div>
</template>

<style scoped lang="scss">
.layout-footer {
    display: flex;
    align-items: center;
    justify-content: center;
    // position: fixed;
    // left: 180px;
    // bottom: 0;
    width: 100%;
    height: 60px;
    background: #f9f9f9;
    box-shadow: 0px -1px 0px 0px #e8e8e8 inset, 0px 1px 0px 0px #e8e8e8 inset;
}
</style>
