<script lang="ts" setup>
import { useAppStore } from '@/store'

import { SvgIcon } from '@/components/Icon'

defineOptions({ name: 'MenuCollapse' })

const app = useAppStore()
</script>

<template>
  <div class="h-full w-40px flex-center cursor-pointer text-white" @click="app.toggleSiderCollapse">
    <SvgIcon
      v-if="app.siderCollapse"
      local-icon="slmc-icon-open"
      size="18"
    />
    <SvgIcon
      v-else
      local-icon="slmc-icon-takeup"
      size="18"
    />
  </div>
</template>

<style scoped></style>
