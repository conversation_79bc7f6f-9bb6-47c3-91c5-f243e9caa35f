<script lang='ts' setup>
import type { TreeOption } from 'wowjoy-vui'
import { localStg } from '@/utils'
import { SvgIcon } from '@/components/Icon'

const emit = defineEmits(['setOrganSuccess'])

const userInfo = localStg.get('userInfo')

const organId = userInfo?.organId
const organList = userInfo?.organList ?? []

function formatOrganListTree(list: Auth.OrganList[]) {
  return list?.map((item: Auth.OrganList) => {
    const children: Auth.OrganList[] | null = item?.children?.length ? formatOrganListTree(item.children) : null

    if (children?.length) {
      item.children = children
    }
    else {
      item.prefix = () => h(
        SvgIcon,
        {
          localIcon: 'slmc-icon-yiyuan',

        },

      )
    }

    const obj = {
      ...item,

    }
    return obj
  })
}

// const organListTree = organList

function renderSuffix() {
  return h(
    SvgIcon,
    {
      localIcon: 'slmc-icon-select1',
      class: 'showCheckIcon',
    },

  )
}

// 树节点的属性
function nodeProps({ option }: { option: TreeOption }) {
  return {
    onclick: (e: Event) => {
      const targetNodeName = (e.target as HTMLInputElement).nodeName

      if (['svg', 'path'].includes(targetNodeName)) {
        return null
      }
      else {
        const setUserInfo = {
          ...userInfo,
          organId: option.id as string,
          organName: option.organName as string,
        }
        localStg.set('userInfo', setUserInfo as Auth.User)

        emit('setOrganSuccess')
      }
    },
    class: 'nodeProps',

  }
}
function clickOrgan(option) {
  const setUserInfo = {
    ...userInfo,
    organId: option.id as string,
    organName: option.organName as string,
  }
  localStg.set('userInfo', setUserInfo as Auth.User)

  emit('setOrganSuccess')
}
</script>

<template>
  <div>
    <!-- <n-tree
      block-line default-expand-all key-field="id" label-field="organName" class="tree"
      :data="organListTree" :render-suffix="renderSuffix" :node-props="nodeProps"
    /> -->
    <div
      v-for="organ in organList" :key="organ.id"
      class="h-30px flex cursor-pointer items-center justify-between px-10px hover:bg-#FFFBE0"
      @click="clickOrgan(organ)"
    >
      <div class="flex items-center justify-start">
        <SvgIcon local-icon="slmc-icon-yiyuan1" />
        <span class="ml-10px">{{ organ.organName }}</span>
      </div>
      <SvgIcon v-show="organId === organ.id" local-icon="slmc-icon-select" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.tree {
    :deep(.showCheckIcon) {
        visibility: hidden;
    }

    :deep(.n-tree-node-folder-content){
        margin-top: 0px;
    }
    :deep(.n-tree-node){
        display: flex;
        align-items: center;
    }
    :deep(.n-tree-node--selected ){
        .showCheckIcon {
            visibility: visible;
        }
    }

}
</style>
