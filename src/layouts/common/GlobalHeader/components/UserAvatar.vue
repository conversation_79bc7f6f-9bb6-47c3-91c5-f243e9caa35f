<script lang="ts" setup>
import { h } from 'vue'
import { NPopover, useMessage } from 'wowjoy-vui'
import RenderHospital from './RenderHospital.vue'
import { SvgIcon } from '@/components/Icon'
import ResetPassword from '@/views/login/resetPassword.vue'
import { useModal } from '@/components/Modal'

import { useAuthStore } from '@/store'

const message = useMessage()

const userInfo = useAuthStore().userInfo
const userName = userInfo?.userName
const firstName = userName?.slice(0, 1)
const organName = userInfo?.organName
const organList = userInfo?.organList ?? []
const haveChildren = organList?.length > 0

const hoverColor = reactive({
  password: '#666',
  loginOut: '#666',
  organ: '#666',
})

// 渲染头像
function renderAvatar() {
  const avatarStyle = {
    width: '32px',
    height: '32px',
    color: '#fff',
    borderRadius: '50%',
    textAlign: 'center',
    lineHeight: '32px',
    margin: '0 auto',
  }
  return h(
    'div',
    {
      style: avatarStyle,
      class: 'bg-primary',
    },
    { default: () => firstName },
  )
}

// 渲染名字
function renderName() {
  const nameStyle = {
    width: '100%',
    color: '#666',
    textAlign: 'center',
    marginTop: '10px',
  }
  return h(
    'div',
    {
      style: nameStyle,
    },
    { default: () => userName },
  )
}

// 渲染机构名称
function renderOrganizationName() {
  const nameStyle = {
    width: '100%',
    color: '#666',
    textAlign: 'center',
    marginTop: '10px',
  }
  return h(
    'div',
    {
      style: nameStyle,
    },
    { default: () => `所属机构：${organName}` },
  )
}

// 渲染登出
function renderLoginOut() {
  const itemStyle = {
    display: 'flex',
    justifyContent: 'flex-start',
    alignItems: 'center',
    color: '#333333',
    cursor: 'pointer',
  }
  const imgStyle = {

    marginRight: '9px',
  }
  return h(
    'div',
    {
      style: itemStyle,
      onClick: handleLoginOut,
      onMouseenter: () => {
        hoverColor.loginOut = 'text-primary'
      },
      onMouseleave: () => {
        hoverColor.loginOut = 'text-#666'
      },
    },
    [
      h(SvgIcon, {
        style: imgStyle,
        localIcon: 'slmc-icon-outing1',
        class: hoverColor.loginOut,
      }),
      h('div', { class: hoverColor.loginOut }, { default: () => '退出登录' }),
    ],
  )
}

// 渲染密码
function renderPassword() {
  const itemStyle = {
    display: 'flex',
    justifyContent: 'flex-start',
    alignItems: 'center',
    color: '#333333',
    cursor: 'pointer',
  }
  const imgStyle = {
    marginRight: '9px',
  }
  return h(
    'div',
    {
      style: itemStyle,
      onClick: handleChangePassword,
      onMouseenter: () => {
        hoverColor.password = 'text-primary'
      },
      onMouseleave: () => {
        hoverColor.password = 'text-#666'
      },
    },
    [
      h(SvgIcon, {
        style: imgStyle,
        localIcon: 'slmc-icon-password_',
        class: hoverColor.password,

      }),
      h('div', { class: hoverColor.password }, { default: () => '修改密码' }),
    ],
  )
}
// 渲染医院机构
function renderOrgan() {
  if (haveChildren) {
    return h(
      NPopover,
      {
        showArrow: false,
        placement: 'left-start',
        scrollable: true,
        raw: true,
        contentStyle: {
          backgroundColor: '#fff',
          padding: 0,
          width: '240px',
        },
        style: {
          marginRight: '15px',
        },

      },
      {
        trigger: () => h('div', {
          class: 'flex flex-start items-center cursor-pointer ',
        }, [
          h(SvgIcon, {

            localIcon: 'slmc-icon-changecompany1',
            class: `${hoverColor.organ} mr-10px`,

          }),
          h('div', {
            class: `${hoverColor.organ} flex flex-start items-center`,
            onMouseenter: () => {
              hoverColor.organ = 'text-primary'
            },
            onMouseleave: () => {
              hoverColor.organ = 'text-#666'
            },
          }, [
            h('div', { class: `${hoverColor.organ}  ` }, { default: () => '切换医院' }),
            h(SvgIcon, {
              localIcon: 'slmc-icon-jiantou-you1',
              class: 'hoverColor.organ  text-7px ml-5px inline-block ',
            }),
          ]),
        ]),
        default: () => h(RenderHospital, {
          onSetOrganSuccess: () => {
            message.success('切换机构成功')
            location.reload()
          },
        }),

      },

    )
  }
  else {
    return null
  }
}

// 渲染头部
function renderCustomHeader() {
  const boxStyle = {
    width: haveChildren ? '284px' : '200px',
    // height: '200px',
    fontSize: '12px',
  }
  const topBoxStyle = {
    padding: '6px 10px 10px',
    background: '#F5F7F8',
  }
  const bottomBoxStyle = {
    display: 'flex',
    justifyContent: 'space-between',
    alignItems: 'center',
    // height: '60px',
    color: '#666',
    padding: '12px 14px',
  }
  return h(
    'div',
    {
      style: boxStyle,
    },
    [
      h('div', { style: topBoxStyle }, [h(renderAvatar()), h(renderName()), h(renderOrganizationName())]),
      h('div', { style: bottomBoxStyle }, [h(renderOrgan()), h(renderPassword()), h(renderLoginOut())]),
    //   h('div', { style: bottomBoxStyle }, []),
    ],
  )
}

const options = [
  {
    key: 'header',
    type: 'render',
    render: renderCustomHeader,
  },
]

const dropShow = ref(false)

function handleClick() {
  dropShow.value = !dropShow.value
}
function handleChangePassword() {
  showResetModal()
  dropShow.value = !dropShow.value
}
function handleLoginOut() {
  dropShow.value = !dropShow.value
  useAuthStore().resetAuthStore()
}

// 获取验证码弹窗
const [registerVerifyModal, { openModal: openVerifyModal }] = useModal()
function showVerifyModal() {
  openVerifyModal()
}

const resetInfo = ref({
  code: '',
  phone: '',
})

let timer: any = null
function onVerifySuccess({ code, phone }: { code: string; phone: string }) {
  resetInfo.value.code = code
  resetInfo.value.phone = phone
  timer = setTimeout(() => {
    showResetModal()
    clearTimeout(timer)
    timer = null
  }, 500)
}

// 重置密码弹窗
const [registerResetPassword, { openModal: openResetPassword }] = useModal()
function showResetModal() {
  openResetPassword()
}
</script>

<template>
  <div id="avatarBox" class="avatarBox">
    <n-dropdown
      trigger="hover"
      :options="options" placement="bottom-start"
    >
      <div class="avatar flex items-center" @click="handleClick">
        <n-ellipsis style="max-width: 100px" class="mr-5px">
          <div>
            {{ userName }}
          </div>
        </n-ellipsis>

        <SvgIcon local-icon="slmc-icon-drop_down" color="#fff" size="12" />
      </div>
    </n-dropdown>
    <!-- 获取验证码 -->
    <!-- <VerifyModal @register="registerVerifyModal" @verify-success="onVerifySuccess" /> -->
    <!-- 重置密码 -->
    <!-- <ResetModal :reset-info="resetInfo" @register="registerResetModal" /> -->
    <ResetPassword :reset-info="resetInfo" @register="registerResetPassword" />
  </div>
</template>

<style scoped lang="scss">
.avatarBox {
    padding-left:6px ;
    padding-right: 6px;
    &:hover {
        background-color: rgba(255,255,255, 0.2);

    }
    .avatar {
        color: #fff;
        cursor: pointer;
        height: 100%;
    }

}
</style>
