<script lang='ts' setup>
import GlobalLogo from '../GlobalLogo/index.vue'
import MenuCollapse from './components/MenuCollapse.vue'
import UserAvatar from './components/UserAvatar.vue'

import { useThemeStore } from '@/store'

interface Props {
  /** 显示logo */
  showLogo: App.GlobalHeaderProps['showLogo']
  /** 显示头部菜单 */
  showHeaderMenu: App.GlobalHeaderProps['showHeaderMenu']
  /** 显示菜单折叠按钮 */
  showMenuCollapse: App.GlobalHeaderProps['showMenuCollapse']
}

defineProps<Props>()

const theme = useThemeStore()
</script>

<template>
  <div
    class="global-header flex-y-center justify-start bg-primary pl-28px"
    :style="{ height: `${theme.header.height}px` }"
  >
    <MenuCollapse v-if="showMenuCollapse" class="mr-30px" />
    <GlobalLogo v-if="showLogo" :show-title="true" class="h-full" />
    <div class="mr-20px h-full flex justify-end">
      <UserAvatar />
      <!-- <full-screen />
      <system-message />
      <setting-button v-if="showButton" />
      <user-avatar /> -->
    </div>
  </div>
</template>

<style scoped lang="scss">
.global-header {
  box-shadow: 0 1px 2px rgb(0 21 41 / 8%);

}
</style>
