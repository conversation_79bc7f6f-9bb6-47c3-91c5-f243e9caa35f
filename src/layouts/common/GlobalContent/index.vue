<script setup lang="ts">
import { useAppStore, useRouteStore, useThemeStore } from '@/store'

defineOptions({ name: 'GlobalContent' })

withDefaults(defineProps<Props>(), {
  showPadding: true,
})

interface Props {
  /** 显示padding */
  showPadding?: boolean
}

const app = useAppStore()
const theme = useThemeStore()
const routeStore = useRouteStore()
</script>

<template>
  <router-view v-slot="{ Component, route }">
    <transition
      :name="theme.pageAnimateMode" mode="out-in" :appear="true" @before-leave="app.setDisableMainXScroll(true)"
      @after-enter="app.setDisableMainXScroll(false)"
    >
      <keep-alive :include="routeStore.cacheRoutes">
        <component
          :is="Component" v-if="app.reloadFlag" :key="route.fullPath"
          class="flex-grow bg-#f6f9f8 transition duration-300 ease-in-out dark:bg-#101014"
        />
      </keep-alive>
    </transition>
  </router-view>
</template>

<style scoped></style>
