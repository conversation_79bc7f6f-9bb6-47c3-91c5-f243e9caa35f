<script lang='ts' setup>
import GlobalHeader from '../common/GlobalHeader/index.vue'
import GlobalSide from '../common/GlobalSide/index.vue'
import GlobalContent from '../common/GlobalContent/index.vue'
import { AdminLayout } from '@/components/business/AdminLayout'
import { useAppStore, useThemeStore } from '@/store'
import { useBasicLayout } from '@/hooks'

const app = useAppStore()
const theme = useThemeStore()

const { mode, isMobile, headerProps, siderVisible, siderWidth, siderCollapsedWidth } = useBasicLayout()
</script>

<template>
  <div class="h-full">
    <AdminLayout
      :mode="mode"
      :is-mobile="isMobile"
      :scroll-mode="theme.scrollMode"
      :scroll-el-id="app.scrollElId"
      :full-content="app.contentFull"
      :fixed-top="theme.fixedHeaderAndTab"
      :header-height="theme.header.height"
      :tab-visible="theme.tab.visible"
      :tab-height="theme.tab.height"
      :content-class="app.disableMainXScroll ? 'overflow-x-hidden' : ''"
      :sider-visible="siderVisible"
      :sider-collapse="app.siderCollapse"
      :sider-width="siderWidth"
      :sider-collapsed-width="siderCollapsedWidth"
      :footer-visible="theme.footer.visible"
      :fixed-footer="theme.footer.fixed"
      :right-footer="theme.footer.right"
      @click-mobile-sider-mask="app.setSiderCollapse(true)"
    >
      <template #header>
        <GlobalHeader v-bind="headerProps" />
      </template>
      <template #side>
        <GlobalSide />
      </template>
      <!-- <GlobalBreadcrumb v-if="theme.header.crumb.visible" /> -->
      <GlobalContent />
    </AdminLayout>
  </div>
</template>

<style scoped lang="scss"></style>
