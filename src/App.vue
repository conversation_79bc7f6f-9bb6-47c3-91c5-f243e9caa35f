<script setup lang="ts">
import { dateZhCN, zhCN } from 'wowjoy-vui'
import { ElConfigProvider } from 'element-plus'
import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import { subscribeStore, useThemeStore } from '@/store'

const theme = useThemeStore()

subscribeStore()
</script>

<template>
  <ElConfigProvider :locale="zhCn">
    <n-config-provider
      :locale="zhCN" :date-locale="dateZhCN" class="h-full" :theme="theme.naiveTheme"
      :theme-overrides="theme.naiveThemeOverrides"
    >
      <naive-provider>
        <router-view />
      </naive-provider>
    </n-config-provider>
  </ElConfigProvider>
</template>

<style scoped>

</style>
