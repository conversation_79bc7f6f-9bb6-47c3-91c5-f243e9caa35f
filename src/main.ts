import { createApp } from 'vue'

import { surveyPlugin } from 'survey-vue3-ui'
import App from './App.vue'
import { AppLoading } from './components/Loading'
import { setupStore } from './store'
import { setupRouter } from './router'
import { setupAssets } from './plugins'
import { setupDirectives } from './directives'
import { vLongPress } from './utils'
import '~/build/utils/info'

async function setupApp() {
  // import assets: js、css
  setupAssets()

  const app = createApp(App)
  // app loading
  const appLoading = createApp(AppLoading)

  appLoading.mount('#appLoading')
  // store plugin: pinia
  setupStore(app)

  // vue custom directives
  setupDirectives(app)
  app.directive('longpress', vLongPress)

  app.use(surveyPlugin)

  // vue router
  await setupRouter(app)

  appLoading.unmount()
  // mount app
  app.mount('#app')
}

setupApp()
