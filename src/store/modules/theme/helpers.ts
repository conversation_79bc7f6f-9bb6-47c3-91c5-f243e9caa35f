import type { ButtonProps, GlobalThemeOverrides } from 'wowjoy-vui'
import { cloneDeep } from 'lodash-es'

import { themeSetting } from '@/settings'
import { addColorAlpha, getColorPalette, localStg } from '@/utils'

type ButtonThemeOverrides = NonNullable<ButtonProps['themeOverrides']>

/** 初始化主题配置 */
export function initThemeSettings() {
  const isProd = import.meta.env.PROD
  // 生产环境才缓存主题配置，本地开发实时调整配置更改配置的json
  const storageSettings = localStg.get('themeSettings')

  if (isProd && storageSettings)
    return storageSettings

  const themeColor = localStg.get('themeColor') || themeSetting.themeColor

  const info = themeSetting.isCustomizeInfoColor ? themeSetting.otherColor.info : getColorPalette(themeColor, 7)
  const buttonThemeOverrides: ButtonThemeOverrides = {
    color: '#FFFFFF',
    colorHover: '#FFFFFF',
    colorPressed: '#E7F9F7',
    colorFocus: '#E7F9F7',
    textColor: '#666666',
    textColorHover: '#06AEA6',
    textColorPressed: '#06AEA6',
    textColorFocus: '#06AEA6',
    border: '1px solid #D1D1D1',
    borderHover: '1px solid #06AEA6',
    borderPressed: '1px solid #06AEA6',
    borderFocus: '1px solid #06AEA6',
  }
  const otherColor = { ...themeSetting.otherColor, info, buttonThemeOverrides }

  const setting = cloneDeep({ ...themeSetting, themeColor, otherColor })

  return setting
}

type ColorType = 'primary' | 'info' | 'success' | 'warning' | 'error'
type ColorScene = '' | 'Suppl' | 'Hover' | 'Pressed' | 'Active'
type ColorKey = `${ColorType}Color${ColorScene}`
type ThemeColor = Partial<Record<ColorKey, string>>

interface ColorAction {
  scene: ColorScene
  handler: (color: string) => string
}

/** 获取主题颜色的各种场景对应的颜色 */
function getThemeColors(colors: [ColorType, string][]) {
  const colorActions: ColorAction[] = [
    { scene: '', handler: color => color },
    { scene: 'Suppl', handler: color => color },
    // { scene: 'Hover', handler: color => getColorPalette(color, 5) },
    // { scene: 'Pressed', handler: color => getColorPalette(color, 7) },
    // { scene: 'Active', handler: color => addColorAlpha(color, 0.1) },
    { scene: 'Hover', handler: () => '#1AC3BB' },
    { scene: 'Pressed', handler: () => '#4A9591' },
    { scene: 'Active', handler: color => addColorAlpha(color, 0.1) },
  ]

  const themeColor: ThemeColor = {}

  colors.forEach((color) => {
    colorActions.forEach((action) => {
      const [colorType, colorValue] = color
      const colorKey: ColorKey = `${colorType}Color${action.scene}`
      themeColor[colorKey] = action.handler(colorValue)
    })
  })

  return themeColor
}

/** 获取naive的主题颜色 */
export function getNaiveThemeOverrides(colors: Record<ColorType, string>): GlobalThemeOverrides {
  const { primary, success, warning, error, buttonThemeOverrides } = colors

  const info = themeSetting.isCustomizeInfoColor ? colors.info : getColorPalette(primary, 7)

  const themeColors = getThemeColors([
    ['primary', primary],
    ['info', info],
    ['success', success],
    ['warning', warning],
    ['error', error],
  ])

  const colorLoading = primary

  return {
    common: {
      ...themeColors,
    },
    LoadingBar: {
      colorLoading,
    },
    DataTable: {
      thHeightMedium: themeSetting.dataTable.rowHeight,
      thColor: themeSetting.dataTable.headerColor,
    },
    Button: {
      ...buttonThemeOverrides,
    },
  }
}
