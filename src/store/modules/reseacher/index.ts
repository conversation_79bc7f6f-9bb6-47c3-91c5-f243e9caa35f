import { defineStore } from 'pinia'
import { useAuthStore } from '../auth'
import { getSubjectDetail, getTheTopicDetail, getToDoList } from '~/src/api/research'

export const useReseacherStore = defineStore('reseacher-store', {
  state: () => ({
    topicInfo: null,
    topicDetailTab: 'info',
    subjectInfo: null,
    waitToDo: null, /// 待办任务
  }),
  actions: {
    async getInfo(id: string) {
      const res = await getTheTopicDetail(id)
      this.topicInfo = res?.data
    },
    async getSubjectInfo(id: string) {
      const res = await getSubjectDetail(id)
      this.subjectInfo = res?.data
    },
    async getWaitToDoList(params) {
      const res = await getToDoList(params)
      this.waitToDo = res?.data
    },
    getPower(create: string) {
      /// 查看
      const user = useAuthStore().userInfo
      if (user?.isAdmin)
        return true
      else
        return user?.userName === create
    },
    updateTopicInfo(data) {
      this.topicInfo = data
    },
    updateTopicTab(data) {
      this.topicDetailTab = data
    },
    clearAll() {
      this.topicInfo = null
      this.subjectInfo = null
      this.waitToDo = null
    },
  },
})
