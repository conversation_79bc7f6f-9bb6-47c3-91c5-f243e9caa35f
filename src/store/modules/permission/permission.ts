import { defineStore } from 'pinia'
import type { RouteRecordRaw } from 'vue-router'
import type { UserInfo } from '../user/type'
import { store } from '@/store'
import LocalCache from '@/utils/cache'

// import { getUserPermissionApi } from '@/api/system/user'
import { asyncRoutes } from '@/router/routes'
import { isArray } from '@/utils/common'

export interface IPermissionState {
  menuList: MenuNode[] | any
  permCodeList: string[] // Permission code list
  permissionUrl: string[]
  isDynamicAddedRoute: boolean
}
interface MenuNode {
  powerName: string
  powerUrl: string | null
  children?: MenuNode[] | null
  powerCode: string
  isMenu: boolean
  powerIcon: string | null
}
interface Node {
  appId: string
  dcType: string
  orderIndex: number
  parentId: string
  powerCode: string
  powerIcon: null | null
  powerId: string
  powerName: string
  powerUrl: string | null
  preCode: null
  preId: string
  resourceType: string
  status: number
}
interface TreeNode {
  childLeaf: boolean
  node: null | Node
  nodeId: null | string
  children: TreeNode[]
}
interface Permission {
  treeNode: TreeNode
}

export const usePermissionStore = defineStore({
  id: 'app-permission',
  state: (): IPermissionState => ({
    menuList: [],
    permCodeList: [],
    permissionUrl: [],
    // Whether the route has been dynamically added
    isDynamicAddedRoute: false,
  }),
  getters: {
    getIsDynamicAddedRoute(): boolean {
      return this.isDynamicAddedRoute
    },
  },
  actions: {
    resetState(): void {
      this.isDynamicAddedRoute = false
      this.permCodeList = []
      this.menuList = []
    },
    setDynamicAddedRoute(added: boolean) {
      this.isDynamicAddedRoute = added
    },

    // 获取左侧菜单列表
    filterMenu(nodeTree: any) {

    },

    // 动态添加路由
    filterRoutes(routes: RouteRecordRaw[], permissionUrl: string[]) {
      return routes
        .filter((route) => {
          return permissionUrl.includes(route?.meta?.power)
        })
        .map((route) => {
          route = Object.assign({}, route)
          if (route.children)
            route.children = this.filterRoutes(route.children, permissionUrl)

          return route
        })
    },
    // 获取用户权限
    async getUserPermission() {
      const userInfo: UserInfo = LocalCache.getLocalStorage('userInfo')
      const userPower = userInfo?.user?.power

      if (userPower && isArray(userPower)) {
        this.permissionUrl = userPower
        return this.dynamicAddedRoute()
      }
    },
    dynamicAddedRoute() {
      const routes = [...asyncRoutes]
      const filterRoutes = this.filterRoutes(routes, this.permissionUrl)
      return filterRoutes
    },
  },
})

// Need to be used outside the setup
export function usePermissionStoreWithOut() {
  return usePermissionStore(store)
}
