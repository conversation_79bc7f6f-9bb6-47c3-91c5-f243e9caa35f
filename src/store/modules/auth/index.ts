import { nextTick } from 'vue'
import { defineStore } from 'pinia'

import { useRouteStore } from '../route'
import { clearAuthStorage, getToken, getUserInfo } from './helpers'

// import { fetchLogin, fetchUserInfo } from '@/service'
import { useRouterPush } from '@/hooks'
import { localStg } from '@/utils'
import { logOutApi, loginApi } from '@/api/system/login'

interface AuthState {
  /** 用户信息 */
  userInfo: Auth.User
  /** 用户token */
  token: string
  /** 登录的加载状态 */
  loginLoading: boolean
  /** 菜单的key */
  menuKey: string[]
}

export const useAuthStore = defineStore('auth-store', {
  state: (): AuthState => ({
    userInfo: getUserInfo(),
    token: getToken(),
    loginLoading: false,
    menuKey: [],
  }),
  getters: {
    /** 是否登录 */
    isLogin(state) {
      return Boolean(state.token)
    },
    isTopLevel(state) {
      return state.userInfo.organList[0].organLevel === '1'
    },
  },
  actions: {
    /** 重置auth状态 */
    resetAuthStore() {
      const { toLogin } = useRouterPush(false)

      const { resetRouteStore } = useRouteStore()

      clearAuthStorage()
      this.$reset()

      // if (route.meta.requiresAuth)
      toLogin()
      this.logOut()

      nextTick(() => {
        resetRouteStore()
      })
    },
    // 登出
    async logOut() {
      document.cookie = 'slmcToken=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'
      await logOutApi()
    },
    /**
     * 处理登录后成功或失败的逻辑
     * @param userInfo - 返回的用户信息
     */
    async handleActionAfterLogin(userInfo: Auth.UserInfo) {
      const route = useRouteStore()
      const { toLoginRedirect } = useRouterPush(false)

      const loginSuccess = this.loginByToken(userInfo)

      if (loginSuccess) {
        // await this.getMenuKey()
        await route.initAuthRoute()

        // 跳转登录后的地址
        toLoginRedirect()

        // 登录成功弹出欢迎提示
        if (route.isInitAuthRoute) {
          window.$notification?.success({
            title: '登录成功!',
            content: `欢迎回来，${this.userInfo.userName}!`,
            duration: 3000,
          })
        }

        return
      }

      // 不成功则重置状态
      this.resetAuthStore()
    },
    /**
     * 根据token进行登录
     * @param userInfo - 用户信息
     */
    loginByToken(userInfo: Auth.UserInfo) {
      let successFlag = false

      // 先把token存储到缓存中(后面接口的请求头需要token)
      const { access_token, user } = userInfo
      localStg.set('accessToken', access_token)
      localStg.set('userInfo', user)
      userInfo?.hisNo && localStg.set('hisNo', userInfo.hisNo)

      // 更新状态
      this.userInfo = user
      this.token = access_token

      successFlag = true
      return successFlag
    },
    /**
     * 登录
     * @param userName - 用户名
     * @param password - 密码
     */
    async login(params) {
      this.loginLoading = true

      const res = await loginApi<Auth.UserInfo>(params)

      if (res.data)
        await this.handleActionAfterLogin(res.data)

      this.loginLoading = false
    },

  },
})
