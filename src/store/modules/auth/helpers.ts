import { localStg } from '@/utils'

/** 获取token */
export function getToken() {
  return localStg.get('accessToken') || ''
}

/** 获取用户信息 */
export function getUserInfo() {
  const emptyInfo: Auth.User = {
    phone: '',
    id: '',
    power: [],
    userName: '',
    job: '',
    title: '',
    userNumber: '',
    userType: '',
    organList: [],
    organId: '',
    organName: '',
  }
  const userInfo: Auth.User = localStg.get('userInfo') || emptyInfo

  return userInfo
}

/** 去除用户相关缓存 */
export function clearAuthStorage() {
  localStg.remove('accessToken')
  localStg.remove('refreshToken')
  localStg.remove('userInfo')
  localStg.remove('hisNo')
}
