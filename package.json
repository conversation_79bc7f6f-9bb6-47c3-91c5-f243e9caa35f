{"name": "slmc-admin", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --noEmit", "lint:eslint": "eslint . --fix", "prepare": "husky install"}, "dependencies": {"@antv/g6": "4.8.24", "@better-scroll/core": "^2.5.1", "@types/lodash-es": "^4.17.7", "@unocss/reset": "^0.58.4", "@vueuse/components": "^10.5.0", "@vueuse/core": "^10.2.1", "@vueuse/router": "^10.6.0", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.12", "axios": "^1.4.0", "colord": "^2.9.3", "crypto-js": "^4.1.1", "dayjs": "^1.11.9", "echarts": "^5.2.2", "element-plus": "^2.8.3", "form-data": "^4.0.0", "lodash-es": "^4.17.21", "pinia": "^2.1.6", "pinyin-pro": "^3.17.0", "qs": "^6.11.2", "sass": "^1.63.6", "survey-creator-core": "^1.11.6", "survey-creator-knockout": "^1.11.6", "survey-vue3-ui": "^1.11.6", "unocss": "^0.58.4", "unplugin-auto-import": "^0.15.3", "unplugin-vue-components": "^0.24.1", "uuid": "^9.0.0", "v-scale-screen": "^2.2.0", "vue": "^3.4.15", "vue-router": "^4.2.2", "wowjoy-vui": "^2.34.3"}, "devDependencies": {"@antfu/eslint-config": "^0.39.6", "@commitlint/cli": "^17.6.6", "@commitlint/config-conventional": "^17.6.6", "@commitlint/cz-commitlint": "^17.5.0", "@faker-js/faker": "^8.0.2", "@iconify/vue": "^4.1.1", "@types/echarts": "^4.9.18", "@types/uuid": "^9.0.2", "@unocss/eslint-config": "^0.51.13", "@vitejs/plugin-vue": "^4.6.2", "consola": "^3.2.3", "eslint": "^8.44.0", "eslint-define-config": "^1.21.0", "husky": "^8.0.3", "lint-staged": "^13.2.3", "mockjs": "^1.1.0", "npm-run-all": "^4.1.5", "typescript": "^5.1.6", "unplugin-info": "^1.0.2", "unplugin-turbo-console": "^1.3.0", "vite": "^5.1.3", "vite-plugin-mock": "^3.0.0", "vue-tsc": "^1.8.3"}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "lint-staged": {"src/**/*.{js,ts,jsx,tsx,.vue}": ["git add "], "*.vue": ["git add "]}}