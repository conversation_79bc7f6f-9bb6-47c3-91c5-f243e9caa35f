pipeline {
    agent {
        label "node-web"
    }
    environment {
        NAME_IMAGE = "slmc/slmc-admin:${BRANCH_NAME}.b${BUILD_ID}"
    }
     stages {
        stage ("project-build") {
            when {
                branch "main";
            }
            steps {
                sh '''
                    set +ex
                    . /root/.nvm/nvm.sh
                    nvm use v18.15.0
                    set -ex
                    node -v
                    npm install
                    npm run build-only
                '''
            }
        }

        stage("publish-image") {
          when {
              branch "main";
          }
          steps {
              sh '''
                  docker build -t ${REGISTRY_ZZ}/${NAME_IMAGE} -f ./Dockerfile .
                  docker push ${REGISTRY_ZZ}/${NAME_IMAGE}
                  docker tag ${REGISTRY_ZZ}/${NAME_IMAGE} ${REGISTRY_DH}/${NAME_IMAGE}
                  docker push ${REGISTRY_DH}/${NAME_IMAGE}
                  docker rmi ${REGISTRY_DH}/${NAME_IMAGE}
                  docker rmi ${REGISTRY_ZZ}/${NAME_IMAGE}
              '''
          }
        }
    }
}
